import{bH as Ut,bI as qt,bJ as $t,bK as Zt,bL as Qt,bM as Jt,bN as en,K as tn,bO as nn,bP as on,bQ as rn,bR as an,bs as sn,bS as ln,bT as cn,br as un,bU as dn,bV as fn,bW as hn,bX as pn,bY as gn,bZ as mn,b_ as vn,b$ as bn,c0 as yn,c1 as wn,z as En,c2 as Sn,j as Dn,k as _n,c as Cn,i as Tn,c3 as On,c4 as An,c5 as In,c6 as Nn,bg as Mn,A as Pn,n as xn,f as Rn,c7 as Fn,c8 as kn,B as Bn,c9 as Hn,ca as Xn,cb as Yn,cc as Gn,cd as Kn,ce as Ln,cf as jn,cg as Wn,ch as zn,ci as Vn,cj as Un,d as qn,ck as $n,cl as Zn,cm as Qn,cn as Jn,co as eo,cp as to,cq as no,cr as oo,cs as io,aw as ro,ct as ao,cu as so,cv as lo,cw as co,D as uo,cx as fo,cy as ho,cz as po,cA as go,cB as mo,cC as vo,cD as bo,P as yo,Q as wo,cE as Eo,au as So,cF as Do,cG as _o,b1 as Co,cH as To,cI as Oo,cJ as Ao,F as Io,cK as No,cL as Mo,cM as Po,cN as xo,aZ as Ro,bm as Fo,o as ko,x as Bo,Z as Ho,cO as Xo,v as Yo,cP as Go,C as Ko,cQ as Lo,r as jo,cR as Wo,cS as zo,L as Vo,bq as Uo,e as qo,I as $o,by as Zo,cT as Qo,cU as Jo,cV as ei,cW as ti,cX as ni,cY as oi,cZ as ii,c_ as ri,c$ as ai,d0 as si,d1 as li,t as ci,d2 as ui,d3 as di,d4 as fi,d5 as hi,N as pi,d6 as gi,d7 as mi,d8 as vi,l as bi,d9 as yi,da as wi,db as Ei,dc as Si,dd as Di,de as _i,df as Ci,dg as Ti,dh as Oi,di as Ai,dj as Ii,dk as Ni,O as Mi,dl as Pi,dm as xi,w as Ri,dn as Fi,dp as ki,dq as Bi,dr as Hi,h as Xi,ds as Yi,J as Gi,m as Ki,dt as Li,p as ji,du as Wi,dv as Rt}from"./index-B0qHf98Y.js";/**
* vue v3.4.31
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const zi=()=>{},Vi=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Ut,BaseTransitionPropsValidators:qt,Comment:$t,DeprecationTypes:Zt,EffectScope:Qt,ErrorCodes:Jt,ErrorTypeStrings:en,Fragment:tn,KeepAlive:nn,ReactiveEffect:on,Static:rn,Suspense:an,Teleport:sn,Text:ln,TrackOpTypes:cn,Transition:un,TransitionGroup:dn,TriggerOpTypes:fn,VueElement:hn,assertNumber:pn,callWithAsyncErrorHandling:gn,callWithErrorHandling:mn,camelize:vn,capitalize:bn,cloneVNode:yn,compatUtils:wn,compile:zi,computed:En,createApp:Sn,createBlock:Dn,createCommentVNode:_n,createElementBlock:Cn,createElementVNode:Tn,createHydrationRenderer:On,createPropsRestProxy:An,createRenderer:In,createSSRApp:Nn,createSlots:Mn,createStaticVNode:Pn,createTextVNode:xn,createVNode:Rn,customRef:Fn,defineAsyncComponent:kn,defineComponent:Bn,defineCustomElement:Hn,defineEmits:Xn,defineExpose:Yn,defineModel:Gn,defineOptions:Kn,defineProps:Ln,defineSSRCustomElement:jn,defineSlots:Wn,devtools:zn,effect:Vn,effectScope:Un,getCurrentInstance:qn,getCurrentScope:$n,getTransitionRawChildren:Zn,guardReactiveProps:Qn,h:Jn,handleError:eo,hasInjectionContext:to,hydrate:no,initCustomFormatter:oo,initDirectivesForSSR:io,inject:ro,isMemoSame:ao,isProxy:so,isReactive:lo,isReadonly:co,isRef:uo,isRuntimeOnly:fo,isShallow:ho,isVNode:po,markRaw:go,mergeDefaults:mo,mergeModels:vo,mergeProps:bo,nextTick:yo,normalizeClass:wo,normalizeProps:Eo,normalizeStyle:So,onActivated:Do,onBeforeMount:_o,onBeforeUnmount:Co,onBeforeUpdate:To,onDeactivated:Oo,onErrorCaptured:Ao,onMounted:Io,onRenderTracked:No,onRenderTriggered:Mo,onScopeDispose:Po,onServerPrefetch:xo,onUnmounted:Ro,onUpdated:Fo,openBlock:ko,popScopeId:Bo,provide:Ho,proxyRefs:Xo,pushScopeId:Yo,queuePostFlushCb:Go,reactive:Ko,readonly:Lo,ref:jo,registerRuntimeCompiler:Wo,render:zo,renderList:Vo,renderSlot:Uo,resolveComponent:qo,resolveDirective:$o,resolveDynamicComponent:Zo,resolveFilter:Qo,resolveTransitionHooks:Jo,setBlockTracking:ei,setDevtoolsHook:ti,setTransitionHooks:ni,shallowReactive:oi,shallowReadonly:ii,shallowRef:ri,ssrContextKey:ai,ssrUtils:si,stop:li,toDisplayString:ci,toHandlerKey:ui,toHandlers:di,toRaw:fi,toRef:hi,toRefs:pi,toValue:gi,transformVNodeArgs:mi,triggerRef:vi,unref:bi,useAttrs:yi,useCssModule:wi,useCssVars:Ei,useModel:Si,useSSRContext:Di,useSlots:_i,useTransitionState:Ci,vModelCheckbox:Ti,vModelDynamic:Oi,vModelRadio:Ai,vModelSelect:Ii,vModelText:Ni,vShow:Mi,version:Pi,warn:xi,watch:Ri,watchEffect:Fi,watchPostEffect:ki,watchSyncEffect:Bi,withAsyncContext:Hi,withCtx:Xi,withDefaults:Yi,withDirectives:Gi,withKeys:Ki,withMemo:Li,withModifiers:ji,withScopeId:Wi},Symbol.toStringTag,{value:"Module"})),Pr=Rt(Vi);/**!
 * Sortable 1.14.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Dt(o,e){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(o);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),t.push.apply(t,n)}return t}function J(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Dt(Object(t),!0).forEach(function(n){Ui(o,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):Dt(Object(t)).forEach(function(n){Object.defineProperty(o,n,Object.getOwnPropertyDescriptor(t,n))})}return o}function ze(o){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ze=function(e){return typeof e}:ze=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(o)}function Ui(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}function z(){return z=Object.assign||function(o){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(o[n]=t[n])}return o},z.apply(this,arguments)}function qi(o,e){if(o==null)return{};var t={},n=Object.keys(o),i,r;for(r=0;r<n.length;r++)i=n[r],!(e.indexOf(i)>=0)&&(t[i]=o[i]);return t}function $i(o,e){if(o==null)return{};var t=qi(o,e),n,i;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(o);for(i=0;i<r.length;i++)n=r[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(o,n)&&(t[n]=o[n])}return t}function Zi(o){return Qi(o)||Ji(o)||er(o)||tr()}function Qi(o){if(Array.isArray(o))return ht(o)}function Ji(o){if(typeof Symbol<"u"&&o[Symbol.iterator]!=null||o["@@iterator"]!=null)return Array.from(o)}function er(o,e){if(o){if(typeof o=="string")return ht(o,e);var t=Object.prototype.toString.call(o).slice(8,-1);if(t==="Object"&&o.constructor&&(t=o.constructor.name),t==="Map"||t==="Set")return Array.from(o);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ht(o,e)}}function ht(o,e){(e==null||e>o.length)&&(e=o.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=o[t];return n}function tr(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var nr="1.14.0";function te(o){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(o)}var ne=te(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Be=te(/Edge/i),_t=te(/firefox/i),Pe=te(/safari/i)&&!te(/chrome/i)&&!te(/android/i),Ft=te(/iP(ad|od|hone)/i),or=te(/chrome/i)&&te(/android/i),kt={capture:!1,passive:!1};function w(o,e,t){o.addEventListener(e,t,!ne&&kt)}function y(o,e,t){o.removeEventListener(e,t,!ne&&kt)}function Ze(o,e){if(e){if(e[0]===">"&&(e=e.substring(1)),o)try{if(o.matches)return o.matches(e);if(o.msMatchesSelector)return o.msMatchesSelector(e);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(e)}catch{return!1}return!1}}function ir(o){return o.host&&o!==document&&o.host.nodeType?o.host:o.parentNode}function q(o,e,t,n){if(o){t=t||document;do{if(e!=null&&(e[0]===">"?o.parentNode===t&&Ze(o,e):Ze(o,e))||n&&o===t)return o;if(o===t)break}while(o=ir(o))}return null}var Ct=/\s+/g;function O(o,e,t){if(o&&e)if(o.classList)o.classList[t?"add":"remove"](e);else{var n=(" "+o.className+" ").replace(Ct," ").replace(" "+e+" "," ");o.className=(n+(t?" "+e:"")).replace(Ct," ")}}function h(o,e,t){var n=o&&o.style;if(n){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(o,""):o.currentStyle&&(t=o.currentStyle),e===void 0?t:t[e];!(e in n)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),n[e]=t+(typeof t=="string"?"":"px")}}function he(o,e){var t="";if(typeof o=="string")t=o;else do{var n=h(o,"transform");n&&n!=="none"&&(t=n+" "+t)}while(!e&&(o=o.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(t)}function Bt(o,e,t){if(o){var n=o.getElementsByTagName(e),i=0,r=n.length;if(t)for(;i<r;i++)t(n[i],i);return n}return[]}function Q(){var o=document.scrollingElement;return o||document.documentElement}function T(o,e,t,n,i){if(!(!o.getBoundingClientRect&&o!==window)){var r,a,s,l,c,f,d;if(o!==window&&o.parentNode&&o!==Q()?(r=o.getBoundingClientRect(),a=r.top,s=r.left,l=r.bottom,c=r.right,f=r.height,d=r.width):(a=0,s=0,l=window.innerHeight,c=window.innerWidth,f=window.innerHeight,d=window.innerWidth),(e||t)&&o!==window&&(i=i||o.parentNode,!ne))do if(i&&i.getBoundingClientRect&&(h(i,"transform")!=="none"||t&&h(i,"position")!=="static")){var p=i.getBoundingClientRect();a-=p.top+parseInt(h(i,"border-top-width")),s-=p.left+parseInt(h(i,"border-left-width")),l=a+r.height,c=s+r.width;break}while(i=i.parentNode);if(n&&o!==window){var E=he(i||o),b=E&&E.a,S=E&&E.d;E&&(a/=S,s/=b,d/=b,f/=S,l=a+f,c=s+d)}return{top:a,left:s,bottom:l,right:c,width:d,height:f}}}function Tt(o,e,t){for(var n=se(o,!0),i=T(o)[e];n;){var r=T(n)[t],a=void 0;if(a=i>=r,!a)return n;if(n===Q())break;n=se(n,!1)}return!1}function ye(o,e,t,n){for(var i=0,r=0,a=o.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==g.ghost&&(n||a[r]!==g.dragged)&&q(a[r],t.draggable,o,!1)){if(i===e)return a[r];i++}r++}return null}function bt(o,e){for(var t=o.lastElementChild;t&&(t===g.ghost||h(t,"display")==="none"||e&&!Ze(t,e));)t=t.previousElementSibling;return t||null}function N(o,e){var t=0;if(!o||!o.parentNode)return-1;for(;o=o.previousElementSibling;)o.nodeName.toUpperCase()!=="TEMPLATE"&&o!==g.clone&&(!e||Ze(o,e))&&t++;return t}function Ot(o){var e=0,t=0,n=Q();if(o)do{var i=he(o),r=i.a,a=i.d;e+=o.scrollLeft*r,t+=o.scrollTop*a}while(o!==n&&(o=o.parentNode));return[e,t]}function rr(o,e){for(var t in o)if(o.hasOwnProperty(t)){for(var n in e)if(e.hasOwnProperty(n)&&e[n]===o[t][n])return Number(t)}return-1}function se(o,e){if(!o||!o.getBoundingClientRect)return Q();var t=o,n=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var i=h(t);if(t.clientWidth<t.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return Q();if(n||e)return t;n=!0}}while(t=t.parentNode);return Q()}function ar(o,e){if(o&&e)for(var t in e)e.hasOwnProperty(t)&&(o[t]=e[t]);return o}function ot(o,e){return Math.round(o.top)===Math.round(e.top)&&Math.round(o.left)===Math.round(e.left)&&Math.round(o.height)===Math.round(e.height)&&Math.round(o.width)===Math.round(e.width)}var xe;function Ht(o,e){return function(){if(!xe){var t=arguments,n=this;t.length===1?o.call(n,t[0]):o.apply(n,t),xe=setTimeout(function(){xe=void 0},e)}}}function sr(){clearTimeout(xe),xe=void 0}function Xt(o,e,t){o.scrollLeft+=e,o.scrollTop+=t}function yt(o){var e=window.Polymer,t=window.jQuery||window.Zepto;return e&&e.dom?e.dom(o).cloneNode(!0):t?t(o).clone(!0)[0]:o.cloneNode(!0)}function At(o,e){h(o,"position","absolute"),h(o,"top",e.top),h(o,"left",e.left),h(o,"width",e.width),h(o,"height",e.height)}function it(o){h(o,"position",""),h(o,"top",""),h(o,"left",""),h(o,"width",""),h(o,"height","")}var k="Sortable"+new Date().getTime();function lr(){var o=[],e;return{captureAnimationState:function(){if(o=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(h(i,"display")==="none"||i===g.ghost)){o.push({target:i,rect:T(i)});var r=J({},o[o.length-1].rect);if(i.thisAnimationDuration){var a=he(i,!0);a&&(r.top-=a.f,r.left-=a.e)}i.fromRect=r}})}},addAnimationState:function(n){o.push(n)},removeAnimationState:function(n){o.splice(rr(o,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(e),typeof n=="function"&&n();return}var r=!1,a=0;o.forEach(function(s){var l=0,c=s.target,f=c.fromRect,d=T(c),p=c.prevFromRect,E=c.prevToRect,b=s.rect,S=he(c,!0);S&&(d.top-=S.f,d.left-=S.e),c.toRect=d,c.thisAnimationDuration&&ot(p,d)&&!ot(f,d)&&(b.top-d.top)/(b.left-d.left)===(f.top-d.top)/(f.left-d.left)&&(l=ur(b,p,E,i.options)),ot(d,f)||(c.prevFromRect=f,c.prevToRect=d,l||(l=i.options.animation),i.animate(c,b,d,l)),l&&(r=!0,a=Math.max(a,l),clearTimeout(c.animationResetTimer),c.animationResetTimer=setTimeout(function(){c.animationTime=0,c.prevFromRect=null,c.fromRect=null,c.prevToRect=null,c.thisAnimationDuration=null},l),c.thisAnimationDuration=l)}),clearTimeout(e),r?e=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),o=[]},animate:function(n,i,r,a){if(a){h(n,"transition",""),h(n,"transform","");var s=he(this.el),l=s&&s.a,c=s&&s.d,f=(i.left-r.left)/(l||1),d=(i.top-r.top)/(c||1);n.animatingX=!!f,n.animatingY=!!d,h(n,"transform","translate3d("+f+"px,"+d+"px,0)"),this.forRepaintDummy=cr(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function cr(o){return o.offsetWidth}function ur(o,e,t,n){return Math.sqrt(Math.pow(e.top-o.top,2)+Math.pow(e.left-o.left,2))/Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))*n.animation}var pe=[],rt={initializeByDefault:!0},He={mount:function(e){for(var t in rt)rt.hasOwnProperty(t)&&!(t in e)&&(e[t]=rt[t]);pe.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),pe.push(e)},pluginEvent:function(e,t,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var r=e+"Global";pe.forEach(function(a){t[a.pluginName]&&(t[a.pluginName][r]&&t[a.pluginName][r](J({sortable:t},n)),t.options[a.pluginName]&&t[a.pluginName][e]&&t[a.pluginName][e](J({sortable:t},n)))})},initializePlugins:function(e,t,n,i){pe.forEach(function(s){var l=s.pluginName;if(!(!e.options[l]&&!s.initializeByDefault)){var c=new s(e,t,e.options);c.sortable=e,c.options=e.options,e[l]=c,z(n,c.defaults)}});for(var r in e.options)if(e.options.hasOwnProperty(r)){var a=this.modifyOption(e,r,e.options[r]);typeof a<"u"&&(e.options[r]=a)}},getEventProperties:function(e,t){var n={};return pe.forEach(function(i){typeof i.eventProperties=="function"&&z(n,i.eventProperties.call(t[i.pluginName],e))}),n},modifyOption:function(e,t,n){var i;return pe.forEach(function(r){e[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[t]=="function"&&(i=r.optionListeners[t].call(e[r.pluginName],n))}),i}};function Ae(o){var e=o.sortable,t=o.rootEl,n=o.name,i=o.targetEl,r=o.cloneEl,a=o.toEl,s=o.fromEl,l=o.oldIndex,c=o.newIndex,f=o.oldDraggableIndex,d=o.newDraggableIndex,p=o.originalEvent,E=o.putSortable,b=o.extraEventProperties;if(e=e||t&&t[k],!!e){var S,M=e.options,j="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!ne&&!Be?S=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(S=document.createEvent("Event"),S.initEvent(n,!0,!0)),S.to=a||t,S.from=s||t,S.item=i||t,S.clone=r,S.oldIndex=l,S.newIndex=c,S.oldDraggableIndex=f,S.newDraggableIndex=d,S.originalEvent=p,S.pullMode=E?E.lastPutMode:void 0;var x=J(J({},b),He.getEventProperties(n,e));for(var H in x)S[H]=x[H];t&&t.dispatchEvent(S),M[j]&&M[j].call(e,S)}}var dr=["evt"],X=function(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,r=$i(n,dr);He.pluginEvent.bind(g)(e,t,J({dragEl:u,parentEl:A,ghostEl:v,rootEl:C,nextEl:fe,lastDownEl:Ve,cloneEl:I,cloneHidden:ae,dragStarted:Ie,putSortable:R,activeSortable:g.active,originalEvent:i,oldIndex:be,oldDraggableIndex:Re,newIndex:L,newDraggableIndex:re,hideGhostForTarget:Lt,unhideGhostForTarget:jt,cloneNowHidden:function(){ae=!0},cloneNowShown:function(){ae=!1},dispatchSortableEvent:function(s){B({sortable:t,name:s,originalEvent:i})}},r))};function B(o){Ae(J({putSortable:R,cloneEl:I,targetEl:u,rootEl:C,oldIndex:be,oldDraggableIndex:Re,newIndex:L,newDraggableIndex:re},o))}var u,A,v,C,fe,Ve,I,ae,be,L,Re,re,Ye,R,ve=!1,Qe=!1,Je=[],ue,V,at,st,It,Nt,Ie,ge,Fe,ke=!1,Ge=!1,Ue,F,lt=[],pt=!1,et=[],nt=typeof document<"u",Ke=Ft,Mt=Be||ne?"cssFloat":"float",fr=nt&&!or&&!Ft&&"draggable"in document.createElement("div"),Yt=function(){if(nt){if(ne)return!1;var o=document.createElement("x");return o.style.cssText="pointer-events:auto",o.style.pointerEvents==="auto"}}(),Gt=function(e,t){var n=h(e),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=ye(e,0,t),a=ye(e,1,t),s=r&&h(r),l=a&&h(a),c=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+T(r).width,f=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+T(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&s.float&&s.float!=="none"){var d=s.float==="left"?"left":"right";return a&&(l.clear==="both"||l.clear===d)?"vertical":"horizontal"}return r&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||c>=i&&n[Mt]==="none"||a&&n[Mt]==="none"&&c+f>i)?"vertical":"horizontal"},hr=function(e,t,n){var i=n?e.left:e.top,r=n?e.right:e.bottom,a=n?e.width:e.height,s=n?t.left:t.top,l=n?t.right:t.bottom,c=n?t.width:t.height;return i===s||r===l||i+a/2===s+c/2},pr=function(e,t){var n;return Je.some(function(i){var r=i[k].options.emptyInsertThreshold;if(!(!r||bt(i))){var a=T(i),s=e>=a.left-r&&e<=a.right+r,l=t>=a.top-r&&t<=a.bottom+r;if(s&&l)return n=i}}),n},Kt=function(e){function t(r,a){return function(s,l,c,f){var d=s.options.group.name&&l.options.group.name&&s.options.group.name===l.options.group.name;if(r==null&&(a||d))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return t(r(s,l,c,f),a)(s,l,c,f);var p=(a?s:l).options.group.name;return r===!0||typeof r=="string"&&r===p||r.join&&r.indexOf(p)>-1}}var n={},i=e.group;(!i||ze(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=t(i.pull,!0),n.checkPut=t(i.put),n.revertClone=i.revertClone,e.group=n},Lt=function(){!Yt&&v&&h(v,"display","none")},jt=function(){!Yt&&v&&h(v,"display","")};nt&&document.addEventListener("click",function(o){if(Qe)return o.preventDefault(),o.stopPropagation&&o.stopPropagation(),o.stopImmediatePropagation&&o.stopImmediatePropagation(),Qe=!1,!1},!0);var de=function(e){if(u){e=e.touches?e.touches[0]:e;var t=pr(e.clientX,e.clientY);if(t){var n={};for(var i in e)e.hasOwnProperty(i)&&(n[i]=e[i]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[k]._onDragOver(n)}}},gr=function(e){u&&u.parentNode[k]._isOutsideThisEl(e.target)};function g(o,e){if(!(o&&o.nodeType&&o.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));this.el=o,this.options=e=z({},e),o[k]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(o.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Gt(o,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,s){a.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&!Pe,emptyInsertThreshold:5};He.initializePlugins(this,o,t);for(var n in t)!(n in e)&&(e[n]=t[n]);Kt(e);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=e.forceFallback?!1:fr,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?w(o,"pointerdown",this._onTapStart):(w(o,"mousedown",this._onTapStart),w(o,"touchstart",this._onTapStart)),this.nativeDraggable&&(w(o,"dragover",this),w(o,"dragenter",this)),Je.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),z(this,lr())}g.prototype={constructor:g,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(ge=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,u):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,i=this.options,r=i.preventOnFilter,a=e.type,s=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,l=(s||e).target,c=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||l,f=i.filter;if(Dr(n),!u&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||i.disabled)&&!c.isContentEditable&&!(!this.nativeDraggable&&Pe&&l&&l.tagName.toUpperCase()==="SELECT")&&(l=q(l,i.draggable,n,!1),!(l&&l.animated)&&Ve!==l)){if(be=N(l),Re=N(l,i.draggable),typeof f=="function"){if(f.call(this,e,l,this)){B({sortable:t,rootEl:c,name:"filter",targetEl:l,toEl:n,fromEl:n}),X("filter",t,{evt:e}),r&&e.cancelable&&e.preventDefault();return}}else if(f&&(f=f.split(",").some(function(d){if(d=q(c,d.trim(),n,!1),d)return B({sortable:t,rootEl:d,name:"filter",targetEl:l,fromEl:n,toEl:n}),X("filter",t,{evt:e}),!0}),f)){r&&e.cancelable&&e.preventDefault();return}i.handle&&!q(c,i.handle,n,!1)||this._prepareDragStart(e,s,l)}}},_prepareDragStart:function(e,t,n){var i=this,r=i.el,a=i.options,s=r.ownerDocument,l;if(n&&!u&&n.parentNode===r){var c=T(n);if(C=r,u=n,A=u.parentNode,fe=u.nextSibling,Ve=n,Ye=a.group,g.dragged=u,ue={target:u,clientX:(t||e).clientX,clientY:(t||e).clientY},It=ue.clientX-c.left,Nt=ue.clientY-c.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,u.style["will-change"]="all",l=function(){if(X("delayEnded",i,{evt:e}),g.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!_t&&i.nativeDraggable&&(u.draggable=!0),i._triggerDragStart(e,t),B({sortable:i,name:"choose",originalEvent:e}),O(u,a.chosenClass,!0)},a.ignore.split(",").forEach(function(f){Bt(u,f.trim(),ct)}),w(s,"dragover",de),w(s,"mousemove",de),w(s,"touchmove",de),w(s,"mouseup",i._onDrop),w(s,"touchend",i._onDrop),w(s,"touchcancel",i._onDrop),_t&&this.nativeDraggable&&(this.options.touchStartThreshold=4,u.draggable=!0),X("delayStart",this,{evt:e}),a.delay&&(!a.delayOnTouchOnly||t)&&(!this.nativeDraggable||!(Be||ne))){if(g.eventCanceled){this._onDrop();return}w(s,"mouseup",i._disableDelayedDrag),w(s,"touchend",i._disableDelayedDrag),w(s,"touchcancel",i._disableDelayedDrag),w(s,"mousemove",i._delayedDragTouchMoveHandler),w(s,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&w(s,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(l,a.delay)}else l()}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){u&&ct(u),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._disableDelayedDrag),y(e,"touchend",this._disableDelayedDrag),y(e,"touchcancel",this._disableDelayedDrag),y(e,"mousemove",this._delayedDragTouchMoveHandler),y(e,"touchmove",this._delayedDragTouchMoveHandler),y(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?w(document,"pointermove",this._onTouchMove):t?w(document,"touchmove",this._onTouchMove):w(document,"mousemove",this._onTouchMove):(w(u,"dragend",this),w(C,"dragstart",this._onDragStart));try{document.selection?qe(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(ve=!1,C&&u){X("dragStarted",this,{evt:t}),this.nativeDraggable&&w(document,"dragover",gr);var n=this.options;!e&&O(u,n.dragClass,!1),O(u,n.ghostClass,!0),g.active=this,e&&this._appendGhost(),B({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(V){this._lastX=V.clientX,this._lastY=V.clientY,Lt();for(var e=document.elementFromPoint(V.clientX,V.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(V.clientX,V.clientY),e!==t);)t=e;if(u.parentNode[k]._isOutsideThisEl(e),t)do{if(t[k]){var n=void 0;if(n=t[k]._onDragOver({clientX:V.clientX,clientY:V.clientY,target:e,rootEl:t}),n&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);jt()}},_onTouchMove:function(e){if(ue){var t=this.options,n=t.fallbackTolerance,i=t.fallbackOffset,r=e.touches?e.touches[0]:e,a=v&&he(v,!0),s=v&&a&&a.a,l=v&&a&&a.d,c=Ke&&F&&Ot(F),f=(r.clientX-ue.clientX+i.x)/(s||1)+(c?c[0]-lt[0]:0)/(s||1),d=(r.clientY-ue.clientY+i.y)/(l||1)+(c?c[1]-lt[1]:0)/(l||1);if(!g.active&&!ve){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(v){a?(a.e+=f-(at||0),a.f+=d-(st||0)):a={a:1,b:0,c:0,d:1,e:f,f:d};var p="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(v,"webkitTransform",p),h(v,"mozTransform",p),h(v,"msTransform",p),h(v,"transform",p),at=f,st=d,V=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!v){var e=this.options.fallbackOnBody?document.body:C,t=T(u,!0,Ke,!0,e),n=this.options;if(Ke){for(F=e;h(F,"position")==="static"&&h(F,"transform")==="none"&&F!==document;)F=F.parentNode;F!==document.body&&F!==document.documentElement?(F===document&&(F=Q()),t.top+=F.scrollTop,t.left+=F.scrollLeft):F=Q(),lt=Ot(F)}v=u.cloneNode(!0),O(v,n.ghostClass,!1),O(v,n.fallbackClass,!0),O(v,n.dragClass,!0),h(v,"transition",""),h(v,"transform",""),h(v,"box-sizing","border-box"),h(v,"margin",0),h(v,"top",t.top),h(v,"left",t.left),h(v,"width",t.width),h(v,"height",t.height),h(v,"opacity","0.8"),h(v,"position",Ke?"absolute":"fixed"),h(v,"zIndex","100000"),h(v,"pointerEvents","none"),g.ghost=v,e.appendChild(v),h(v,"transform-origin",It/parseInt(v.style.width)*100+"% "+Nt/parseInt(v.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,i=e.dataTransfer,r=n.options;if(X("dragStart",this,{evt:e}),g.eventCanceled){this._onDrop();return}X("setupClone",this),g.eventCanceled||(I=yt(u),I.draggable=!1,I.style["will-change"]="",this._hideClone(),O(I,this.options.chosenClass,!1),g.clone=I),n.cloneId=qe(function(){X("clone",n),!g.eventCanceled&&(n.options.removeCloneOnHide||C.insertBefore(I,u),n._hideClone(),B({sortable:n,name:"clone"}))}),!t&&O(u,r.dragClass,!0),t?(Qe=!0,n._loopId=setInterval(n._emulateDragOver,50)):(y(document,"mouseup",n._onDrop),y(document,"touchend",n._onDrop),y(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",r.setData&&r.setData.call(n,i,u)),w(document,"drop",n),h(u,"transform","translateZ(0)")),ve=!0,n._dragStartId=qe(n._dragStarted.bind(n,t,e)),w(document,"selectstart",n),Ie=!0,Pe&&h(document.body,"user-select","none")},_onDragOver:function(e){var t=this.el,n=e.target,i,r,a,s=this.options,l=s.group,c=g.active,f=Ye===l,d=s.sort,p=R||c,E,b=this,S=!1;if(pt)return;function M(_e,zt){X(_e,b,J({evt:e,isOwner:f,axis:E?"vertical":"horizontal",revert:a,dragRect:i,targetRect:r,canSort:d,fromSortable:p,target:n,completed:x,onMove:function(St,Vt){return Le(C,t,u,i,St,T(St),e,Vt)},changed:H},zt))}function j(){M("dragOverAnimationCapture"),b.captureAnimationState(),b!==p&&p.captureAnimationState()}function x(_e){return M("dragOverCompleted",{insertion:_e}),_e&&(f?c._hideClone():c._showClone(b),b!==p&&(O(u,R?R.options.ghostClass:c.options.ghostClass,!1),O(u,s.ghostClass,!0)),R!==b&&b!==g.active?R=b:b===g.active&&R&&(R=null),p===b&&(b._ignoreWhileAnimating=n),b.animateAll(function(){M("dragOverAnimationComplete"),b._ignoreWhileAnimating=null}),b!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(n===u&&!u.animated||n===t&&!n.animated)&&(ge=null),!s.dragoverBubble&&!e.rootEl&&n!==document&&(u.parentNode[k]._isOutsideThisEl(e.target),!_e&&de(e)),!s.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),S=!0}function H(){L=N(u),re=N(u,s.draggable),B({sortable:b,name:"change",toEl:t,newIndex:L,newDraggableIndex:re,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),n=q(n,s.draggable,t,!0),M("dragOver"),g.eventCanceled)return S;if(u.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||b._ignoreWhileAnimating===n)return x(!1);if(Qe=!1,c&&!s.disabled&&(f?d||(a=A!==C):R===this||(this.lastPutMode=Ye.checkPull(this,c,u,e))&&l.checkPut(this,c,u,e))){if(E=this._getDirection(e,n)==="vertical",i=T(u),M("dragOverValid"),g.eventCanceled)return S;if(a)return A=C,j(),this._hideClone(),M("revert"),g.eventCanceled||(fe?C.insertBefore(u,fe):C.appendChild(u)),x(!0);var D=bt(t,s.draggable);if(!D||yr(e,E,this)&&!D.animated){if(D===u)return x(!1);if(D&&t===e.target&&(n=D),n&&(r=T(n)),Le(C,t,u,i,n,r,e,!!n)!==!1)return j(),t.appendChild(u),A=t,H(),x(!0)}else if(D&&br(e,E,this)){var $=ye(t,0,s,!0);if($===u)return x(!1);if(n=$,r=T(n),Le(C,t,u,i,n,r,e,!1)!==!1)return j(),t.insertBefore(u,$),A=t,H(),x(!0)}else if(n.parentNode===t){r=T(n);var Z=0,le,we=u.parentNode!==t,G=!hr(u.animated&&u.toRect||i,n.animated&&n.toRect||r,E),Ee=E?"top":"left",oe=Tt(n,"top","top")||Tt(u,"top","top"),Se=oe?oe.scrollTop:void 0;ge!==n&&(le=r[Ee],ke=!1,Ge=!G&&s.invertSwap||we),Z=wr(e,n,r,E,G?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,Ge,ge===n);var ee;if(Z!==0){var ce=N(u);do ce-=Z,ee=A.children[ce];while(ee&&(h(ee,"display")==="none"||ee===v))}if(Z===0||ee===n)return x(!1);ge=n,Fe=Z;var De=n.nextElementSibling,ie=!1;ie=Z===1;var Xe=Le(C,t,u,i,n,r,e,ie);if(Xe!==!1)return(Xe===1||Xe===-1)&&(ie=Xe===1),pt=!0,setTimeout(vr,30),j(),ie&&!De?t.appendChild(u):n.parentNode.insertBefore(u,ie?De:n),oe&&Xt(oe,0,Se-oe.scrollTop),A=u.parentNode,le!==void 0&&!Ge&&(Ue=Math.abs(le-T(n)[Ee])),H(),x(!0)}if(t.contains(u))return x(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){y(document,"mousemove",this._onTouchMove),y(document,"touchmove",this._onTouchMove),y(document,"pointermove",this._onTouchMove),y(document,"dragover",de),y(document,"mousemove",de),y(document,"touchmove",de)},_offUpEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._onDrop),y(e,"touchend",this._onDrop),y(e,"pointerup",this._onDrop),y(e,"touchcancel",this._onDrop),y(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;if(L=N(u),re=N(u,n.draggable),X("drop",this,{evt:e}),A=u&&u.parentNode,L=N(u),re=N(u,n.draggable),g.eventCanceled){this._nulling();return}ve=!1,Ge=!1,ke=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),gt(this.cloneId),gt(this._dragStartId),this.nativeDraggable&&(y(document,"drop",this),y(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Pe&&h(document.body,"user-select",""),h(u,"transform",""),e&&(Ie&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),v&&v.parentNode&&v.parentNode.removeChild(v),(C===A||R&&R.lastPutMode!=="clone")&&I&&I.parentNode&&I.parentNode.removeChild(I),u&&(this.nativeDraggable&&y(u,"dragend",this),ct(u),u.style["will-change"]="",Ie&&!ve&&O(u,R?R.options.ghostClass:this.options.ghostClass,!1),O(u,this.options.chosenClass,!1),B({sortable:this,name:"unchoose",toEl:A,newIndex:null,newDraggableIndex:null,originalEvent:e}),C!==A?(L>=0&&(B({rootEl:A,name:"add",toEl:A,fromEl:C,originalEvent:e}),B({sortable:this,name:"remove",toEl:A,originalEvent:e}),B({rootEl:A,name:"sort",toEl:A,fromEl:C,originalEvent:e}),B({sortable:this,name:"sort",toEl:A,originalEvent:e})),R&&R.save()):L!==be&&L>=0&&(B({sortable:this,name:"update",toEl:A,originalEvent:e}),B({sortable:this,name:"sort",toEl:A,originalEvent:e})),g.active&&((L==null||L===-1)&&(L=be,re=Re),B({sortable:this,name:"end",toEl:A,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){X("nulling",this),C=u=A=v=fe=I=Ve=ae=ue=V=Ie=L=re=be=Re=ge=Fe=R=Ye=g.dragged=g.ghost=g.clone=g.active=null,et.forEach(function(e){e.checked=!0}),et.length=at=st=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":u&&(this._onDragOver(e),mr(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],t,n=this.el.children,i=0,r=n.length,a=this.options;i<r;i++)t=n[i],q(t,a.draggable,this.el,!1)&&e.push(t.getAttribute(a.dataIdAttr)||Sr(t));return e},sort:function(e,t){var n={},i=this.el;this.toArray().forEach(function(r,a){var s=i.children[a];q(s,this.options.draggable,i,!1)&&(n[r]=s)},this),t&&this.captureAnimationState(),e.forEach(function(r){n[r]&&(i.removeChild(n[r]),i.appendChild(n[r]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return q(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(t===void 0)return n[e];var i=He.modifyOption(this,e,t);typeof i<"u"?n[e]=i:n[e]=t,e==="group"&&Kt(n)},destroy:function(){X("destroy",this);var e=this.el;e[k]=null,y(e,"mousedown",this._onTapStart),y(e,"touchstart",this._onTapStart),y(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(y(e,"dragover",this),y(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Je.splice(Je.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!ae){if(X("hideClone",this),g.eventCanceled)return;h(I,"display","none"),this.options.removeCloneOnHide&&I.parentNode&&I.parentNode.removeChild(I),ae=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(ae){if(X("showClone",this),g.eventCanceled)return;u.parentNode==C&&!this.options.group.revertClone?C.insertBefore(I,u):fe?C.insertBefore(I,fe):C.appendChild(I),this.options.group.revertClone&&this.animate(u,I),h(I,"display",""),ae=!1}}};function mr(o){o.dataTransfer&&(o.dataTransfer.dropEffect="move"),o.cancelable&&o.preventDefault()}function Le(o,e,t,n,i,r,a,s){var l,c=o[k],f=c.options.onMove,d;return window.CustomEvent&&!ne&&!Be?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=e,l.from=o,l.dragged=t,l.draggedRect=n,l.related=i||e,l.relatedRect=r||T(e),l.willInsertAfter=s,l.originalEvent=a,o.dispatchEvent(l),f&&(d=f.call(c,l,a)),d}function ct(o){o.draggable=!1}function vr(){pt=!1}function br(o,e,t){var n=T(ye(t.el,0,t.options,!0)),i=10;return e?o.clientX<n.left-i||o.clientY<n.top&&o.clientX<n.right:o.clientY<n.top-i||o.clientY<n.bottom&&o.clientX<n.left}function yr(o,e,t){var n=T(bt(t.el,t.options.draggable)),i=10;return e?o.clientX>n.right+i||o.clientX<=n.right&&o.clientY>n.bottom&&o.clientX>=n.left:o.clientX>n.right&&o.clientY>n.top||o.clientX<=n.right&&o.clientY>n.bottom+i}function wr(o,e,t,n,i,r,a,s){var l=n?o.clientY:o.clientX,c=n?t.height:t.width,f=n?t.top:t.left,d=n?t.bottom:t.right,p=!1;if(!a){if(s&&Ue<c*i){if(!ke&&(Fe===1?l>f+c*r/2:l<d-c*r/2)&&(ke=!0),ke)p=!0;else if(Fe===1?l<f+Ue:l>d-Ue)return-Fe}else if(l>f+c*(1-i)/2&&l<d-c*(1-i)/2)return Er(e)}return p=p||a,p&&(l<f+c*r/2||l>d-c*r/2)?l>f+c/2?1:-1:0}function Er(o){return N(u)<N(o)?1:-1}function Sr(o){for(var e=o.tagName+o.className+o.src+o.href+o.textContent,t=e.length,n=0;t--;)n+=e.charCodeAt(t);return n.toString(36)}function Dr(o){et.length=0;for(var e=o.getElementsByTagName("input"),t=e.length;t--;){var n=e[t];n.checked&&et.push(n)}}function qe(o){return setTimeout(o,0)}function gt(o){return clearTimeout(o)}nt&&w(document,"touchmove",function(o){(g.active||ve)&&o.cancelable&&o.preventDefault()});g.utils={on:w,off:y,css:h,find:Bt,is:function(e,t){return!!q(e,t,e,!1)},extend:ar,throttle:Ht,closest:q,toggleClass:O,clone:yt,index:N,nextTick:qe,cancelNextTick:gt,detectDirection:Gt,getChild:ye};g.get=function(o){return o[k]};g.mount=function(){for(var o=arguments.length,e=new Array(o),t=0;t<o;t++)e[t]=arguments[t];e[0].constructor===Array&&(e=e[0]),e.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(g.utils=J(J({},g.utils),n.utils)),He.mount(n)})};g.create=function(o,e){return new g(o,e)};g.version=nr;var P=[],Ne,mt,vt=!1,ut,dt,tt,Me;function _r(){function o(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return o.prototype={dragStarted:function(t){var n=t.originalEvent;this.sortable.nativeDraggable?w(document,"dragover",this._handleAutoScroll):this.options.supportPointer?w(document,"pointermove",this._handleFallbackAutoScroll):n.touches?w(document,"touchmove",this._handleFallbackAutoScroll):w(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var n=t.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):(y(document,"pointermove",this._handleFallbackAutoScroll),y(document,"touchmove",this._handleFallbackAutoScroll),y(document,"mousemove",this._handleFallbackAutoScroll)),Pt(),$e(),sr()},nulling:function(){tt=mt=Ne=vt=Me=ut=dt=null,P.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,n){var i=this,r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,s=document.elementFromPoint(r,a);if(tt=t,n||this.options.forceAutoScrollFallback||Be||ne||Pe){ft(t,this.options,s,n);var l=se(s,!0);vt&&(!Me||r!==ut||a!==dt)&&(Me&&Pt(),Me=setInterval(function(){var c=se(document.elementFromPoint(r,a),!0);c!==l&&(l=c,$e()),ft(t,i.options,c,n)},10),ut=r,dt=a)}else{if(!this.options.bubbleScroll||se(s,!0)===Q()){$e();return}ft(t,this.options,se(s,!1),!1)}}},z(o,{pluginName:"scroll",initializeByDefault:!0})}function $e(){P.forEach(function(o){clearInterval(o.pid)}),P=[]}function Pt(){clearInterval(Me)}var ft=Ht(function(o,e,t,n){if(e.scroll){var i=(o.touches?o.touches[0]:o).clientX,r=(o.touches?o.touches[0]:o).clientY,a=e.scrollSensitivity,s=e.scrollSpeed,l=Q(),c=!1,f;mt!==t&&(mt=t,$e(),Ne=e.scroll,f=e.scrollFn,Ne===!0&&(Ne=se(t,!0)));var d=0,p=Ne;do{var E=p,b=T(E),S=b.top,M=b.bottom,j=b.left,x=b.right,H=b.width,D=b.height,$=void 0,Z=void 0,le=E.scrollWidth,we=E.scrollHeight,G=h(E),Ee=E.scrollLeft,oe=E.scrollTop;E===l?($=H<le&&(G.overflowX==="auto"||G.overflowX==="scroll"||G.overflowX==="visible"),Z=D<we&&(G.overflowY==="auto"||G.overflowY==="scroll"||G.overflowY==="visible")):($=H<le&&(G.overflowX==="auto"||G.overflowX==="scroll"),Z=D<we&&(G.overflowY==="auto"||G.overflowY==="scroll"));var Se=$&&(Math.abs(x-i)<=a&&Ee+H<le)-(Math.abs(j-i)<=a&&!!Ee),ee=Z&&(Math.abs(M-r)<=a&&oe+D<we)-(Math.abs(S-r)<=a&&!!oe);if(!P[d])for(var ce=0;ce<=d;ce++)P[ce]||(P[ce]={});(P[d].vx!=Se||P[d].vy!=ee||P[d].el!==E)&&(P[d].el=E,P[d].vx=Se,P[d].vy=ee,clearInterval(P[d].pid),(Se!=0||ee!=0)&&(c=!0,P[d].pid=setInterval((function(){n&&this.layer===0&&g.active._onTouchMove(tt);var De=P[this.layer].vy?P[this.layer].vy*s:0,ie=P[this.layer].vx?P[this.layer].vx*s:0;typeof f=="function"&&f.call(g.dragged.parentNode[k],ie,De,o,tt,P[this.layer].el)!=="continue"||Xt(P[this.layer].el,ie,De)}).bind({layer:d}),24))),d++}while(e.bubbleScroll&&p!==l&&(p=se(p,!1)));vt=c}},30),Wt=function(e){var t=e.originalEvent,n=e.putSortable,i=e.dragEl,r=e.activeSortable,a=e.dispatchSortableEvent,s=e.hideGhostForTarget,l=e.unhideGhostForTarget;if(t){var c=n||r;s();var f=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,d=document.elementFromPoint(f.clientX,f.clientY);l(),c&&!c.el.contains(d)&&(a("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function wt(){}wt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=ye(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(t,i):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:Wt};z(wt,{pluginName:"revertOnSpill"});function Et(){}Et.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable,i=n||this.sortable;i.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),i.animateAll()},drop:Wt};z(Et,{pluginName:"removeOnSpill"});var W;function Cr(){function o(){this.defaults={swapClass:"sortable-swap-highlight"}}return o.prototype={dragStart:function(t){var n=t.dragEl;W=n},dragOverValid:function(t){var n=t.completed,i=t.target,r=t.onMove,a=t.activeSortable,s=t.changed,l=t.cancel;if(a.options.swap){var c=this.sortable.el,f=this.options;if(i&&i!==c){var d=W;r(i)!==!1?(O(i,f.swapClass,!0),W=i):W=null,d&&d!==W&&O(d,f.swapClass,!1)}s(),n(!0),l()}},drop:function(t){var n=t.activeSortable,i=t.putSortable,r=t.dragEl,a=i||this.sortable,s=this.options;W&&O(W,s.swapClass,!1),W&&(s.swap||i&&i.options.swap)&&r!==W&&(a.captureAnimationState(),a!==n&&n.captureAnimationState(),Tr(r,W),a.animateAll(),a!==n&&n.animateAll())},nulling:function(){W=null}},z(o,{pluginName:"swap",eventProperties:function(){return{swapItem:W}}})}function Tr(o,e){var t=o.parentNode,n=e.parentNode,i,r;!t||!n||t.isEqualNode(e)||n.isEqualNode(o)||(i=N(o),r=N(e),t.isEqualNode(n)&&i<r&&r++,t.insertBefore(e,t.children[i]),n.insertBefore(o,n.children[r]))}var m=[],K=[],Ce,U,Te=!1,Y=!1,me=!1,_,Oe,je;function Or(){function o(e){for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this));e.options.supportPointer?w(document,"pointerup",this._deselectMultiDrag):(w(document,"mouseup",this._deselectMultiDrag),w(document,"touchend",this._deselectMultiDrag)),w(document,"keydown",this._checkKeyDown),w(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(i,r){var a="";m.length&&U===e?m.forEach(function(s,l){a+=(l?", ":"")+s.textContent}):a=r.textContent,i.setData("Text",a)}}}return o.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var n=t.dragEl;_=n},delayEnded:function(){this.isMultiDrag=~m.indexOf(_)},setupClone:function(t){var n=t.sortable,i=t.cancel;if(this.isMultiDrag){for(var r=0;r<m.length;r++)K.push(yt(m[r])),K[r].sortableIndex=m[r].sortableIndex,K[r].draggable=!1,K[r].style["will-change"]="",O(K[r],this.options.selectedClass,!1),m[r]===_&&O(K[r],this.options.chosenClass,!1);n._hideClone(),i()}},clone:function(t){var n=t.sortable,i=t.rootEl,r=t.dispatchSortableEvent,a=t.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||m.length&&U===n&&(xt(!0,i),r("clone"),a()))},showClone:function(t){var n=t.cloneNowShown,i=t.rootEl,r=t.cancel;this.isMultiDrag&&(xt(!1,i),K.forEach(function(a){h(a,"display","")}),n(),je=!1,r())},hideClone:function(t){var n=this;t.sortable;var i=t.cloneNowHidden,r=t.cancel;this.isMultiDrag&&(K.forEach(function(a){h(a,"display","none"),n.options.removeCloneOnHide&&a.parentNode&&a.parentNode.removeChild(a)}),i(),je=!0,r())},dragStartGlobal:function(t){t.sortable,!this.isMultiDrag&&U&&U.multiDrag._deselectMultiDrag(),m.forEach(function(n){n.sortableIndex=N(n)}),m=m.sort(function(n,i){return n.sortableIndex-i.sortableIndex}),me=!0},dragStarted:function(t){var n=this,i=t.sortable;if(this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){m.forEach(function(a){a!==_&&h(a,"position","absolute")});var r=T(_,!1,!0,!0);m.forEach(function(a){a!==_&&At(a,r)}),Y=!0,Te=!0}i.animateAll(function(){Y=!1,Te=!1,n.options.animation&&m.forEach(function(a){it(a)}),n.options.sort&&We()})}},dragOver:function(t){var n=t.target,i=t.completed,r=t.cancel;Y&&~m.indexOf(n)&&(i(!1),r())},revert:function(t){var n=t.fromSortable,i=t.rootEl,r=t.sortable,a=t.dragRect;m.length>1&&(m.forEach(function(s){r.addAnimationState({target:s,rect:Y?T(s):a}),it(s),s.fromRect=a,n.removeAnimationState(s)}),Y=!1,Ar(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(t){var n=t.sortable,i=t.isOwner,r=t.insertion,a=t.activeSortable,s=t.parentEl,l=t.putSortable,c=this.options;if(r){if(i&&a._hideClone(),Te=!1,c.animation&&m.length>1&&(Y||!i&&!a.options.sort&&!l)){var f=T(_,!1,!0,!0);m.forEach(function(p){p!==_&&(At(p,f),s.appendChild(p))}),Y=!0}if(!i)if(Y||We(),m.length>1){var d=je;a._showClone(n),a.options.animation&&!je&&d&&K.forEach(function(p){a.addAnimationState({target:p,rect:Oe}),p.fromRect=Oe,p.thisAnimationDuration=null})}else a._showClone(n)}},dragOverAnimationCapture:function(t){var n=t.dragRect,i=t.isOwner,r=t.activeSortable;if(m.forEach(function(s){s.thisAnimationDuration=null}),r.options.animation&&!i&&r.multiDrag.isMultiDrag){Oe=z({},n);var a=he(_,!0);Oe.top-=a.f,Oe.left-=a.e}},dragOverAnimationComplete:function(){Y&&(Y=!1,We())},drop:function(t){var n=t.originalEvent,i=t.rootEl,r=t.parentEl,a=t.sortable,s=t.dispatchSortableEvent,l=t.oldIndex,c=t.putSortable,f=c||this.sortable;if(n){var d=this.options,p=r.children;if(!me)if(d.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),O(_,d.selectedClass,!~m.indexOf(_)),~m.indexOf(_))m.splice(m.indexOf(_),1),Ce=null,Ae({sortable:a,rootEl:i,name:"deselect",targetEl:_});else{if(m.push(_),Ae({sortable:a,rootEl:i,name:"select",targetEl:_}),n.shiftKey&&Ce&&a.el.contains(Ce)){var E=N(Ce),b=N(_);if(~E&&~b&&E!==b){var S,M;for(b>E?(M=E,S=b):(M=b,S=E+1);M<S;M++)~m.indexOf(p[M])||(O(p[M],d.selectedClass,!0),m.push(p[M]),Ae({sortable:a,rootEl:i,name:"select",targetEl:p[M]}))}}else Ce=_;U=f}if(me&&this.isMultiDrag){if(Y=!1,(r[k].options.sort||r!==i)&&m.length>1){var j=T(_),x=N(_,":not(."+this.options.selectedClass+")");if(!Te&&d.animation&&(_.thisAnimationDuration=null),f.captureAnimationState(),!Te&&(d.animation&&(_.fromRect=j,m.forEach(function(D){if(D.thisAnimationDuration=null,D!==_){var $=Y?T(D):j;D.fromRect=$,f.addAnimationState({target:D,rect:$})}})),We(),m.forEach(function(D){p[x]?r.insertBefore(D,p[x]):r.appendChild(D),x++}),l===N(_))){var H=!1;m.forEach(function(D){if(D.sortableIndex!==N(D)){H=!0;return}}),H&&s("update")}m.forEach(function(D){it(D)}),f.animateAll()}U=f}(i===r||c&&c.lastPutMode!=="clone")&&K.forEach(function(D){D.parentNode&&D.parentNode.removeChild(D)})}},nullingGlobal:function(){this.isMultiDrag=me=!1,K.length=0},destroyGlobal:function(){this._deselectMultiDrag(),y(document,"pointerup",this._deselectMultiDrag),y(document,"mouseup",this._deselectMultiDrag),y(document,"touchend",this._deselectMultiDrag),y(document,"keydown",this._checkKeyDown),y(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!(typeof me<"u"&&me)&&U===this.sortable&&!(t&&q(t.target,this.options.draggable,this.sortable.el,!1))&&!(t&&t.button!==0))for(;m.length;){var n=m[0];O(n,this.options.selectedClass,!1),m.shift(),Ae({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:n})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},z(o,{pluginName:"multiDrag",utils:{select:function(t){var n=t.parentNode[k];!n||!n.options.multiDrag||~m.indexOf(t)||(U&&U!==n&&(U.multiDrag._deselectMultiDrag(),U=n),O(t,n.options.selectedClass,!0),m.push(t))},deselect:function(t){var n=t.parentNode[k],i=m.indexOf(t);!n||!n.options.multiDrag||!~i||(O(t,n.options.selectedClass,!1),m.splice(i,1))}},eventProperties:function(){var t=this,n=[],i=[];return m.forEach(function(r){n.push({multiDragElement:r,index:r.sortableIndex});var a;Y&&r!==_?a=-1:Y?a=N(r,":not(."+t.options.selectedClass+")"):a=N(r),i.push({multiDragElement:r,index:a})}),{items:Zi(m),clones:[].concat(K),oldIndicies:n,newIndicies:i}},optionListeners:{multiDragKey:function(t){return t=t.toLowerCase(),t==="ctrl"?t="Control":t.length>1&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}function Ar(o,e){m.forEach(function(t,n){var i=e.children[t.sortableIndex+(o?Number(n):0)];i?e.insertBefore(t,i):e.appendChild(t)})}function xt(o,e){K.forEach(function(t,n){var i=e.children[t.sortableIndex+(o?Number(n):0)];i?e.insertBefore(t,i):e.appendChild(t)})}function We(){m.forEach(function(o){o!==_&&o.parentNode&&o.parentNode.removeChild(o)})}g.mount(new _r);g.mount(Et,wt);const Ir=Object.freeze(Object.defineProperty({__proto__:null,MultiDrag:Or,Sortable:g,Swap:Cr,default:g},Symbol.toStringTag,{value:"Module"})),xr=Rt(Ir);export{xr as a,Pr as r};
