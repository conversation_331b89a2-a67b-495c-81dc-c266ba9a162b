import{_ as Ce,d as Ie,u as De,a as xe,r as b,z as y,w as Pe,F as Ne,e as s,I as Ue,J as Se,c as Y,i as f,f as e,j as k,k as A,t as I,h as l,n as c,o as g,K as B,L as E,l as L,G as V,v as Ye,x as Ae,M as le}from"./index-B0qHf98Y.js";import{g as Me,a as qe,b as ze,c as Te,u as $e}from"./wmscheckinOut-C2B7FNiS.js";const te=U=>(Ye("data-v-7f6f469f"),U=U(),Ae(),U),Fe={class:"warehousing-detail"},Re={class:"page-title"},Be={class:"action-buttons"},Ee=te(()=>f("h3",null,"基本信息",-1)),Le={class:"detail-header"},je=te(()=>f("h3",null,"入库明细",-1)),Ke={class:"total-amount"},Qe={style:{color:"#D9001B"}},Ge={style:{color:"#D9001B"}},Je={class:"pagination"},Oe={__name:"wmsAddEdit",setup(U){const{proxy:j}=Ie(),{stock_in_type:oe,rk_unit:ne,goods_type:ue}=j.useDict("stock_in_type","rk_unit","goods_type"),w=De();xe();const M=b(!1),K={add:"新增入库单",edit:"编辑入库单",view:"入库单详情"};if(!w.meta.title||w.meta.title==="入库管理"){const t=w.query.type;t&&K[t]&&(document.title=K[t])}const D=y(()=>w.query.type||"view"),de=y(()=>w.query.id),q=y(()=>D.value==="add"),re=y(()=>D.value==="edit"),r=y(()=>D.value==="view"),ie=y(()=>({add:"新增入库单",edit:"编辑入库单",view:"入库单详情"})[D.value]),n=b({}),v=b([]),Q=b(null),se={stockInDate:[{required:!0,message:"请选择入库日期",trigger:"blur"}],stockInPerson:[{required:!0,message:"请输入入库人员",trigger:"blur"}],stockInType:[{required:!0,message:"请选择入库类型",trigger:"blur"}],manufacturer:[{required:!0,message:"请输入生产厂家",trigger:"blur"}],creator:[{required:!0,message:"请输入制单人",trigger:"blur"}],createDate:[{required:!0,message:"请选择制单日期",trigger:"blur"}],orderNo:[{required:!0,message:"请输入订单号",trigger:"blur"}],orderInvoice:[{required:!0,message:"请输入订单发票号",trigger:"blur"}]},G=y(()=>v.value.reduce((t,o)=>t+(o.purchaseAmount||0),0)),J=y(()=>v.value.reduce((t,o)=>t+(o.retailAmount||0),0)),z=t=>{t.purchaseAmount=(t.quantity||0)*(t.purchasePrice||0),t.retailAmount=(t.quantity||0)*(t.retailPrice||0)},ce=t=>{v.value.splice(t,1),O()},O=()=>{v.value.forEach((t,o)=>{t.index=o+1})},x=b(!1),p=b({goodsCategory:"",likeParamStr:"",pageNum:1,pageSize:50,status:1}),W=b([]),H=b(0),C=b([]),pe=()=>{x.value=!0,P()},me=()=>{p.value={goodsCategory:"",likeParamStr:"",pageNum:1,pageSize:50,status:1},P()},P=async()=>{const t=await qe({...p.value});W.value=t.rows,H.value=t.total},ve=t=>{C.value=t},ge=()=>{if(C.value.length===0){V.warning("请至少选择一件物品");return}const t=new Set(v.value.map(u=>u.medicationCode)),o=C.value.filter(u=>!t.has(u.medicineCode)).map(u=>({medicationCode:u.medicineCode,medicationName:u.medicineName,manufacturer:u.manufacturer,quantity:0,unit:"箱",purchasePrice:u.purchasePrice,retailPrice:u.retailPrice,batchNo:u.approvalNumber,expiryDate:u.expiryWarningDays,purchaseAmount:0,retailAmount:0}));if(o.length===0){V.warning("物品已经添加，请勿重复添加！");return}if(o.length<C.value.length){const u=C.value.length-o.length;V.warning(`有${u}件物品已存在于明细中，未重复添加`)}v.value=[...v.value,...o],O(),x.value=!1,C.value=[]},fe=t=>{p.value.pageSize=t,P()},_e=t=>{p.value.pageNum=t,P()},be=async()=>{n.value={stockInNo:q.value?await he():"",stockInDate:le().format("YYYY-MM-DD"),stockInPerson:"",stockInType:"",manufacturer:"",creator:"",createDate:le().format("YYYY-MM-DD"),orderNo:"",orderInvoice:"",remark:""},v.value=[]},he=async()=>{const t=await ze({prefix:"RK"});if(t.code==200)return t.msg},X=async()=>{if(q.value){await be();return}try{const t=await Me(de.value);n.value=t.data,v.value=n.value.details}catch(t){V.error("获取数据失败: "+t.message),T()}},ye=async()=>{try{if(await Q.value.validate(),v.value.length===0){V.warning("请至少添加一条入库明细");return}const t={...n.value,details:v.value,purchaseAmount:G.value.toFixed(2),retailAmount:J.value.toFixed(2)};M.value=!0,q.value?(await Te(t)).code==200?V.success("入库成功"):V.error("入库失败"):re.value&&(await $e(t),V.success("修改成功")),M.value=!1,T()}catch(t){if(t!=null&&t.errors)return}},T=()=>{j.$tab.closeOpenPage({path:"/warehouse/wmscheckin"})};return Pe(()=>w.query,t=>{t.type&&t.type!==D.value&&X()},{immediate:!0}),Ne(()=>{X()}),(t,o)=>{const u=s("el-button"),m=s("el-form-item"),_=s("el-col"),N=s("el-row"),$=s("el-date-picker"),h=s("el-input"),S=s("el-option"),F=s("el-select"),Z=s("el-form"),ee=s("el-card"),d=s("el-table-column"),R=s("el-input-number"),ae=s("el-table"),Ve=s("el-pagination"),ke=s("el-dialog"),we=Ue("loading");return Se((g(),Y("div",Fe,[f("h2",Re,I(ie.value),1),f("div",Be,[r.value?A("",!0):(g(),k(u,{key:0,type:"primary",onClick:ye},{default:l(()=>[c("直接入库")]),_:1})),e(u,{onClick:T,icon:"back"},{default:l(()=>[c("返回")]),_:1})]),e(ee,{class:"form-card"},{default:l(()=>[Ee,e(Z,{model:n.value,"label-width":"100px",rules:se,ref_key:"formRef",ref:Q},{default:l(()=>[e(N,{gutter:20},{default:l(()=>[e(_,{span:8},{default:l(()=>[e(m,{label:"入库单号",prop:"stockInNo"},{default:l(()=>[c(I(n.value.stockInNo),1)]),_:1})]),_:1})]),_:1}),e(N,{gutter:20},{default:l(()=>[e(_,{span:8},{default:l(()=>[e(m,{label:"入库日期",prop:"stockInDate"},{default:l(()=>[e($,{modelValue:n.value.stockInDate,"onUpdate:modelValue":o[0]||(o[0]=a=>n.value.stockInDate=a),type:"date","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:r.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(m,{label:"入库人员",prop:"stockInPerson"},{default:l(()=>[e(h,{modelValue:n.value.stockInPerson,"onUpdate:modelValue":o[1]||(o[1]=a=>n.value.stockInPerson=a),disabled:r.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(m,{label:"入库类型",prop:"stockInType"},{default:l(()=>[e(F,{modelValue:n.value.stockInType,"onUpdate:modelValue":o[2]||(o[2]=a=>n.value.stockInType=a),style:{width:"100%"},disabled:r.value},{default:l(()=>[(g(!0),Y(B,null,E(L(oe),a=>(g(),k(S,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(N,{gutter:20},{default:l(()=>[e(_,{span:8},{default:l(()=>[e(m,{label:"生产厂家",prop:"manufacturer"},{default:l(()=>[e(h,{modelValue:n.value.manufacturer,"onUpdate:modelValue":o[3]||(o[3]=a=>n.value.manufacturer=a),disabled:r.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(m,{label:"制单人",prop:"creator"},{default:l(()=>[e(h,{modelValue:n.value.creator,"onUpdate:modelValue":o[4]||(o[4]=a=>n.value.creator=a),disabled:r.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(m,{label:"制单日期",prop:"createDate"},{default:l(()=>[e($,{modelValue:n.value.createDate,"onUpdate:modelValue":o[5]||(o[5]=a=>n.value.createDate=a),type:"date","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:r.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(N,{gutter:20},{default:l(()=>[e(_,{span:8},{default:l(()=>[e(m,{label:"订单编号",prop:"orderNo"},{default:l(()=>[e(h,{modelValue:n.value.orderNo,"onUpdate:modelValue":o[6]||(o[6]=a=>n.value.orderNo=a),disabled:r.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(m,{label:"订单发票",prop:"orderInvoice"},{default:l(()=>[e(h,{modelValue:n.value.orderInvoice,"onUpdate:modelValue":o[7]||(o[7]=a=>n.value.orderInvoice=a),disabled:r.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(N,{gutter:20},{default:l(()=>[e(_,{span:24},{default:l(()=>[e(m,{label:"备注",prop:"remark"},{default:l(()=>[e(h,{modelValue:n.value.remark,"onUpdate:modelValue":o[8]||(o[8]=a=>n.value.remark=a),type:"textarea",disabled:r.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(ee,{class:"detail-card"},{default:l(()=>[f("div",Le,[je,r.value?A("",!0):(g(),k(u,{key:0,type:"primary",onClick:pe,icon:"Plus"},{default:l(()=>[c(" 添加物品 ")]),_:1}))]),e(ae,{data:v.value,border:"",style:{width:"100%"}},{default:l(()=>[e(d,{prop:"index",label:"序号",width:"60",align:"center"},{default:l(a=>[c(I(a.$index+1),1)]),_:1}),e(d,{prop:"medicationCode",label:"编码","min-width":"180",align:"center"}),e(d,{prop:"medicationName",label:"名称",width:"150",align:"center"}),e(d,{prop:"manufacturer",label:"生产厂家",width:"150",align:"center"}),e(d,{label:"入库数量",width:"120",align:"center",prop:"quantity"},{default:l(({row:a})=>[e(R,{modelValue:a.quantity,"onUpdate:modelValue":i=>a.quantity=i,min:0,max:9999,"controls-position":"right",onChange:i=>z(a),disabled:r.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(d,{label:"单位",width:"120",align:"center",prop:"unit"},{default:l(({row:a})=>[e(F,{modelValue:a.unit,"onUpdate:modelValue":i=>a.unit=i,style:{width:"100%"},disabled:r.value},{default:l(()=>[(g(!0),Y(B,null,E(L(ne),i=>(g(),k(S,{key:i.value,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(d,{label:"采购价",width:"120",align:"center",prop:"purchasePrice"},{default:l(({row:a})=>[e(R,{modelValue:a.purchasePrice,"onUpdate:modelValue":i=>a.purchasePrice=i,min:0,precision:2,"controls-position":"right",onChange:i=>z(a),disabled:r.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(d,{label:"零售价",width:"120",align:"center",prop:"retailPrice"},{default:l(({row:a})=>[e(R,{modelValue:a.retailPrice,"onUpdate:modelValue":i=>a.retailPrice=i,min:0,precision:2,"controls-position":"right",onChange:i=>z(a),disabled:r.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(d,{prop:"batchNo",label:"批号",width:"120",align:"center"},{default:l(({row:a})=>[e(h,{modelValue:a.batchNo,"onUpdate:modelValue":i=>a.batchNo=i,disabled:r.value},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(d,{label:"有效期",width:"150",align:"center",prop:"expiryDate"},{default:l(({row:a})=>[e($,{modelValue:a.expiryDate,"onUpdate:modelValue":i=>a.expiryDate=i,type:"date","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:r.value},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(d,{prop:"purchaseAmount",label:"采购金额",width:"120",align:"center"}),e(d,{prop:"retailAmount",label:"零售金额",width:"120",align:"center"}),r.value?A("",!0):(g(),k(d,{key:0,label:"操作",width:"100",align:"center",fixed:"right"},{default:l(({$index:a})=>[e(u,{type:"danger",onClick:i=>ce(a),link:""},{default:l(()=>[c("删除")]),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),f("div",Ke,[f("span",null,[c("采购金额合计: "),f("b",Qe,I(G.value.toFixed(2)),1),c("元")]),f("span",null,[c("零售金额合计: "),f("b",Ge,I(J.value.toFixed(2)),1),c("元")])])]),_:1}),r.value?A("",!0):(g(),k(ke,{key:0,modelValue:x.value,"onUpdate:modelValue":o[14]||(o[14]=a=>x.value=a),title:"添加物品",width:"70%"},{footer:l(()=>[e(u,{onClick:o[13]||(o[13]=a=>x.value=!1)},{default:l(()=>[c("取消")]),_:1}),e(u,{type:"primary",onClick:ge},{default:l(()=>[c("确定")]),_:1})]),default:l(()=>[e(Z,{inline:!0,model:p.value,class:"item-search-form"},{default:l(()=>[e(m,{label:"类别"},{default:l(()=>[e(F,{modelValue:p.value.goodsCategory,"onUpdate:modelValue":o[9]||(o[9]=a=>p.value.goodsCategory=a),placeholder:"全部",clearable:"",style:{width:"200px"}},{default:l(()=>[e(S,{label:"全部",value:""}),(g(!0),Y(B,null,E(L(ue),a=>(g(),k(S,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(h,{modelValue:p.value.likeParamStr,"onUpdate:modelValue":o[10]||(o[10]=a=>p.value.likeParamStr=a),placeholder:"请输入药品编码/药品名称",clearable:""},null,8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(u,{type:"primary",onClick:P,icon:"Search"},{default:l(()=>[c("查询")]),_:1}),e(u,{onClick:me,icon:"Refresh"},{default:l(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(ae,{data:W.value,border:"",style:{width:"100%"},onSelectionChange:ve},{default:l(()=>[e(d,{type:"selection",width:"55",align:"center"}),e(d,{prop:"index",label:"序号",width:"60",align:"center"},{default:l(a=>[c(I(a.$index+1),1)]),_:1}),e(d,{prop:"medicineCode",label:"编码","min-width":"180",align:"center"}),e(d,{prop:"medicineName",label:"名称",width:"150",align:"center"}),e(d,{prop:"goodsCategory",label:"类别",width:"100",align:"center"}),e(d,{prop:"specification",label:"规格",width:"150",align:"center"}),e(d,{prop:"manufacturer",label:"生产厂家",width:"150",align:"center"}),e(d,{prop:"currentQuantity",label:"库存",width:"100",align:"center"})]),_:1},8,["data"]),f("div",Je,[e(Ve,{background:"","current-page":p.value.pageNum,"onUpdate:currentPage":o[11]||(o[11]=a=>p.value.pageNum=a),"page-size":p.value.pageSize,"onUpdate:pageSize":o[12]||(o[12]=a=>p.value.pageSize=a),total:H.value,"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:fe,onCurrentChange:_e},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"]))])),[[we,M.value]])}}},Xe=Ce(Oe,[["__scopeId","data-v-7f6f469f"]]);export{Xe as default};
