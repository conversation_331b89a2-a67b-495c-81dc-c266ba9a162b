import K from"./replaceDetail-3xVwAW3E.js";import Q from"./consumablesDetailSheet-DCemosm2.js";import{_ as A,r as d,F as J,e as p,c as w,o as b,f as e,h as o,n as s,i as v,K as L,L as $,j as F,t as N,aE as O,aF as W,E as X,aG as Z,G as E}from"./index-B0qHf98Y.js";import{g as ee}from"./roommanage-DBG5TiIR.js";const le={class:"consumables-detail"},ae={class:"pagination-container"},te={class:"pagination-container"},oe={__name:"replaceHistory",setup(ne){const m=d("consumables"),_=d([]),f=d(0),V=d(null),a=d({pageNum:1,pageSize:10,date:"",name:"",projectName:""}),k=d([]),C=d([]),x=d(null),g=()=>{m.value==="consumables"?(a.value.pageNum=1,a.value.status=1,U()):m.value==="consumablesSummary"&&(a.value.pageNum=1,a.value.status=1,W({...a.value}).then(n=>{console.log(n,"rrrrrrrrrrrrrrr"),C.value=n.rows,f.value=n.total}))},h=()=>{a.value={pageNum:1,pageSize:10},g()},D=n=>{m.value==="consumablesSummary"?x.value.openDialog(n):m.value==="consumables"&&V.value.openDialog(n)},j=n=>{X.confirm("注：删除耗材明细将失去原始数据，请慎重删除","确定删除该耗材明细吗?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Z(n.id).then(t=>{t.code===200?(E({type:"success",message:"删除成功!"}),g()):E({type:"error",message:t.msg})})})},H=n=>{m.value=n.props.name,h()},S=n=>{a.value.pageSize=n,g()},z=n=>{a.value.pageNum=n,g()},U=async()=>{const n=await O({...a.value,status:1});k.value=n.rows||[],f.value=n.total||0},G=async()=>{const n=await ee();_.value=n.rows||[]};return J(()=>{G(),U()}),(n,t)=>{const i=p("el-button"),R=p("el-date-picker"),r=p("el-form-item"),y=p("el-option"),B=p("el-select"),c=p("el-input"),M=p("el-form"),u=p("el-table-column"),q=p("el-table"),T=p("el-pagination"),I=p("el-tab-pane"),P=p("el-tabs");return b(),w("div",le,[e(i,{type:"primary",onClick:t[0]||(t[0]=l=>n.$router.back())},{default:o(()=>[s("返回工作台")]),_:1}),e(P,{modelValue:m.value,"onUpdate:modelValue":t[16]||(t[16]=l=>m.value=l),onTabClick:H},{default:o(()=>[e(I,{label:"耗材明细表",name:"consumables"},{default:o(()=>[e(M,{inline:!0,class:"query-form",model:a.value,ref:"searchFormRef1","label-width":"100px"},{default:o(()=>[e(r,{label:"服务日期:",prop:"serviceDate"},{default:o(()=>[e(R,{modelValue:a.value.serviceDate,"onUpdate:modelValue":t[1]||(t[1]=l=>a.value.serviceDate=l),type:"date",placeholder:"选择日期",style:{width:"200px"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(r,{label:"楼栋信息",prop:"buildingName"},{default:o(()=>[e(B,{modelValue:a.value.buildingName,"onUpdate:modelValue":t[2]||(t[2]=l=>a.value.buildingName=l),style:{width:"200px"},placeholder:"全部",clearable:""},{default:o(()=>[(b(!0),w(L,null,$(_.value,l=>(b(),F(y,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"房间号",prop:"roomNumber"},{default:o(()=>[e(c,{modelValue:a.value.roomNumber,"onUpdate:modelValue":t[3]||(t[3]=l=>a.value.roomNumber=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(c,{modelValue:a.value.elderName,"onUpdate:modelValue":t[4]||(t[4]=l=>a.value.elderName=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"服务项目",prop:"supplyItem"},{default:o(()=>[e(c,{modelValue:a.value.supplyItem,"onUpdate:modelValue":t[5]||(t[5]=l=>a.value.supplyItem=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"操作人",prop:"nurseName"},{default:o(()=>[e(c,{modelValue:a.value.nurseName,"onUpdate:modelValue":t[6]||(t[6]=l=>a.value.nurseName=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,null,{default:o(()=>[e(i,{type:"primary",onClick:g,icon:"Search"},{default:o(()=>[s("查询")]),_:1}),e(i,{onClick:h,icon:"Refresh"},{default:o(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(q,{data:k.value,border:"",style:{width:"100%"}},{default:o(()=>[e(u,{prop:"sequence",label:"序号",width:"80",align:"center"},{default:o(l=>[v("span",null,N(l.$index+1),1)]),_:1}),e(u,{prop:"serviceDate",label:"服务日期",width:"120",align:"center"}),e(u,{prop:"elderName",label:"老人姓名",width:"120",align:"center"}),e(u,{prop:"roomNumber",label:"房间号",width:"80",align:"center"}),e(u,{prop:"bedNumber",label:"床位号",width:"80",align:"center"}),e(u,{prop:"buildingName",label:"楼栋信息",width:"120",align:"center"}),e(u,{prop:"supplyItem",label:"服务项目",width:"120",align:"center"}),e(u,{prop:"quantity",label:"数量",width:"80",align:"center"}),e(u,{prop:"price",label:"价格",width:"80",align:"center"}),e(u,{prop:"nurseName",label:"操作人",width:"120",align:"center"}),e(u,{prop:"updateTime",label:"记录时间",width:"180",align:"center"}),e(u,{prop:"remark",label:"备注",align:"center","min-width":"120"}),e(u,{prop:"operator",label:"操作","min-width":"120",align:"center",fixed:"right"},{default:o(l=>[e(i,{link:"",type:"primary",onClick:Y=>D(l.row)},{default:o(()=>[s("详情")]),_:2},1032,["onClick"]),e(i,{link:"",type:"primary",onClick:Y=>j(l.row)},{default:o(()=>[s("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),v("div",ae,[e(T,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[7]||(t[7]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[8]||(t[8]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:f.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:S,onCurrentChange:z},null,8,["current-page","page-size","total"])])]),_:1}),e(I,{label:"耗材费用汇总表",name:"consumablesSummary"},{default:o(()=>[e(M,{inline:!0,class:"query-form",model:a.value,ref:"searchFormRef2","label-width":"100px"},{default:o(()=>[e(r,{label:"汇总月份:",prop:"queryMonth"},{default:o(()=>[e(R,{modelValue:a.value.queryMonth,"onUpdate:modelValue":t[9]||(t[9]=l=>a.value.queryMonth=l),type:"month",placeholder:"选择月份",style:{width:"200px"},"value-format":"YYYY-MM"},null,8,["modelValue"])]),_:1}),e(r,{label:"楼栋信息",prop:"buildingId"},{default:o(()=>[e(B,{modelValue:a.value.buildingId,"onUpdate:modelValue":t[10]||(t[10]=l=>a.value.buildingId=l),placeholder:"全部",style:{width:"200px"}},{default:o(()=>[e(y,{label:"全部",value:""}),(b(!0),w(L,null,$(_.value,l=>(b(),F(y,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"房间号",prop:"roomNumber"},{default:o(()=>[e(c,{modelValue:a.value.roomNumber,"onUpdate:modelValue":t[11]||(t[11]=l=>a.value.roomNumber=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(c,{modelValue:a.value.elderName,"onUpdate:modelValue":t[12]||(t[12]=l=>a.value.elderName=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"床位号",prop:"roomBed"},{default:o(()=>[e(c,{modelValue:a.value.roomBed,"onUpdate:modelValue":t[13]||(t[13]=l=>a.value.roomBed=l),placeholder:"请输入",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,null,{default:o(()=>[e(i,{type:"primary",onClick:g,icon:"Search"},{default:o(()=>[s("查询")]),_:1}),e(i,{onClick:h,icon:"Refresh"},{default:o(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(q,{data:C.value,border:"",style:{width:"100%"}},{default:o(()=>[e(u,{prop:"sequence",label:"序号",width:"80",align:"center"},{default:o(l=>[v("span",null,N(l.$index+1),1)]),_:1}),e(u,{prop:"queryMonth",label:"汇总月份","min-width":"120",align:"center"}),e(u,{prop:"elderName",label:"老人姓名",width:"120",align:"center"}),e(u,{prop:"roomNumber",label:"房间号",width:"80",align:"center"}),e(u,{prop:"roomBed",label:"床位号",width:"80",align:"center"}),e(u,{prop:"buildingName",label:"楼栋信息","min-width":"120",align:"center"}),e(u,{prop:"monthTotal",label:"服务费用","min-width":"120",align:"center"},{default:o(l=>[v("span",null,"¥ "+N(l.row.monthTotal),1)]),_:1}),e(u,{prop:"operator",label:"操作","min-width":"120",align:"center"},{default:o(l=>[e(i,{link:"",type:"primary",onClick:Y=>D(l.row)},{default:o(()=>[s("详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),v("div",te,[e(T,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[14]||(t[14]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[15]||(t[15]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:f.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:S,onCurrentChange:z},null,8,["current-page","page-size","total"])])]),_:1})]),_:1},8,["modelValue"]),e(K,{ref_key:"replaceDetailRef",ref:x},null,512),e(Q,{ref_key:"consumablesDetailSheetRef",ref:V},null,512)])}}},de=A(oe,[["__scopeId","data-v-3fbf7342"]]);export{de as default};
