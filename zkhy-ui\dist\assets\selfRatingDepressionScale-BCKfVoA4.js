import{a as ue}from"./assessmentRecord-4xWX4TZA.js";import{e as ie}from"./eventBus-BDDolVUG.js";import{_ as de,B as ce,d as re,C as pe,r as a,N as ve,z as fe,w as _e,e as i,c as $,o as D,f as l,h as t,i as n,k as G,n as p,j as he,Q as ye,t as P,l as r,v as me,x as we,G as Y}from"./index-B0qHf98Y.js";const v=f=>(me("data-v-e4f7dc2c"),f=f(),we(),f),be={class:"depression-screening"},ge=v(()=>n("div",{class:"card-header"},[n("h2",null,"抑郁自评量表")],-1)),Se=v(()=>n("h4",null,"评分标准：",-1)),Ve=v(()=>n("ul",null,[n("li",null,[n("strong",null,"<5分"),p("：正常")]),n("li",null,[n("strong",null,"5~9分"),p("：有抑郁倾向")]),n("li",null,[n("strong",null,"≥10分"),p("：抑郁")])],-1)),Ne=v(()=>n("p",null,'注：选择"是"得1分，选择"否"得0分（部分题目反向计分）',-1)),Ce={class:"result-section"},Ie={class:"score-display"},ke=v(()=>n("span",{class:"score-label"},"当前总分：",-1)),xe=v(()=>n("span",{class:"score-label"},"分",-1)),Oe={class:"form-footer"},Te={class:"assessment-comments"},Me=v(()=>n("div",{class:"comments-header"},"评估意见：",-1)),Re={key:0,class:"action-buttons"},Be=ce({name:"selfRating"}),Ee=Object.assign(Be,{props:{elderId:{type:String,default:null},isShow:{type:String,default:null},data:{type:Object,default:null}},emits:["updateList"],setup(f,{emit:Ue}){const{proxy:Q}=re(),u=f,H=pe({form:{}}),h=a(0),y=a(0),m=a(0),w=a(0),b=a(0),g=a(0),S=a(0),V=a(0),N=a(0),C=a(0),I=a(0),k=a(0),x=a(0),O=a(0),T=a(0),{form:o}=ve(H),E=a([{id:1,content:"你对生活基本上满意吗？",answer:null,positive:!1},{id:2,content:"你是否已放弃了许多活动与兴趣？",answer:null,positive:!1},{id:3,content:"你是否觉得生活空虚？",answer:null,positive:!1},{id:4,content:"你是否干到厌烦？",answer:null,positive:!1},{id:5,content:"你是否大部分时间精力充沛？",answer:null,positive:!1},{id:6,content:"你是否害怕会有不幸的事落到你头上？",answer:null,positive:!1},{id:7,content:"你是否大部分时间感到幸福？",answer:null,positive:!1},{id:8,content:"你是否常感到孤立无援？",answer:null,positive:!1},{id:9,content:"你是否希望呆在家里而不愿去做些新鲜事？",answer:null,positive:!1},{id:10,content:"你是否觉得记忆力比以前差？",answer:null,positive:!1},{id:11,content:"你是否觉得像现在这样活着毫无意义？",answer:null,positive:!1},{id:12,content:"你觉得生活充满活力吗？",answer:null,positive:!1},{id:13,content:"你曾觉得您的处境已毫无希望？",answer:null,positive:!1},{id:14,content:"你是否觉得大多数人比你强的多？",answer:null,positive:!1},{id:15,content:"你是否感到自己没什么价值？",answer:null,positive:!1}]),c=a(0);a("");const M=a("info"),R=a(""),B=a("");function K(){if(console.log(u.data,"props"),u.isShow=="add")console.log("add");else if(u.isShow=="edit")o.value=u.data;else if(u.isShow=="show"){console.log("show"),o.value=u.data,console.log(u.data,"propsdata");let s=JSON.parse(u.data.itemName).type;c.value=u.data.totalScoreValue,s.forEach(e=>{e.type==1?h.value=e.score:e.type==2?y.value=e.score:e.type==3?m.value=e.score:e.type==4?w.value=e.score:e.type==5?b.value=e.score:e.type==6?g.value=e.score:e.type==7?S.value=e.score:e.type==8?V.value=e.score:e.type==9?N.value=e.score:e.type==10?C.value=e.score:e.type==11?I.value=e.score:e.type==12?k.value=e.score:e.type==13?x.value=e.score:e.type==14?O.value=e.score:e.type==15&&(T.value=e.score)}),console.log(u.data,"data"),E.value.forEach(e=>{e.id==1?e.answer=h.value:e.id==2?e.answer=y.value:e.id==3?e.answer=m.value:e.id==4?e.answer=w.value:e.id==5?e.answer=b.value:e.id==6?e.answer=g.value:e.id==7?e.answer=S.value:e.id==8?e.answer=V.value:e.id==9?e.answer=N.value:e.id==10?e.answer=C.value:e.id==11?e.answer=I.value:e.id==12?e.answer=k.value:e.id==13?e.answer=x.value:e.id==14?e.answer=O.value:e.id==15&&(e.answer=T.value)})}}const W=s=>{console.log(s,"row-----"),s.id==1?h.value=s.answer:s.id==2?y.value=s.answer:s.id==3?m.value=s.answer:s.id==4?w.value=s.answer:s.id==5?b.value=s.answer:s.id==6?g.value=s.answer:s.id==7?S.value=s.answer:s.id==8?V.value=s.answer:s.id==9?N.value=s.answer:s.id==10?C.value=s.answer:s.id==11?I.value=s.answer:s.id==12?k.value=s.answer:s.id==13?x.value=s.answer:s.id==14?O.value=s.answer:s.id==15&&(T.value=s.answer),c.value=E.value.reduce((e,_)=>e+(_.answer||0),0)},X=fe(()=>c.value<5?"normal":c.value<=9?"tendency":"depressed");_e(c,s=>{s<5?(M.value="success",R.value="评估结果：正常",B.value="您的抑郁症状在正常范围内"):s>=5&&s<=9?(M.value="warning",R.value="评估结果：有抑郁倾向",B.value="您可能有轻度抑郁倾向，建议关注心理健康"):s>=10&&(M.value="error",R.value="评估结果：抑郁",B.value="您可能有较明显的抑郁症状，建议寻求专业帮助")});const Z=()=>{if(u.elderId===null){Y.error("请选择老人信息");return}if(!o.value.assessorName||!o.value.assessmentTime){Y.error("请填写评估师姓名和日期");return}let s=[{type:1,score:h.value},{type:2,score:y.value},{type:3,score:m.value},{type:4,score:w.value},{type:5,score:b.value},{type:6,score:g.value},{type:7,score:S.value},{type:8,score:V.value},{type:9,score:N.value},{type:10,score:C.value},{type:11,score:I.value},{type:12,score:k.value},{type:13,score:x.value},{type:14,score:O.value},{type:15,score:T.value}];o.value.itemName=JSON.stringify({type:s}),o.value.totalScoreValue=c.value,o.value.assessmentMethod="01",o.value.assessmentFormId="32";let e={elderId:u.elderId,assessmentFormId:32,assessmentMethod:"01",assessmentScores:[],assessmentOrgName:"和孚养老机构"};e.assessmentScores.push(o.value),ue(e).then(_=>{Y.success("评估表提交成功！"),ie.emit("uploadListEvent",{data:"some data"}),Q.$tab.closeOpenPage({path:"/assessment/assessmentRecord"})})};function ee(){o.value={assessmentTime:null,assessorName:null,assessmentOpinion:null}}return K(),(s,e)=>{const _=i("el-alert"),U=i("el-table-column"),j=i("el-radio"),se=i("el-radio-group"),le=i("el-table"),ae=i("el-divider"),z=i("el-input"),A=i("el-card"),F=i("el-form-item"),q=i("el-col"),ne=i("el-date-picker"),te=i("el-row"),oe=i("el-form"),J=i("el-button");return D(),$("div",be,[l(A,{class:"screening-card"},{header:t(()=>[ge]),default:t(()=>[l(_,{type:"info",closable:!1,class:"scoring-info"},{default:t(()=>[Se,Ve,Ne]),_:1}),l(le,{data:E.value,border:"",class:"question-table"},{default:t(()=>[l(U,{prop:"id",label:"序号",width:"80",align:"center"}),l(U,{prop:"content",label:"问题内容","min-width":"200"}),l(U,{label:"选择",width:"180",align:"center"},{default:t(({row:d})=>[l(se,{modelValue:d.answer,"onUpdate:modelValue":L=>d.answer=L,onChange:L=>W(d)},{default:t(()=>[l(j,{label:d.positive?0:1},{default:t(()=>[p("是")]),_:2},1032,["label"]),l(j,{label:d.positive?1:0},{default:t(()=>[p("否")]),_:2},1032,["label"])]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1})]),_:1},8,["data"]),n("div",Ce,[l(ae),n("div",Ie,[ke,n("span",{class:ye(["score-value",X.value])},P(c.value),3),xe]),c.value>0?(D(),he(_,{key:0,title:R.value,type:M.value,"show-icon":"",class:"result-alert"},{default:t(()=>[n("p",null,P(B.value),1)]),_:1},8,["title","type"])):G("",!0)]),n("div",Oe,[l(oe,{model:r(o),"label-width":"100px"},{default:t(()=>[n("div",Te,[l(A,{shadow:"never"},{header:t(()=>[Me]),default:t(()=>[l(z,{modelValue:r(o).assessmentOpinion,"onUpdate:modelValue":e[0]||(e[0]=d=>r(o).assessmentOpinion=d),type:"textarea",rows:4,placeholder:"请输入评估意见...",resize:"none",disabled:u.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),l(te,{gutter:20},{default:t(()=>[l(q,{span:12},{default:t(()=>[l(F,{label:"评估师姓名："},{default:t(()=>[l(z,{modelValue:r(o).assessorName,"onUpdate:modelValue":e[1]||(e[1]=d=>r(o).assessorName=d),placeholder:"请输入评估师姓名",disabled:u.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(q,{span:12},{default:t(()=>[l(F,{label:"日期："},{default:t(()=>[l(ne,{modelValue:r(o).assessmentTime,"onUpdate:modelValue":e[2]||(e[2]=d=>r(o).assessmentTime=d),type:"date",placeholder:"选择评估日期","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:u.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),u.isShow!="show"?(D(),$("div",Re,[l(J,{type:"primary",onClick:Z},{default:t(()=>[p(" 提交 ")]),_:1}),l(J,{onClick:ee},{default:t(()=>[p(" 重置 ")]),_:1})])):G("",!0)]),_:1})])}}}),ze=de(Ee,[["__scopeId","data-v-e4f7dc2c"]]);export{ze as default};
