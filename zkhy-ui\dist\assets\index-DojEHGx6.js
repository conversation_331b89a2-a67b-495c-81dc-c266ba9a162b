import{g as O}from"./cache-DspHyQOp.js";import{i as g}from"./index-a8qYZQmS.js";import{B as z,r as f,d as B,e as _,c as t,o as a,f as i,h as n,i as e,k as c,l as s,t as o,n as b}from"./index-B0qHf98Y.js";const F={class:"app-container"},I=e("span",{style:{"vertical-align":"middle"}},"基本信息",-1),N={class:"el-table el-table--enable-row-hover el-table--medium"},S={cellspacing:"0",style:{width:"100%"}},V=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"Redis版本")],-1),D={class:"el-table__cell is-leaf"},E={key:0,class:"cell"},L=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"运行模式")],-1),P={class:"el-table__cell is-leaf"},M={key:0,class:"cell"},R=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"端口")],-1),T={class:"el-table__cell is-leaf"},$={key:0,class:"cell"},j=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"客户端数")],-1),A={class:"el-table__cell is-leaf"},K={key:0,class:"cell"},U=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"运行时间(天)")],-1),q={class:"el-table__cell is-leaf"},G={key:0,class:"cell"},H=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"使用内存")],-1),J={class:"el-table__cell is-leaf"},Q={key:0,class:"cell"},W=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"使用CPU")],-1),X={class:"el-table__cell is-leaf"},Y={key:0,class:"cell"},Z=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"内存配置")],-1),ee={class:"el-table__cell is-leaf"},le={key:0,class:"cell"},se=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"AOF是否开启")],-1),te={class:"el-table__cell is-leaf"},ae={key:0,class:"cell"},oe=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"RDB是否成功")],-1),ce={class:"el-table__cell is-leaf"},ie={key:0,class:"cell"},ne=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"Key数量")],-1),_e={class:"el-table__cell is-leaf"},de={key:0,class:"cell"},re=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"网络入口/出口")],-1),me={class:"el-table__cell is-leaf"},he={key:0,class:"cell"},ue=e("span",{style:{"vertical-align":"middle"}},"命令统计",-1),fe={class:"el-table el-table--enable-row-hover el-table--medium"},be=e("span",{style:{"vertical-align":"middle"}},"内存信息",-1),ve={class:"el-table el-table--enable-row-hover el-table--medium"},pe=z({name:"Cache"}),we=Object.assign(pe,{setup(ye){const l=f([]),v=f(null),p=f(null),{proxy:y}=B();function k(){y.$modal.loading("正在加载缓存监控数据，请稍候！"),O().then(r=>{y.$modal.closeLoading(),l.value=r.data;const m=g(v.value,"macarons");m.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},series:[{name:"命令",type:"pie",roseType:"radius",radius:[15,95],center:["50%","38%"],data:r.data.commandStats,animationEasing:"cubicInOut",animationDuration:1e3}]});const d=g(p.value,"macarons");d.setOption({tooltip:{formatter:"{b} <br/>{a} : "+l.value.info.used_memory_human},series:[{name:"峰值",type:"gauge",min:0,max:1e3,detail:{formatter:l.value.info.used_memory_human},data:[{value:parseFloat(l.value.info.used_memory_human),name:"内存消耗"}]}]}),window.addEventListener("resize",()=>{m.resize(),d.resize()})})}return k(),(r,m)=>{const d=_("Monitor"),h=_("el-card"),u=_("el-col"),x=_("PieChart"),w=_("Odometer"),C=_("el-row");return a(),t("div",F,[i(C,{gutter:10},{default:n(()=>[i(u,{span:24,class:"card-box"},{default:n(()=>[i(h,null,{header:n(()=>[i(d,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),b(),I]),default:n(()=>[e("div",N,[e("table",S,[e("tbody",null,[e("tr",null,[V,e("td",D,[s(l).info?(a(),t("div",E,o(s(l).info.redis_version),1)):c("",!0)]),L,e("td",P,[s(l).info?(a(),t("div",M,o(s(l).info.redis_mode=="standalone"?"单机":"集群"),1)):c("",!0)]),R,e("td",T,[s(l).info?(a(),t("div",$,o(s(l).info.tcp_port),1)):c("",!0)]),j,e("td",A,[s(l).info?(a(),t("div",K,o(s(l).info.connected_clients),1)):c("",!0)])]),e("tr",null,[U,e("td",q,[s(l).info?(a(),t("div",G,o(s(l).info.uptime_in_days),1)):c("",!0)]),H,e("td",J,[s(l).info?(a(),t("div",Q,o(s(l).info.used_memory_human),1)):c("",!0)]),W,e("td",X,[s(l).info?(a(),t("div",Y,o(parseFloat(s(l).info.used_cpu_user_children).toFixed(2)),1)):c("",!0)]),Z,e("td",ee,[s(l).info?(a(),t("div",le,o(s(l).info.maxmemory_human),1)):c("",!0)])]),e("tr",null,[se,e("td",te,[s(l).info?(a(),t("div",ae,o(s(l).info.aof_enabled=="0"?"否":"是"),1)):c("",!0)]),oe,e("td",ce,[s(l).info?(a(),t("div",ie,o(s(l).info.rdb_last_bgsave_status),1)):c("",!0)]),ne,e("td",_e,[s(l).dbSize?(a(),t("div",de,o(s(l).dbSize),1)):c("",!0)]),re,e("td",me,[s(l).info?(a(),t("div",he,o(s(l).info.instantaneous_input_kbps)+"kps/"+o(s(l).info.instantaneous_output_kbps)+"kps",1)):c("",!0)])])])])])]),_:1})]),_:1}),i(u,{span:12,class:"card-box"},{default:n(()=>[i(h,null,{header:n(()=>[i(x,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),b(),ue]),default:n(()=>[e("div",fe,[e("div",{ref_key:"commandstats",ref:v,style:{height:"420px"}},null,512)])]),_:1})]),_:1}),i(u,{span:12,class:"card-box"},{default:n(()=>[i(h,null,{header:n(()=>[i(w,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),b(),be]),default:n(()=>[e("div",ve,[e("div",{ref_key:"usedmemory",ref:p,style:{height:"420px"}},null,512)])]),_:1})]),_:1})]),_:1})])}}});export{we as default};
