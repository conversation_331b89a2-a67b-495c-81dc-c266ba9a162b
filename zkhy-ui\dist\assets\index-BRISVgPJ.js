import{g as $,b as ee,a as se}from"./roommanage-DBG5TiIR.js";import{_ as te,r as C,F as ae,z as F,e as h,c as E,o as c,i as a,f as B,h as r,K as v,L as k,Q as b,n as g,t as A,au as le,k as ne,l as D,v as ie,x as Ae}from"./index-B0qHf98Y.js";const de="data:image/png;base64,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",ue="data:image/png;base64,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",Ee="data:image/png;base64,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",p=T=>(ie("data-v-7010b023"),T=T(),Ae(),T),ce={class:"dashboard-container"},Be={class:"filters"},re={class:"filter-group"},oe={class:"filter-group"},ge={class:"filter-group"},Qe={class:"filter-group"},Ce={class:"filter-group"},Ie={class:"filter-actions"},ve={class:"summary-bar"},ke={class:"main-content"},be={class:"sidebar"},fe={class:"building-nav"},pe=["onClick"],Ne=p(()=>a("i",{class:"el-icon-office-building"},null,-1)),Se={class:"building-graphic"},me=["onClick"],Te=p(()=>a("div",{class:"building-base"},null,-1)),Le=p(()=>a("div",{class:"building-trees"},"🌲",-1)),ye={class:"room-grid"},he={key:0,class:"no-data"},we={class:"room-header"},Re={class:"room-info"},xe=p(()=>a("span",{class:"camera-icon",style:{"margin-left":"6px","vertical-align":"middle",display:"inline-block",width:"18px",height:"18px"}},[a("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18"},[a("path",{d:"M17 10.5V7C17 5.89543 16.1046 5 15 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19H15C16.1046 19 17 18.1046 17 17V13.5L21 17V7L17 10.5Z",stroke:"#409eff","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1)),De={class:"room-status"},Me={class:"bed-grid"},Pe={class:"bed-icon"},Je=["src"],Fe={class:"bed-number"},Ke={key:0,class:"bed-info"},Oe={class:"name"},We=p(()=>a("br",null,null,-1)),ze={class:"details"},qe=p(()=>a("br",null,null,-1)),Ue={key:1,class:"bed-info"},Ye={key:2,class:"away-tag"},Ve={__name:"index",setup(T){const K=["满员","空闲","外出"],O=["东","南","西","北","其他"],W=["男","女","未知"],z=["自理区","介护区","介助区","康复区","其他区"],q=["单人间","双人间","三人间","四人间"],N=C([]),w=C([]),Q=C([]),f=C(!1),U=C({roomTypes:[],areas:[],directions:[],statuses:[],genders:[]}),M=C(null),P=C("全部"),L=C(null),R=C("全部"),i=C({buildingName:"",floorName:"",roomNumber:"",areaName:"",roomType:"",roomOrientation:"",status:"",bedStatus:"",elderName:"",gender:"",age:"",careLevel:"",abilityLevel:""});ae(()=>{Y()});const Y=async()=>{try{f.value=!0;const t=await $();t.code===200&&(N.value=t.rows||[],N.value.length>0?J(N.value[0].id,N.value[0].buildingName):S())}catch(t){console.error("获取楼栋列表失败:",t)}finally{f.value=!1}},V=async t=>{try{f.value=!0;const l=await se(t);l.code===200&&(w.value=l.rows||[])}catch(l){console.error("获取楼层列表失败:",l)}finally{f.value=!1}},S=async()=>{try{f.value=!0;const t={...i.value},l=t.gender;delete t.gender,Object.keys(t).forEach(o=>{t[o]||delete t[o]}),console.log("API请求参数:",t);const d=await ee(t);if(d.code===200){Q.value=[];const o=new Map;(d.data||[]).forEach(s=>{const e=`${s.buildingName||""}_${s.floorName||""}_${s.roomNumber||s.id||s.roomId}`;o.has(e)||o.set(e,{...s,beds:[]});const n=o.get(e);if(s.elderName&&!n.beds.some(y=>y.elderName===s.elderName)){const y=s.gender==="0"?"女":s.gender==="1"?"男":"未知";let x="occupied";s.bedStatus==="外出"&&(x="外出"),s.bedNumber?n.beds[s.bedNumber-1]={elderName:s.elderName,age:s.age,gender:y,careLevel:s.careLevel||"未设置",abilityLevel:s.abilityLevel||"未设置",status:x,bedNumber:s.bedNumber}:n.beds.push({elderName:s.elderName,age:s.age,gender:y,careLevel:s.careLevel||"未设置",abilityLevel:s.abilityLevel||"未设置",status:x})}}),Q.value=Array.from(o.values()),Q.value=Q.value.map(s=>{s.beds||(s.beds=[]);const e=s.capacity||0;for(let u=0;u<e;u++)s.beds[u]?s.beds[u].bedNumber||(s.beds[u].bedNumber=u+1):s.beds[u]={bedNumber:u+1};s.beds.length>e&&(s.beds=s.beds.slice(0,e));const n=s.beds.filter(u=>u.elderName).length;return s.occupiedCount=n,n===0?s.status="空闲":n===e?s.status="满员":s.status="部分入住",s}),l&&(console.log("应用性别筛选:",l),Q.value=Q.value.filter(s=>{if(s.beds.some(n=>n.gender===l)){s.beds=s.beds.filter(u=>!u.elderName||u.gender===l);const n=s.beds.filter(u=>u.elderName).length;return s.occupiedCount=n,!0}return!1})),G()}}catch(t){console.error("获取房间卡片列表失败:",t)}finally{f.value=!1}},G=()=>{const t=new Set,l=new Set,d=new Set,o=new Set,I=new Set;Q.value.forEach(s=>{s.roomType&&t.add(s.roomType),s.areaName&&l.add(s.areaName),s.roomOrientation&&d.add(s.roomOrientation),s.status&&o.add(s.status),s.beds&&s.beds.length>0&&s.beds.forEach(e=>{e.gender&&I.add(e.gender)})}),U.value={roomTypes:Array.from(t),areas:Array.from(l),directions:Array.from(d),statuses:Array.from(o),genders:Array.from(I)}},m=(t,l)=>{t==="status"&&l==="外出"?i.value.bedStatus==="外出"?i.value.bedStatus="":(i.value.bedStatus="外出",i.value.status=""):t==="status"?(i.value.status===l?i.value.status="":i.value.status=l,i.value.bedStatus=""):i.value[t]===l?i.value[t]="":i.value[t]=l,S()},H=()=>{const t=i.value.buildingName,l=i.value.floorName;Object.keys(i.value).forEach(d=>{i.value[d]=""}),i.value.buildingName=t,i.value.floorName=l,i.value.bedStatus="",S()},J=(t,l)=>{M.value=t,P.value=l||"全部",L.value=null,R.value="全部",i.value.buildingName=l||"",i.value.floorName="",t?V(t):w.value=[],S()},X=(t,l)=>{L.value=t,R.value=l||"全部",i.value.floorName=l||"",S()},Z=F(()=>Q.value.reduce((t,l)=>{var d;return t+(((d=l.beds)==null?void 0:d.length)||0)},0)),j=F(()=>Q.value.reduce((t,l)=>l.beds?t+l.beds.filter(d=>!d.elderName).length:t,0));function _(t){return t.elderName?t.gender==="男"?"occupied-male":t.gender==="女"?"occupied-female":"occupied-other":"vacant"}return(t,l)=>{const d=h("el-button"),o=h("el-button-group"),I=h("el-form-item"),s=h("el-tag");return c(),E("div",ce,[a("div",Be,[a("div",re,[B(I,{label:"房间状态："},{default:r(()=>[B(o,null,{default:r(()=>[(c(),E(v,null,k(K,e=>B(d,{key:e,class:b({"is-active":e==="外出"?i.value.bedStatus==="外出":i.value.status===e}),onClick:n=>m("status",e)},{default:r(()=>[g(A(e),1)]),_:2},1032,["class","onClick"])),64))]),_:1})]),_:1})]),a("div",oe,[B(I,{label:"房间朝向："},{default:r(()=>[B(o,null,{default:r(()=>[(c(),E(v,null,k(O,e=>B(d,{key:e,class:b({"is-active":i.value.roomOrientation===e}),onClick:n=>m("roomOrientation",e)},{default:r(()=>[g(A(e),1)]),_:2},1032,["class","onClick"])),64))]),_:1})]),_:1})]),a("div",ge,[B(I,{label:"老人性别："},{default:r(()=>[B(o,null,{default:r(()=>[(c(),E(v,null,k(W,e=>B(d,{key:e,class:b({"is-active":i.value.gender===e}),onClick:n=>m("gender",e)},{default:r(()=>[g(A(e),1)]),_:2},1032,["class","onClick"])),64))]),_:1})]),_:1})]),a("div",Qe,[B(I,{label:"房间区域："},{default:r(()=>[B(o,null,{default:r(()=>[(c(),E(v,null,k(z,e=>B(d,{key:e,class:b({"is-active":i.value.areaName===e}),onClick:n=>m("areaName",e)},{default:r(()=>[g(A(e),1)]),_:2},1032,["class","onClick"])),64))]),_:1})]),_:1})]),a("div",Ce,[B(I,{label:"房间类型："},{default:r(()=>[B(o,null,{default:r(()=>[(c(),E(v,null,k(q,e=>B(d,{key:e,class:b({"is-active":i.value.roomType===e}),onClick:n=>m("roomType",e)},{default:r(()=>[g(A(e),1)]),_:2},1032,["class","onClick"])),64))]),_:1})]),_:1})]),a("div",Ie,[B(d,{onClick:H},{default:r(()=>[g("重置筛选")]),_:1})])]),a("div",ve,[a("span",null,[g("楼栋: "),a("strong",null,A(P.value),1)]),a("span",null,[g("楼层: "),a("strong",null,A(R.value),1)]),a("span",null,[g("房间: "),a("strong",null,A(Q.value.length),1)]),a("span",null,[g("床位: "),a("strong",null,A(Z.value),1)]),a("span",null,[g("空床: "),a("strong",null,A(j.value),1)])]),a("div",ke,[a("div",be,[a("div",fe,[(c(!0),E(v,null,k(N.value,e=>(c(),E("div",{key:e.id,class:b(["nav-item",{active:e.id===M.value}]),onClick:n=>J(e.id,e.buildingName)},[Ne,g(A(e.buildingName),1)],10,pe))),128))]),a("div",Se,[(c(!0),E(v,null,k(w.value,e=>(c(),E("div",{key:e.id,class:"building-floor",style:le({background:e.id===L.value?"#409eff":"rgba(255,255,255,0.6)",color:e.id===L.value?"#fff":"#303133"}),onClick:n=>X(e.id,e.floorName)},A(e.floorName),13,me))),128)),Te,Le])]),a("div",ye,[Q.value.length===0?(c(),E("div",he,"暂无数据")):(c(!0),E(v,{key:1},k(Q.value,e=>(c(),E("div",{key:e.roomId||e.id,class:"room-card"},[a("div",we,[a("div",Re,[a("span",null,A(e.roomNumber),1),xe,g(" "+A(e.areaName)+" "+A(e.roomOrientation),1)]),a("div",De,[B(s,{type:e.status==="满员"?"primary":e.status==="空闲"?"success":e.status==="外出"?"warning":"info",size:"small"},{default:r(()=>[g(A((e.occupiedCount||0)+"/"+(e.capacity||0)),1)]),_:2},1032,["type"])])]),a("div",Me,[(c(!0),E(v,null,k(e.beds||[],(n,u)=>(c(),E("div",{key:n.id||u,class:b(["bed-card",_(n)])},[a("div",Pe,[a("img",{src:n.elderName?n.gender==="男"?D(de):D(ue):D(Ee),alt:"床位图标",class:"bed-image"},null,8,Je),a("span",Fe,A(n.bedNumber||u+1),1)]),n.elderName?(c(),E("div",Ke,[a("span",Oe,A(n.elderName),1),g(" "+A(n.age)+"岁 ",1),We,a("span",ze,[a("span",null,A(n.careLevel),1),g(),qe,a("span",null,A(n.abilityLevel),1)])])):(c(),E("div",Ue,"可入住")),n.status==="外出"?(c(),E("span",Ye,"外")):ne("",!0)],2))),128))])]))),128))])])])}}},Xe=te(Ve,[["__scopeId","data-v-7010b023"]]);export{Xe as default};
