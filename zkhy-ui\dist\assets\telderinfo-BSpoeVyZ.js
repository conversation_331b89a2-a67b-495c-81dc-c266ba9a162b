import{X as t}from"./index-B0qHf98Y.js";function n(e){return t({url:"/elderinfo/basicInfo/list",method:"get",params:e})}function r(e){return t({url:"/elderinfo/basicInfo/listinfo",method:"get",params:e})}function o(e){return t({url:"/elderinfo/basicInfo",method:"post",data:e})}function i(e){return t({url:"/elderinfo/basicInfo",method:"put",data:e})}function f(e){return t({url:"/elderinfo/basicInfo/"+e,method:"delete"})}function s(e){return t({url:"/elderinfo/aggregate/info/"+e,method:"get"})}function d(e){return t({url:"/elderinfo/aggregate/save",method:"post",data:e})}function u(e){return t({url:"/elderinfo/aggregate/update",method:"put",data:e})}export{n as a,i as b,o as c,f as d,s as g,r as l,d as s,u};
