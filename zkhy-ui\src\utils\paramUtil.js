// 处理查询条件范围
export const dealParams = (params, paramAll, names) => {
    if (names?.length > 0) {
        let tmpParams = {};
        names.forEach(item => {
            if (paramAll.value[item]?.length > 0) {
                tmpParams["begin" + item] = paramAll.value[item][0];
                tmpParams["end" + item] = paramAll.value[item][1];
            }
        });
        params.params = tmpParams;
    }
};

// 计算当前临时id
export const getTemId = () => {
    const timestamp = Date.now()
                          .toString(36);
    const random = Math.random()
                       .toString(36)
                       .substring(2, 5);
    let tmpId = `tmp-${timestamp}-${random}`;
   return tmpId;
};

