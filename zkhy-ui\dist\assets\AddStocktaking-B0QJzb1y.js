import{_ as G,B as W,u as X,r as c,a as Z,d as ee,C as oe,N as te,e as d,I as ae,c as le,i as u,J as O,f as o,j as V,k as q,l as a,h as l,n as f,t as P,O as ne,m as S,o as v,M as re,v as se,x as ie}from"./index-B0qHf98Y.js";import{g as ce,l as de}from"./tWarehouseMedication-Ycu1QDaW.js";import{g as ue,a as me}from"./tWarehouseInventoryCheck-Dhh0m5wG.js";const x=k=>(se("data-v-c2f68efe"),k=k(),ie(),k),pe={class:"app-container contentDiv"},fe={class:"bottom_room_table"},ge={class:"cardBox"},_e=x(()=>u("div",{class:"title_room"},[u("h3",null,"盘点信息")],-1)),be=x(()=>u("span",{class:"title"},"盘点单号：",-1)),ye=x(()=>u("span",{class:"title"},"盘点人：",-1)),he=x(()=>u("span",{class:"title"},"创建时间：",-1)),ve=["innerHTML"],ke=W({name:"AddMedication"}),Ne=Object.assign(ke,{setup(k){const m=X(),N=c(JSON.parse(localStorage.getItem("userInfo")));console.log(N,"-------user-------");const R=Z(),{proxy:_}=ee(),{medication_type:Ce,goods_type:we}=_.useDict("medication_type","goods_type"),b=c([]);c(!1);const U=c(!0),p=c(!0);c([]);const C=c(""),I=c(""),w=c(),Q=oe({form:{status:0},queryParams:{pageNum:1,pageSize:1e3,category:null,medicineCode:null,medicineName:null,locationCode:null},rules:{medicineCode:[{required:!0,message:"请输入药品编码",trigger:"blur"}],barcode:[{required:!0,message:"请输入条形码",trigger:"blur"}],medicineName:[{required:!0,message:"请输入药品名称",trigger:"blur"}],invoiceItem:[{required:!0,message:"请输入发票项目",trigger:"blur"}],purchasePrice:[{required:!0,message:"请输入采购价",trigger:"blur"}]}}),{queryParams:s,form:Ve,rules:Se}=te(Q);function B(){console.log(m.params.id,"id"),console.log(m.params.type,"type"),m.params.type=="add"?(p.value=!1,I.value="新增商品信息",w.value=re().format("YYYY-MM-DD HH:mm"),K(),ce({prefix:"PD"}).then(t=>{C.value=t.msg,console.log(t,"newcode")})):m.params.type=="edit"?(I.value="修改商品信息",p.value=!1):m.params.type=="show"&&(I.value="查看商品信息",p.value=!0,ue(m.params.id).then(t=>{console.log(t,"edit111111111111"),C.value=t.data.checkNo,N.value.nickName=t.data.checkPerson,w.value=t.data.checkDate,b.value=t.data.details})),U.value=!1}function K(){m.params.id&&de(s.value).then(t=>{console.log(t.rows,"res"),b.value=t.rows.map(e=>({itemCode:e.medicineCode,itemName:e.medicineName,locationCode:e.locationCode,category:e.category,specification:e.specification,currentQuantity:e.currentQuantity,goodsCategory:e.goodsCategory}))})}const L=(t,e)=>Number(Number(e)-Number(t))||0,H=(t,e)=>{if(console.log(t,e),e!=null){const n=Number(Number(e)-Number(t))||0;return Number(Number(e)-Number(t))>0?`<span style="color:green;font-weight:600">+${n}</span>`:Number(Number(e)-Number(t))<0?`<span style="color:red;font-weight:600">${n}</span>`:+(Number(e)-Number(t)==0)?'<span style="font-weight:600">0</span>':'<span style="color:#999;">盘盈盘亏</span>'}else return'<span style="color:#999;">盘盈盘亏</span>'};function Y(){b.value.map(n=>{n.differenceQuantity=L(n.currentQuantity,n.checkedQuantity)||""});const t=b.value.filter(n=>n.checkedQuantity),e={checkNo:C.value,checkPerson:N.value.nickName,checkDate:w.value,status:1,details:t};console.log(t,"989898"),me(e).then(n=>{n.code==200&&(_.$tab.closeOpenPage(),R.push("/warehouse/warehouse/wmsstocktaking")),console.log(n)})}function $(){T(),_.$tab.closeOpenPage(),R.push("/warehouse/warehouse/wmsstocktaking")}function T(){s.value={category:void 0,medicineCode:void 0,medicineName:void 0,locationCode:void 0},_.resetForm("queryRef")}function g(){s.value.pageNum=1,B()}function j(){_.resetForm("addMedicationRef"),g()}return B(),(t,e)=>{const n=d("el-button"),D=d("el-col"),A=d("el-row"),y=d("el-input"),h=d("el-form-item"),E=d("el-form"),i=d("el-table-column"),F=d("el-input-number"),J=d("el-table"),z=ae("loading");return v(),le("div",pe,[u("div",fe,[u("div",ge,[_e,u("div",null,[a(p)?q("",!0):(v(),V(n,{key:0,type:"primary",onClick:Y},{default:l(()=>[f("完成盘点")]),_:1})),o(n,{icon:"Refresh",onClick:$},{default:l(()=>[f("返回")]),_:1})])]),o(A,null,{default:l(()=>[o(D,{span:6},{default:l(()=>[be,f(P(a(C)),1)]),_:1}),o(D,{span:6},{default:l(()=>[ye,f(P(a(N).nickName),1)]),_:1}),o(D,{span:6},{default:l(()=>[he,f(P(a(w)),1)]),_:1})]),_:1})]),O(o(E,{model:a(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[o(h,{label:"类别",prop:"category"},{default:l(()=>[o(y,{modelValue:a(s).category,"onUpdate:modelValue":e[0]||(e[0]=r=>a(s).category=r),placeholder:"请输入类别",clearable:"",onKeyup:S(g,["enter"])},null,8,["modelValue"])]),_:1}),o(h,{label:"物品编码",prop:"medicineCode"},{default:l(()=>[o(y,{modelValue:a(s).medicineCode,"onUpdate:modelValue":e[1]||(e[1]=r=>a(s).medicineCode=r),placeholder:"请输入物品编码",clearable:"",onKeyup:S(g,["enter"])},null,8,["modelValue"])]),_:1}),o(h,{label:"物品名称",prop:"medicineName"},{default:l(()=>[o(y,{modelValue:a(s).medicineName,"onUpdate:modelValue":e[2]||(e[2]=r=>a(s).medicineName=r),placeholder:"请输入物品名称",clearable:"",onKeyup:S(g,["enter"])},null,8,["modelValue"])]),_:1}),o(h,{label:"货位号",prop:"locationCode"},{default:l(()=>[o(y,{modelValue:a(s).locationCode,"onUpdate:modelValue":e[3]||(e[3]=r=>a(s).locationCode=r),placeholder:"请输入货位号",clearable:"",onKeyup:S(g,["enter"])},null,8,["modelValue"])]),_:1}),o(h,null,{default:l(()=>[o(n,{type:"primary",icon:"Search",onClick:g},{default:l(()=>[f("查询")]),_:1}),o(n,{icon:"Refresh",onClick:j},{default:l(()=>[f("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ne,!a(p)]]),O((v(),V(J,{data:a(b),border:"",stripe:""},{default:l(()=>[o(i,{type:"index",label:"序号",width:"55",align:"center"}),o(i,{label:"物品编码",align:"center",prop:"itemCode"}),o(i,{label:"物品名称",align:"center",prop:"itemName",width:"180"}),o(i,{label:"货位号",align:"center",prop:"locationCode"}),o(i,{label:"类别",align:"center",prop:"goodsCategory"}),o(i,{label:"物品规格",align:"center",prop:"specification"}),o(i,{label:"当前库存",align:"center",prop:"currentQuantity"}),o(i,{label:"盘点库存",align:"center",prop:"checkedQuantity"},{default:l(r=>[a(p)?q("",!0):(v(),V(F,{key:0,min:"0",max:"100000",modelValue:r.row.checkedQuantity,"onUpdate:modelValue":M=>r.row.checkedQuantity=M,placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),o(i,{label:"盘盈盘亏",align:"center",prop:"differenceQuantity"},{default:l(r=>[u("span",{innerHTML:H(r.row.currentQuantity,r.row.checkedQuantity)},null,8,ve)]),_:1}),o(i,{label:"备注",align:"center",prop:"remark"},{default:l(r=>[a(p)?q("",!0):(v(),V(y,{key:0,modelValue:r.row.remark,"onUpdate:modelValue":M=>r.row.remark=M,placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue"]))]),_:1})]),_:1},8,["data"])),[[z,a(U)]])])}}}),Me=G(Ne,[["__scopeId","data-v-c2f68efe"]]);export{Me as default};
