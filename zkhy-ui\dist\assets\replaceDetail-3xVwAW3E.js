import{_ as I,r as d,e as f,I as V,J as S,c as u,o as p,f as m,h as v,k as q,i as e,t,K as T,L as B,n as w,aE as E,v as L,x as M}from"./index-B0qHf98Y.js";const n=r=>(L("data-v-32617104"),r=r(),M(),r),A={class:"hfrecords"},H=n(()=>e("div",{class:"title_room"}," 老人信息 ",-1)),P={key:0,class:"detail-content"},R={class:"room-info"},W={class:"info-left"},z={class:"info-item"},F=n(()=>e("span",{class:"label"},"老人姓名：",-1)),J={class:"value"},K={class:"info-item"},O=n(()=>e("span",{class:"label"},"楼栋信息：",-1)),U={class:"value"},Y={class:"info-item"},$=n(()=>e("span",{class:"label"},"床位号：",-1)),j={class:"value"},G={class:"info-item"},Q=n(()=>e("span",{class:"label"},"房间号：",-1)),X={class:"value"},Z=n(()=>e("div",{class:"title_room"},[e("span",{class:"label"},"耗材明细")],-1)),ee={class:"table-area"},te={class:"costDate"},se={class:"costSum"},le={class:"table-style"},oe=n(()=>e("thead",null,[e("tr",null,[e("th",{style:{"text-align":"center",width:"60px"},class:""},"序号"),e("th",{style:{"text-align":"center"}},"服务日期"),e("th",{style:{"text-align":"center"}},"服务项目"),e("th",{style:{"text-align":"center"}},"数量"),e("th",{style:{"text-align":"center"}},"价格"),e("th",{style:{"text-align":"center"}},"操作人"),e("th",{style:{"text-align":"center"}},"记录时间"),e("th",{style:{"text-align":"center",width:"120px"}},"备注")])],-1)),ne={style:{"text-align":"center",width:"60px"}},ae={style:{"text-align":"center"}},ce={style:{"text-align":"center"}},ie={style:{"text-align":"center"}},de={style:{"text-align":"center"}},re={style:{"text-align":"center"}},_e={style:{"text-align":"center"}},he={style:{"text-align":"center"}},ue={__name:"replaceDetail",setup(r,{expose:C}){const _=d(!1),D=d(!1),l=d(null),x=d([]),g=d(null),N=o=>{E({queryMonth:o.queryMonth.replace("年","-").replace("月",""),elderId:o.elderId,elderName:o.elderName,bedId:o.bedId,status:1}).then(a=>{l.value=o||{},_.value=!0,x.value=a.rows||[]})},b=()=>{_.value=!1,l.value=null},k=()=>{const o=g.value.cloneNode(!0);o.querySelectorAll(".el-input, .el-textarea").forEach(h=>{var i;const y=((i=h.querySelector("input, textarea"))==null?void 0:i.value)||"",s=document.createElement("div");s.textContent=y,s.style.padding="8px",h.replaceWith(s)});const c=window.open("","_blank");c.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>耗材明细</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td , .table-style th {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
              .table-style td{              
                  color:#666;                
              }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${o.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `),c.document.close()};return C({openDialog:N}),(o,a)=>{const c=f("el-button"),h=f("el-dialog"),y=V("loading");return S((p(),u("div",A,[m(h,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=s=>_.value=s),title:"详情",width:"60%",onClose:b},{footer:v(()=>[m(c,{onClick:b},{default:v(()=>[w("返回")]),_:1}),m(c,{type:"primary",onClick:k},{default:v(()=>[w("打印")]),_:1})]),default:v(()=>[H,l.value?(p(),u("div",P,[e("div",R,[e("div",W,[e("div",z,[F,e("span",J,t(l.value.elderName||"-"),1)]),e("div",K,[O,e("span",U,t(l.value.buildingName||"-"),1)]),e("div",Y,[$,e("span",j,t(l.value.roomBed||"-"),1)]),e("div",G,[Q,e("span",X,t(l.value.roomNumber||"-"),1)])])]),Z,e("div",ee,[e("div",te,t(l.value.queryMonth),1),e("div",se," 费用总价：￥"+t(l.value.monthTotal)+"元 ",1)]),e("div",{class:"attachment-area",ref_key:"printContent",ref:g},[e("table",le,[oe,e("tbody",null,[(p(!0),u(T,null,B(x.value,(s,i)=>(p(),u("tr",{key:i},[e("td",ne,t(i+1),1),e("td",ae,t(s.serviceDate),1),e("td",ce,t(s.supplyItem),1),e("td",ie,t(s.quantity),1),e("td",de,t(s.price),1),e("td",re,t(s.nurseName),1),e("td",_e,t(s.updateTime),1),e("td",he,t(s.remark),1)]))),128))])])],512)])):q("",!0)]),_:1},8,["modelValue"])])),[[y,D.value]])}}},ve=I(ue,[["__scopeId","data-v-32617104"]]);export{ve as default};
