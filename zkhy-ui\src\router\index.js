/* Layout */
import Layout from "@/layout";
import {createRouter, createWebHistory} from "vue-router";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */

    // 公共路由
export const constantRoutes = [
        {
            path     : "/redirect",
            component: Layout,
            hidden   : true,
            children : [
                {
                    path     : "/redirect/:path(.*)",
                    component: () => import("@/views/redirect/index.vue"),
                },
            ],
        },
        {
            path     : "/login",
            component: () => import("@/views/login"),
            hidden   : true,
        },
        {
            path     : "/register",
            component: () => import("@/views/register"),
            hidden   : true,
        },
        {
            path     : "/:pathMatch(.*)*",
            component: () => import("@/views/error/404"),
            hidden   : true,
        },
        {
            path     : "/401",
            component: () => import("@/views/error/401"),
            hidden   : true,
        },
        {
            path     : "",
            component: Layout,
            redirect : "/index",
            children : [
                {
                    path     : "/index",
                    component: () => import("@/views/index"),
                    name     : "Index",
                    meta     : {
                        title: "首页",
                        icon : "dashboard",
                        affix: true,
                    },
                },

            ],
        },
        {
            path     : "/user",
            component: Layout,
            hidden   : true,
            redirect : "noredirect",
            children : [
                {
                    path     : "profile",
                    component: () => import("@/views/system/user/profile/index"),
                    name     : "Profile",
                    meta     : {
                        title: "个人中心",
                        icon : "user",
                    },
                },
            ],
        },
     {
            path     : "/eldersystem/home",
            component: Layout,
            hidden   : true,
            redirect : "noredirect",
            children : [
                {
                    path     : "noticeList",
                    component: () => import("@/views/eldersystem/home/<USER>"),

                    name     : "noticeList",
                    meta     : {
                    title     : "公告列表",
                    activeMenu: "/home",
                },
                },
            ],
        },
        //库存管理
        {
            path     : "/warehousing",
            component: Layout,
            hidden   : true,
            children :[{
                path     : "/warehousingAddorEdit",
                component: () => import("@/views/eldersystem/warehouse/wmscheckin/wmsAddEdit.vue"),
                name     : "warehousingAddorEdit",
                meta:{
                    title     : "入库管理表单",
                    activeMenu: "/warehousingAddorEdit",
                }
            },{
                path     : "/warehouseOutAddorEdit",
                component: () => import("@/views/eldersystem/warehouse/wmscheckout/wmsOutAddEdit.vue"),
                name     : "warehouseOutAddorEdit",
                meta:{
                    title     : "出库管理表单",
                    activeMenu: "/warehouseOutAddorEdit",
                }
            }]
        },
        //盘点详情
        {
            path: '/wmsstocktaking/AddStocktaking',
            component: Layout,
            hidden: true,
            redirect: 'noRedirect',
            children: [
                {
                    path: 'show/:id(\\d+)/:type',
                    component: () => import('@/views/eldersystem/warehouse/wmsstocktaking/AddStocktaking.vue'),
                    name: 'showStocktakingDetail',
                    meta: { title: '盘点详情', activeMenu: '/warehouse/wmsstocktaking' }
                }
            ]
        },
        //药品和商品新增
        {
            path: '/wmsmedication/AddMedication',
            component: Layout,
            hidden: true,
            redirect: 'noRedirect',
            children:[ {
                path: 'add/:id(\\d+)/:type',
                component: () => import('@/views/eldersystem/warehouse/wmsmedication/AddMedication.vue'),
                name:'AddMedication',
                meta: { title: '药品管理', activeMenu: '/warehouse/wmsmedication' }
            }
            ]
        },
        //药品和商品修改
        {
            path: '/wmsmedication/editMedication',
            component: Layout,
            hidden: true,
            redirect: 'noRedirect',
            children:[ {
                path: 'edit/:id(\\d+)/:type',
                component: () => import('@/views/eldersystem/warehouse/wmsmedication/editMedication.vue'),
                name:'editMedication',
                meta: { title: '药品显示', activeMenu: '/warehouse/wmsmedication' }
            }
            ]
        },
        //库存详情
        {
            path: '/wmsmedication/addManagement',
            component: Layout,
            hidden: true,
            redirect: 'noRedirect',
            children:[ {
                path: 'show/:id(\\d+)',
                component: () => import('@/views/eldersystem/warehouse/wmsmanagement/addManagement.vue'),
                name:'showManagement',
                meta: { title: '库存管理', activeMenu: '/warehouse/addManagement' }
            }
            ]
        }
    ];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
    {
        path       : "/system/user-auth",
        component  : Layout,
        hidden     : true,
        permissions: ["system:user:edit"],
        children   : [
            {
                path     : "role/:userId(\\d+)",
                component: () => import("@/views/system/user/authRole"),
                name     : "AuthRole",
                meta     : {
                    title     : "分配角色",
                    activeMenu: "/system/user",
                },
            },
        ],
    },
    {
        path       : "/system/role-auth",
        component  : Layout,
        hidden     : true,
        permissions: ["system:role:edit"],
        children   : [
            {
                path     : "user/:roleId(\\d+)",
                component: () => import("@/views/system/role/authUser"),
                name     : "AuthUser",
                meta     : {
                    title     : "分配用户",
                    activeMenu: "/system/role",
                },
            },
        ],
    },
    {
        path       : "/system/dict-data",
        component  : Layout,
        hidden     : true,
        permissions: ["system:dict:list"],
        children   : [
            {
                path     : "index/:dictId(\\d+)",
                component: () => import("@/views/system/dict/data"),
                name     : "Data",
                meta     : {
                    title     : "字典数据",
                    activeMenu: "/system/dict",
                },
            },
        ],
    },
    {
        path       : "/monitor/job-log",
        component  : Layout,
        hidden     : true,
        permissions: ["monitor:job:list"],
        children   : [
            {
                path     : "index/:jobId(\\d+)",
                component: () => import("@/views/monitor/job/log"),
                name     : "JobLog",
                meta     : {
                    title     : "调度日志",
                    activeMenu: "/monitor/job",
                },
            },
        ],
    },
    { //       {
        //     path     : "/eldersystem/home/<USER>",
        //          component: () => import("@/views/eldersystem/home/<USER>"),

        //     hidden   : true,
        // },
    //       {
    //     path       : "/eldersystem/home/<USER>",
   
    //     hidden     : true,
    //     component: () => import("@/views/eldersystem/home/<USER>"),
    //        component: Layout,
    //             // meta     :
        path       : "/tool/gen-edit",
        component  : Layout,
        hidden     : true,
        permissions: ["tool:gen:edit"],
        children   : [
            {
                path     : "index/:tableId(\\d+)",
                component: () => import("@/views/tool/gen/editTable"),
                name     : "GenEdit",
                meta     : {
                    title     : "修改生成配置",
                    activeMenu: "/tool/gen",
                },
            },
        ],
    },
    {
        path       : "/ReceptionManagement/receptionVisit",
        component  : Layout,
        hidden     : true,
        permissions: ["reception:receptionVisit:Visitlist"],
        children   : [
            {
                path     : "detail/:id(\\d+)",
                component: () => import("@/views/eldersystem/receptionVisit"),
                name     : "receptionVisit",
                meta     : {
                    title     : "回访记录",
                    activeMenu: "/ReceptionManagement/reception",
                },
            },
        ],
    },
    {
        path       : "/elderInfo/elderFiles",
        component  : Layout,
        hidden     : true,
        permissions: ["elderInfo:elderFiles:detail"],
        children   : [
            {
                path     : "detail/:id(\\d+)/:type",
                component: () => import("@/views/eldersystem/elderInfo/detail.vue"),
                name     : "elderInfoDetail",
                meta     : {
                    title     : "档案详情",
                    activeMenu: "elderInfo/elderFiles",
                },
            },
        ],
    },
    // {
    //     path       : "/elderInfo/elderFiles",
    //     component  : Layout,
    //     hidden     : true,
    //     permissions: ["elderInfo:elderFiles:add"],
    //     children   : [
    //         {
    //             path     : "add",
    //             component: () => import("@/views/eldersystem/elderInfo/detail.vue"),
    //             name     : "add",
    //             meta     : {
    //                 title     : "新增档案",
    //                 activeMenu: "elderInfo/elderFiles",
    //             },
    //         },
    //     ],
    // },
    {
        path       : "/eldercheckin/addelder",
        component  : Layout,
        hidden     : true,
        permissions: ["eldercheckin:addelder:add"],
        children   : [
            {
                path     : "addel/:type",
                component: () => import("@/views/eldersystem/checkin/addElder.vue"),
                name     : "addelder",
                meta     : {
                    title     : "新增入住",
                    activeMenu: "/elderInfo/checkin",
                },
            },
        ],
    },
    {
        path       : "/eldercheckin/editelder",
        component  : Layout,
        hidden     : true,
        permissions: ["eldercheckin:editelder:edit"],
        children   : [
            {
                path     : "edit/:id(\\d+)/:type",
                component: () => import("@/views/eldersystem/checkin/addElder.vue"),
                name     : "editelder",
                meta     : {
                    title     : "编辑入住",
                    activeMenu: "/elderInfo/checkin",
                },
            },
        ],
    },
    {
        path       : "/eldercheckin/showelder",
        component  : Layout,
        hidden     : true,
        permissions: ["eldercheckin:showelder:show"],
        children   : [
            {
                path     : "show/:id(\\d+)/:type",
                component: () => import("@/views/eldersystem/checkin/addElder.vue"),
                //component: () => import('@/views/system/dict/data'),
                name: "showelder",
                meta: {
                    title     : "查看入住",
                    activeMenu: "/elderInfo/checkin",
                },
            },
        ],
    },
    {
        path       : "/eldercheckin/showAssessmentDetails",
        component  : Layout,
        hidden     : true,
        permissions: ["eldercheckin:showAssessmentDetails:add"],
        children   : [
            // {
            //     path       : "detail/:id(\\d+)",
            //     permissions: ["eldercheckin:showAssessmentDetails:detail"],
            //     component  : () => import("@/views/eldersystem/elderInfo/assessmentDetail/index.vue"),
            //     //component: () => import('@/views/system/dict/data'),
            //     name: "assessmentDetail",
            //     meta: {
            //         title     : "评估详情",
            //         activeMenu: "/assessment/assessmentRecord",
            //     },
            // },
            {
                path       : "add/:id(\\d+)/:type",
                permissions: ["eldercheckin:showAssessmentDetails:add"],
                component  : () => import("@/views/eldersystem/assessment/assessmentFormList/index.vue"),
                //component: () => import('@/views/system/dict/data'),
                name: "assessmentAdds",
                meta: {
                    title     : "新增评估",
                    activeMenu: "/assessment/assessmentRecord",
                },
            },
        ],
    },

    {
    //纸质的详情
    path: '/eldercheckin/showAssessmentDetails', 
    component: Layout,
    hidden: true,
    permissions: ['eldercheckin:showAssessmentDetails:detail'],
    children: [
      {
        path: 'detail/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/assessment/assessmentFormList/index.vue'),
        //component: () => import('@/views/system/dict/data'),
        name: 'assessmentDetail',
        meta: { title: '纸质评估详情', activeMenu: '/eldercheckin/showAssessmentDetails' }
      }
    ]
  },
  {
    //在线的详情
    path: '/eldercheckin/showAssessmentDetails', 
    component: Layout,
    hidden: true,
    permissions: ['eldercheckin:showAssessmentDetails:detailOnLine'],
    children: [
      {
        path: 'detailOnLine/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/assessment/assessmentFormList/index.vue'),
        //component: () => import('@/views/system/dict/data'),
        name: 'detailOnLine',
        meta: { title: '在线评估详情', activeMenu: '/eldercheckin/showAssessmentDetails' }
      }
    ]
  },

  {
    //护士交接新增
    path: '/nursecheckin/nurseCheckinAdd', 
    component: Layout,
    hidden: true,
    permissions: ['nursecheckin:nurseCheckinAdd:add'],
    children: [
      {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurse/nurseAdd.vue'),
        name:'nurseCheckinAddForm',
        meta: { title: '护士交接新增', activeMenu: '/nursecheckin/nurseCheckinAdd' }
      }
    ]
  },
  {
    //护士工作台
    path: '/roomInspection/roomInspectionForm', 
    component: Layout,
    hidden: true,
    permissions: ['roomInspection:roomInspectionForm:add'],
    children: [
      {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/roomInspectionForm.vue'),
        name: 'roomInspectionAdd',
        meta: { title: '查房表', activeMenu: '/roomInspection/roomInspectionForm' }
      }
    ]
  },
  {
    //护士工作台查看历史记录
    path: '/nurseworkstation/recordlist', 
    component: Layout,
    hidden: true,
    permissions: ['nurseworkstation:recordlist:add'],
    children: [
      {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/recordlist.vue'),
        name: 'nurseworkstationAdd',
        meta: { title: '巡房表历史记录', activeMenu: '/nurseworkstation/recordlist' }
      }
    ]
  },
  // 护士交接班表新增
  {
    path: '/nurseShiftChangeReport/nurseShiftAdd', 
    component: Layout,
    hidden: true,
    permissions: ['nurseShiftChangeReport:nurseShiftAdd:add'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/nurseShiftChangeReport/nurseShiftAdd.vue'),
        name:'nurseShiftAddForm',
        meta: { title: '护士交接班表', activeMenu: '/nurseShiftChangeReport/nurseShiftAdd' }
        }
    ]
  },
  // 护士交接班表查看历史记录
  {
    path: '/nurseShiftHistory/nurseShiftHistoryForm', 
    component: Layout,
    hidden: true,
    permissions: ['nurseShiftHistory:nurseShiftHistoryForm:add'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/nurseShiftChangeReport/nursingShiftHistory.vue'),
        name:'nurseShiftHistoryForm',
        meta: { title: '护士交接班表历史记录', activeMenu: '/nurseShiftHistory/nurseShiftHistoryForm' }
        }
    ]
  },
  // 护士交接班表详情查看
  {
    path: '/nurseShiftChangeReport/nurseShiftDetail', 
    component: Layout,
    hidden: true,
    permissions: ['nurseShiftChangeReport:nurseShiftDetail:add'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/nurseShiftChangeReport/nurseShiftDetail.vue'),
        name:'nurseShiftDetailForm',
        meta: { title: '护士交接班表详情', activeMenu: '/nurseShiftChangeReport/nurseShiftDetail' }
        }
    ]
  },
  {
  path: '/eldersystem/work/nurseworkstation/replaceConsumables',
  name: 'ReplaceConsumables',
  component: () => import('@/views/eldersystem/work/nurseworkstation/replaceConsumables.vue')
},
// 更换易耗品记录表
  {
    path: '/nurseShiftChangeReport/replaceConsumablesRecord', 
    component: Layout,
    hidden: true,
    permissions: ['nurseShiftChangeReport:replaceConsumablesRecord:add'],
    children: [
        {
        path: 'add/:id(\\d+)/:type', 
        component: () => import('@/views/eldersystem/work/nurseworkstation/replaceConsumables/completeTable.vue'),
        name:'replaceConsumablesRecordForm',
        meta: { title: '更换易耗品记录表', activeMenu: '/nurseShiftChangeReport/replaceConsumablesRecord' }
        }
    ]
  },
// 更换易耗品记录历史记录列表
{
    path: '/nurseShiftChangeReport/replaceConsumablesRecordHistory',
    component: Layout,
    hidden: true,
    permissions: ['nurseShiftChangeReport:replaceConsumablesRecordHistory:list'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/replaceConsumables/replaceHistory.vue'),
        name:'replaceConsumablesRecordHistoryList',
        meta: { title: '更换易耗品历史记录', activeMenu: '/nurseShiftChangeReport/replaceConsumablesRecordHistory' }
        }
    ]
  },
  //护士日志
  {
    path: '/nurseLogs/nurseLog',
    component: Layout,
    hidden: true,
    permissions: ['nurseLogs:nurseLog:list'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/nurseLog/nurseLog.vue'),
        name:'nurseLog',
        meta: { title: '护士日志', activeMenu: '/nurseLogs/nurseLog' }
        }
    ]
  },
  // 护士日志历史记录列表
  {
    path: '/nurseLogs/nurseLogHistory',
    component: Layout,
    hidden: true,
    permissions: ['nurseLogs:nurseLogHistory:list'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/nurseLog/nurseLogHistory.vue'),
        name:'nurseLogHistory',
        meta: { title: '护士日志历史记录', activeMenu: '/nurseLogs/nurseLogHistory' }
        }
    ]
  },
  //紫外线消毒记录表
  {
    path: '/ultravioletDisinfectionLog/uvDisinfectionRecord',
    component: Layout,
    hidden: true,
    permissions: ['ultravioletDisinfectionLog:uvDisinfectionRecord:list'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/ultravioletDisinfectionLog/uvDisinfectionRecord.vue'),
        name:'uvDisinfectionRecord',
        meta: { title: '紫外线消毒记录表', activeMenu: '/ultravioletDisinfectionLog/uvDisinfectionRecord' }
        }
    ]
  },
  // 紫外线消毒记录表历史记录
  {
    path: '/ultravioletDisinfectionLog/uvDisinfectionRecordHistory',
    component: Layout,
    hidden: true,
    permissions: ['ultravioletDisinfectionLog:uvDisinfectionRecordHistory:list'],
    children: [
        {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/ultravioletDisinfectionLog/uvDisinfectionRecordHistory.vue'),
        name:'uvDisinfectionRecordHistory',
        meta: { title: '紫外线消毒记录表历史记录', activeMenu: '/ultravioletDisinfectionLog/uvDisinfectionRecordHistory' }
        }
    ]
  },
  //紧急救护记录表
  {
      path: '/emergencyRescueLog/emergencyRescueRecord',
      component: Layout,
      hidden: true,
      permissions: ['emergencyRescueLog:emergencyRescueRecord:list'],
      children:[ {
        path: 'add/:id(\\d+)/:type',
        component: () => import('@/views/eldersystem/work/nurseworkstation/emergencyRescueLog/emergencyRescueRecord.vue'),
        name:'emergencyRescueRecord',
        meta: { title: '紧急救护记录表', activeMenu: '/emergencyRescueLog/emergencyRescueRecord' }
      }
      ]
  },
  //紧急救护历史记录表
  {
    path: '/emergencyRescueLog/emergencyRescueRecordHistory',
    component: Layout,
    hidden: true,
    permissions: ['emergencyRescueLog:emergencyRescueRecordHistory:list'],
    children:[ {
      path: 'add/:id(\\d+)/:type',
      component: () => import('@/views/eldersystem/work/nurseworkstation/emergencyRescueLog/emergencyRescueRecordHistory.vue'),
      name:'emergencyRescueRecordHistory',
      meta: { title: '紧急救护记录表历史记录', activeMenu: '/emergencyRescueLog/emergencyRescueRecordHistory' }
    }
    ]
  },
  // 机构管理
    {
      path: '/orgmanagement/addForm',
      component: Layout,
      hidden: true,
      permissions: ['orgmanagement:addForm:add'],
      children: [
        {
          path: 'add/:id(\\d+)/:type',
          component: () => import('@/views/eldersystem/orgmanagement/addForm.vue'),
          name: 'addForm',
          meta: { 
            title: '新增机构', 
            activeMenu: '/orgmanagement/org'
          }
        }
      ]
    }


];

const router = createRouter({
                                history: createWebHistory(),
                                routes : constantRoutes,
                                scrollBehavior(to, from, savedPosition) {
                                    if (savedPosition) {
                                        return savedPosition;
                                    }
                                    return {top: 0};
                                },
                            });

export default router;