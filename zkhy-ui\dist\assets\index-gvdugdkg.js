import{X as $,_ as Ue,B as Le,d as xe,r as i,C as He,N as Se,F as Be,e as m,I as Re,c as w,o as f,f as e,n as h,h as t,i as y,j as c,k as q,l as a,bh as Me,bi as Oe,bj as Fe,t as x,J as Y,m as Q,K as H,L as S,O as J,v as Ye,x as $e}from"./index-B0qHf98Y.js";import{g as X}from"./buildmanage-CIqJJJF0.js";import Ae from"./nursingAdd-CYjOOwO8.js";import Ee from"./detailNursing-BKZQCSYl.js";import{l as Pe}from"./tLiveRoom-DmSXfHxo.js";import{g as je,a as ze}from"./roommanage-DBG5TiIR.js";import"./index-DCxZ1IEc.js";import"./tNursingHandover-ugsVwCUd.js";function Ke(b){return $({url:"/handover/nursing/list",method:"get",params:b})}function qe(b){return $({url:"/handover/nursing",method:"post",data:b})}function Qe(b){return $({url:"/handover/nursing",method:"put",data:b})}const Je=b=>(Ye("data-v-91942b74"),b=b(),$e(),b),Xe={class:"app-container"},Ge={class:"treeStyle"},We=Je(()=>y("div",{class:"panel-header"},[y("span",{class:"title"},"楼层信息")],-1)),Ze={class:"custom-tree-node"},el={class:"node-content"},ll=Le({name:"nurse"}),ol=Object.assign(ll,{setup(b){const{proxy:D}=xe();i(!1);const{nursing_handover_status:A,sys_notice_type:al,room_type:tl,room_area:nl}=D.useDict("nursing_handover_status","sys_notice_type","room_type","room_area");i(!1);const E=i(""),B=i([]),k=i([]),R=i([]),M=i(!0),G=i([]),W=i(!0),Z=i(!0),ee=i(!0),O=i(0),P=i([]),le=i(""),I=i(!0),oe=i([]),j=i(!1),ae=i(),te=i(),ne=He({queryParams:{pageNum:1,pageSize:10,handoverDate:null,floorId:null,floorNumber:null,buildingId:null,buildingName:null,roomId:null,roomNumber:null,roomType:null,dayNurse:null,dayHandoverTime:null,nightNurse:null,nightHandoverTime:null,dayTotalCount:null,dayOutCount:null,dayLeaveCount:null,dayDeathCount:null,nightTotalCount:null,nightOutCount:null,nightLeaveCount:null,nightDeathCount:null,status:null},form:{},rules:{}}),{queryParams:d,form:n,rules:ue}=Se(ne),de={children:"children",label:"label"},z=i([]);function _(){M.value=!0,Ke(d.value).then(u=>{P.value=u.rows,O.value=u.total,M.value=!1}),X().then(u=>{console.log(u,"restree"),oe.value=u.data}),je().then(u=>{B.value=u.rows||[]})}function re(u){d.value.buildingName=u;const o=B.value.filter(g=>g.buildingName==u);ze(o[0].id).then(g=>{console.log(g,"getFloorListByBuild"),k.value=g.rows})}function ie(u){d.value.floorNumber=u;const o=k.value.filter(g=>g.floorName==u);console.log(k.value,"floorList"),console.log(o,"floorId"),Pe({floorId:o[0].id}).then(g=>{console.log(g,"getRoomListByBuild"),R.value=g.rows})}function se(){I.value=!1,me()}function me(){n.value={id:null,handoverDate:null,floorId:null,floorNumber:null,buildingId:null,buildingName:null,roomId:null,roomNumber:null,roomType:null,dayNurse:null,dayHandoverTime:null,nightNurse:null,nightHandoverTime:null,dayTotalCount:null,dayOutCount:null,dayLeaveCount:null,dayDeathCount:null,nightTotalCount:null,nightOutCount:null,nightLeaveCount:null,nightDeathCount:null,status:null,remark:null,createTime:null,updateTime:null,createBy:null,updateBy:null}}function pe(u,o){console.log(u,o,"node"),o.level==1?(d.value.floorNumber=null,d.value.roomName=null,d.value.roomId=null,d.value.buildingName=u.buildingName):o.level==2?(d.value.roomName=null,d.value.floorNumber=u.floorName):o.level==3&&(d.value.roomId=u.id,d.value.roomNumber=u.roomNumber),console.log(u,o,"node"),N()}function fe(u){R.value.map(o=>{o.id==u&&(d.value.roomNumber=o.roomNumber)})}function N(){d.value.pageNum=1,_()}function ge(){d.value={handoverDate:null,dayNurse:null,nightNurse:null,status:null,floorNumber:null,buildingName:null,roomNumber:null},N()}function be(u){G.value=u.map(o=>o.id),W.value=u.length!==1,Z.value=!u.length}function ve(u){E.value=u.id,D.$refs.nursingDetailRef.init(u.id)}function ye(){D.$refs.form.validate(u=>{u&&(n.id!=null?Qe(n.value).then(o=>{$modal.msgSuccess("修改成功"),I.value=!1,_()}):qe(n.value).then(o=>{$modal.msgSuccess("新增成功"),I.value=!1,_()}))})}const ce=async()=>{j.value=!0;try{const u=await X();u.code===200&&(console.log(u,"restree"),z.value=u.data)}catch(u){console.error("获取房间树形数据失败:",u),ElMessage.error("获取楼栋信息失败")}j.value=!1};function he(u){return"#409EFF"}function _e(){D.$refs.nursingAddRef.init()}function Ve(){_()}return _(),Be(()=>{ce()}),(u,o)=>{const g=m("el-icon"),Ne=m("el-tree"),v=m("el-col"),T=m("el-date-picker"),r=m("el-form-item"),s=m("el-input"),U=m("el-option"),L=m("el-select"),F=m("el-row"),K=m("el-form"),V=m("el-button"),p=m("el-table-column"),Ce=m("dict-tag"),we=m("View"),De=m("el-table"),ke=m("pagination"),Ie=m("el-dialog"),Te=Re("loading");return f(),w("div",Xe,[e(F,{gutter:20},{default:t(()=>[e(v,{span:4},{default:t(()=>[y("div",Ge,[We,e(Ne,{data:z.value,props:de,"node-key":"id","default-expand-all":"","highlight-current":"","default-expanded-keys":["1","1-1"],"current-key":"1-1-1",onNodeClick:pe},{default:t(({node:l,data:C})=>[y("div",Ze,[y("div",el,[e(g,{size:16,color:he(C),style:{"margin-right":"4px"}},{default:t(()=>[C.type==="building"?(f(),c(a(Me),{key:0})):C.type==="floor"?(f(),c(a(Oe),{key:1})):C.type==="room"?(f(),c(a(Fe),{key:2})):q("",!0)]),_:2},1032,["color"]),y("span",null,x(l.label),1)])])]),_:1},8,["data"])])]),_:1}),e(v,{span:20},{default:t(()=>[Y(e(K,{model:a(d),ref:"queryForm",inline:!0,"label-width":"100px"},{default:t(()=>[e(F,null,{default:t(()=>[e(v,{span:6},{default:t(()=>[e(r,{label:"交接班日期",prop:"handoverDate"},{default:t(()=>[e(T,{clearable:"",modelValue:a(d).handoverDate,"onUpdate:modelValue":o[0]||(o[0]=l=>a(d).handoverDate=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择交接班日期",style:{width:"200px"},value:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(r,{label:"白班交接人",prop:"dayNurse"},{default:t(()=>[e(s,{modelValue:a(d).dayNurse,"onUpdate:modelValue":o[1]||(o[1]=l=>a(d).dayNurse=l),placeholder:"请输入白班交接人",clearable:"",onKeyup:Q(N,["enter"]),style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(r,{label:"夜班交接人",prop:"nightNurse"},{default:t(()=>[e(s,{modelValue:a(d).nightNurse,"onUpdate:modelValue":o[2]||(o[2]=l=>a(d).nightNurse=l),placeholder:"请输入夜班交接人",clearable:"",onKeyup:Q(N,["enter"]),style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(r,{label:"状        态",prop:"status"},{default:t(()=>[e(L,{modelValue:a(d).status,"onUpdate:modelValue":o[3]||(o[3]=l=>a(d).status=l),clearable:"",style:{width:"200px"}},{default:t(()=>[(f(!0),w(H,null,S(a(A),l=>(f(),c(U,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(r,{label:"楼  栋 信 息",prop:"buildingName"},{default:t(()=>[e(L,{modelValue:a(d).buildingName,"onUpdate:modelValue":o[4]||(o[4]=l=>a(d).buildingName=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:re},{default:t(()=>[(f(!0),w(H,null,S(B.value,l=>(f(),c(U,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(r,{label:"楼 栋 层 数",prop:"buildingName"},{default:t(()=>[e(L,{modelValue:a(d).floorNumber,"onUpdate:modelValue":o[5]||(o[5]=l=>a(d).floorNumber=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:ie},{default:t(()=>[(f(!0),w(H,null,S(k.value,l=>(f(),c(U,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:6},{default:t(()=>[e(r,{label:"房   间    号",prop:"roomNumber"},{default:t(()=>[e(L,{modelValue:a(d).roomNumber,"onUpdate:modelValue":o[6]||(o[6]=l=>a(d).roomNumber=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:fe},{default:t(()=>[(f(!0),w(H,null,S(R.value,l=>(f(),c(U,{key:l.id,label:l.roomNumber,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),[[J,ee.value]]),e(F,{justify:"end",style:{"margin-bottom":"10px"}},{default:t(()=>[e(V,{type:"primary",icon:"Search",onClick:N},{default:t(()=>[h("搜索")]),_:1}),e(V,{icon:"Refresh",onClick:ge},{default:t(()=>[h("重置")]),_:1}),e(V,{type:"primary",plain:"",icon:"Plus",onClick:_e},{default:t(()=>[h("新增交接")]),_:1})]),_:1}),Y((f(),c(De,{border:"",stripe:"",data:P.value,onSelectionChange:be},{default:t(()=>[e(p,{label:"序号",type:"index",align:"center",width:"60"}),e(p,{label:"交接日期",align:"center",prop:"handoverDate",width:"140"},{default:t(l=>[y("span",null,x(u.parseTime(l.row.handoverDate,"{y}-{m}-{d}")),1)]),_:1}),e(p,{label:"房间号",align:"center",prop:"roomNumber",width:"100"}),e(p,{label:"楼层号",align:"center",prop:"floorNumber",width:"100"}),e(p,{label:"楼栋名称",align:"center",prop:"buildingName",width:"100"}),e(p,{label:"房间类型",align:"center",prop:"roomType",width:"100"}),e(p,{label:"白班护士",align:"center",prop:"dayNurse",width:"100"}),e(p,{label:"白班交接时间",align:"center",prop:"dayHandoverTime",width:"180"},{default:t(l=>[y("span",null,x(u.parseTime(l.row.dayHandoverTime,"{y}-{m}-{d}")),1)]),_:1}),e(p,{label:"夜班护士",align:"center",prop:"nightNurse"}),e(p,{label:"夜班交接时间",align:"center",prop:"nightHandoverTime",width:"180"},{default:t(l=>[y("span",null,x(u.parseTime(l.row.nightHandoverTime,"{y}-{m}-{d}")),1)]),_:1}),e(p,{label:"白班交接总人数",align:"center",prop:"dayTotalCount",width:"150"}),e(p,{label:"状态",align:"center",prop:"status"},{default:t(l=>[e(Ce,{options:a(A),value:l.row.status},null,8,["options","value"])]),_:1}),q("",!0),e(p,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},{default:t(l=>[e(V,{type:"primary",link:"",onClick:C=>ve(l.row)},{default:t(()=>[e(g,null,{default:t(()=>[e(we)]),_:1}),h("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Te,M.value]]),Y(e(ke,{limit:a(d).pageSize,"onUpdate:limit":o[7]||(o[7]=l=>a(d).pageSize=l),page:a(d).pageNum,"onUpdate:page":o[8]||(o[8]=l=>a(d).pageNum=l),total:O.value,onPagination:_},null,8,["limit","page","total"]),[[J,O.value>0]])]),_:1})]),_:1}),e(Ie,{title:le.value,visible:I.value,width:"500px","append-to-body":""},{default:t(()=>[e(K,{ref_key:"form",ref:n,model:a(n),rules:a(ue),"label-width":"80px"},{default:t(()=>[e(r,{label:"交接日期",prop:"handoverDate"},{default:t(()=>[e(T,{clearable:"",modelValue:a(n).handoverDate,"onUpdate:modelValue":o[9]||(o[9]=l=>a(n).handoverDate=l),type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择交接日期"},null,8,["modelValue"])]),_:1}),e(r,{label:"楼层ID",prop:"floorId"},{default:t(()=>[e(s,{modelValue:a(n).floorId,"onUpdate:modelValue":o[10]||(o[10]=l=>a(n).floorId=l),placeholder:"请输入楼层ID"},null,8,["modelValue"])]),_:1}),e(r,{label:"楼层号",prop:"floorNumber"},{default:t(()=>[e(s,{modelValue:a(n).floorNumber,"onUpdate:modelValue":o[11]||(o[11]=l=>a(n).floorNumber=l),placeholder:"请输入楼层号"},null,8,["modelValue"])]),_:1}),e(r,{label:"楼栋ID",prop:"buildingId"},{default:t(()=>[e(s,{modelValue:a(n).buildingId,"onUpdate:modelValue":o[12]||(o[12]=l=>a(n).buildingId=l),placeholder:"请输入楼栋ID"},null,8,["modelValue"])]),_:1}),e(r,{label:"楼栋名称",prop:"buildingName"},{default:t(()=>[e(s,{modelValue:a(n).buildingName,"onUpdate:modelValue":o[13]||(o[13]=l=>a(n).buildingName=l),placeholder:"请输入楼栋名称"},null,8,["modelValue"])]),_:1}),e(r,{label:"房间ID",prop:"roomId"},{default:t(()=>[e(s,{modelValue:a(n).roomId,"onUpdate:modelValue":o[14]||(o[14]=l=>a(n).roomId=l),placeholder:"请输入房间ID"},null,8,["modelValue"])]),_:1}),e(r,{label:"房间号",prop:"roomNumber"},{default:t(()=>[e(s,{modelValue:a(n).roomNumber,"onUpdate:modelValue":o[15]||(o[15]=l=>a(n).roomNumber=l),placeholder:"请输入房间号"},null,8,["modelValue"])]),_:1}),e(r,{label:"白班护士",prop:"dayNurse"},{default:t(()=>[e(s,{modelValue:a(n).dayNurse,"onUpdate:modelValue":o[16]||(o[16]=l=>a(n).dayNurse=l),placeholder:"请输入白班护士"},null,8,["modelValue"])]),_:1}),e(r,{label:"白班交接时间",prop:"dayHandoverTime"},{default:t(()=>[e(T,{clearable:"",modelValue:a(n).dayHandoverTime,"onUpdate:modelValue":o[17]||(o[17]=l=>a(n).dayHandoverTime=l),type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择白班交接时间"},null,8,["modelValue"])]),_:1}),e(r,{label:"夜班护士",prop:"nightNurse"},{default:t(()=>[e(s,{modelValue:a(n).nightNurse,"onUpdate:modelValue":o[18]||(o[18]=l=>a(n).nightNurse=l),placeholder:"请输入夜班护士"},null,8,["modelValue"])]),_:1}),e(r,{label:"夜班交接时间",prop:"nightHandoverTime"},{default:t(()=>[e(T,{clearable:"",modelValue:a(n).nightHandoverTime,"onUpdate:modelValue":o[19]||(o[19]=l=>a(n).nightHandoverTime=l),type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择夜班交接时间"},null,8,["modelValue"])]),_:1}),e(r,{label:"白班交接总人数",prop:"dayTotalCount"},{default:t(()=>[e(s,{modelValue:a(n).dayTotalCount,"onUpdate:modelValue":o[20]||(o[20]=l=>a(n).dayTotalCount=l),placeholder:"请输入白班交接总人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"白班外出人数",prop:"dayOutCount"},{default:t(()=>[e(s,{modelValue:a(n).dayOutCount,"onUpdate:modelValue":o[21]||(o[21]=l=>a(n).dayOutCount=l),placeholder:"请输入白班外出人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"白班离院人数",prop:"dayLeaveCount"},{default:t(()=>[e(s,{modelValue:a(n).dayLeaveCount,"onUpdate:modelValue":o[22]||(o[22]=l=>a(n).dayLeaveCount=l),placeholder:"请输入白班离院人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"白班死亡人数",prop:"dayDeathCount"},{default:t(()=>[e(s,{modelValue:a(n).dayDeathCount,"onUpdate:modelValue":o[23]||(o[23]=l=>a(n).dayDeathCount=l),placeholder:"请输入白班死亡人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"夜班交接总人数",prop:"nightTotalCount"},{default:t(()=>[e(s,{modelValue:a(n).nightTotalCount,"onUpdate:modelValue":o[24]||(o[24]=l=>a(n).nightTotalCount=l),placeholder:"请输入夜班交接总人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"夜班外出人数",prop:"nightOutCount"},{default:t(()=>[e(s,{modelValue:a(n).nightOutCount,"onUpdate:modelValue":o[25]||(o[25]=l=>a(n).nightOutCount=l),placeholder:"请输入夜班外出人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"夜班离院人数",prop:"nightLeaveCount"},{default:t(()=>[e(s,{modelValue:a(n).nightLeaveCount,"onUpdate:modelValue":o[26]||(o[26]=l=>a(n).nightLeaveCount=l),placeholder:"请输入夜班离院人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"夜班死亡人数",prop:"nightDeathCount"},{default:t(()=>[e(s,{modelValue:a(n).nightDeathCount,"onUpdate:modelValue":o[27]||(o[27]=l=>a(n).nightDeathCount=l),placeholder:"请输入夜班死亡人数"},null,8,["modelValue"])]),_:1}),e(r,{label:"备注",prop:"remark"},{default:t(()=>[e(s,{modelValue:a(n).remark,"onUpdate:modelValue":o[28]||(o[28]=l=>a(n).remark=l),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),e(V,{type:"primary",onClick:ye},{default:t(()=>[h("确 定")]),_:1}),e(V,{onClick:se},{default:t(()=>[h("取 消")]),_:1})]),_:1},8,["title","visible"]),e(Ae,{ref_key:"nursingAddRef",ref:ae,isShow:u.add,onCloseEvent:Ve},null,8,["isShow"]),e(Ee,{ref_key:"nursingDetailRef",ref:te,detailId:E.value},null,8,["detailId"]),h('" ')])}}}),gl=Ue(ol,[["__scopeId","data-v-91942b74"]]);export{gl as default};
