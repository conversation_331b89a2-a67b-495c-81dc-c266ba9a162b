import{g as <PERSON><PERSON>,a as Ae}from"./roommanage-DBG5TiIR.js";import{g as Be,a as Ee,b as <PERSON>,c as He}from"./index-2bfkpdNb.js";import{_ as je,H as w,bl as qe,d as Je,r as d,z as _e,w as ye,F as Ke,bm as Qe,b1 as Ge,e as R,I as Xe,c as t,o as l,i as e,K as h,L as k,Q as f,l as o,t as r,f as _,h as y,n as te,j as K,D as X,au as oe,bn as be,J as Ze,P as pe,v as Se,x as ea}from"./index-B0qHf98Y.js";import{d as aa}from"./index-e0lvOvDC.js";const ne=le=>(Se("data-v-fe1867fe"),le=le(),ea(),le),sa={class:"medication-screen"},la={class:"toolbar-2row"},ta={class:"toolbar-row toolbar-row-1"},oa={class:"toolbar-left"},na={class:"view-tabs"},ca=["onClick"],ia={class:"toolbar-center"},ra={class:"date-navigation"},da={class:"quick-nav"},ua={class:"date-display"},va={class:"date-picker"},_a={class:"toolbar-right"},pa={class:"time-buttons"},ga={class:"toolbar-row toolbar-row-2"},ma={class:"toolbar-left"},ha={class:"toolbar-right"},ka={class:"statistics"},fa={class:"stat-item morning"},ya={class:"stat-item noon"},ba={class:"stat-item evening"},wa={class:"content"},Da={class:"elder-cards-container"},Ya={class:"elder-header"},Ma={class:"elder-info"},Na={class:"name"},Va={class:"room"},Ra={class:"room_info"},Ca={class:"medication-times"},Oa={class:"medication-list-wrapper"},Ta={class:"med-name"},La={class:"med-right"},Pa={class:"med-dose"},$a={key:1,class:"no-medication"},Wa={key:1},xa={class:"medication-list-wrapper"},za={key:0},Ia={class:"med-name"},Ua={class:"med-right"},Aa={class:"med-dose"},Ba={key:1,class:"no-medication"},Ea={key:0,class:"loading-indicator"},Fa=ne(()=>e("span",null,"正在加载更多...",-1)),Ha={key:1,class:"load-tip"},ja={key:1,class:"load-complete-tip"},qa={key:2,class:"no-data-tip"},Ja=ne(()=>e("span",null,"暂无数据",-1)),Ka={class:"elder-cell"},Qa={class:"room_info"},Ga={class:"elder-details"},Xa={class:"name"},Za={class:"room"},Sa={key:0,class:"day-cell"},es={class:"medication-list-wrapper"},as={class:"med-name"},ss={class:"med-right"},ls={class:"med-dose"},ts={key:1,class:"no-medication"},os={key:1,class:"no-data"},ns={key:1,class:"day-cell"},cs={class:"medication-list-wrapper"},is={class:"med-name"},rs={class:"med-right"},ds={class:"med-dose"},us={key:1,class:"no-medication"},vs={key:1,class:"no-data"},_s={key:0,class:"loading-indicator"},ps=ne(()=>e("span",null,"正在加载更多...",-1)),gs={key:1,class:"load-tip"},ms={key:1,class:"load-complete-tip"},hs={key:2,class:"no-data-tip"},ks=ne(()=>e("span",null,"暂无数据",-1)),we=10,fs={__name:"index",setup(le){w.extend(qe);const{proxy:ge}=Je();function B(n,a="YYYY-MM-DD"){return n?w(n).format(a):""}const m=d("day"),C=d(B(new Date)),b=d(B(new Date)),O=d(!1),D=d(""),$=d(""),W=d(""),L=d(""),x=d(""),E=d(1),Q=d(0),T=d(!0),De=d(null),Ye=d(null),F=d(null),H=d(null),Me=d([{label:"日视图",value:"day"},{label:"周视图",value:"week"}]),Z=d([{label:"早上",value:"morning",color:"rgb(112, 182, 3)"},{label:"中午",value:"noon",color:"rgb(99, 0, 191)"},{label:"晚上",value:"evening",color:"rgb(245, 154, 35)"}]),z=d([]),I=d([]),ce=d([]),me=d([]),ie=d(0),re=d(0),de=d(0),Ne=()=>{$.value="",W.value="",L.value="",x.value=""},Ve=async()=>{try{const n=await Ue();me.value=n.rows||[]}catch(n){console.error("获取楼栋列表失败:",n)}},Re=async n=>{ce.value=[],L.value="";try{const a=await Ae(n);ce.value=a.rows||[]}catch(a){console.error("获取楼层列表失败:",a)}},he=async()=>{var n,a,v,p,U,q,J,ee;O.value=!0;try{if(m.value==="day"){const P={pageNum:E.value,pageSize:we,medicationDatePlan:C.value,elderName:$.value,roomNumber:W.value,floorId:L.value,buildingId:x.value,timePeriod:D.value},N=await Be(P);N.code===200?E.value===1?z.value=N.rows||[]:z.value.push(...N.rows||[]):z.value=[],Q.value=N.total||0,T.value=Q.value>0&&((n=z.value)==null?void 0:n.length)<Q.value;const A=await Ee({medicationDatePlan:C.value,elderName:$.value,roomNumber:W.value,floorId:L.value,buildingId:x.value,timePeriod:D.value});ie.value=((a=A.data)==null?void 0:a.morning)||0,re.value=((v=A.data)==null?void 0:v.noon)||0,de.value=((p=A.data)==null?void 0:p.evening)||0}else{const P={pageNum:E.value,pageSize:we,elderName:$.value,roomNumber:W.value,floorId:L.value,buildingId:x.value,timePeriod:D.value},N=w(b.value).startOf("week").format("YYYY-MM-DD"),A=w(b.value).endOf("week").format("YYYY-MM-DD"),ae=[N,A],G=await Fe(ge.addDateRange(P,ae,"MedicationDatePlan"));G.code===200?E.value===1?I.value=G.rows||[]:I.value.push(...G.rows||[]):I.value=[];const se=await He(ge.addDateRange(P,ae,"MedicationDatePlan"));ie.value=((U=se.data)==null?void 0:U.morning)||0,re.value=((q=se.data)==null?void 0:q.noon)||0,de.value=((J=se.data)==null?void 0:J.evening)||0,Q.value=G.total||0,T.value=Q.value>0&&((ee=I.value)==null?void 0:ee.length)<Q.value}}catch(P){console.error("获取用药数据失败:",P),E.value===1&&(z.value=[],I.value=[]),S()}finally{O.value=!1,pe(()=>{fe()})}},Ce=_e(()=>B(C.value,"YYYY年MM月DD日")),Oe=_e(()=>{try{const n=w(b.value).startOf("week"),a=w(b.value).endOf("week"),v=n.year(),p=n.week();return`${v}年第${p}周 (${n.format("MM/DD")} - ${a.format("MM/DD")})`}catch(n){return console.error("计算周范围时出错:",n),"日期错误"}}),Te=_e(()=>{const n=[];try{const a=w(b.value).startOf("week");for(let v=0;v<7;v++){const p=a.add(v,"day"),U=p.format("YYYY-MM-DD"),q="日一二三四五六"[p.day()],J=`${p.format("MM-DD")} 周${q}`;n.push({date:U,label:J,isToday:Le(U)})}}catch(a){console.error("生成周日期时出错:",a)}return n}),Le=n=>n===B(new Date),ue=async()=>{!T.value||O.value||(E.value+=1,await he())};let Y=null,M=null;const ke=()=>{const n={root:null,rootMargin:"100px",threshold:.1},a=v=>{v.forEach(p=>{console.log("观察器触发:",p.isIntersecting,"hasMore:",T.value,"loading:",O.value),p.isIntersecting&&T.value&&!O.value&&ue()})};Y&&Y.disconnect(),M&&M.disconnect(),Y=new IntersectionObserver(a,n),M=new IntersectionObserver(a,n),pe(()=>{F.value&&Y.observe(F.value),H.value&&M.observe(H.value)})},fe=()=>{pe(()=>{S(),m.value==="day"&&F.value?Y==null||Y.observe(F.value):m.value==="week"&&H.value&&(M==null||M.observe(H.value))})},S=()=>{Y&&F.value&&Y.unobserve(F.value),M&&H.value&&M.unobserve(H.value)},Pe=n=>{const{scrollTop:a,scrollHeight:v,clientHeight:p}=n.target;console.log("日视图滚动位置:",{scrollTop:a,scrollHeight:v,clientHeight:p}),v-a-p<50&&T.value&&!O.value&&(console.log("触发日视图加载更多"),ue())},$e=n=>{const{scrollTop:a,scrollHeight:v,clientHeight:p}=n.target;v-a-p<50&&T.value&&!O.value&&ue()},We=n=>{D.value=D.value===n?"":n,j()},xe=n=>{C.value=n||B(new Date),j()},ze=n=>{b.value=n||B(new Date),j()},ve=n=>{if(m.value==="day"){let a=w(C.value||new Date);switch(n){case"prev":a=a.subtract(1,"day");break;case"current":a=w();break;case"next":a=a.add(1,"day");break}C.value=a.format("YYYY-MM-DD")}else{let a=w(b.value||new Date).startOf("week");switch(n){case"prev":a=a.subtract(1,"week");break;case"current":a=w().startOf("week");break;case"next":a=a.add(1,"week");break}b.value=a.format("YYYY-MM-DD")}j()},j=()=>{E.value=1,T.value=!0,m.value==="day"?z.value=[]:I.value=[],S(),he()},Ie=()=>{C.value=B(new Date);const n=w().startOf("week");b.value=n.format("YYYY-MM-DD")};return ye(m,()=>{S(),j(),setTimeout(fe,100)}),ye([$,W,L,x],aa(()=>{j()},500),{deep:!0}),Ke(()=>{Ve(),Ie(),ke(),j()}),Qe(()=>{ke()}),Ge(()=>{S(),Y&&Y.disconnect(),M&&M.disconnect()}),(n,a)=>{const v=R("el-button"),p=R("el-button-group"),U=R("el-date-picker"),q=R("el-option"),J=R("el-select"),ee=R("el-input"),P=R("el-avatar"),N=R("el-icon"),A=R("DocumentRemove"),ae=R("el-table-column"),G=R("el-table"),se=Xe("loading");return l(),t("div",sa,[e("div",la,[e("div",ta,[e("div",oa,[e("div",na,[(l(!0),t(h,null,k(o(Me),s=>(l(),t("button",{key:s.value,class:f(["tab-btn",{active:o(m)===s.value}]),onClick:g=>{m.value=s.value,D.value="",Ne()}},r(s.label),11,ca))),128))])]),e("div",ia,[e("div",ra,[e("div",da,[_(p,null,{default:y(()=>[_(v,{onClick:a[0]||(a[0]=s=>ve("prev")),icon:"ArrowLeft"},{default:y(()=>[te("上一"+r(o(m)==="day"?"天":"周"),1)]),_:1}),_(v,{onClick:a[1]||(a[1]=s=>ve("current"))},{default:y(()=>[te("今"+r(o(m)==="day"?"天":"周"),1)]),_:1}),_(v,{onClick:a[2]||(a[2]=s=>ve("next")),icon:"ArrowRight"},{default:y(()=>[te("下一"+r(o(m)==="day"?"天":"周"),1)]),_:1})]),_:1})]),e("div",ua,r(o(m)==="day"?o(Ce):o(Oe)),1),e("div",va,[o(m)==="day"?(l(),K(U,{key:0,modelValue:o(C),"onUpdate:modelValue":a[3]||(a[3]=s=>X(C)?C.value=s:null),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",onChange:xe,clearable:!1},null,8,["modelValue"])):(l(),K(U,{key:1,modelValue:o(b),"onUpdate:modelValue":a[4]||(a[4]=s=>X(b)?b.value=s:null),type:"week",format:"YYYY年第ww周","value-format":"YYYY-MM-DD",placeholder:"选择周",onChange:ze,clearable:!1},null,8,["modelValue"]))])])]),e("div",_a,[e("div",pa,[(l(!0),t(h,null,k(o(Z),s=>(l(),K(v,{key:s.value,class:f(["time-btn",s.value,{active:o(D)===s.value}]),onClick:g=>We(s.value),plain:""},{default:y(()=>[te(r(s.label),1)]),_:2},1032,["class","onClick"]))),128))])])]),e("div",ga,[e("div",ma,[_(J,{modelValue:o(x),"onUpdate:modelValue":a[5]||(a[5]=s=>X(x)?x.value=s:null),placeholder:"选择楼栋",clearable:"",onChange:Re},{default:y(()=>[(l(!0),t(h,null,k(o(me),s=>(l(),K(q,{key:s.value,label:s.buildingName,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_(J,{modelValue:o(L),"onUpdate:modelValue":a[6]||(a[6]=s=>X(L)?L.value=s:null),placeholder:"选择楼层",clearable:""},{default:y(()=>[(l(!0),t(h,null,k(o(ce),s=>(l(),K(q,{key:s.value,label:s.floorName,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_(ee,{modelValue:o(W),"onUpdate:modelValue":a[7]||(a[7]=s=>X(W)?W.value=s:null),placeholder:"搜索房间号",style:{width:"120px"},clearable:""},null,8,["modelValue"]),_(ee,{modelValue:o($),"onUpdate:modelValue":a[8]||(a[8]=s=>X($)?$.value=s:null),placeholder:"搜索老人姓名",style:{width:"120px"},clearable:""},null,8,["modelValue"])]),e("div",ha,[e("div",ka,[e("span",fa,"早上: "+r(o(ie))+"人",1),e("span",ya,"中午: "+r(o(re))+"人",1),e("span",ba,"晚上: "+r(o(de))+"人",1)])])])]),e("div",wa,[o(m)==="day"?(l(),t("div",{key:0,class:"day-view",ref_key:"dayViewContainer",ref:Ye,onScroll:Pe},[e("div",Da,[(l(!0),t(h,null,k(o(z),(s,g)=>(l(),t("div",{key:g,class:"elder-card"},[e("div",Ya,[_(P,{src:s.avatar,class:"elder-avatar",size:60},null,8,["src"]),e("div",Ma,[e("div",Na,r(s.elderName),1),e("div",Va,r(s.roomNumber)+" - "+r(s.bedNumber),1)]),e("div",Ra,r(s.roomNumber),1)]),e("div",Ca,[o(D)?(l(!0),t(h,{key:0},k(o(Z).filter(c=>c.value===o(D)),c=>{var i,u;return l(),t("div",{key:c.value,class:"time-slot"},[e("div",{class:f(["time-label",{activeMorning:c.value==="morning",activeAfternoon:c.value==="noon",activeEvening:c.value==="evening"}]),style:oe({color:c.color})},r(c.label),7),e("div",Oa,[e("div",{class:f(["medication-list",{scrollable:((i=s.dailyRecords[c.value])==null?void 0:i.length)>4}])},[((u=s.dailyRecords[c.value])==null?void 0:u.length)>0?(l(!0),t(h,{key:0},k(s.dailyRecords[c.value],V=>(l(),t("div",{key:V.id,class:"medication-item"},[e("span",Ta,r(V.medicineName),1),e("div",La,[e("span",Pa,r(V.dosage),1),e("span",{class:f(["med-status",{taken:V.status=="1","not-taken":V.status=="0","part-taken":V.status=="2"}])},null,2)])]))),128)):(l(),t("div",$a,"暂无用药"))],2)])])}),128)):(l(),t("div",Wa,[(l(!0),t(h,null,k(o(Z),c=>{var i;return l(),t("div",{key:c.value,class:"time-slot"},[e("div",{class:f(["time-label",{activeMorning:c.value==="morning",activeAfternoon:c.value==="noon",activeEvening:c.value==="evening"}]),style:oe({color:c.color})},r(c.label),7),e("div",xa,[e("div",{class:f(["medication-list",{scrollable:((i=s.dailyRecords[c.value])==null?void 0:i.length)>4}])},[s.dailyRecords[c.value]&&s.dailyRecords[c.value].length>0?(l(),t("div",za,[(l(!0),t(h,null,k(s.dailyRecords[c.value],(u,V)=>(l(),t("div",{key:u.id,class:"medication-item"},[e("span",Ia,r(u.medicineName),1),e("div",Ua,[e("span",Aa,r(u.dosage),1),e("span",{class:f(["med-status",{taken:u.status=="1","not-taken":u.status=="0","part-taken":u.status=="2"}])},null,2)])]))),128))])):(l(),t("div",Ba,"暂无用药"))],2)])])}),128))]))])]))),128))]),o(T)?(l(),t("div",{key:0,class:"scroll-load-tip",ref_key:"dayLoadTrigger",ref:F},[o(O)?(l(),t("div",Ea,[_(N,{class:"is-loading"},{default:y(()=>[_(o(be))]),_:1}),Fa])):(l(),t("div",Ha," 滚动到底部加载更多 "))],512)):o(z).length>0?(l(),t("div",ja," 已显示全部 ")):(l(),t("div",qa,[_(N,null,{default:y(()=>[_(A)]),_:1}),Ja]))],544)):(l(),t("div",{key:1,class:"week-view",ref_key:"weekViewContainer",ref:De,onScroll:$e},[Ze((l(),K(G,{data:o(I),style:{width:"100%"},"element-loading-text":"加载中...",border:"",ref:"weekTable"},{default:y(()=>[_(ae,{prop:"name",label:"老人信息",width:"180",fixed:"",align:"center"},{default:y(({row:s})=>[e("div",Ka,[e("div",Qa,r(s.roomNumber),1),_(P,{src:s.avatar,size:60},null,8,["src"]),e("div",Ga,[e("div",Xa,r(s.elderName),1),e("div",Za,r(s.roomNumber)+" - "+r(s.bedNumber),1)])])]),_:1}),(l(!0),t(h,null,k(o(Te),(s,g)=>(l(),K(ae,{key:s.date,label:s.label,"min-width":"180","header-align":"center"},{default:y(({row:c})=>[o(D)?(l(),t("div",Sa,[c.dailyRecords&&c.dailyRecords[g]?(l(!0),t(h,{key:0},k(o(Z).filter(i=>i.value===o(D)),i=>(l(),t("div",{key:i.value,class:"week-time-slot"},[e("div",{class:f(["time-label",{activeMorning:i.value==="morning",activeAfternoon:i.value==="noon",activeEvening:i.value==="evening"}]),style:oe({color:i.color})},r(i.label),7),e("div",es,[e("div",{class:f(["medication-list",{scrollable:c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>4}])},[c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>0?(l(!0),t(h,{key:0},k(c.dailyRecords[g][i.value],(u,V)=>(l(),t("div",{key:V,class:"medication-item"},[e("span",as,r(u.medicineName),1),e("div",ss,[e("span",ls,r(u.dosage),1),e("span",{class:f(["med-status",{taken:u.status=="1","not-taken":u.status=="0","part-taken":u.status=="2"}])},null,2)])]))),128)):(l(),t("div",ts,"暂无用药"))],2)])]))),128)):(l(),t("div",os,"无记录"))])):(l(),t("div",ns,[c.dailyRecords&&c.dailyRecords[g]?(l(!0),t(h,{key:0},k(o(Z),i=>(l(),t("div",{key:i.value,class:"week-time-slot"},[e("div",{class:f(["time-label",{activeMorning:i.value==="morning",activeAfternoon:i.value==="noon",activeEvening:i.value==="evening"}]),style:oe({color:i.color})},r(i.label),7),e("div",cs,[e("div",{class:f(["medication-list",{scrollable:c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>4}])},[c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>0?(l(!0),t(h,{key:0},k(c.dailyRecords[g][i.value],(u,V)=>(l(),t("div",{key:V,class:"medication-item"},[e("span",is,r(u.medicineName),1),e("div",rs,[e("span",ds,r(u.dosage),1),e("span",{class:f(["med-status",{taken:u.status=="1","not-taken":u.status=="0","part-taken":u.status=="2"}])},null,2)])]))),128)):(l(),t("div",us,"暂无用药"))],2)])]))),128)):(l(),t("div",vs,"无记录"))]))]),_:2},1032,["label"]))),128))]),_:1},8,["data"])),[[se,o(O)]]),o(T)?(l(),t("div",{key:0,class:"scroll-load-tip",ref_key:"weekLoadTrigger",ref:H},[o(O)?(l(),t("div",_s,[_(N,{class:"is-loading"},{default:y(()=>[_(o(be))]),_:1}),ps])):(l(),t("div",gs," 滚动到底部加载更多 "))],512)):o(I).length>0?(l(),t("div",ms," 已显示全部 ")):(l(),t("div",hs,[_(N,null,{default:y(()=>[_(A)]),_:1}),ks]))],544))])])}}},Ys=je(fs,[["__scopeId","data-v-fe1867fe"]]);export{Ys as default};
