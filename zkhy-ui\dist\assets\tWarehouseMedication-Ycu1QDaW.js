import{X as t}from"./index-B0qHf98Y.js";function o(e){return t({url:"/warehouse/medication/list",method:"get",params:e})}function i(e){return t({url:"/warehouse/medication/"+e,method:"get"})}function n(e){return t({url:"/warehouse/medication",method:"post",data:e})}function r(e){return t({url:"/warehouse/medication",method:"put",data:e})}function d(e){return t({url:"/warehouse/medication/"+e,method:"delete"})}function u(e){return t({url:"/warehouse/medication/getNewCode",method:"get",params:e})}export{n as a,i as b,d,u as g,o as l,r as u};
