import{g as ze,a as Ie}from"./roommanage-DBG5TiIR.js";import{H as Ue,d as Ae,e as Be,f as Ee}from"./index-2bfkpdNb.js";import{_ as je,H as w,bl as qe,d as Je,r as d,z as _e,w as fe,F as Ke,bm as Qe,b1 as Ge,e as R,I as Xe,c as t,o as l,i as e,K as h,L as k,Q as y,l as o,t as r,f as _,h as f,n as te,j as K,D as X,au as oe,bn as be,J as Ze,P as pe,v as Se,x as ea}from"./index-B0qHf98Y.js";import{d as aa}from"./index-e0lvOvDC.js";const ne=le=>(Se("data-v-e2113b48"),le=le(),ea(),le),sa={class:"medication-screen"},la={class:"toolbar-2row"},ta={class:"toolbar-row toolbar-row-1"},oa={class:"toolbar-left"},na={class:"view-tabs"},ca=["onClick"],ia={class:"toolbar-center"},ra={class:"date-navigation"},da={class:"quick-nav"},ua={class:"date-display"},va={class:"date-picker"},_a={class:"toolbar-right"},pa={class:"time-buttons"},ga={class:"toolbar-row toolbar-row-2"},ma={class:"toolbar-left"},ha={class:"toolbar-right"},ka={class:"statistics"},ya={class:"stat-item morning"},fa={class:"stat-item noon"},ba={class:"stat-item evening"},wa={class:"content"},Da={class:"elder-cards-container"},Ya={class:"elder-header"},Ma={class:"elder-info"},Na={class:"name"},Va={class:"room"},Ra={class:"room_info"},Ca={class:"medication-times"},Oa={class:"medication-list-wrapper"},Ta={class:"med-name"},La={class:"med-right"},Pa={class:"med-dose"},Fa={key:1,class:"no-medication"},$a={key:1},Ha={class:"medication-list-wrapper"},Wa={key:0},xa={class:"med-name"},za={class:"med-right"},Ia={class:"med-dose"},Ua={key:1,class:"no-medication"},Aa={key:0,class:"loading-indicator"},Ba=ne(()=>e("span",null,"正在加载更多...",-1)),Ea={key:1,class:"load-tip"},ja={key:1,class:"load-complete-tip"},qa={key:2,class:"no-data-tip"},Ja=ne(()=>e("span",null,"暂无数据",-1)),Ka={class:"elder-cell"},Qa={class:"room_info"},Ga={class:"elder-details"},Xa={class:"name"},Za={class:"room"},Sa={key:0,class:"day-cell"},es={class:"medication-list-wrapper"},as={class:"med-name"},ss={class:"med-right"},ls={class:"med-dose"},ts={key:1,class:"no-medication"},os={key:1,class:"no-data"},ns={key:1,class:"day-cell"},cs={class:"medication-list-wrapper"},is={class:"med-name"},rs={class:"med-right"},ds={class:"med-dose"},us={key:1,class:"no-medication"},vs={key:1,class:"no-data"},_s={key:0,class:"loading-indicator"},ps=ne(()=>e("span",null,"正在加载更多...",-1)),gs={key:1,class:"load-tip"},ms={key:1,class:"load-complete-tip"},hs={key:2,class:"no-data-tip"},ks=ne(()=>e("span",null,"暂无数据",-1)),we=10,ys={__name:"index",setup(le){w.extend(qe);const{proxy:ge}=Je();function U(n,a="YYYY-MM-DD"){return n?w(n).format(a):""}const m=d("day"),C=d(U(new Date)),b=d(U(new Date)),O=d(!1),D=d(""),F=d(""),$=d(""),L=d(""),H=d(""),A=d(1),Q=d(0),T=d(!0),De=d(null),Ye=d(null),B=d(null),E=d(null),Me=d([{label:"日视图",value:"day"},{label:"周视图",value:"week"}]),Z=d([{label:"早上",value:"morning",color:"rgb(112, 182, 3)"},{label:"中午",value:"noon",color:"rgb(99, 0, 191)"},{label:"晚上",value:"evening",color:"rgb(245, 154, 35)"}]),W=d([]),x=d([]),ce=d([]),me=d([]),ie=d(0),re=d(0),de=d(0),Ne=()=>{F.value="",$.value="",L.value="",H.value=""},Ve=async()=>{try{const n=await ze();me.value=n.rows||[]}catch(n){console.error("获取楼栋列表失败:",n)}},Re=async n=>{ce.value=[],L.value="";try{const a=await Ie(n);ce.value=a.rows||[]}catch(a){console.error("获取楼层列表失败:",a)}},he=async()=>{var n,a,v,p,z,q,J,ee;O.value=!0;try{if(m.value==="day"){const P={pageNum:A.value,pageSize:we,medicationDatePlan:C.value,elderName:F.value,roomNumber:$.value,floorId:L.value,buildingId:H.value,timePeriod:D.value},N=await Ue(P);N.code===200?A.value===1?W.value=N.rows||[]:W.value.push(...N.rows||[]):W.value=[],Q.value=N.total||0,T.value=Q.value>0&&((n=W.value)==null?void 0:n.length)<Q.value;const I=await Ae({medicationDatePlan:C.value,elderName:F.value,roomNumber:$.value,floorId:L.value,buildingId:H.value,timePeriod:D.value});ie.value=((a=I.data)==null?void 0:a.morning)||0,re.value=((v=I.data)==null?void 0:v.noon)||0,de.value=((p=I.data)==null?void 0:p.evening)||0}else{const P={pageNum:A.value,pageSize:we,elderName:F.value,roomNumber:$.value,floorId:L.value,buildingId:H.value,timePeriod:D.value},N=w(b.value).startOf("week").format("YYYY-MM-DD"),I=w(b.value).endOf("week").format("YYYY-MM-DD"),ae=[N,I],G=await Be(ge.addDateRange(P,ae,"MedicationDatePlan"));G.code===200?A.value===1?x.value=G.rows||[]:x.value.push(...G.rows||[]):x.value=[];const se=await Ee(ge.addDateRange(P,ae,"MedicationDatePlan"));ie.value=((z=se.data)==null?void 0:z.morning)||0,re.value=((q=se.data)==null?void 0:q.noon)||0,de.value=((J=se.data)==null?void 0:J.evening)||0,Q.value=G.total||0,T.value=Q.value>0&&((ee=x.value)==null?void 0:ee.length)<Q.value}}catch(P){console.error("获取用药数据失败:",P),A.value===1&&(W.value=[],x.value=[]),S()}finally{O.value=!1,pe(()=>{ye()})}},Ce=_e(()=>U(C.value,"YYYY年MM月DD日")),Oe=_e(()=>{try{const n=w(b.value).startOf("week"),a=w(b.value).endOf("week"),v=n.year(),p=n.week();return`${v}年第${p}周 (${n.format("MM/DD")} - ${a.format("MM/DD")})`}catch(n){return console.error("计算周范围时出错:",n),"日期错误"}}),Te=_e(()=>{const n=[];try{const a=w(b.value).startOf("week");for(let v=0;v<7;v++){const p=a.add(v,"day"),z=p.format("YYYY-MM-DD"),q="日一二三四五六"[p.day()],J=`${p.format("MM-DD")} 周${q}`;n.push({date:z,label:J,isToday:Le(z)})}}catch(a){console.error("生成周日期时出错:",a)}return n}),Le=n=>n===U(new Date),ue=async()=>{!T.value||O.value||(A.value+=1,await he())};let Y=null,M=null;const ke=()=>{const n={root:null,rootMargin:"100px",threshold:.1},a=v=>{v.forEach(p=>{console.log("观察器触发:",p.isIntersecting,"hasMore:",T.value,"loading:",O.value),p.isIntersecting&&T.value&&!O.value&&ue()})};Y&&Y.disconnect(),M&&M.disconnect(),Y=new IntersectionObserver(a,n),M=new IntersectionObserver(a,n),pe(()=>{B.value&&Y.observe(B.value),E.value&&M.observe(E.value)})},ye=()=>{pe(()=>{S(),m.value==="day"&&B.value?Y==null||Y.observe(B.value):m.value==="week"&&E.value&&(M==null||M.observe(E.value))})},S=()=>{Y&&B.value&&Y.unobserve(B.value),M&&E.value&&M.unobserve(E.value)},Pe=n=>{const{scrollTop:a,scrollHeight:v,clientHeight:p}=n.target;console.log("日视图滚动位置:",{scrollTop:a,scrollHeight:v,clientHeight:p}),v-a-p<50&&T.value&&!O.value&&(console.log("触发日视图加载更多"),ue())},Fe=n=>{const{scrollTop:a,scrollHeight:v,clientHeight:p}=n.target;v-a-p<50&&T.value&&!O.value&&ue()},$e=n=>{D.value=D.value===n?"":n,j()},He=n=>{C.value=n||U(new Date),j()},We=n=>{b.value=n||U(new Date),j()},ve=n=>{if(m.value==="day"){let a=w(C.value||new Date);switch(n){case"prev":a=a.subtract(1,"day");break;case"current":a=w();break;case"next":a=a.add(1,"day");break}C.value=a.format("YYYY-MM-DD")}else{let a=w(b.value||new Date).startOf("week");switch(n){case"prev":a=a.subtract(1,"week");break;case"current":a=w().startOf("week");break;case"next":a=a.add(1,"week");break}b.value=a.format("YYYY-MM-DD")}j()},j=()=>{A.value=1,T.value=!0,m.value==="day"?W.value=[]:x.value=[],S(),he()},xe=()=>{C.value=U(new Date);const n=w().startOf("week");b.value=n.format("YYYY-MM-DD")};return fe(m,()=>{S(),j(),setTimeout(ye,100)}),fe([F,$,L,H],aa(()=>{j()},500),{deep:!0}),Ke(()=>{Ve(),xe(),ke(),j()}),Qe(()=>{ke()}),Ge(()=>{S(),Y&&Y.disconnect(),M&&M.disconnect()}),(n,a)=>{const v=R("el-button"),p=R("el-button-group"),z=R("el-date-picker"),q=R("el-option"),J=R("el-select"),ee=R("el-input"),P=R("el-avatar"),N=R("el-icon"),I=R("DocumentRemove"),ae=R("el-table-column"),G=R("el-table"),se=Xe("loading");return l(),t("div",sa,[e("div",la,[e("div",ta,[e("div",oa,[e("div",na,[(l(!0),t(h,null,k(o(Me),s=>(l(),t("button",{key:s.value,class:y(["tab-btn",{active:o(m)===s.value}]),onClick:g=>{m.value=s.value,D.value="",Ne()}},r(s.label),11,ca))),128))])]),e("div",ia,[e("div",ra,[e("div",da,[_(p,null,{default:f(()=>[_(v,{onClick:a[0]||(a[0]=s=>ve("prev")),icon:"ArrowLeft"},{default:f(()=>[te("上一"+r(o(m)==="day"?"天":"周"),1)]),_:1}),_(v,{onClick:a[1]||(a[1]=s=>ve("current"))},{default:f(()=>[te("今"+r(o(m)==="day"?"天":"周"),1)]),_:1}),_(v,{onClick:a[2]||(a[2]=s=>ve("next")),icon:"ArrowRight"},{default:f(()=>[te("下一"+r(o(m)==="day"?"天":"周"),1)]),_:1})]),_:1})]),e("div",ua,r(o(m)==="day"?o(Ce):o(Oe)),1),e("div",va,[o(m)==="day"?(l(),K(z,{key:0,modelValue:o(C),"onUpdate:modelValue":a[3]||(a[3]=s=>X(C)?C.value=s:null),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",onChange:He,clearable:!1},null,8,["modelValue"])):(l(),K(z,{key:1,modelValue:o(b),"onUpdate:modelValue":a[4]||(a[4]=s=>X(b)?b.value=s:null),type:"week",format:"YYYY年第ww周","value-format":"YYYY-MM-DD",placeholder:"选择周",onChange:We,clearable:!1},null,8,["modelValue"]))])])]),e("div",_a,[e("div",pa,[(l(!0),t(h,null,k(o(Z),s=>(l(),K(v,{key:s.value,class:y(["time-btn",s.value,{active:o(D)===s.value}]),onClick:g=>$e(s.value),plain:""},{default:f(()=>[te(r(s.label),1)]),_:2},1032,["class","onClick"]))),128))])])]),e("div",ga,[e("div",ma,[_(J,{modelValue:o(H),"onUpdate:modelValue":a[5]||(a[5]=s=>X(H)?H.value=s:null),placeholder:"选择楼栋",clearable:"",onChange:Re},{default:f(()=>[(l(!0),t(h,null,k(o(me),s=>(l(),K(q,{key:s.value,label:s.buildingName,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_(J,{modelValue:o(L),"onUpdate:modelValue":a[6]||(a[6]=s=>X(L)?L.value=s:null),placeholder:"选择楼层",clearable:""},{default:f(()=>[(l(!0),t(h,null,k(o(ce),s=>(l(),K(q,{key:s.value,label:s.floorName,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_(ee,{modelValue:o($),"onUpdate:modelValue":a[7]||(a[7]=s=>X($)?$.value=s:null),placeholder:"搜索房间号",style:{width:"120px"},clearable:""},null,8,["modelValue"]),_(ee,{modelValue:o(F),"onUpdate:modelValue":a[8]||(a[8]=s=>X(F)?F.value=s:null),placeholder:"搜索老人姓名",style:{width:"120px"},clearable:""},null,8,["modelValue"])]),e("div",ha,[e("div",ka,[e("span",ya,"早上: "+r(o(ie))+"人",1),e("span",fa,"中午: "+r(o(re))+"人",1),e("span",ba,"晚上: "+r(o(de))+"人",1)])])])]),e("div",wa,[o(m)==="day"?(l(),t("div",{key:0,class:"day-view",ref_key:"dayViewContainer",ref:Ye,onScroll:Pe},[e("div",Da,[(l(!0),t(h,null,k(o(W),(s,g)=>(l(),t("div",{key:g,class:"elder-card"},[e("div",Ya,[_(P,{src:s.avatar,class:"elder-avatar",size:60},null,8,["src"]),e("div",Ma,[e("div",Na,r(s.elderName),1),e("div",Va,r(s.roomNumber)+" - "+r(s.bedNumber),1)]),e("div",Ra,r(s.roomNumber),1)]),e("div",Ca,[o(D)?(l(!0),t(h,{key:0},k(o(Z).filter(c=>c.value===o(D)),c=>{var i,u;return l(),t("div",{key:c.value,class:"time-slot"},[e("div",{class:y(["time-label",{activeMorning:c.value==="morning",activeAfternoon:c.value==="noon",activeEvening:c.value==="evening"}]),style:oe({color:c.color})},r(c.label),7),e("div",Oa,[e("div",{class:y(["medication-list",{scrollable:((i=s.dailyRecords[c.value])==null?void 0:i.length)>4}])},[((u=s.dailyRecords[c.value])==null?void 0:u.length)>0?(l(!0),t(h,{key:0},k(s.dailyRecords[c.value],V=>(l(),t("div",{key:V.id,class:"medication-item"},[e("span",Ta,r(V.medicineName),1),e("div",La,[e("span",Pa,r(V.dosage),1),e("span",{class:y(["med-status",{taken:V.status=="1","not-taken":V.status=="0","part-taken":V.status=="2"}])},null,2)])]))),128)):(l(),t("div",Fa,"暂无用药"))],2)])])}),128)):(l(),t("div",$a,[(l(!0),t(h,null,k(o(Z),c=>{var i;return l(),t("div",{key:c.value,class:"time-slot"},[e("div",{class:y(["time-label",{activeMorning:c.value==="morning",activeAfternoon:c.value==="noon",activeEvening:c.value==="evening"}]),style:oe({color:c.color})},r(c.label),7),e("div",Ha,[e("div",{class:y(["medication-list",{scrollable:((i=s.dailyRecords[c.value])==null?void 0:i.length)>4}])},[s.dailyRecords[c.value]&&s.dailyRecords[c.value].length>0?(l(),t("div",Wa,[(l(!0),t(h,null,k(s.dailyRecords[c.value],(u,V)=>(l(),t("div",{key:u.id,class:"medication-item"},[e("span",xa,r(u.medicineName),1),e("div",za,[e("span",Ia,r(u.dosage),1),e("span",{class:y(["med-status",{taken:u.status=="1","not-taken":u.status=="0","part-taken":u.status=="2"}])},null,2)])]))),128))])):(l(),t("div",Ua,"暂无用药"))],2)])])}),128))]))])]))),128))]),o(T)?(l(),t("div",{key:0,class:"scroll-load-tip",ref_key:"dayLoadTrigger",ref:B},[o(O)?(l(),t("div",Aa,[_(N,{class:"is-loading"},{default:f(()=>[_(o(be))]),_:1}),Ba])):(l(),t("div",Ea," 滚动到底部加载更多 "))],512)):o(W).length>0?(l(),t("div",ja," 已显示全部 ")):(l(),t("div",qa,[_(N,null,{default:f(()=>[_(I)]),_:1}),Ja]))],544)):(l(),t("div",{key:1,class:"week-view",ref_key:"weekViewContainer",ref:De,onScroll:Fe},[Ze((l(),K(G,{data:o(x),style:{width:"100%"},"element-loading-text":"加载中...",border:"",ref:"weekTable"},{default:f(()=>[_(ae,{prop:"name",label:"老人信息",width:"180",fixed:"",align:"center"},{default:f(({row:s})=>[e("div",Ka,[e("div",Qa,r(s.roomNumber),1),_(P,{src:s.avatar,size:60},null,8,["src"]),e("div",Ga,[e("div",Xa,r(s.elderName),1),e("div",Za,r(s.roomNumber)+" - "+r(s.bedNumber),1)])])]),_:1}),(l(!0),t(h,null,k(o(Te),(s,g)=>(l(),K(ae,{key:s.date,label:s.label,"min-width":"180","header-align":"center"},{default:f(({row:c})=>[o(D)?(l(),t("div",Sa,[c.dailyRecords&&c.dailyRecords[g]?(l(!0),t(h,{key:0},k(o(Z).filter(i=>i.value===o(D)),i=>(l(),t("div",{key:i.value,class:"week-time-slot"},[e("div",{class:y(["time-label",{activeMorning:i.value==="morning",activeAfternoon:i.value==="noon",activeEvening:i.value==="evening"}]),style:oe({color:i.color})},r(i.label),7),e("div",es,[e("div",{class:y(["medication-list",{scrollable:c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>4}])},[c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>0?(l(!0),t(h,{key:0},k(c.dailyRecords[g][i.value],(u,V)=>(l(),t("div",{key:V,class:"medication-item"},[e("span",as,r(u.medicineName),1),e("div",ss,[e("span",ls,r(u.dosage),1),e("span",{class:y(["med-status",{taken:u.status=="1","not-taken":u.status=="0","part-taken":u.status=="2"}])},null,2)])]))),128)):(l(),t("div",ts,"暂无用药"))],2)])]))),128)):(l(),t("div",os,"无记录"))])):(l(),t("div",ns,[c.dailyRecords&&c.dailyRecords[g]?(l(!0),t(h,{key:0},k(o(Z),i=>(l(),t("div",{key:i.value,class:"week-time-slot"},[e("div",{class:y(["time-label",{activeMorning:i.value==="morning",activeAfternoon:i.value==="noon",activeEvening:i.value==="evening"}]),style:oe({color:i.color})},r(i.label),7),e("div",cs,[e("div",{class:y(["medication-list",{scrollable:c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>4}])},[c.dailyRecords[g][i.value]&&c.dailyRecords[g][i.value].length>0?(l(!0),t(h,{key:0},k(c.dailyRecords[g][i.value],(u,V)=>(l(),t("div",{key:V,class:"medication-item"},[e("span",is,r(u.medicineName),1),e("div",rs,[e("span",ds,r(u.dosage),1),e("span",{class:y(["med-status",{taken:u.status=="1","not-taken":u.status=="0","part-taken":u.status=="2"}])},null,2)])]))),128)):(l(),t("div",us,"暂无用药"))],2)])]))),128)):(l(),t("div",vs,"无记录"))]))]),_:2},1032,["label"]))),128))]),_:1},8,["data"])),[[se,o(O)]]),o(T)?(l(),t("div",{key:0,class:"scroll-load-tip",ref_key:"weekLoadTrigger",ref:E},[o(O)?(l(),t("div",_s,[_(N,{class:"is-loading"},{default:f(()=>[_(o(be))]),_:1}),ps])):(l(),t("div",gs," 滚动到底部加载更多 "))],512)):o(x).length>0?(l(),t("div",ms," 已显示全部 ")):(l(),t("div",hs,[_(N,null,{default:f(()=>[_(I)]),_:1}),ks]))],544))])])}}},Ys=je(ys,[["__scopeId","data-v-e2113b48"]]);export{Ys as default};
