import ge from"./index-CCXF19OR.js";import{r as be,l as O,u as Q}from"./telderAttachement-C4ARfNBy.js";import{r as _e,t as Ve,u as he,v as ye}from"./index-2bfkpdNb.js";import{_ as Ie,d as Ne,r as p,z as j,C as ke,e as m,I as qe,J as Ue,c as y,o as g,f as e,h as l,i as n,k as G,t as _,l as I,K as L,L as M,j as S,n as J,v as Se,x as Te,G as b}from"./index-B0qHf98Y.js";import"./leave-Dd4WELmg.js";const w=N=>(Se("data-v-256c7c2a"),N=N(),Te(),N),De={class:"addMedicationReceive"},Ce={class:"medicine-dialog"},xe={class:"section"},Pe=w(()=>n("h3",null,"老人信息",-1)),Le={class:"value"},Me={key:0,class:"avatar-container"},we={class:"value"},Ee={class:"value"},Fe={class:"value"},Ae={class:"value"},Re={class:"value"},Ye={class:"value"},$e={class:"value"},ze={class:"section"},Be=w(()=>n("h3",null,"药品信息",-1)),Oe={class:"section"},Qe=w(()=>n("h3",null,"委托人信息",-1)),je={class:"section"},Ge={class:"dialog-footer"},Je={__name:"addMedicationReceive",emits:["success"],setup(N,{expose:K,emit:X}){const{proxy:E}=Ne(),T=p(!1),{sys_user_sex:H,inventory_results:W,pharmaceutical_packaging:Z,pharmaceutical_properties:ee}=E.useDict("inventory_results","pharmaceutical_packaging","pharmaceutical_properties","sys_user_sex"),V=p(!1),k=p(null),f=p("view"),le=p(null),q=p([]),U=p([]),i=j(()=>f.value==="view"),ae=j(()=>({view:"查看药品信息",add:"新增药品信息",edit:"编辑药品信息"})[f.value]),a=p({}),te=ke({elderName:[{required:!0,message:"请选择老人",trigger:""}],collectionTime:[{required:!0,message:"请选择收药时间",trigger:"blur"}],medicationName:[{required:!0,message:"请输入药品名称",trigger:"blur"}],specification:[{required:!0,message:"请输入药品规格",trigger:"blur"}],dosage:[{required:!0,message:"请输入用量",trigger:"blur"}],manufacturer:[{required:!0,message:"请输入生产厂家",trigger:"blur"}],remark:[{required:!1}],medicationId:[{required:!0,message:"请输入药品编号",trigger:"blur"}],batchNumber:[{required:!0,message:"请输入药品批号",trigger:"blur"}],specificationQuantity:[{required:!0,message:"请输入规格数量",trigger:"blur"}],administrationMethod:[{required:!0,message:"请输入服用方法",trigger:"blur"}],packaging:[{required:!0,message:"请选择药品包装",trigger:"blur"}],medicationStatus:[{required:!0,message:"请选择药品状态",trigger:"blur"}],medicationType:[{required:!0,message:"请选择药品属性",trigger:"blur"}],quantity:[{required:!0,message:"请输入药品数量",trigger:"blur"}],expiryDate:[{required:!0,message:"请选择有效期",trigger:"blur"}],purpose:[{required:!0,message:"请输入药品用途",trigger:"blur"}],collector:[{required:!0,message:"请输入收取人",trigger:"blur"}],delegator:[{required:!0,message:"请输入委托人",trigger:"blur"}],delegatorPhone:[{required:!0,message:"请输入委托人电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],delegatorIdCard:[{required:!0,message:"请输入委托人身份证",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号码",trigger:"blur"}],noticeFile:[{required:!0,message:"请上传知情通知书",trigger:"change"}],medicinePhotos:[{required:!0,message:"请上传药品实物照片",trigger:"change"}]}),h=p({pageNum:1,pageSize:2e3,elderId:null}),de=p([]),oe=p([]);p({});const ue=()=>{E.$refs.elderSelectComponentRef.openElderSelect()},re=u=>{console.log(u),u&&(a.value={elderName:u.elderName,elderId:u.id,elderCode:u.elderCode,gender:u.gender,avatar:u.avatar,bedNumber:u.bedNumber,roomNumber:u.roomNumber,age:u.age,buildingName:u.buildingName,buildingId:u.buildingId,floorNumber:u.floorNumber,floorId:u.floorId,nursingLevel:u.nursingLevel,checkInDate:u.checkInDate,roomId:u.roomId,roomNumber:u.roomNumber,bedId:u.bedId,bedNumber:u.bedNumber})},D=()=>{a.value={},de.value=[],oe.value=[],k.value&&k.value.resetFields()},se=async()=>{var u,t;try{if(await k.value.validate(),f.value==="add"){if(a.value.medicationId&&!(await Ve(a.value.medicationId)).data){b.warning("药品编号已存在，请修改");return}const s=await he({...a.value});s.code==200?((u=c.value)==null?void 0:u.length)>0&&Q(c.value,s.data.id).then(o=>{o.code==200?b.success("新增成功"):b.error(o.msg)}):b.error(s.msg)}else if(f.value==="edit"){const s=await ye({...a.value});s.code==200?((t=c.value)==null?void 0:t.length)>0&&h.value.elderId?Q(c.value,h.value.elderId).then(o=>{o.code==200?b.success("编辑成功"):b.error(o.msg)}):b.success("编辑成功"):b.error(s.msg)}V.value=!1,ie("success")}catch(s){b.warning("请填写完整信息"),console.error("表单验证失败:",s)}},ie=X,ne=p([]),c=p([]);function F(u){var t,s;console.log(u,"handleGetFile---------"),f.value=="add"?(u&&(Array.isArray(u)?c.value=c.value.concat(u.map(o=>o.ossId)):c.value.push(u)),ne.value.push(u[0])):(((t=q.value)==null?void 0:t.length)>0&&q.value.forEach(o=>{c.value.push(o.ossId)}),((s=U.value)==null?void 0:s.length)>0&&U.value.forEach(o=>{c.value.push(o.ossId)}),u&&(Array.isArray(u)?c.value=c.value.concat(u.map(o=>o.ossId)):c.value.push(u)))}const A=(u,t)=>{be(u).then(s=>{O(h.value).then(o=>{const r=a.value[t].map(C=>C.id).indexOf(u);a.value[t].splice(r,1)})})};function R(u){u.id&&(T.value=!0,_e(u.id).then(t=>{console.log(t,"res"),a.value=t.data,h.value.elderId=t.data.id,pe(h.value),T.value=!1}))}const pe=function(u){O(u).then(t=>{var s;(!a.value.notice_att||!a.value.medicinePhotos_att)&&(a.value.notice_att=[],a.value.medicinePhotos_att=[]),a.value.notice_att=(s=t.rows.filter(o=>o.attachmentType=="notice_att"))==null?void 0:s.map(o=>o.filePath),q.value=t.rows.filter(o=>o.attachmentType=="notice_att"),a.value.notice_att=q.value,U.value=t.rows.filter(o=>o.attachmentType=="medicinePhotos_att"),a.value.medicinePhotos_att=U.value})};return K({openView:async u=>{f.value="view",V.value=!0,D(),R(u)},openAdd:u=>{f.value="add",le.value=null,V.value=!0,D()},openEdit:async u=>{f.value="edit",V.value=!0,D(),R(u)}}),(u,t)=>{const s=m("el-input"),o=m("el-form-item"),r=m("el-col"),C=m("dict-tag-span"),v=m("el-row"),me=m("el-avatar"),Y=m("el-date-picker"),$=m("el-input-number"),x=m("el-option"),P=m("el-select"),z=m("ImageUpload"),ce=m("el-form"),B=m("el-button"),ve=m("el-dialog"),fe=qe("loading");return Ue((g(),y("div",De,[e(ve,{title:ae.value,modelValue:V.value,"onUpdate:modelValue":t[26]||(t[26]=d=>V.value=d),width:"70%","close-on-click-modal":!1},{footer:l(()=>[n("span",Ge,[e(B,{onClick:t[25]||(t[25]=d=>V.value=!1)},{default:l(()=>[J("关闭")]),_:1}),i.value?G("",!0):(g(),S(B,{key:0,type:"primary",onClick:se},{default:l(()=>[J(" 提交 ")]),_:1}))])]),default:l(()=>[e(ce,{ref_key:"medicineForm",ref:k,model:a.value,"label-width":"120px",rules:te,"label-position":"left"},{default:l(()=>[n("div",Ce,[n("div",xe,[Pe,e(v,{gutter:24,class:"elder-info"},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"老人姓名",prop:"elderName"},{default:l(()=>[e(s,{modelValue:a.value.elderName,"onUpdate:modelValue":t[0]||(t[0]=d=>a.value.elderName=d),placeholder:"请输入老人姓名",readonly:"",onClick:ue,disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"老人编号",prop:"elderCode"},{default:l(()=>[n("span",Le,_(a.value.elderCode),1)]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"性别",prop:"gender"},{default:l(()=>[e(C,{options:I(H),value:a.value.gender},null,8,["options","value"])]),_:1})]),_:1})]),_:1}),a.value.avatar?(g(),y("div",Me,[e(me,{shape:"square",size:140,fit:"fill",src:a.value.avatar},null,8,["src"])])):G("",!0),e(v,{gutter:24,class:"elder-info"},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"床位编号",prop:"bedNumber"},{default:l(()=>[n("span",we,_(a.value.roomNumber?a.value.roomNumber+"-"+a.value.bedNumber:a.value.bedNumber),1)]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"房间信息",prop:"roomNumber"},{default:l(()=>[n("span",Ee,_(a.value.roomNumber),1)]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"年龄",prop:"age"},{default:l(()=>[n("span",Fe,_(a.value.age),1)]),_:1})]),_:1})]),_:1}),e(v,{gutter:24,class:"elder-info"},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"楼栋信息",prop:"buildingName"},{default:l(()=>[n("span",Ae,_(a.value.buildingName),1)]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"楼层信息",prop:"floorNumber"},{default:l(()=>[n("span",Re,_(a.value.floorNumber),1)]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"护理等级",prop:"nursingLevel"},{default:l(()=>[n("span",Ye,_(a.value.nursingLevel),1)]),_:1})]),_:1})]),_:1}),e(v,{gutter:24,class:"elder-info"},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"入住时间",prop:"checkInDate"},{default:l(()=>[n("span",$e,_(a.value.checkInDate),1)]),_:1})]),_:1})]),_:1})]),n("div",ze,[Be,e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"收药时间",prop:"collectionTime"},{default:l(()=>[e(Y,{modelValue:a.value.collectionTime,"onUpdate:modelValue":t[1]||(t[1]=d=>a.value.collectionTime=d),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"药品名称",prop:"medicationName"},{default:l(()=>[e(s,{modelValue:a.value.medicationName,"onUpdate:modelValue":t[2]||(t[2]=d=>a.value.medicationName=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"药品规格",prop:"specification"},{default:l(()=>[e(s,{modelValue:a.value.specification,"onUpdate:modelValue":t[3]||(t[3]=d=>a.value.specification=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"用量",prop:"dosage"},{default:l(()=>[e(s,{modelValue:a.value.dosage,"onUpdate:modelValue":t[4]||(t[4]=d=>a.value.dosage=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"生产厂家",prop:"manufacturer"},{default:l(()=>[e(s,{modelValue:a.value.manufacturer,"onUpdate:modelValue":t[5]||(t[5]=d=>a.value.manufacturer=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"药品编号",prop:"medicationId"},{default:l(()=>[e(s,{modelValue:a.value.medicationId,"onUpdate:modelValue":t[6]||(t[6]=d=>a.value.medicationId=d),placeholder:"请输入",disabled:i.value||f.value=="edit"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"药品批号",prop:"batchNumber"},{default:l(()=>[e(s,{modelValue:a.value.batchNumber,"onUpdate:modelValue":t[7]||(t[7]=d=>a.value.batchNumber=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"规格数量",prop:"specificationQuantity"},{default:l(()=>[e($,{modelValue:a.value.specificationQuantity,"onUpdate:modelValue":t[8]||(t[8]=d=>a.value.specificationQuantity=d),placeholder:"请输入",min:0,disabled:i.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"服用方法",prop:"administrationMethod"},{default:l(()=>[e(s,{modelValue:a.value.administrationMethod,"onUpdate:modelValue":t[9]||(t[9]=d=>a.value.administrationMethod=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"药品包装",prop:"packaging"},{default:l(()=>[e(P,{modelValue:a.value.packaging,"onUpdate:modelValue":t[10]||(t[10]=d=>a.value.packaging=d),placeholder:"选择",style:{width:"100%"},disabled:i.value},{default:l(()=>[(g(!0),y(L,null,M(I(Z),d=>(g(),S(x,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"药品状态",prop:"medicationStatus"},{default:l(()=>[e(P,{modelValue:a.value.medicationStatus,"onUpdate:modelValue":t[11]||(t[11]=d=>a.value.medicationStatus=d),placeholder:"选择",style:{width:"100%"},disabled:i.value},{default:l(()=>[(g(!0),y(L,null,M(I(W),d=>(g(),S(x,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"药品属性",prop:"medicationType"},{default:l(()=>[e(P,{modelValue:a.value.medicationType,"onUpdate:modelValue":t[12]||(t[12]=d=>a.value.medicationType=d),placeholder:"选择",style:{width:"100%"},disabled:i.value},{default:l(()=>[(g(!0),y(L,null,M(I(ee),d=>(g(),S(x,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"药品数量",prop:"quantity"},{default:l(()=>[e($,{modelValue:a.value.quantity,"onUpdate:modelValue":t[13]||(t[13]=d=>a.value.quantity=d),placeholder:"请输入",min:0,disabled:i.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"有效期",prop:"expiryDate"},{default:l(()=>[e(Y,{modelValue:a.value.expiryDate,"onUpdate:modelValue":t[14]||(t[14]=d=>a.value.expiryDate=d),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"药品用途",prop:"purpose"},{default:l(()=>[e(s,{modelValue:a.value.purpose,"onUpdate:modelValue":t[15]||(t[15]=d=>a.value.purpose=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:l(()=>[e(r,{span:24},{default:l(()=>[e(o,{label:"备注事项",prop:"remark"},{default:l(()=>[e(s,{modelValue:a.value.remark,"onUpdate:modelValue":t[16]||(t[16]=d=>a.value.remark=d),placeholder:"请输入",type:"textarea",rows:2,disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),n("div",Oe,[Qe,e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"收 取 人",prop:"collector"},{default:l(()=>[e(s,{modelValue:a.value.collector,"onUpdate:modelValue":t[17]||(t[17]=d=>a.value.collector=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(o,{label:"委 托 人",prop:"delegator"},{default:l(()=>[e(s,{modelValue:a.value.delegator,"onUpdate:modelValue":t[18]||(t[18]=d=>a.value.delegator=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"委托人电话",prop:"delegatorPhone"},{default:l(()=>[e(s,{modelValue:a.value.delegatorPhone,"onUpdate:modelValue":t[19]||(t[19]=d=>a.value.delegatorPhone=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(o,{label:"委托人身份证",prop:"delegatorIdCard"},{default:l(()=>[e(s,{modelValue:a.value.delegatorIdCard,"onUpdate:modelValue":t[20]||(t[20]=d=>a.value.delegatorIdCard=d),placeholder:"请输入",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),n("div",je,[e(o,{prop:"notice_att",label:"自带药品使用知情通知书"},{default:l(()=>[e(z,{modelValue:a.value.notice_att,"onUpdate:modelValue":t[21]||(t[21]=d=>a.value.notice_att=d),fileData:{category:"notice_type",attachmentType:"notice_att"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:10,disabled:i.value,onSubmitParentValue:F,onRemoveAtt:t[22]||(t[22]=d=>A(d,"notice_att"))},null,8,["modelValue","disabled"])]),_:1}),e(o,{prop:"medicinePhotos_att",label:"药品实物拍照"},{default:l(()=>[e(z,{modelValue:a.value.medicinePhotos_att,"onUpdate:modelValue":t[23]||(t[23]=d=>a.value.medicinePhotos_att=d),fileData:{category:"medicinePhotos_type",attachmentType:"medicinePhotos_att"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:10,disabled:i.value,onSubmitParentValue:F,onRemoveAtt:t[24]||(t[24]=d=>A(d,"medicinePhotos_att"))},null,8,["modelValue","disabled"])]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(I(ge),{ref:"elderSelectComponentRef",onSelectLerder:re},null,512)])),[[fe,T.value]])}}},el=Ie(Je,[["__scopeId","data-v-256c7c2a"]]);export{el as default};
