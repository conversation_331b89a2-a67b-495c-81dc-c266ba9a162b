import{_ as ie,a as ce,u as pe,r as V,C as me,z as W,F as Q,w as fe,e as m,c as Z,o as x,i as f,f as e,t as O,h as a,n as h,l as v,aU as ee,j,m as ge,K as be,L as _e,Q as he,aV as ve,aW as Ve,aX as N,aY as ye,P as F,G as y}from"./index-B0qHf98Y.js";import{g as Se,a as Le,e as Ie,b as Te}from"./index-CjVbTVeO.js";import{r as Ae,u as we,l as Pe}from"./telderAttachement-C4ARfNBy.js";const Re={class:"add-form-container"},Ce={class:"page-header"},Ne={class:"page-title"},Ue={class:"section-title"},De={class:"section-title"},xe={class:"service-container"},ke={class:"section-title"},Ee={class:"section-title"},Be={class:"section-title"},Me={class:"upload-section"},qe={class:"upload-section"},Ye={class:"upload-section"},Oe={class:"upload-section"},je={class:"upload-section"},Fe={class:"upload-section"},ze={class:"form-footer"},He={__name:"addForm",setup(Ge){const k=ce(),w=pe(),z=V(null),l=me({name:"",provinceInfo:[],orgNature:"",startupFund:"",detailAddress:"",establishTime:"",recordNumber:"",legalPerson:"",legalPhone:"",remoteDesign:"",medicalInsurance:"",countyDesign:"",socialCreditCode:"",nursingType:"",houseNature:"",saleTimeRange:[],bedCount:"",buildingArea:"",phone:"",starLevel:"",wechatAccount:"",feeRange:"",status:"",remark:"",traffic:"",transportation:"",feeStandard:"",feeStandard1:"",orgIntroduction:"",remoteAccept:"",nursing:"",constructionSubsidy:"否",services:[],nonProfitRegNumber:"",issuingAuthority:"",has_clinic:[],has_hospital:[],has_rehabilitation:[],has_canteen:[],has_license:[],coverPhoto:[],businessLicense:[],medicalLicense:[],foodLicense:[],eldercareLicense:[],eldercareRecordPhoto:[],createBy:"",createTime:"",updateBy:"",updateTime:"",searchValue:"",params:{}}),q=V(!1),S=W(()=>w.params.type==="edit"||w.query.mode==="edit"),L=W(()=>w.params.id||w.query.id||""),H=V([]),G=async()=>{try{const d=await Le();console.log("获取到的原始区域数据:",d),d.code===200&&d.data&&(H.value=d.data)}catch(d){console.error("获取省份数据出错:",d),y.error("获取省份数据出错")}};Q(()=>{S.value||(l.services=[])});const I=V([]),E=V({pageNum:1,pageSize:2e3,elderId:null}),P=d=>{console.log("文件上传数据:",d),S.value?d&&(Array.isArray(d)?I.value=I.value.concat(d.map(t=>t.ossId)):I.value.push(d.ossId)):d&&(Array.isArray(d)?I.value=I.value.concat(d.map(t=>t.ossId)):I.value.push(d.ossId))},U=V(""),K=V(null),le={助医服务:"tag-blue",康复服务:"tag-orange",文化娱乐:"tag-purple",健康指导:"tag-pink",呼叫服务:"tag-yellow",代办服务:"tag-green",专业护理:"tag-lightgreen",助餐服务:"tag-lightpurple",助浴服务:"tag-lightblue"},R=V(["助医服务","康复服务","文化娱乐","健康指导","呼叫服务","代办服务","专业护理"]),ae=d=>le[d]||"tag-default",Y=V(!1),te=()=>{Y.value=!0,F(()=>{K.value.input.focus()})},$=()=>{U.value&&R.value.push(U.value),Y.value=!1,U.value=""},oe=d=>{R.value.splice(R.value.indexOf(d),1)},C=(d,t)=>{Ae(d).then(u=>{u.code===200?(S.value&&L.value&&(E.value.elderId=L.value,X(E.value)),y.success("文件删除成功")):y.error("文件删除失败")}).catch(u=>{console.error("删除文件失败:",u),y.error("删除文件失败")})},X=function(d){Pe(d).then(t=>{t.code===200&&(l.coverPhoto||(l.coverPhoto=[]),l.businessLicense||(l.businessLicense=[]),l.medicalLicense||(l.medicalLicense=[]),l.foodLicense||(l.foodLicense=[]),l.eldercareLicense||(l.eldercareLicense=[]),l.eldercareRecordPhoto||(l.eldercareRecordPhoto=[]),l.coverPhoto=t.rows.filter(u=>u.attachmentType==="cover_photo"),l.businessLicense=t.rows.filter(u=>u.attachmentType==="business_license"),l.medicalLicense=t.rows.filter(u=>u.attachmentType==="medical_license"),l.foodLicense=t.rows.filter(u=>u.attachmentType==="food_license"),l.eldercareLicense=t.rows.filter(u=>u.attachmentType==="eldercare_license"),l.eldercareRecordPhoto=t.rows.filter(u=>u.attachmentType==="eldercare_record_photo"))}).catch(t=>{console.error("获取文件列表失败:",t)})},re={name:[{required:!0,message:"请输入机构名称",trigger:"blur"},{min:2,max:50,message:"机构名称长度在 2 到 50 个字符",trigger:"blur"}],provinceInfo:[{required:!0,message:"请选择省份信息",trigger:"change"}],orgNature:[{required:!0,message:"请选择机构性质",trigger:"change"}],startupFund:[{required:!0,message:"请输入开办资金",trigger:"blur"}],detailAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"},{min:2,max:200,message:"地址长度在 2 到 200 个字符",trigger:"blur"}],establishTime:[{required:!0,message:"请选择开办时间",trigger:"change"}],recordNumber:[{required:!0,message:"请输入备案号",trigger:"blur"}],legalPerson:[{required:!0,message:"请输入企业法人",trigger:"blur"}],socialCreditCode:[{required:!0,message:"请输入统一社会信用代码",trigger:"blur"}],bedCount:[{required:!0,message:"请输入床位数",trigger:"blur"},{pattern:/^\d+$/,message:"床位数必须为正整数",trigger:"blur"}],buildingArea:[{required:!0,message:"请输入建筑面积",trigger:"blur"},{pattern:/^\d+(\.\d+)?$/,message:"建筑面积必须为数字",trigger:"blur"}],starLevel:[{required:!0,message:"请选择机构星级",trigger:"change"}],coverPhoto:[{type:"array",required:!0,min:1,message:"请上传封面照片",trigger:"change"}]},ne=async()=>{var d,t,u,c,n,r,s,p,b,i,A;await z.value.validate();try{q.value=!0,console.log("提交前的省市区数据:",l.provinceInfo);const _={acceptNonlocalElder:l.remoteAccept==="1"?1:0,address:l.detailAddress,admissionStandard:l.feeStandard1,bedCount:l.bedCount,capital:l.startupFund,chargeStandard:l.feeStandard,province:((d=l.provinceInfo)==null?void 0:d[1])||"",city:((t=l.provinceInfo)==null?void 0:t[2])||"",county:((u=l.provinceInfo)==null?void 0:u[3])||"",consultPhone:l.phone,establishDate:l.establishTime,feeRange:l.feeRange,floorArea:l.buildingArea,hasCanteen:((c=l.has_canteen)==null?void 0:c.join(","))||"",hasClinic:((n=l.has_clinic)==null?void 0:n.join(","))||"",hasConstructionSubsidy:l.constructionSubsidy==="1"?1:0,hasHospital:((r=l.has_hospital)==null?void 0:r.join(","))||"",hasLicense:l.has_license.includes("医疗机构执业许可证")?1:0,hasMedicalInsurance:l.medicalInsurance==="1"?1:0,hasRehabilitation:((s=l.has_rehabilitation)==null?void 0:s.join(","))||"",institutionType:l.orgNature,introduction:l.orgIntroduction,isNursingInstitution:l.nursing==="1"?1:0,issuingAuthority:l.issuingAuthority,leaseEndDate:(p=l.saleTimeRange)==null?void 0:p[1],leaseStartDate:(b=l.saleTimeRange)==null?void 0:b[0],legalPerson:l.legalPerson,legalPhone:l.legalPhone,nonEnterpriseRegNo:l.nonProfitRegNumber,orgName:l.name,propertyType:l.houseNature,publicAccount:l.wechatAccount,recordNumber:l.recordNumber,serviceItems:((i=R.value)==null?void 0:i.join(","))||"",socialCreditCode:l.socialCreditCode,starLevel:parseInt(l.starLevel)||0,transportation:l.traffic};let T;if(S.value?(_.id=L.value,T=await Ie(_),y.success("更新机构成功")):(T=await Te(_),console.log("新增机构结果:",T),y.success("新增机构成功")),console.log("orgId:",L.value),I.value.length>0){const B=S.value?L.value:(A=T.data)==null?void 0:A.id;if(B)try{await we(I.value,B),console.log("文件关联成功")}catch(M){console.error("文件关联失败:",M),y.warning("机构保存成功，但文件关联失败")}}k.back()}catch(_){console.error(_),y.error("提交失败："+(_.message||"未知错误"))}finally{q.value=!1}},se=()=>{k.options.routes.some(d=>d.path==="/eldersystem/orgmanagement")?k.push("/eldersystem/orgmanagement"):k.go(-1)},de=async()=>{await F();const d=document.getElementById("transportation-editor"),t=document.getElementById("fee-standard-editor"),u=document.getElementById("org-introduction-editor");d&&(d.contentEditable=!0,d.style.border="1px solid #dcdfe6",d.style.borderRadius="4px",d.style.padding="12px",d.style.minHeight="200px",d.innerHTML=l.transportation||"乘地铁7号线，10号线到双井站B1口步行约20分钟，23路，28路，35路等20余条公交线路直达。"),t&&(t.contentEditable=!0,t.style.border="1px solid #dcdfe6",t.style.borderRadius="4px",t.style.padding="12px",t.style.minHeight="200px",t.innerHTML=l.feeStandard||"1.房型套餐化定价：单人间（30-40㎡）配备独立卫生间、智能护理床及紧急呼叫系统，月费12,000-18,000元；双人间（25-35㎡）分床型双人间（9,000-10,000元/月）与床位型双人间（11,000-13,000元/月）；套房（60-80㎡）含独立客厅、厨房及阳台，价格依配置差异动态定价30,000元/月。"),u&&(u.contentEditable=!0,u.style.border="1px solid #dcdfe6",u.style.borderRadius="4px",u.style.padding="12px",u.style.minHeight="300px",u.innerHTML=l.orgIntroduction||'一、机构概况：城市核心区的养老新地标<br><br>北京汉井春和苑坐落于北京市朝阳区百子湾南二路92号，地处东三环国贸商圈核心区内，与汉井地铁站仅距离高端763米。项目总建筑面积27,699平方米，规划床位330张，由南北两栋建筑构成，其中南楼13层，北楼4层，形成"垂直社区"式布局。其独特的地理位置既享受可达性又兼顾市华配套，又通过5,000平方米绿地环境与1,200平方米屋顶花园"出则繁华、入则宁静"的养老生活。作为成熟养老集团旗舰项目，该机构于2013年成为北京市首批"医养结合"试点单位，开创了"机构-社区-居家"三位一体服务模式的先河。')},J=async()=>{var d,t,u,c,n;if(console.log("loadOrgData"),!(!S.value||!L.value))try{await G();const r=await Se(L.value);if(r.code===200){const s=r.data;l.name=s.orgName||"",F(()=>{l.provinceInfo=s.province?[1e5,Number(s.province),Number(s.city),Number(s.county)]:[]}),console.log("省份数据加载完成:",l.provinceInfo),l.orgNature=s.institutionType||"",l.startupFund=s.capital||"",l.detailAddress=s.address||"",l.establishTime=s.establishDate||"",l.recordNumber=s.recordNumber||"",l.legalPerson=s.legalPerson||"",l.legalPhone=s.legalPhone||"",l.remoteAccept=s.acceptNonlocalElder?"1":"0",l.medicalInsurance=s.hasMedicalInsurance?"1":"0",l.nursing=s.isNursingInstitution?"1":"0",l.constructionSubsidy=s.hasConstructionSubsidy?"1":"0",l.socialCreditCode=s.socialCreditCode||"",l.houseNature=s.propertyType||"",l.saleTimeRange=[s.leaseStartDate,s.leaseEndDate].filter(Boolean),l.bedCount=s.bedCount||"",l.buildingArea=s.floorArea||"",l.phone=s.consultPhone||"",l.starLevel=s.starLevel?s.starLevel.toString():"",l.wechatAccount=s.publicAccount||"",l.feeRange=s.feeRange||"",l.traffic=s.transportation||"",l.feeStandard=s.chargeStandard||"",l.feeStandard1=s.admissionStandard||"",l.orgIntroduction=s.introduction||"",R.value=((d=s.serviceItems)==null?void 0:d.split(",").filter(Boolean))||[],l.nonProfitRegNumber=s.nonEnterpriseRegNo||"",l.issuingAuthority=s.issuingAuthority||"";const p=((t=s.hasClinic)==null?void 0:t.split(",").filter(Boolean))||[];l.has_clinic=p;const b=((u=s.hasHospital)==null?void 0:u.split(",").filter(Boolean))||[];l.has_hospital=b;const i=((c=s.hasRehabilitation)==null?void 0:c.split(",").filter(Boolean))||[];l.has_rehabilitation=i;const A=((n=s.hasCanteen)==null?void 0:n.split(",").filter(Boolean))||[];l.has_canteen=A,console.log("医疗机构执业许可证状态:",s.hasLicense,typeof s.hasLicense),l.has_license=s.hasLicense==1?["医疗机构执业许可证"]:[],E.value.elderId=L.value,X(E.value)}}catch(r){console.error("加载机构数据失败:",r),y.error("加载数据失败")}};return fe(()=>[w.params,w.query],([d,t])=>{const u=d.id||t.id,c=d.type==="edit"||t.mode==="edit";u&&c&&J()},{immediate:!0}),Q(async()=>{await G(),await J(),console.log("表单数据加载完成 - has_license:",l.has_license),console.log("医疗机构执业许可证是否选中:",l.has_license.includes("医疗机构执业许可证")),await de()}),(d,t)=>{const u=m("el-icon"),c=m("el-input"),n=m("el-form-item"),r=m("el-col"),s=m("el-cascader"),p=m("el-option"),b=m("el-select"),i=m("el-row"),A=m("el-date-picker"),_=m("el-card"),T=m("el-button"),B=m("el-tag"),M=m("editor"),g=m("el-checkbox"),D=m("el-checkbox-group"),ue=m("el-form");return x(),Z("div",Re,[f("div",Ce,[f("h2",Ne,O(S.value?"编辑机构":"新增机构"),1)]),e(ue,{ref_key:"formRef",ref:z,model:l,rules:re,"label-width":"140px",class:"org-form"},{default:a(()=>[e(_,{class:"form-section",shadow:"never"},{header:a(()=>[f("div",Ue,[e(u,null,{default:a(()=>[e(v(ee))]),_:1}),h(" 机构信息 ")])]),default:a(()=>[e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"机构名称",prop:"name"},{default:a(()=>[e(c,{modelValue:l.name,"onUpdate:modelValue":t[0]||(t[0]=o=>l.name=o),placeholder:"汉井春和苑",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"省份信息",prop:"provinceInfo"},{default:a(()=>[e(s,{modelValue:l.provinceInfo,"onUpdate:modelValue":t[1]||(t[1]=o=>l.provinceInfo=o),options:H.value,placeholder:"请选择省/市/区",clearable:"",props:{expandTrigger:"hover",value:"value",label:"label",emitPath:!0,checkStrictly:!1}},null,8,["modelValue","options"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"机构性质",prop:"orgNature"},{default:a(()=>[e(b,{modelValue:l.orgNature,"onUpdate:modelValue":t[2]||(t[2]=o=>l.orgNature=o),placeholder:"公办民营",clearable:""},{default:a(()=>[e(p,{label:"公办民营",value:"公办民营"}),e(p,{label:"民办",value:"民办"}),e(p,{label:"公办",value:"公办"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"开办资金",prop:"startupFund"},{default:a(()=>[e(c,{modelValue:l.startupFund,"onUpdate:modelValue":t[3]||(t[3]=o=>l.startupFund=o),placeholder:"5000万",clearable:""},{append:a(()=>[h("万")]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"详细地址",prop:"detailAddress"},{default:a(()=>[e(c,{modelValue:l.detailAddress,"onUpdate:modelValue":t[4]||(t[4]=o=>l.detailAddress=o),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"开办时间",prop:"establishTime"},{default:a(()=>[e(A,{modelValue:l.establishTime,"onUpdate:modelValue":t[5]||(t[5]=o=>l.establishTime=o),type:"date",placeholder:"2025-06-26",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"备案号",prop:"recordNumber"},{default:a(()=>[e(c,{modelValue:l.recordNumber,"onUpdate:modelValue":t[6]||(t[6]=o=>l.recordNumber=o),placeholder:"BJ50003",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"企业法人",prop:"legalPerson"},{default:a(()=>[e(c,{modelValue:l.legalPerson,"onUpdate:modelValue":t[7]||(t[7]=o=>l.legalPerson=o),placeholder:"税海平",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"法人电话",prop:"legalPhone"},{default:a(()=>[e(c,{modelValue:l.legalPhone,"onUpdate:modelValue":t[8]||(t[8]=o=>l.legalPhone=o),placeholder:"18545896566",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"具备医保定点资格",prop:"medicalInsurance"},{default:a(()=>[e(b,{modelValue:l.medicalInsurance,"onUpdate:modelValue":t[9]||(t[9]=o=>l.medicalInsurance=o),placeholder:"请选择",clearable:""},{default:a(()=>[e(p,{label:"是",value:"1"}),e(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"是否接受异地老人",prop:"remoteAccept"},{default:a(()=>[e(b,{modelValue:l.remoteAccept,"onUpdate:modelValue":t[10]||(t[10]=o=>l.remoteAccept=o),placeholder:"请选择",clearable:""},{default:a(()=>[e(p,{label:"是",value:"1"}),e(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"是否护理型养老机构",prop:"nursing"},{default:a(()=>[e(b,{modelValue:l.nursing,"onUpdate:modelValue":t[11]||(t[11]=o=>l.nursing=o),placeholder:"请选择",clearable:""},{default:a(()=>[e(p,{label:"是",value:"1"}),e(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"是否享受建设补贴",prop:"constructionSubsidy"},{default:a(()=>[e(b,{modelValue:l.constructionSubsidy,"onUpdate:modelValue":t[12]||(t[12]=o=>l.constructionSubsidy=o),placeholder:"请选择",clearable:""},{default:a(()=>[e(p,{label:"是",value:"1"}),e(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"统一社会信用代码",prop:"socialCreditCode"},{default:a(()=>[e(c,{modelValue:l.socialCreditCode,"onUpdate:modelValue":t[13]||(t[13]=o=>l.socialCreditCode=o),placeholder:"91110101MADMG77B1G",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"房屋性质",prop:"houseNature"},{default:a(()=>[e(b,{modelValue:l.houseNature,"onUpdate:modelValue":t[14]||(t[14]=o=>l.houseNature=o),placeholder:"请选择",clearable:""},{default:a(()=>[e(p,{label:"自有",value:"自有"}),e(p,{label:"租赁",value:"租赁"}),e(p,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"租赁时间",prop:"saleTimeRange"},{default:a(()=>[e(A,{modelValue:l.saleTimeRange,"onUpdate:modelValue":t[15]||(t[15]=o=>l.saleTimeRange=o),type:"daterange","range-separator":"-","start-placeholder":"2025-06-26","end-placeholder":"2025-06-26",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(_,{class:"form-section",shadow:"never"},{header:a(()=>[f("div",De,[e(u,null,{default:a(()=>[e(v(ee))]),_:1}),h(" 基本信息 ")])]),default:a(()=>[e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"床位数",prop:"bedCount"},{default:a(()=>[e(c,{modelValue:l.bedCount,"onUpdate:modelValue":t[16]||(t[16]=o=>l.bedCount=o),placeholder:"2000张",clearable:""},{append:a(()=>[h("张")]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"建筑面积",prop:"buildingArea"},{default:a(()=>[e(c,{modelValue:l.buildingArea,"onUpdate:modelValue":t[17]||(t[17]=o=>l.buildingArea=o),placeholder:"899971.61㎡",clearable:""},{append:a(()=>[h("㎡")]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"公众号",prop:"wechatAccount"},{default:a(()=>[e(c,{modelValue:l.wechatAccount,"onUpdate:modelValue":t[18]||(t[18]=o=>l.wechatAccount=o),placeholder:"津和苑养老社区",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"咨询电话",prop:"phone"},{default:a(()=>[e(c,{modelValue:l.phone,"onUpdate:modelValue":t[19]||(t[19]=o=>l.phone=o),placeholder:"010-********",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"机构星级",prop:"starLevel"},{default:a(()=>[e(b,{modelValue:l.starLevel,"onUpdate:modelValue":t[20]||(t[20]=o=>l.starLevel=o),placeholder:"五星级",clearable:""},{default:a(()=>[e(p,{label:"一星级",value:"1"}),e(p,{label:"二星级",value:"2"}),e(p,{label:"三星级",value:"3"}),e(p,{label:"四星级",value:"4"}),e(p,{label:"五星级",value:"5"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"费用区间",prop:"feeRange"},{default:a(()=>[e(c,{modelValue:l.feeRange,"onUpdate:modelValue":t[21]||(t[21]=o=>l.feeRange=o),placeholder:"9800-30000元/月",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"服务项目",prop:"services"},{default:a(()=>[f("div",xe,[Y.value?(x(),j(c,{key:0,ref_key:"InputRef",ref:K,modelValue:U.value,"onUpdate:modelValue":t[22]||(t[22]=o=>U.value=o),class:"w-20",size:"small",onKeyup:ge($,["enter"]),onBlur:$},null,8,["modelValue"])):(x(),j(T,{key:1,class:"button-new-tag",onClick:te,size:"small",type:"primary"},{default:a(()=>[h(" 添加 ")]),_:1})),(x(!0),Z(be,null,_e(R.value,o=>(x(),j(B,{key:o,closable:"",round:"",effect:"dark",class:he(ae(o)),"disable-transitions":!1,onClose:Ke=>oe(o)},{default:a(()=>[h(O(o),1)]),_:2},1032,["class","onClose"]))),128))])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"交通情况",prop:"traffic"},{default:a(()=>[e(c,{modelValue:l.traffic,"onUpdate:modelValue":t[23]||(t[23]=o=>l.traffic=o),placeholder:"请输入交通情况",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"收住标准",prop:"feeStandard1"},{default:a(()=>[e(c,{modelValue:l.feeStandard1,"onUpdate:modelValue":t[24]||(t[24]=o=>l.feeStandard1=o),placeholder:"请输入收住标准",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"收费标准",prop:"feeStandard"},{default:a(()=>[e(M,{modelValue:l.feeStandard,"onUpdate:modelValue":t[25]||(t[25]=o=>l.feeStandard=o),"min-height":192},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"机构介绍",prop:"orgIntroduction"},{default:a(()=>[e(M,{modelValue:l.orgIntroduction,"onUpdate:modelValue":t[26]||(t[26]=o=>l.orgIntroduction=o),"min-height":192},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(_,{class:"form-section",shadow:"never"},{header:a(()=>[f("div",ke,[e(u,null,{default:a(()=>[e(v(ve))]),_:1}),h(" 登记证书 ")])]),default:a(()=>[e(i,{gutter:24},{default:a(()=>[e(r,{span:12},{default:a(()=>[e(n,{label:"民办非企业单位登记号",prop:"nonProfitRegNumber"},{default:a(()=>[e(c,{modelValue:l.nonProfitRegNumber,"onUpdate:modelValue":t[27]||(t[27]=o=>l.nonProfitRegNumber=o),placeholder:"11058059565X",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(n,{label:"发证机关",prop:"issuingAuthority"},{default:a(()=>[e(c,{modelValue:l.issuingAuthority,"onUpdate:modelValue":t[28]||(t[28]=o=>l.issuingAuthority=o),placeholder:"北京市朝阳区民政局",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(_,{class:"form-section",shadow:"never"},{header:a(()=>[f("div",Ee,[e(u,null,{default:a(()=>[e(v(Ve))]),_:1}),h(" 医疗及食堂设施 ")])]),default:a(()=>[e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"是否内设",prop:"has_clinic"},{default:a(()=>[e(D,{modelValue:l.has_clinic,"onUpdate:modelValue":t[29]||(t[29]=o=>l.has_clinic=o)},{default:a(()=>[e(g,{label:"医务室",value:"医务室"}),e(g,{label:"护理站",value:"护理站"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"是否开办",prop:"has_hospital"},{default:a(()=>[e(D,{modelValue:l.has_hospital,"onUpdate:modelValue":t[30]||(t[30]=o=>l.has_hospital=o)},{default:a(()=>[e(g,{label:"老年康复医院",value:"老年康复医院"}),e(g,{label:"康复医院",value:"康复医院"}),e(g,{label:"护理院",value:"护理院"}),e(g,{label:"中医医院",value:"中医医院"}),e(g,{label:"安宁疗护",value:"安宁疗护"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(n,{label:"是否开办",prop:"has_rehabilitation"},{default:a(()=>[e(D,{modelValue:l.has_rehabilitation,"onUpdate:modelValue":t[31]||(t[31]=o=>l.has_rehabilitation=o)},{default:a(()=>[e(g,{label:"康复医疗中心",value:"康复医疗中心"}),e(g,{label:"护理中心",value:"护理中心"}),e(g,{label:"门诊部",value:"门诊部"}),e(g,{label:"诊所",value:"诊所"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(n,{label:"是否开办",prop:"has_canteen"},{default:a(()=>[e(D,{modelValue:l.has_canteen,"onUpdate:modelValue":t[32]||(t[32]=o=>l.has_canteen=o)},{default:a(()=>[e(g,{label:"中心食堂",value:"中心食堂"}),e(g,{label:"老年食堂",value:"老年食堂"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:16},{default:a(()=>[e(n,{label:"是否获得",prop:"has_license"},{default:a(()=>[e(D,{modelValue:l.has_license,"onUpdate:modelValue":t[33]||(t[33]=o=>l.has_license=o),onChange:t[34]||(t[34]=o=>console.log("checkbox变化:",o))},{default:a(()=>[e(g,{label:"医疗机构执业许可证",value:"医疗机构执业许可证",checked:l.has_license.includes("医疗机构执业许可证")},null,8,["checked"])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(_,{class:"form-section",shadow:"never"},{header:a(()=>[f("div",Be,[e(u,null,{default:a(()=>[e(v(ye))]),_:1}),h(" 照片及其它资源 ")])]),default:a(()=>[e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"封面照片",prop:"coverPhoto",required:""},{default:a(()=>[f("div",Me,[e(v(N),{modelValue:l.coverPhoto,"onUpdate:modelValue":t[35]||(t[35]=o=>l.coverPhoto=o),fileData:{category:"cover_photo",attachmentType:"cover_photo"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:20,limit:5,disabled:!1,width:300,height:200,fit:"cover",onSubmitParentValue:P,onRemoveAtt:t[36]||(t[36]=o=>C(o,"coverPhoto"))},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"营业执照",prop:"businessLicense"},{default:a(()=>[f("div",qe,[e(v(N),{modelValue:l.businessLicense,"onUpdate:modelValue":t[37]||(t[37]=o=>l.businessLicense=o),fileData:{category:"business_license",attachmentType:"business_license"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:20,limit:5,disabled:!1,onSubmitParentValue:P,onRemoveAtt:t[38]||(t[38]=o=>C(o,"businessLicense"))},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"医疗机构执业许可证",prop:"medicalLicense"},{default:a(()=>[f("div",Ye,[e(v(N),{modelValue:l.medicalLicense,"onUpdate:modelValue":t[39]||(t[39]=o=>l.medicalLicense=o),fileData:{category:"medical_license",attachmentType:"medical_license"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:20,limit:5,disabled:!1,onSubmitParentValue:P,onRemoveAtt:t[40]||(t[40]=o=>C(o,"medicalLicense"))},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"餐饮许可证",prop:"foodLicense"},{default:a(()=>[f("div",Oe,[e(v(N),{modelValue:l.foodLicense,"onUpdate:modelValue":t[41]||(t[41]=o=>l.foodLicense=o),fileData:{category:"food_license",attachmentType:"food_license"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:20,limit:5,disabled:!1,onSubmitParentValue:P,onRemoveAtt:t[42]||(t[42]=o=>C(o,"foodLicense"))},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"养老机构设立许可证",prop:"eldercareLicense"},{default:a(()=>[f("div",je,[e(v(N),{modelValue:l.eldercareLicense,"onUpdate:modelValue":t[43]||(t[43]=o=>l.eldercareLicense=o),fileData:{category:"eldercare_license",attachmentType:"eldercare_license"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:20,limit:5,disabled:!1,onSubmitParentValue:P,onRemoveAtt:t[44]||(t[44]=o=>C(o,"eldercareLicense"))},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1}),e(i,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"养老机构备案照片",prop:"eldercareRecordPhoto"},{default:a(()=>[f("div",Fe,[e(v(N),{modelValue:l.eldercareRecordPhoto,"onUpdate:modelValue":t[45]||(t[45]=o=>l.eldercareRecordPhoto=o),fileData:{category:"eldercare_record_photo",attachmentType:"eldercare_record_photo"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:20,limit:5,disabled:!1,onSubmitParentValue:P,onRemoveAtt:t[46]||(t[46]=o=>C(o,"eldercareRecordPhoto"))},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1})]),_:1}),f("div",ze,[e(T,{onClick:se},{default:a(()=>[h("取消")]),_:1}),e(T,{type:"primary",onClick:ne,loading:q.value},{default:a(()=>[h(O(S.value?"更新":"保存"),1)]),_:1},8,["loading"])])]),_:1},8,["model"])])}}},We=ie(He,[["__scopeId","data-v-c7b77c1f"]]);export{We as default};
