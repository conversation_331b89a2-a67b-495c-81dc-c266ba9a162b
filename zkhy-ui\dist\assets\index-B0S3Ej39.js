import{_ as ul,a as il,d as sl,r as b,w as cl,N as ml,C as pl,e as y,I as Ve,c as D,o as f,J as I,f as e,O as we,h as a,m as X,l as o,n as _,j as h,i as V,t as Z,k as se,K as $,L as E,b as vl,G as K,v as fl,x as gl}from"./index-B0qHf98Y.js";import{l as bl,d as _l,u as yl,b as Vl,c as wl,e as hl,f as he,s as Cl,h as Nl,g as kl,i as Sl,a as Ul}from"./contract-DgThwd93.js";import{d as Il}from"./paramUtil-DJB1oWef.js";import xl from"./ContractPrintView-CtGNqyN6.js";import{l as Ll}from"./tFeeItem-CPd7lByO.js";const J=Q=>(fl("data-v-d9f317be"),Q=Q(),gl(),Q),Dl={class:"app-container"},Tl={key:0,style:{display:"flex","justify-content":"center",gap:"8px"}},Al={key:1,style:{display:"flex","flex-direction":"column","align-items":"center",gap:"2px"}},Pl={style:{display:"flex","justify-content":"center",gap:"8px"}},zl={style:{display:"flex","justify-content":"center",gap:"8px"}},$l=J(()=>V("div",{class:"section-title",width:"100%"},"老人基本信息",-1)),El=J(()=>V("div",{class:"section-title",width:"100%"},"监护人信息",-1)),Jl=J(()=>V("div",{class:"section-title",width:"100%"},"居住信息",-1)),Rl=J(()=>V("div",{class:"section-title",width:"100%"},"费用信息",-1)),Fl={class:"payment-time-wrapper"},ql={class:"payment-input-group"},Ml={class:"payment-tags-wrapper"},Ol={key:0,style:{display:"flex","justify-content":"flex-end","margin-bottom":"16px"}},Yl=J(()=>V("div",{class:"section-title",width:"100%"},"服务信息",-1)),jl={class:"care-items-container"},Bl={class:"care-items-grid"},Kl=J(()=>V("div",{class:"section-title",width:"100%"},"合同附件",-1)),Ql=J(()=>V("div",{class:"el-upload__tip"}," 支持PDF、JPG和PNG格式文件，单个文件不超过10MB ",-1)),Gl={class:"button-container"},Hl={class:"pagination-container"},Wl={__name:"index",setup(Q){const Ce=il();function Ne(){Ce.push("/contractManage/contractTemp")}const{proxy:S}=sl(),{fee_contract_status:ke}=S.useDict("fee_contract_status"),{capability_level:Se,care_level:Ue,nursing_grade:Ie}=S.useDict("capability_level","care_level","nursing_grade"),ce=b([]),R=b(!1),xe=b(!1),G=b(!1),me=b(null),i=b(!1),ee=b(!0),Le=b(!0),pe=b([]),De=b(!0),Te=b(!0),le=b(0),M=b(""),O=b(!1),ae=b([]),te=b(0),x=b([]),C=b(null),U=b([]),T=b([]),L=b([]);async function Ae(n){if(!d.value.id){L.value=L.value.filter(t=>t.uid!==n.uid);return}try{console.log("fileid",n.id),await Vl(n.id),L.value=L.value.filter(t=>t.uid!==n.uid),K.success("附件删除成功！")}catch{K.error("附件删除失败！")}}const z=b([]);cl(T,n=>{d.value.serviceItemsJson=JSON.stringify(n||[])},{deep:!0});const H=b([]),Pe=["整理床单元","床头柜整理","床单，被套更换洗","老人衣服换洗","物品整齐摆放","出轨内衣物整理","晨间协助老人洗漱","房间内垃圾桶倾倒","老人足部洗脚","加压清洗","定时洗澡","胃管老人口腔护理","气垫床使用","协助排便","提醒，协助老人服药","每月理发","水瓶内接水","尿不湿会阴区护理","尿袋倒尿","按时喂水，提醒喝水","定时更换导尿管","生活用品清洗","护理垫，纸尿裤更换","失能老人每2小时翻身"],ze=pl({form:{},queryParams:{pageNum:1,pageSize:10,contractNo:null,elderName:null,signTime:[],contractStarttime:[],contractEndtime:null},currentUser:{},rules:{contractNo:[{required:!0,message:"合同编号不能为空",trigger:"blur"}],elderId:[{required:!0,message:"老人ID不能为空",trigger:"blur"}],orgName:[{required:!0,message:"养老机构名称不能为空",trigger:"blur"}],signTime:[{required:!0,message:"签约时间不能为空",trigger:"blur"}],actualAmount:[{required:!0,message:"收费金额不能为空",trigger:"blur"}]}}),{queryParams:w,form:d,rules:$e,currentUser:Y}=ml(ze),de=async()=>{vl().getInfo().then(n=>{Y.value=n.user})};function F(){ee.value=!0;const n={...w.value};Il(n,w,["signTime","contractStarttime"]),delete n.signTime,delete n.contractStarttime,bl(n).then(t=>{ce.value=t.rows,le.value=t.total,ee.value=!1}),Ll().then(t=>{H.value=t.rows})}function Ee(n){H.value.map(n)}function Je(){R.value=!1,ne()}async function ne(){d.value={id:null,contractNo:null,elderId:null,orgName:null,signTime:null,contractStarttime:null,contractEndtime:null,contractPeriod:null,actualAmount:null,paymentStatus:null,paymentTime:null,collectorName:null,collectorCode:null,collectorId:null,paymentMethod:null,handlerName:null,handlerCode:null,handlerId:null,contractStatus:"1",remark:null,createTime:null,updateTime:null,createBy:null,updateBy:null},xe.value=!1,S.resetForm("contractRef"),Y.value||await de(),d.value.recorderName=Y.value.nickName}function W(){w.value.pageNum=1,F()}function Re(){S.resetForm("queryRef"),w.value.signTime=[],w.value.contractStarttime=[],W()}function Fe(n){pe.value=n.map(t=>t.id),De.value=n.length!=1,Te.value=!n.length}function qe(){ne(),U.value=[],x.value=[],T.value=[],C.value=null,typeof L<"u"&&(L.value=[]),d.value.careLevel2="",d.value.care_level_2="",d.value.serviceItemsJson="",R.value=!0,M.value="添加费用合同",i.value=!1}const Me=async()=>{var n,t;try{if(await S.$refs.contractRef.validate()){console.log("serviceItemsJson:",d.value.serviceItemsJson),console.log("careLevel2:",d.value.careLevel2),d.value.elderId&&U.value.forEach(v=>{v.elderId=d.value.elderId});const r={contract:{...d.value,contractStarttime:(n=U.value[0])==null?void 0:n.startTime,contractEndtime:(t=U.value[0])==null?void 0:t.endTime,actualAmount:U.value.reduce((v,c)=>v+c.feeStandard,0),paymentDate:JSON.stringify(x.value)},contractService:{serviceItemsJson:d.value.serviceItemsJson||JSON.stringify(T.value),careLevel:d.value.careLevel,careLevel2:d.value.careLevel2,nursingLevel:d.value.nursingLevel,abilityAssessmentResult:d.value.abilityAssessment,carePlan:d.value.carePlan,remark:d.value.remarks,recorderName:d.value.recorderName,...d.value.contractServiceId?{id:d.value.contractServiceId}:{}},feeDetails:U.value};if(r.contractService={...r.contractService,careLevel2:d.value.care_level_2||d.value.careLevel2||"",care_level_2:d.value.care_level_2||d.value.careLevel2||""},d.value.id){await hl(r);const v=d.value.id;console.log("contractId:",v),console.log("uploadedOssIds:",z.value),z.value.length&&v&&await he(z.value,v),S.$modal.msgSuccess("修改成功")}else{let v=await Cl(r).then(c=>c.data.contractid);z.value.length&&await he(z.value,v),S.$modal.msgSuccess("新增成功")}R.value=!1,z.value=[],F()}}catch(s){console.log(s),S.$modal.msgError("保存失败")}};function Oe(n){const t=n.id||pe.value;S.$modal.confirm('是否确认删除费用合同编号为"'+t+'"的数据项？').then(function(){return _l(t)}).then(()=>{F(),S.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ve(n){G.value=!0,me.value=n.id,openExport.value=!1}function Ye({row:n,rowIndex:t}){return t%2===1?"alternate-row":""}function je(n){S.$modal.confirm('是否确认终止费用合同编号为"'+n.contractNo+'"的数据项？').then(function(){return yl({id:n.id,contractStatus:2})}).then(()=>{F(),K.success("终止合同成功")}).catch(()=>{err&&err!=="cancel"&&err!=="close"&&err!=="cancelled"&&err.message!=="cancel"&&K.error("终止合同失败")})}const oe=async(n,t)=>{ne();const s=t.id;n==="renew"&&(U.value=[],x.value=[],T.value=[],C.value=null,typeof L<"u"&&(L.value=[]),d.value.careLevel2="",d.value.care_level_2="");try{const r=await kl(s),{contract:v,contractService:c,feeDetails:P}=r.data||{};if(n==="renew"){const u={...v};delete u.id,u.contractNo="",u.attachments=[],d.value=u}else d.value=v||{};if(n!=="renew"&&s){const u=await Sl({elderId:s});L.value=Array.isArray(u==null?void 0:u.rows)?u.rows.map(p=>({...p,name:p.fileName||p.name||p.originalName||"附件",url:p.url||p.fileUrl})):[]}if(c){const u={contractServiceId:n!=="renew"?c.id:void 0,careLevel:c.careLevel,nursingLevel:c.nursingLevel,abilityAssessment:c.abilityAssessmentResult,carePlan:c.carePlan,remarks:c.remark,recorderName:c.recorderName,serviceItemsJson:c.serviceItemsJson||JSON.stringify(T.value),careLevel2:c.careLevel2,care_level_2:c.care_level_2||c.careLevel2||""};if(d.value={...d.value,...u},c.serviceItemsJson)try{T.value=JSON.parse(c.serviceItemsJson)}catch{T.value=[]}}if(n==="renew"?U.value=Array.isArray(P)?P.map(u=>{const p={...u};return delete p.id,p}):[]:U.value=Array.isArray(P)?P:[],v!=null&&v.paymentDate)try{x.value=JSON.parse(v.paymentDate)}catch{x.value=[]}else x.value=[];if(v!=null&&v.elderId)try{const u=await Ul(v.elderId),p=u.data||u.rows&&u.rows[0];p&&(d.value={...d.value,elderName:p.elderName,idCard:p.idCard,age:p.age,gender:p.gender==="1"?"男":p.gender==="0"?"女":"-",phone:p.phone,elderCode:p.elderCode,...n==="renew"?{elderId:p.id}:{}})}catch{}n==="renew"&&(Y.value||await de(),d.value.recorderName=Y.value.nickName),R.value=!0,M.value=n==="view"?"查看合同信息":n==="update"?"修改合同信息":"合同续签",i.value=n==="view"}catch(r){console.error("合同操作处理失败:",r),S.$modal.msgError("获取合同信息失败")}},fe=n=>{oe("view",n)},Be=n=>{oe("update",n)},ge=n=>{oe("renew",n)},N=b({pageNum:1,pageSize:10,elderName:"",idCard:"",gender:""}),Ke=()=>{ae.value=[],N.value={pageNum:1,pageSize:10,elderName:"",idCard:""},te.value=0,O.value=!0,j()},j=async()=>{try{const n={...N.value},t=await Nl(n);ae.value=t.rows,te.value=t.total}catch(n){O.value&&K.error("获取老人列表失败"+n)}},re=()=>{N.value.pageNum=1,j()},Qe=()=>{N.value={pageNum:1,pageSize:10,elderName:"",idCard:"",gender:""},j()},Ge=n=>{d.value={...d.value,elderName:n.elderName,idCard:n.idCard,age:n.age,gender:n.gender==="0"?"男":"女",phone:n.phone,elderCode:n.elderCode,elderId:n.id},d.value&&(d.value.elderId=n.id),O.value=!1},He=()=>{if(C.value){let n="";if(typeof C.value=="string")n=C.value;else if(C.value instanceof Date){const t=C.value.getFullYear(),s=String(C.value.getMonth()+1).padStart(2,"0"),r=String(C.value.getDate()).padStart(2,"0");n=`${t}-${s}-${r}`}n&&!x.value.includes(n)&&x.value.push(n),C.value=null}},We=n=>{const t=x.value.indexOf(n);t!==-1&&x.value.splice(t,1)},Xe=()=>{U.value.push({feeItem:H.value,feeStandard:0,startTime:"",endTime:"",actualAmount:0,discount:0})},Ze=n=>{U.value.splice(n,1)},el=async n=>{const t=new FormData;t.append("file",n.file),t.append("category","contract_manage"),t.append("attachmentType","contract_attachment");try{const s=await wl(t);s&&s.data?(L.value.push({fileName:n.file.name,filePath:s.data.filePath||"",ossid:s.data.ossId}),z.value.push(s.data.ossId),n.onSuccess(s,n.file)):n.onError("上传失败")}catch(s){n.onError(s)}};return F(),de(),(n,t)=>{const s=y("el-input"),r=y("el-form-item"),v=y("el-date-picker"),c=y("el-button"),P=y("el-form"),u=y("el-col"),p=y("el-row"),g=y("el-table-column"),be=y("dict-tag"),ue=y("el-table"),ll=y("pagination"),k=y("el-option"),q=y("el-select"),al=y("el-tag"),_e=y("el-input-number"),tl=y("el-checkbox"),dl=y("el-checkbox-group"),nl=y("el-upload"),ie=y("el-dialog"),ol=y("el-pagination"),A=Ve("hasPermi"),rl=Ve("loading");return f(),D("div",Dl,[I(e(P,{ref:"queryRef",inline:!0,model:o(w),"label-width":"68px"},{default:a(()=>[e(r,{label:"用户姓名",prop:"elderName"},{default:a(()=>[e(s,{modelValue:o(w).elderName,"onUpdate:modelValue":t[0]||(t[0]=l=>o(w).elderName=l),clearable:"",placeholder:"请输入用户姓名",style:{width:"140px"},onKeyup:X(W,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"合同编号",prop:"contractNo"},{default:a(()=>[e(s,{modelValue:o(w).contractNo,"onUpdate:modelValue":t[1]||(t[1]=l=>o(w).contractNo=l),clearable:"",placeholder:"请输入合同编号",style:{width:"140px"},onKeyup:X(W,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"签约时间",prop:"signTime"},{default:a(()=>[e(v,{modelValue:o(w).signTime,"onUpdate:modelValue":t[2]||(t[2]=l=>o(w).signTime=l),clearable:"","end-placeholder":"结束日期","range-separator":"至","start-placeholder":"开始日期",style:{width:"240px"},type:"daterange","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(r,{label:"合同周期",prop:"contractTime"},{default:a(()=>[e(v,{modelValue:o(w).contractStarttime,"onUpdate:modelValue":t[3]||(t[3]=l=>o(w).contractStarttime=l),clearable:"",endPlaceholder:"合同周期止","range-separator":"至",startPlaceholder:"合同周期起",style:{width:"240px"},type:"daterange","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(r,null,{default:a(()=>[e(c,{icon:"Search",type:"primary",onClick:W},{default:a(()=>[_("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:Re},{default:a(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[we,Le.value]]),e(p,{gutter:10,class:"mb8"},{default:a(()=>[e(u,{span:1.5},{default:a(()=>[e(c,{icon:"Document",plain:"",type:"primary",onClick:Ne},{default:a(()=>[_("合同模板")]),_:1})]),_:1}),e(u,{span:1.5},{default:a(()=>[e(c,{icon:"Plus",plain:"",type:"primary",onClick:qe},{default:a(()=>[_("新建合同")]),_:1})]),_:1})]),_:1}),I((f(),h(ue,{data:ce.value,"row-class-name":Ye,onSelectionChange:Fe,border:"",stripe:""},{default:a(()=>[e(g,{align:"center",label:"序号",type:"index",width:"55"}),e(g,{align:"center",label:"合同编号",prop:"contractNo",width:"150"}),e(g,{align:"center",label:"签约时间",prop:"signTime",width:"180"},{default:a(l=>[V("span",null,Z(n.parseTime(l.row.signTime,"{y}-{m}-{d}")),1)]),_:1}),e(g,{align:"center",label:"老人姓名",prop:"elderName"}),e(g,{align:"center",label:"老人编号",prop:"elderCode"}),e(g,{align:"center",label:"养老机构",prop:"orgName"}),e(g,{align:"center",label:"床位号",prop:"bedNo"}),e(g,{align:"center",label:"合同总额",prop:"actualAmount"}),e(g,{align:"center",label:"合同开始时间",prop:"contractStarttime",width:"120"},{default:a(l=>[V("span",null,Z(n.parseTime(l.row.contractStarttime,"{y}-{m}-{d}")),1)]),_:1}),e(g,{align:"center",label:"合同结束时间",prop:"contractEndtime",width:"120"},{default:a(l=>[V("span",null,Z(n.parseTime(l.row.contractEndtime,"{y}-{m}-{d}")),1)]),_:1}),e(g,{align:"center",label:"经办人",prop:"handlerName"}),e(g,{align:"center",label:"状态",prop:"contractStatus"},{default:a(l=>[e(be,{options:o(ke),value:l.row.contractStatus},null,8,["options","value"])]),_:1}),e(g,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"160"},{default:a(l=>[l.row.contractStatus==2?(f(),D("div",Tl,[I((f(),h(c,{link:"",type:"primary",onClick:m=>fe(l.row)},{default:a(()=>[_("查看")]),_:2},1032,["onClick"])),[[A,["contract:contract:detail"]]]),I((f(),h(c,{link:"",type:"primary",onClick:m=>ve(l.row)},{default:a(()=>[_("打印")]),_:2},1032,["onClick"])),[[A,["contract:contract:export"]]]),I((f(),h(c,{link:"",type:"primary",onClick:m=>ge(l.row)},{default:a(()=>[_("续签")]),_:2},1032,["onClick"])),[[A,["contract:contract:renew"]]])])):(f(),D("div",Al,[V("div",Pl,[I((f(),h(c,{link:"",type:"primary",onClick:m=>fe(l.row)},{default:a(()=>[_("查看")]),_:2},1032,["onClick"])),[[A,["contract:contract:detail"]]]),I((f(),h(c,{link:"",type:"primary",onClick:m=>Be(l.row)},{default:a(()=>[_("修改")]),_:2},1032,["onClick"])),[[A,["contract:contract:edit"]]]),I((f(),h(c,{link:"",type:"primary",onClick:m=>Oe(l.row)},{default:a(()=>[_("删除")]),_:2},1032,["onClick"])),[[A,["contract:contract:remove"]]])]),V("div",zl,[I((f(),h(c,{link:"",type:"primary",onClick:m=>ve(l.row)},{default:a(()=>[_("打印")]),_:2},1032,["onClick"])),[[A,["contract:contract:export"]]]),I((f(),h(c,{link:"",type:"primary",onClick:m=>je(l.row)},{default:a(()=>[_("终止")]),_:2},1032,["onClick"])),[[A,["contract:contract:stop"]]]),I((f(),h(c,{link:"",type:"primary",onClick:m=>ge(l.row)},{default:a(()=>[_("续签")]),_:2},1032,["onClick"])),[[A,["contract:contract:renew"]]])])]))]),_:1})]),_:1},8,["data"])),[[rl,ee.value]]),I(e(ll,{limit:o(w).pageSize,"onUpdate:limit":t[4]||(t[4]=l=>o(w).pageSize=l),page:o(w).pageNum,"onUpdate:page":t[5]||(t[5]=l=>o(w).pageNum=l),total:le.value,onPagination:F},null,8,["limit","page","total"]),[[we,le.value>0]]),e(ie,{modelValue:R.value,"onUpdate:modelValue":t[33]||(t[33]=l=>R.value=l),"close-on-click-modal":!1,title:M.value,"append-to-body":"",width:"1200px"},{default:a(()=>[e(P,{ref:"contractRef",model:o(d),rules:o($e),class:"section","label-width":"120px"},{default:a(()=>[$l,e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"老人姓名",prop:"elderName"},{default:a(()=>[e(s,{modelValue:o(d).elderName,"onUpdate:modelValue":t[6]||(t[6]=l=>o(d).elderName=l),disabled:i.value,placeholder:"点击选择老人",readonly:"",onClick:Ke},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"身份证号",prop:"idCard"},{default:a(()=>[e(s,{modelValue:o(d).idCard,"onUpdate:modelValue":t[7]||(t[7]=l=>o(d).idCard=l),disabled:i.value,readonly:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"年龄",prop:"age"},{default:a(()=>[e(s,{modelValue:o(d).age,"onUpdate:modelValue":t[8]||(t[8]=l=>o(d).age=l),disabled:i.value,readonly:""},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"性别",prop:"gender"},{default:a(()=>[e(s,{modelValue:o(d).gender,"onUpdate:modelValue":t[9]||(t[9]=l=>o(d).gender=l),disabled:i.value,readonly:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"联系电话",prop:"phone"},{default:a(()=>[e(s,{modelValue:o(d).phone,"onUpdate:modelValue":t[10]||(t[10]=l=>o(d).phone=l),disabled:i.value,readonly:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"老人编号",prop:"elderCode"},{default:a(()=>[e(s,{modelValue:o(d).elderCode,"onUpdate:modelValue":t[11]||(t[11]=l=>o(d).elderCode=l),disabled:i.value||M.value.includes("修改"),readonly:""},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),El,e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"监护人姓名",prop:"guardianName"},{default:a(()=>[e(s,{modelValue:o(d).guardianName,"onUpdate:modelValue":t[12]||(t[12]=l=>o(d).guardianName=l),disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"与老人关系",prop:"guardianRelation"},{default:a(()=>[e(s,{modelValue:o(d).guardianRelation,"onUpdate:modelValue":t[13]||(t[13]=l=>o(d).guardianRelation=l),disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"监护人电话",prop:"guardianPhone"},{default:a(()=>[e(s,{modelValue:o(d).guardianPhone,"onUpdate:modelValue":t[14]||(t[14]=l=>o(d).guardianPhone=l),disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"监护人身份证",prop:"guardianIdcard"},{default:a(()=>[e(s,{modelValue:o(d).guardianIdcard,"onUpdate:modelValue":t[15]||(t[15]=l=>o(d).guardianIdcard=l),disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:16},{default:a(()=>[e(r,{label:"监护人地址",prop:"guardianAddress"},{default:a(()=>[e(s,{modelValue:o(d).guardianAddress,"onUpdate:modelValue":t[16]||(t[16]=l=>o(d).guardianAddress=l),disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),Jl,e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"养老机构",prop:"orgName"},{default:a(()=>[e(s,{modelValue:o(d).orgName,"onUpdate:modelValue":t[17]||(t[17]=l=>o(d).orgName=l),disabled:i.value,placeholder:"请输入养老机构"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"床位号",prop:"bedNo"},{default:a(()=>[e(s,{modelValue:o(d).bedNo,"onUpdate:modelValue":t[18]||(t[18]=l=>o(d).bedNo=l),disabled:i.value,placeholder:"请输入床位号"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"房间类型",prop:"roomType"},{default:a(()=>[e(q,{modelValue:o(d).roomType,"onUpdate:modelValue":t[19]||(t[19]=l=>o(d).roomType=l),disabled:i.value,placeholder:"请选择房间类型"},{default:a(()=>[e(k,{label:"单人间",value:"单人间"}),e(k,{label:"双人间",value:"双人间"}),e(k,{label:"多人间",value:"多人间"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),Rl,e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"合同编号",prop:"contractNo"},{default:a(()=>[e(s,{modelValue:o(d).contractNo,"onUpdate:modelValue":t[20]||(t[20]=l=>o(d).contractNo=l),disabled:i.value||M.value.includes("修改"),placeholder:"请输入合同编号"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"签约时间",prop:"signTime"},{default:a(()=>[e(v,{modelValue:o(d).signTime,"onUpdate:modelValue":t[21]||(t[21]=l=>o(d).signTime=l),disabled:i.value,placeholder:"请选择签约时间",type:"date"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"合同开始时间",prop:"contractStarttime"},{default:a(()=>[e(v,{modelValue:o(d).contractStarttime,"onUpdate:modelValue":t[22]||(t[22]=l=>o(d).contractStarttime=l),disabled:i.value,placeholder:"请选择合同开始时间",style:{width:"100%"},type:"date"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"合同结束时间",prop:"contractEndtime"},{default:a(()=>[e(v,{modelValue:o(d).contractEndtime,"onUpdate:modelValue":t[23]||(t[23]=l=>o(d).contractEndtime=l),disabled:i.value,placeholder:"请选择合同结束时间",style:{width:"100%"},type:"date"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"支付类型",prop:"paymentType"},{default:a(()=>[e(q,{modelValue:o(d).paymentType,"onUpdate:modelValue":t[24]||(t[24]=l=>o(d).paymentType=l),disabled:i.value,placeholder:"请选择支付类型"},{default:a(()=>[e(k,{label:"按季支付",value:"按季支付"}),e(k,{label:"按月支付",value:"按月支付"}),e(k,{label:"按年支付",value:"按年支付"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(u,{span:16},{default:a(()=>[e(r,{label:"支付时间",prop:"paymentDates"},{default:a(()=>[V("div",Fl,[V("div",ql,[e(v,{modelValue:C.value,"onUpdate:modelValue":t[25]||(t[25]=l=>C.value=l),disabled:i.value,placeholder:"选择支付时间",size:"small",style:{width:"180px"},type:"date"},null,8,["modelValue","disabled"]),e(c,{disabled:i.value||!C.value,size:"small",type:"primary",onClick:He},{default:a(()=>[_("添加")]),_:1},8,["disabled"])]),V("div",Ml,[(f(!0),D($,null,E(x.value,l=>(f(),h(al,{key:l,class:"payment-date-tag",closable:"",size:"small",onClose:m=>We(l)},{default:a(()=>[_(Z(l),1)]),_:2},1032,["onClose"]))),128))])])]),_:1})]),_:1})]),_:1}),e(ue,{data:U.value,style:{width:"100%","margin-bottom":"16px"},border:"",stripe:""},{default:a(()=>[e(g,{label:"费用项目",prop:"feeItem",width:"120"},{default:a(l=>[e(q,{modelValue:l.row.feeItem,"onUpdate:modelValue":m=>l.row.feeItem=m,disabled:i.value,placeholder:"请选择",size:"small",onChange:m=>Ee(l.row)},{default:a(()=>[(f(!0),D($,null,E(H.value,(m,B)=>(f(),h(k,{key:B,label:m.itemName,value:m.itemName},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),_:1}),e(g,{label:"收费标准",prop:"feeStandard",width:"132"},{default:a(l=>[e(_e,{modelValue:l.row.feeStandard,"onUpdate:modelValue":m=>l.row.feeStandard=m,disabled:i.value,size:"small"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(g,{label:"说明",prop:"description",width:"180"},{default:a(l=>[e(s,{modelValue:l.row.description,"onUpdate:modelValue":m=>l.row.description=m,disabled:i.value,size:"small"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(g,{label:"开始时间",prop:"startTime",width:"120"},{default:a(l=>[e(v,{modelValue:l.row.startTime,"onUpdate:modelValue":m=>l.row.startTime=m,disabled:i.value,size:"small",type:"date"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(g,{label:"结束时间",prop:"endTime",width:"120"},{default:a(l=>[e(v,{modelValue:l.row.endTime,"onUpdate:modelValue":m=>l.row.endTime=m,disabled:i.value,size:"small",type:"date"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(g,{label:"优惠",prop:"discount",width:"100"},{default:a(l=>[e(s,{modelValue:l.row.discount,"onUpdate:modelValue":m=>l.row.discount=m,disabled:i.value,size:"small"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(g,{label:"实际缴纳",prop:"actualAmount",width:"132"},{default:a(l=>[e(_e,{modelValue:l.row.actualAmount,"onUpdate:modelValue":m=>l.row.actualAmount=m,disabled:i.value,size:"small"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(g,{label:"备注",prop:"remark",width:"120"},{default:a(l=>[e(s,{modelValue:l.row.remark,"onUpdate:modelValue":m=>l.row.remark=m,disabled:i.value,size:"small"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),i.value?se("",!0):(f(),h(g,{key:0,fixed:"right",label:"操作",width:"100"},{default:a(l=>[e(c,{size:"small",type:"danger",onClick:m=>Ze(l.$index)},{default:a(()=>[_("删除")]),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),i.value?se("",!0):(f(),D("div",Ol,[e(c,{icon:"Plus",size:"small",type:"primary",onClick:Xe},{default:a(()=>[_("添加费用明细")]),_:1})])),Yl,e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"照护等级",prop:"careLevel"},{default:a(()=>[e(q,{modelValue:o(d).careLevel,"onUpdate:modelValue":t[26]||(t[26]=l=>o(d).careLevel=l),disabled:i.value,placeholder:"请选择照护等级"},{default:a(()=>[e(k,{label:"全部",value:"全部"}),(f(!0),D($,null,E(o(Ue),l=>(f(),h(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"护理等级",prop:"nursingLevel"},{default:a(()=>[e(q,{modelValue:o(d).nursingLevel,"onUpdate:modelValue":t[27]||(t[27]=l=>o(d).nursingLevel=l),disabled:i.value,placeholder:"请选择护理等级"},{default:a(()=>[e(k,{label:"全部",value:"全部"}),(f(!0),D($,null,E(o(Ie),l=>(f(),h(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:a(()=>[e(r,{label:"能力评估",prop:"abilityAssessment"},{default:a(()=>[e(q,{modelValue:o(d).abilityAssessment,"onUpdate:modelValue":t[28]||(t[28]=l=>o(d).abilityAssessment=l),disabled:i.value,placeholder:"请选择能力评估"},{default:a(()=>[e(k,{label:"全部",value:"全部"}),(f(!0),D($,null,E(o(Se),l=>(f(),h(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(r,{label:` ${o(d).nursingLevel||"全部"} `,class:"care-items-section"},{default:a(()=>[V("div",jl,[V("div",Bl,[e(dl,{modelValue:T.value,"onUpdate:modelValue":t[29]||(t[29]=l=>T.value=l),disabled:i.value},{default:a(()=>[(f(),D($,null,E(3,(l,m)=>V("div",{key:m,class:"care-column"},[(f(!0),D($,null,E(Pe.filter((B,ye)=>ye%3===m),(B,ye)=>(f(),h(tl,{key:B,label:B,class:"care-checkbox"},null,8,["label"]))),128))])),64))]),_:1},8,["modelValue","disabled"])])])]),_:1},8,["label"]),e(r,{label:` ${o(d).careLevel||"全部"} `,prop:"carePlan"},{default:a(()=>[e(s,{modelValue:o(d).care_level_2,"onUpdate:modelValue":t[30]||(t[30]=l=>o(d).care_level_2=l),disabled:i.value,rows:6,placeholder:"请输入护理计划",type:"textarea"},null,8,["modelValue","disabled"])]),_:1},8,["label"]),e(r,{label:"其他事项",prop:"remark"},{default:a(()=>[e(s,{modelValue:o(d).remark,"onUpdate:modelValue":t[31]||(t[31]=l=>o(d).remark=l),disabled:i.value,rows:3,placeholder:"请输入其他事项",type:"textarea"},null,8,["modelValue","disabled"])]),_:1}),Kl,e(r,{label:"合同附件",prop:"attachments"},{default:a(()=>[e(nl,{disabled:i.value,"file-list":L.value,"http-request":el,"on-remove":Ae,"show-file-list":!0,accept:".pdf,.jpg,.jpeg,.png",action:"#",class:"contract-uploader",multiple:""},{tip:a(()=>[Ql]),default:a(()=>[e(c,{disabled:i.value,type:"primary"},{default:a(()=>[_("点击上传")]),_:1},8,["disabled"])]),_:1},8,["disabled","file-list"])]),_:1}),e(p,null,{default:a(()=>[e(u,{span:8},{default:a(()=>[e(r,{label:"录入人员",prop:"recorderName"},{default:a(()=>[e(s,{modelValue:o(d).recorderName,"onUpdate:modelValue":t[32]||(t[32]=l=>o(d).recorderName=l),disabled:i.value,placeholder:"请输入录入人员"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),V("div",Gl,[e(c,{onClick:Je},{default:a(()=>[_("取 消")]),_:1}),i.value?se("",!0):(f(),h(c,{key:0,type:"primary",onClick:Me},{default:a(()=>[_("提 交")]),_:1}))])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),e(ie,{modelValue:O.value,"onUpdate:modelValue":t[38]||(t[38]=l=>O.value=l),class:"elder-dialog-custom",title:"选择老人",width:"900px"},{default:a(()=>[e(P,{inline:!0,model:N.value,class:"elder-search-form"},{default:a(()=>[e(r,{label:"姓名"},{default:a(()=>[e(s,{modelValue:N.value.elderName,"onUpdate:modelValue":t[34]||(t[34]=l=>N.value.elderName=l),clearable:"",placeholder:"请输入老人姓名",onKeyup:X(re,["enter"])},null,8,["modelValue"])]),_:1}),e(r,{label:"身份证号"},{default:a(()=>[e(s,{modelValue:N.value.idCard,"onUpdate:modelValue":t[35]||(t[35]=l=>N.value.idCard=l),clearable:"",placeholder:"请输入身份证号",onKeyup:X(re,["enter"])},null,8,["modelValue"])]),_:1}),e(r,null,{default:a(()=>[e(c,{icon:"Search",type:"primary",onClick:re},{default:a(()=>[_("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:Qe},{default:a(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(ue,{data:ae.value,"highlight-current-row":"",onRowClick:Ge,border:"",stripe:""},{default:a(()=>[e(g,{label:"姓名",prop:"elderName",width:"120"}),e(g,{label:"身份证号",prop:"idCard",width:"200"}),e(g,{label:"年龄",prop:"age",width:"80"}),e(g,{label:"性别",prop:"gender",width:"80"},{default:a(l=>[e(be,{options:n.sys_user_sex,value:l.row.gender},null,8,["options","value"])]),_:1}),e(g,{label:"联系电话",prop:"phone",width:"150"}),e(g,{label:"老人编号",prop:"elderCode"})]),_:1},8,["data"]),V("div",Hl,[e(ol,{"current-page":N.value.pageNum,"onUpdate:currentPage":t[36]||(t[36]=l=>N.value.pageNum=l),"page-size":N.value.pageSize,"onUpdate:pageSize":t[37]||(t[37]=l=>N.value.pageSize=l),"page-sizes":[5,10,20,50],total:te.value,background:"",layout:"total, sizes, prev, pager, next, jumper",style:{"text-align":"right"},onCurrentChange:j,onSizeChange:j},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"]),e(ie,{modelValue:G.value,"onUpdate:modelValue":t[40]||(t[40]=l=>G.value=l),title:"合同打印预览","close-on-click-modal":!1,"append-to-body":"",width:"1000px","destroy-on-close":"",class:"contract-print-dialog",fullscreen:!1},{default:a(()=>[e(xl,{"contract-id":me.value,onClose:t[39]||(t[39]=l=>G.value=!1)},null,8,["contract-id"])]),_:1},8,["modelValue"])])}}},ta=ul(Wl,[["__scopeId","data-v-d9f317be"]]);export{ta as default};
