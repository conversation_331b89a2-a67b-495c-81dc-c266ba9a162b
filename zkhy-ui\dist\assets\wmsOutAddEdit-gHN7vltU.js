import{_ as Pe,d as Ue,r as h,u as Oe,a as Ie,z as b,w as Se,F as Me,e as p,I as Ne,J as qe,c as N,i as _,f as e,j as k,k as q,t as x,h as l,n as m,o as f,K as Q,L as R,l as B,G as y,v as Ae,x as Ye,M as te,E as $e}from"./index-B0qHf98Y.js";import{d as ze,a as le,b as Te,e as Ee,f as Qe}from"./wmscheckinOut-C2B7FNiS.js";const oe=I=>(Ae("data-v-17e76f1d"),I=I(),Ye(),I),Re={class:"warehousing-detail"},Be={class:"page-title"},Fe={class:"action-buttons"},Le=oe(()=>_("h3",null,"基本信息",-1)),je={class:"detail-header"},Ke=oe(()=>_("h3",null,"出库明细",-1)),Ge={class:"total-amount"},He={style:{color:"#D9001B"}},Je={style:{color:"#D9001B"}},We={class:"pagination"},Xe={__name:"wmsOutAddEdit",setup(I){const{proxy:F}=Ue(),{stock_out_type:ne,rk_unit:ue,goods_type:re}=F.useDict("stock_out_type","rk_unit","goods_type"),S=h(!1),C=Oe();Ie();const L={add:"新增出库单",edit:"编辑出库单",view:"出库单详情"};if(!C.meta.title||C.meta.title==="出库管理"){const t=C.query.type;t&&L[t]&&(document.title=L[t])}const P=b(()=>C.query.type||"view"),de=b(()=>C.query.id),A=b(()=>P.value==="add"),ie=b(()=>P.value==="edit"),s=b(()=>P.value==="view"),se=b(()=>({add:"新增出库单",edit:"编辑出库单",view:"出库单详情"})[P.value]),r=h({}),v=h([]),j=h(null),ce={stockOutDate:[{required:!0,message:"请选择出库日期",trigger:"blur"}],stockOutPerson:[{required:!0,message:"请输入出库人员",trigger:"blur"}],stockOutType:[{required:!0,message:"请选择出库类型",trigger:"blur"}],manufacturer:[{required:!0,message:"请输入生产厂家",trigger:"blur"}],creator:[{required:!0,message:"请输入制单人",trigger:"blur"}],createDate:[{required:!0,message:"请选择制单日期",trigger:"blur"}]},K=b(()=>v.value.reduce((t,o)=>t+(o.purchaseAmount||0),0)),G=b(()=>v.value.reduce((t,o)=>t+(o.retailAmount||0),0)),Y=t=>{if(t.quantity>t.currentQuantity){y.warning(`出库数量不能超过库存数量(${t.currentQuantity})`),t.quantity=t.currentQuantity;return}t.purchaseAmount=(t.quantity||0)*(t.purchasePrice||0),t.retailAmount=(t.quantity||0)*(t.retailPrice||0)},pe=t=>{v.value.splice(t,1),H()},H=()=>{v.value.forEach((t,o)=>{t.index=o+1})},U=h(!1),g=h({goodsCategory:"",likeParamStr:"",pageNum:1,pageSize:50,status:1}),J=h([]),W=h(0),w=h([]),me=()=>{U.value=!0,O()},ve=()=>{g.value={goodsCategory:"",likeParamStr:"",pageNum:1,pageSize:50,status:1},O()},O=async()=>{const t=await le({...g.value});J.value=t.rows,W.value=t.total},ge=t=>{w.value=t},fe=()=>{if(w.value.length===0){y.warning("请至少选择一件物品");return}const t=new Set(v.value.map(n=>n.medicationCode)),o=w.value.filter(n=>!t.has(n.medicineCode)).map(n=>({medicationCode:n.medicineCode,medicationName:n.medicineName,manufacturer:n.manufacturer,quantity:0,unit:"箱",purchasePrice:n.purchasePrice,retailPrice:n.retailPrice,batchNo:n.approvalNumber,expiryDate:n.expiryWarningDays,purchaseAmount:0,retailAmount:0,currentQuantity:n.currentQuantity}));if(o.length===0){y.warning("物品已经添加，请勿重复添加！");return}if(o.length<w.value.length){const n=w.value.length-o.length;y.warning(`有${n}件物品已存在于明细中，未重复添加`)}v.value=[...v.value,...o],H(),U.value=!1,w.value=[]},_e=t=>{g.value.pageSize=t,O()},ye=t=>{g.value.pageNum=t,O()},he=async()=>{r.value={stockOutNo:A.value?await be():"",stockOutDate:te().format("YYYY-MM-DD"),stockOutPerson:"",stockOutType:"",manufacturer:"",creator:"",createDate:te().format("YYYY-MM-DD"),remark:""},v.value=[]},be=async()=>{const t=await Te({prefix:"CK"});if(t.code==200)return t.msg},X=async()=>{if(A.value){await he();return}try{const t=await ze(de.value);r.value=t.data,v.value=r.value.details}catch(t){y.error("获取数据失败: "+t.message),$()}},Ve=async()=>{try{if(await j.value.validate(),v.value.length===0){y.warning("请至少添加一条出库明细");return}const t={...r.value,details:v.value,purchaseAmount:K.value.toFixed(2),retailAmount:G.value.toFixed(2)};if(S.value=!0,A.value)(await Ee(t)).code==200?y.success("出库成功"):y.error("出库失败");else if(ie.value){const n=(await le({pageSize:1e4,status:1})).rows,c=ke(v.value,n);if(!c.isValid){await Ce(c.messages),S.value=!1;return}console.log("验证通过",t),(await Qe(t)).code==200?y.success("修改成功"):y.error("修改失败")}S.value=!1,$()}catch(t){if(t!=null&&t.errors)return}},ke=(t,o)=>{const n={isValid:!0,messages:[]},c={};return o.forEach(i=>{c[i.medicineCode]=i.currentQuantity}),t.forEach(i=>{const V=c[i.medicationCode]||0;i.quantity>V&&(n.isValid=!1,n.messages.push(`物品 ${i.medicationName} (编码: ${i.medicationCode}) 出库数量 ${i.quantity} 超过库存数量 ${V}`))}),n},Ce=async t=>{const o=`
    <div style="max-height: 300px; overflow-y: auto;">
      <ul style="margin: 0; padding-left: 20px;">
        ${t.map(n=>`<li>${n}</li>`).join("")}
      </ul>
    </div>
  `;await $e.alert(o,"库存不足",{dangerouslyUseHTMLString:!0,type:"warning",confirmButtonText:"确定"})},$=()=>{F.$tab.closeOpenPage({path:"/warehouse/wmscheckout"})};return Se(()=>C.query,t=>{t.type&&t.type!==P.value&&X()},{immediate:!0}),Me(()=>{X()}),(t,o)=>{const n=p("el-button"),c=p("el-form-item"),i=p("el-col"),V=p("el-row"),z=p("el-date-picker"),D=p("el-input"),M=p("el-option"),T=p("el-select"),Z=p("el-form"),ee=p("el-card"),u=p("el-table-column"),E=p("el-input-number"),ae=p("el-table"),we=p("el-pagination"),De=p("el-dialog"),xe=Ne("loading");return qe((f(),N("div",Re,[_("h2",Be,x(se.value),1),_("div",Fe,[s.value?q("",!0):(f(),k(n,{key:0,type:"primary",onClick:Ve},{default:l(()=>[m("直接出库")]),_:1})),e(n,{onClick:$,icon:"back"},{default:l(()=>[m("返回")]),_:1})]),e(ee,{class:"form-card"},{default:l(()=>[Le,e(Z,{model:r.value,"label-width":"100px",rules:ce,ref_key:"formRef",ref:j},{default:l(()=>[e(V,{gutter:20},{default:l(()=>[e(i,{span:8,prop:"stockOutNo"},{default:l(()=>[e(c,{label:"出库单号"},{default:l(()=>[m(x(r.value.stockOutNo),1)]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(c,{label:"出库日期",prop:"stockOutDate"},{default:l(()=>[e(z,{modelValue:r.value.stockOutDate,"onUpdate:modelValue":o[0]||(o[0]=a=>r.value.stockOutDate=a),type:"date","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(c,{label:"出库人员",prop:"stockOutPerson"},{default:l(()=>[e(D,{modelValue:r.value.stockOutPerson,"onUpdate:modelValue":o[1]||(o[1]=a=>r.value.stockOutPerson=a),disabled:s.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(c,{label:"出库类型",prop:"stockOutType"},{default:l(()=>[e(T,{modelValue:r.value.stockOutType,"onUpdate:modelValue":o[2]||(o[2]=a=>r.value.stockOutType=a),style:{width:"100%"},disabled:s.value},{default:l(()=>[(f(!0),N(Q,null,R(B(ne),a=>(f(),k(M,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(c,{label:"生产厂家",prop:"manufacturer"},{default:l(()=>[e(D,{modelValue:r.value.manufacturer,"onUpdate:modelValue":o[3]||(o[3]=a=>r.value.manufacturer=a),disabled:s.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(c,{label:"制单人",prop:"creator"},{default:l(()=>[e(D,{modelValue:r.value.creator,"onUpdate:modelValue":o[4]||(o[4]=a=>r.value.creator=a),disabled:s.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(c,{label:"制单日期",prop:"createDate"},{default:l(()=>[e(z,{modelValue:r.value.createDate,"onUpdate:modelValue":o[5]||(o[5]=a=>r.value.createDate=a),type:"date","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(i,{span:24},{default:l(()=>[e(c,{label:"备注",prop:"remark"},{default:l(()=>[e(D,{modelValue:r.value.remark,"onUpdate:modelValue":o[6]||(o[6]=a=>r.value.remark=a),type:"textarea",disabled:s.value,placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(ee,{class:"detail-card"},{default:l(()=>[_("div",je,[Ke,s.value?q("",!0):(f(),k(n,{key:0,type:"primary",onClick:me,icon:"Plus"},{default:l(()=>[m(" 添加物品 ")]),_:1}))]),e(ae,{data:v.value,border:"",style:{width:"100%"}},{default:l(()=>[e(u,{prop:"index",label:"序号",width:"60",align:"center"},{default:l(a=>[m(x(a.$index+1),1)]),_:1}),e(u,{prop:"medicationCode",label:"编码","min-width":"180",align:"center"}),e(u,{prop:"medicationName",label:"名称",width:"150",align:"center"}),e(u,{prop:"manufacturer",label:"生产厂家",width:"150",align:"center"}),e(u,{label:"出库数量",width:"120",align:"center",prop:"quantity"},{default:l(({row:a})=>[e(E,{modelValue:a.quantity,"onUpdate:modelValue":d=>a.quantity=d,min:0,max:a.currentQuantity,"controls-position":"right",onChange:d=>Y(a),disabled:s.value},null,8,["modelValue","onUpdate:modelValue","max","onChange","disabled"])]),_:1}),e(u,{label:"单位",width:"120",align:"center",prop:"unit"},{default:l(({row:a})=>[e(T,{modelValue:a.unit,"onUpdate:modelValue":d=>a.unit=d,style:{width:"100%"},disabled:s.value},{default:l(()=>[(f(!0),N(Q,null,R(B(ue),d=>(f(),k(M,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(u,{label:"采购价",width:"120",align:"center",prop:"purchasePrice"},{default:l(({row:a})=>[e(E,{modelValue:a.purchasePrice,"onUpdate:modelValue":d=>a.purchasePrice=d,min:0,precision:2,"controls-position":"right",onChange:d=>Y(a),disabled:s.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(u,{label:"零售价",width:"120",align:"center",prop:"retailPrice"},{default:l(({row:a})=>[e(E,{modelValue:a.retailPrice,"onUpdate:modelValue":d=>a.retailPrice=d,min:0,precision:2,"controls-position":"right",onChange:d=>Y(a),disabled:s.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(u,{prop:"batchNo",label:"批号",width:"120",align:"center"},{default:l(({row:a})=>[e(D,{modelValue:a.batchNo,"onUpdate:modelValue":d=>a.batchNo=d,disabled:s.value},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(u,{label:"有效期",width:"150",align:"center",prop:"expiryDate"},{default:l(({row:a})=>[e(z,{modelValue:a.expiryDate,"onUpdate:modelValue":d=>a.expiryDate=d,type:"date","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:s.value},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(u,{prop:"purchaseAmount",label:"采购金额",width:"120",align:"center"}),e(u,{prop:"retailAmount",label:"零售金额",width:"120",align:"center"}),s.value?q("",!0):(f(),k(u,{key:0,label:"操作",width:"100",align:"center",fixed:"right"},{default:l(({$index:a})=>[e(n,{type:"danger",onClick:d=>pe(a),link:""},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),_("div",Ge,[_("span",null,[m("采购金额合计: "),_("b",He,x(K.value.toFixed(2)),1),m("元")]),_("span",null,[m("零售金额合计: "),_("b",Je,x(G.value.toFixed(2)),1),m("元")])])]),_:1}),s.value?q("",!0):(f(),k(De,{key:0,modelValue:U.value,"onUpdate:modelValue":o[12]||(o[12]=a=>U.value=a),title:"添加物品",width:"70%"},{footer:l(()=>[e(n,{onClick:o[11]||(o[11]=a=>U.value=!1)},{default:l(()=>[m("取消")]),_:1}),e(n,{type:"primary",onClick:fe},{default:l(()=>[m("确定")]),_:1})]),default:l(()=>[e(Z,{inline:!0,model:g.value,class:"item-search-form"},{default:l(()=>[e(c,{label:"类别"},{default:l(()=>[e(T,{modelValue:g.value.goodsCategory,"onUpdate:modelValue":o[7]||(o[7]=a=>g.value.goodsCategory=a),placeholder:"全部",clearable:"",style:{width:"200px"}},{default:l(()=>[e(M,{label:"全部",value:""}),(f(!0),N(Q,null,R(B(re),a=>(f(),k(M,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,null,{default:l(()=>[e(D,{modelValue:g.value.likeParamStr,"onUpdate:modelValue":o[8]||(o[8]=a=>g.value.likeParamStr=a),placeholder:"请输入药品编码/药品名称",clearable:""},null,8,["modelValue"])]),_:1}),e(c,null,{default:l(()=>[e(n,{type:"primary",onClick:O,icon:"Search"},{default:l(()=>[m("查询")]),_:1}),e(n,{onClick:ve,icon:"Refresh"},{default:l(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(ae,{data:J.value,border:"",style:{width:"100%"},onSelectionChange:ge},{default:l(()=>[e(u,{type:"selection",width:"55",align:"center"}),e(u,{prop:"index",label:"序号",width:"60",align:"center"},{default:l(a=>[m(x(a.$index+1),1)]),_:1}),e(u,{prop:"medicineCode",label:"编码","min-width":"180",align:"center"}),e(u,{prop:"medicineName",label:"名称",width:"150",align:"center"}),e(u,{prop:"goodsCategory",label:"类别",width:"100",align:"center"}),e(u,{prop:"specification",label:"规格",width:"150",align:"center"}),e(u,{prop:"manufacturer",label:"生产厂家",width:"150",align:"center"}),e(u,{prop:"currentQuantity",label:"库存",width:"100",align:"center"})]),_:1},8,["data"]),_("div",We,[e(we,{background:"","current-page":g.value.pageNum,"onUpdate:currentPage":o[9]||(o[9]=a=>g.value.pageNum=a),"page-size":g.value.pageSize,"onUpdate:pageSize":o[10]||(o[10]=a=>g.value.pageSize=a),total:W.value,"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_e,onCurrentChange:ye},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"]))])),[[xe,S.value]])}}},aa=Pe(Xe,[["__scopeId","data-v-17e76f1d"]]);export{aa as default};
