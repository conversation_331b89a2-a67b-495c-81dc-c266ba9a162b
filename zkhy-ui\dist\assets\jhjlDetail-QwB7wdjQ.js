import{_ as D,r as p,d as b,e as d,c as N,o as V,f as c,h as _,i as t,t as s,l as e,n as h,D as C,v as I,x as w}from"./index-B0qHf98Y.js";const n=i=>(I("data-v-24b34053"),i=i(),w(),i),S={class:"replace-consumables"},T=n(()=>t("div",{class:"headerTitle"},[t("h2",{class:"tdColor"},"老人意外情况记录表")],-1)),j={class:"table-style"},k={style:{"text-align":"left"}},L={style:{"text-align":"left"}},B={style:{"text-align":"left"}},R={style:{"text-align":"left"}},E={style:{"text-align":"left"}},J={style:{"text-align":"left"}},P={style:{"text-align":"left"}},U={style:{"text-align":"left"}},$={style:{"text-align":"left"}},q={style:{"text-align":"left"},colspan:"3"},z={style:{"text-align":"left"},colspan:"3"},A={class:"itemDetail"},F=n(()=>t("span",null,"意外发生地址:",-1)),G={style:{"text-align":"left"},colspan:"3"},H={class:"itemDetail"},K=n(()=>t("span",null,"伤情描述:",-1)),M={style:{"text-align":"left"},colspan:"3"},O={class:"itemDetail"},Q=n(()=>t("span",null,"身体处置情况:",-1)),W={style:{"text-align":"left"},colspan:"3"},X={class:"itemDetail"},Y=n(()=>t("span",null,"生命体征情况:",-1)),Z={style:{"text-align":"left"},colspan:"3"},tt={class:"itemDetail"},et=n(()=>t("span",null,"送往医院方式及医院名称:",-1)),lt={style:{"text-align":"left"},colspan:"3"},st={class:"itemDetail"},nt=n(()=>t("span",null,"通知监护人情况:",-1)),ot={style:{"text-align":"left"},colspan:"3"},it={class:"itemDetail"},at=n(()=>t("span",null,"发生意外情况描述:",-1)),dt={style:{"text-align":"left"},colspan:"3"},ct={class:"itemDetail"},_t=n(()=>t("span",null,"意外处置参与人员:",-1)),rt={style:{"text-align":"left"},colspan:"3"},ut={class:"itemDetail"},pt=n(()=>t("span",null,"谈话记录:",-1)),ht={class:"dialog-footer"},gt={__name:"jhjlDetail",setup(i,{expose:g}){const o=p(!1),l=p({}),{proxy:f}=b(),{sys_user_sex:m}=f.useDict("sys_user_sex");return g({openDialog:r=>{o.value=!0,l.value=r.rows.data}}),(r,a)=>{const x=d("dict-tag-span"),y=d("el-button"),v=d("el-dialog");return V(),N("div",S,[c(v,{modelValue:e(o),"onUpdate:modelValue":a[1]||(a[1]=u=>C(o)?o.value=u:null),title:"详情",width:"60%"},{footer:_(()=>[t("div",ht,[c(y,{onClick:a[0]||(a[0]=u=>o.value=!1)},{default:_(()=>[h("返回")]),_:1})])]),default:_(()=>[T,t("table",j,[t("tbody",null,[t("tr",null,[t("td",k,"老人姓名:"+s(e(l).elderName||"-"),1),t("td",L,[h("老人性别: "),c(x,{options:e(m),value:e(l).gender,style:{width:"80%"}},null,8,["options","value"])]),t("td",B,"老人年龄："+s(e(l).age||"-"),1)]),t("tr",null,[t("td",R,"房间信息:"+s(e(l).buildingName?e(l).buildingName+"-"+e(l).roomNumber:"-"),1),t("td",E,"入住时间:"+s(e(l).checkInDate||"-"),1),t("td",J,"能力等级："+s(e(l).abilityLevel||"-"),1)]),t("tr",null,[t("td",P,"护理等级:"+s(e(l).careLevel||"-"),1),t("td",U,"照护等级："+s(e(l).nursingLevel||"-"),1),t("td",$,"当天护理员："+s(e(l).paramedicName||"-"),1)]),t("tr",null,[t("td",q,"意外发生时间:"+s(e(l).accidentTime||"-"),1)]),t("tr",null,[t("td",z,[t("div",A,[F,t("pre",null,s(e(l).accidentLocation||"-"),1)])])]),t("tr",null,[t("td",G,[t("div",H,[K,t("pre",null,s(e(l).injuryCondition||"-"),1)])])]),t("tr",null,[t("td",M,[t("div",O,[Q,t("pre",null,s(e(l).physicalTreatment||"-"),1)])])]),t("tr",null,[t("td",W,[t("div",X,[Y,t("pre",null,s(e(l).vitalSigns||"-"),1)])])]),t("tr",null,[t("td",Z,[t("div",tt,[et,t("pre",null,s(e(l).hospitalTransport||"-"),1)])])]),t("tr",null,[t("td",lt,[t("div",st,[nt,t("pre",null,s(e(l).guardianNotification||"-"),1)])])]),t("tr",null,[t("td",ot,[t("div",it,[at,t("pre",null,s(e(l).accidentDescription||"-"),1)])])]),t("tr",null,[t("td",dt,[t("div",ct,[_t,t("pre",null,s(e(l).handlingParticipants||"-"),1)])])]),t("tr",null,[t("td",rt,[t("div",ut,[pt,t("pre",null,s(e(l).conversationRecord||"-"),1)])])])])])]),_:1},8,["modelValue"])])}}},xt=D(gt,[["__scopeId","data-v-24b34053"]]);export{xt as default};
