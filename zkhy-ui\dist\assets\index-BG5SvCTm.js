import{_ as be,d as ve,r as u,C as we,N as _e,z as Z,e as d,c as he,o as w,f as e,j as T,k as j,h as o,l as i,p as ye,n as g,J as M,i as m,t as s,O as q}from"./index-B0qHf98Y.js";import Ne from"./bedadjustForm-CaTZfmXR.js";import Te from"./badadjustFormAudit-BjZffV6E.js";import{e as G,f as je}from"./tProcessApprovalRecord-OlTXqvFr.js";import{g as Ce}from"./user-u7DySmj3.js";import{d as J}from"./paramUtil-DJB1oWef.js";import"./tLiveBed-B9bJPM9s.js";import"./tLiveRoom-DmSXfHxo.js";import"./telderinfo-BSpoeVyZ.js";import"./yjj-DmX1NTQH.js";import"./telderAttachement-C4ARfNBy.js";const ke={class:"app-container"},De={__name:"index",setup(Be){const{proxy:C}=ve(),{bed_adjust_type:k,sys_yes_no:D,bed_auditor_type:B}=C.useDict("bed_adjust_type","sys_yes_no","bed_auditor_type"),K=u([]),O=u([]),Q=u([]),V=u(0),P=u(0),x=u(0),ee=u(),A=u(!0),U=u(!1),H=u("");u([]);const _=u();u({name:"",bedNo:"",applyDate:"",type:""});const b=u("all"),h=u(!1),y=u(!1),f=u(null);u({});const le=we({queryParams:{pageNum:1,pageSize:10,elderName:null,bedId:null,appTime:null,type:null,status:[],AdjustmentTime:[]}}),{queryParams:r}=_e(le),S=u([]),te=Z(()=>b.value==="all"?S.value:b.value==="apply"?S.value.filter(a=>a.status==="待审核"):b.value==="review"?S.value.filter(a=>a.status==="已拒绝"||a.status==="已完成"):[]);Z(()=>te.value.length),u(10),u(1);function v(){I(),R(),z(),Ce().then(a=>{console.log(a,"userinfo"),ee.value=a.data,a.data.roles.map(t=>{t.roleKey=="process_approval"?A.value=!0:A.value=!1})})}function ae(a){console.log(a,"000000000"),a=="all"?I():a=="apply"?R():a=="review"&&z()}function I(){r.value.statusList=[];const a={...r.value};J(a,r,["AdjustmentTime"]),delete a.AdjustmentTime,G(a).then(t=>{console.log(t,"alllist111"),K.value=t.rows,V.value=t.total})}function R(){const a={...r.value};J(a,r,["AdjustmentTime"]),delete a.AdjustmentTime,console.log(a,"searchParams....."),a.statusList=[],a.statusList=["PENDING","REJECTED"],G(a).then(t=>{console.log(t,"alllist2222"),O.value=t.rows,P.value=t.total})}function z(){const a={...r.value};J(a,r,["AdjustmentTime"]),delete a.AdjustmentTime,console.log(a,"searchParams....."),a.statusList=["PENDING"],G(a).then(t=>{console.log(t,"alllist3333"),Q.value=t.rows,x.value=t.total})}function ne(){v()}function oe(){ie(),v()}function ie(){r.value={elderName:null,originalBedNumber:null,adjustmentDate:null}}function W(a){f.value=a,y.value=!0,h.value=!0,_.value=a.id}function re(){h.value=!1,y.value=!1,f.value=null,v()}function ue(a){const t=a.id||t.value;C.$modal.confirm('是否确认删除编号为"'+t+'"的数据项？').then(function(){return je(t)}).then(()=>{C.$modal.msgSuccess("删除成功")}).catch(()=>{})}function de(a){U.value=!0,H.value="audit",f.value=a,_.value=a.id}function se(){U.value=!1,y.value=!1,f.value=null,v()}return v(),(a,t)=>{const X=d("el-input"),N=d("el-form-item"),pe=d("el-date-picker"),Y=d("el-option"),me=d("el-select"),ce=d("el-form"),c=d("el-button"),ge=d("el-row"),n=d("el-table-column"),p=d("dict-tag"),E=d("el-table"),L=d("pagination"),$=d("el-tab-pane"),fe=d("el-tabs");return w(),he("div",ke,[e(ce,{inline:!0,class:"search-bar",model:i(r),ref:"queryRef",onSubmit:t[4]||(t[4]=ye(()=>{},["prevent"]))},{default:o(()=>[e(N,{label:"老人姓名"},{default:o(()=>[e(X,{modelValue:i(r).elderName,"onUpdate:modelValue":t[0]||(t[0]=l=>i(r).elderName=l),placeholder:"请输入",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(N,{label:"床位号"},{default:o(()=>[e(X,{modelValue:i(r).originalBedNumber,"onUpdate:modelValue":t[1]||(t[1]=l=>i(r).originalBedNumber=l),placeholder:"请输入",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(N,{label:"申请调整时间"},{default:o(()=>[e(pe,{modelValue:i(r).AdjustmentDate,"onUpdate:modelValue":t[2]||(t[2]=l=>i(r).AdjustmentDate=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"},placeholder:"请选择",value:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),e(N,{label:"调整类型"},{default:o(()=>[e(me,{modelValue:i(r).adjustmentType,"onUpdate:modelValue":t[3]||(t[3]=l=>i(r).adjustmentType=l),placeholder:"全部",clearable:"",style:{width:"200px"}},{default:o(()=>[e(Y,{label:"全部",value:""}),e(Y,{label:"床位调整",value:"change"}),e(Y,{label:"床位对调",value:"exchange"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),e(ge,{justify:"end",style:{height:"10px"}},{default:o(()=>[e(c,{type:"primary",onClick:ne},{default:o(()=>[g("查询")]),_:1}),e(c,{onClick:oe},{default:o(()=>[g("重置")]),_:1}),e(c,{type:"primary",style:{"margin-left":"12px"},plain:"",onClick:t[5]||(t[5]=l=>h.value=!0)},{default:o(()=>[g("新增调整")]),_:1})]),_:1}),e(fe,{modelValue:b.value,"onUpdate:modelValue":t[12]||(t[12]=l=>b.value=l),onTabChange:ae},{default:o(()=>[e($,{label:"所有",name:"all"},{default:o(()=>[e(E,{data:K.value,border:"",stripe:"",style:{"margin-top":"0"}},{default:o(()=>[e(n,{label:"序号",align:"center",type:"index",width:"80"}),e(n,{label:"老人姓名",align:"center",prop:"elderName",width:"120"}),e(n,{label:"老人编号",align:"center",prop:"elderCode",width:"120"}),e(n,{label:"调整类型",align:"center",prop:"adjustmentType",width:"120"},{default:o(l=>[e(p,{options:i(k),value:l.row.adjustmentType},null,8,["options","value"])]),_:1}),e(n,{label:"原床位号",align:"center",prop:"originalBedNumber",width:"120"},{default:o(l=>[m("span",null,s(l.row.originalRoomName+"-"+l.row.originalBedNumber),1)]),_:1}),e(n,{label:"调整后床位号",align:"center",prop:"targetBedNumber",width:"120"},{default:o(l=>[m("span",null,s(l.row.targetRoomName)+"-"+s(l.row.targetBedNumber),1)]),_:1}),e(n,{label:"申请调整日期",align:"center",prop:"adjustmentDate",width:"140"}),e(n,{label:"申请时间",align:"center",prop:"adjustmentTime",width:"140"},{default:o(l=>[m("span",null,s(a.parseTime(l.row.adjustmentTime,"{y}-{m}-{d}")),1)]),_:1}),e(n,{label:"调整后床位费是否有变化",align:"center",prop:"isPriceChanged",width:"120"},{default:o(l=>[e(p,{options:i(D),value:l.row.isPriceChanged},null,8,["options","value"])]),_:1}),e(n,{label:"床位费差额",align:"center",prop:"priceDifference",width:"120"}),e(n,{label:"经办人",align:"center",prop:"handlerName",width:"120"}),e(n,{label:"审批人",align:"center",prop:"approverName",width:"120"}),e(n,{label:"审核状态",align:"center",prop:"status",width:"120"},{default:o(l=>[e(p,{options:i(B),value:l.row.status},null,8,["options","value"])]),_:1}),e(n,{label:"操作",fixed:"right",width:"120"},{default:o(l=>[e(c,{link:"",type:"primary",onClick:F=>W(l.row)},{default:o(()=>[g("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),M(e(L,{total:V.value,page:i(r).pageNum,"onUpdate:page":t[6]||(t[6]=l=>i(r).pageNum=l),limit:i(r).pageSize,"onUpdate:limit":t[7]||(t[7]=l=>i(r).pageSize=l),onPagination:I},null,8,["total","page","limit"]),[[q,V.value>0]])]),_:1}),e($,{label:"申请",name:"apply"},{default:o(()=>[e(E,{data:O.value,border:"",stripe:"",style:{"margin-top":"0"}},{default:o(()=>[e(n,{label:"序号",align:"center",type:"index",width:"80"}),e(n,{label:"老人姓名",align:"center",prop:"elderName",width:"120"}),e(n,{label:"老人编号",align:"center",prop:"elderCode",width:"120"}),e(n,{label:"调整类型",align:"center",prop:"adjustmentType",width:"120"},{default:o(l=>[e(p,{options:i(k),value:l.row.adjustmentType},null,8,["options","value"])]),_:1}),e(n,{label:"原床位号",align:"center",prop:"originalBedNumber",width:"120"},{default:o(l=>[m("span",null,s(l.row.originalBedNumber),1)]),_:1}),e(n,{label:"调整后床位号",align:"center",prop:"targetBedNumber",width:"120"},{default:o(l=>[m("span",null,s(l.row.targetRoomName)+"-"+s(l.row.targetBedNumber),1)]),_:1}),e(n,{label:"申请调整日期",align:"center",prop:"adjustmentDate",width:"140"}),e(n,{label:"申请时间",align:"center",prop:"adjustmentTime",width:"140"},{default:o(l=>[m("span",null,s(a.parseTime(l.row.adjustmentTime,"{y}-{m}-{d}")),1)]),_:1}),e(n,{label:"调整后床位费是否有变化",align:"center",prop:"isPriceChanged",width:"120"},{default:o(l=>[e(p,{options:i(D),value:l.row.isPriceChanged},null,8,["options","value"])]),_:1}),e(n,{label:"床位费差额",align:"center",prop:"priceDifference",width:"120"}),e(n,{label:"经办人",align:"center",prop:"handlerName",width:"120"}),e(n,{label:"审批人",align:"center",prop:"approverName",width:"120"}),e(n,{label:"审核状态",align:"center",prop:"status",width:"120"},{default:o(l=>[e(p,{options:i(B),value:l.row.status},null,8,["options","value"])]),_:1}),e(n,{label:"操作",width:"120",fixed:"right"},{default:o(l=>[e(c,{link:"",type:"primary",onClick:F=>W(l.row)},{default:o(()=>[g("查看")]),_:2},1032,["onClick"]),l.row.status=="PENDING"?(w(),T(c,{key:0,link:"",type:"danger",onClick:F=>ue(l.row)},{default:o(()=>[g("删除")]),_:2},1032,["onClick"])):j("",!0)]),_:1})]),_:1},8,["data"]),M(e(L,{total:P.value,page:i(r).pageNum,"onUpdate:page":t[8]||(t[8]=l=>i(r).pageNum=l),limit:i(r).pageSize,"onUpdate:limit":t[9]||(t[9]=l=>i(r).pageSize=l),onPagination:R},null,8,["total","page","limit"]),[[q,P.value>0]])]),_:1}),A.value?(w(),T($,{key:0,label:"审核",name:"review"},{default:o(()=>[e(E,{data:Q.value,border:"",stripe:"",style:{"margin-top":"0"}},{default:o(()=>[e(n,{label:"序号",align:"center",type:"index",width:"80"}),e(n,{label:"老人姓名",align:"center",prop:"elderName",width:"120"}),e(n,{label:"老人编号",align:"center",prop:"elderCode",width:"120"}),e(n,{label:"调整类型",align:"center",prop:"adjustmentType",width:"120"},{default:o(l=>[e(p,{options:i(k),value:l.row.adjustmentType},null,8,["options","value"])]),_:1}),e(n,{label:"原床位号",align:"center",prop:"originalBedNumber",width:"120"},{default:o(l=>[m("span",null,s(l.row.originalBedNumber),1)]),_:1}),e(n,{label:"调整后床位号",align:"center",prop:"targetBedNumber",width:"120"},{default:o(l=>[m("span",null,s(l.row.targetRoomName)+"-"+s(l.row.targetBedNumber),1)]),_:1}),e(n,{label:"申请调整日期",align:"center",prop:"adjustmentDate",width:"140"}),e(n,{label:"申请时间",align:"center",prop:"adjustmentTime",width:"140"},{default:o(l=>[m("span",null,s(a.parseTime(l.row.adjustmentTime,"{y}-{m}-{d}")),1)]),_:1}),e(n,{label:"调整后床位费是否有变化",align:"center",prop:"isPriceChanged",width:"120"},{default:o(l=>[e(p,{options:i(D),value:l.row.isPriceChanged},null,8,["options","value"])]),_:1}),e(n,{label:"床位费差额",align:"center",prop:"priceDifference",width:"120"}),e(n,{label:"经办人",align:"center",prop:"handlerName",width:"120"}),e(n,{label:"审批人",align:"center",prop:"approverName",width:"120"}),e(n,{label:"审核状态",align:"center",prop:"status",width:"120"},{default:o(l=>[e(p,{options:i(B),value:l.row.status},null,8,["options","value"])]),_:1}),e(n,{label:"操作",width:"120",fixed:"right"},{default:o(l=>[e(c,{link:"",type:"primary",onClick:F=>de(l.row)},{default:o(()=>[g("审核")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),M(e(L,{total:x.value,page:i(r).pageNum,"onUpdate:page":t[10]||(t[10]=l=>i(r).pageNum=l),limit:i(r).pageSize,"onUpdate:limit":t[11]||(t[11]=l=>i(r).pageSize=l),onPagination:z},null,8,["total","page","limit"]),[[q,x.value>0]])]),_:1})):j("",!0)]),_:1},8,["modelValue"]),h.value?(w(),T(Ne,{key:0,"is-view":y.value,"form-data":f.value,businessId:_.value,onClose:re},null,8,["is-view","form-data","businessId"])):j("",!0),U.value?(w(),T(Te,{key:1,"is-view":H.value,businessId:_.value,"form-data":f.value,onClose:se},null,8,["is-view","businessId","form-data"])):j("",!0)])}}},Le=be(De,[["__scopeId","data-v-96deac38"]]);export{Le as default};
