import ie from"./hfrecord-Bt1-EH5W.js";import ue from"./xzrecord-BHxJzpOI.js";import de from"./jgrecord-CtUMRE0B.js";import se from"./hlcxRecord-WsDLXlEL.js";import pe from"./hlzzRecord-CLdUfYLt.js";import{_ as ce,r as s,u as me,a as ge,w as ve,P as fe,ah as be,a4 as _e,a5 as he,a6 as ke,F as ye,e as c,c as B,o as x,f as e,h as n,n as u,i as g,K as W,L as Z,j as ee,t as we,ai as ze,E as Ce,G as Ne,aj as Re,ak as Ve,al as De,am as xe,an as Le}from"./index-B0qHf98Y.js";import{g as Se,a as Te}from"./roommanage-DBG5TiIR.js";import"./PrintPreview-BkU_p74Q.js";const Ue={class:"app-container"},Ye={class:"search-box"},Pe={class:"pagination-container"},Me={class:"search-box"},je={class:"pagination-container"},Be={class:"search-box"},$e={class:"pagination-container"},He={class:"search-box"},Ie={class:"pagination-container"},Fe={class:"search-box"},Ee={class:"pagination-container"},Oe={__name:"recordlist",setup(qe){const p=s("peaceNursing"),$=s([]),S=s([]);s([]);const H=me(),a=s({pageNum:1,pageSize:10}),le=ge(),m=s(0),T=s([]),U=s([]),Y=s([]),P=s([]),M=s([]),v=()=>{p.value=="peaceNursing"?(T.value=[],j()):p.value=="administrative"?(Y.value=[],J()):p.value=="nursingRecord"?(P.value=[],K()):p.value=="institutionalRecord"?(U.value=[],G()):p.value=="nursingTeamerLeaderRecord"&&(M.value=[],Q())};ve(()=>H.query,o=>{if(!o.type)return;const i={orgRecord:"institutionalRecord",hfRecord:"peaceNursing",xzRecord:"administrative",hlrecord:"nursingRecord",hlzzrecord:"nursingTeamerLeaderRecord"}[o.type];i&&fe(()=>{p.value=i,A({props:{name:i}})})},{immediate:!0});const _=()=>{Object.keys(a.value).forEach(o=>{o!=="pageNum"&&o!=="pageSize"&&(a.value[o]="")}),a.value.pageNum=1,v()},ae=()=>{le.push("/work/nurseworkstation")},I=s(null),F=s(null),E=s(null),O=s(null),q=s(null),h=o=>{p.value==="peaceNursing"?I.value.openDialog(o):p.value==="administrative"?E.value.openDialog(o):p.value==="nursingRecord"?O.value.openDialog(o):p.value==="institutionalRecord"?F.value.openDialog(o):p.value==="nursingTeamerLeaderRecord"&&q.value.openDialog(o)},k=o=>{a.value.pageSize=o,v()},y=o=>{a.value.pageNum=o,v()},te=async()=>{const o=await Se();$.value=o.rows||[]},ne=async o=>{S.value=[],a.value.floorId="";const t=await Te(o);S.value=t.rows},j=async()=>{const o=await ze({...a.value});T.value=o.rows||[],m.value=o.total||0},A=o=>{a.value={},a.value={pageNum:1,pageSize:10};const{name:t}=o.props;t==="peaceNursing"?(a.value.pageNum=1,j()):t==="administrative"?(a.value.pageNum=1,J()):t==="nursingRecord"?(a.value.pageNum=1,K()):t==="institutionalRecord"?(a.value.pageNum=1,G()):t==="nursingTeamerLeaderRecord"&&(a.value.pageNum=1,Q())},w=(o,t)=>{Ce.confirm("确定删除该查房表吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{t=="hfDelete"?z(o.id,Re):t=="jgDelete"?z(o.id,Ve):t=="xzDelete"?z(o.id,De):t=="hlDelete"?z(o.id,xe):t=="hlzzDelete"&&z(o.id,Le)})},z=async(o,t)=>{(await t(o)).code==200&&(Ne({message:"删除成功",type:"success"}),a.value.pageNum=1,v())},G=async()=>{const o=await be({...a.value});U.value=o.rows||[],m.value=o.total||0},J=async()=>{const o=await _e({...a.value});Y.value=o.rows||[],m.value=o.total||0},K=async()=>{const o=await he({...a.value});P.value=o.rows||[],m.value=o.total||0},Q=async()=>{const o=await ke({...a.value});M.value=o.rows||[],m.value=o.total||0};return ye(()=>{te(),H.query.type||j()}),(o,t)=>{const i=c("el-button"),L=c("el-option"),X=c("el-select"),d=c("el-form-item"),b=c("el-input"),C=c("el-date-picker"),N=c("el-form"),r=c("el-table-column"),oe=c("el-avatar"),R=c("el-table"),V=c("el-pagination"),D=c("el-tab-pane"),re=c("el-tabs");return x(),B("div",Ue,[e(i,{type:"primary",onClick:ae},{default:n(()=>[u("返回工作台")]),_:1}),e(re,{modelValue:p.value,"onUpdate:modelValue":t[24]||(t[24]=l=>p.value=l),class:"record-tabs",onTabClick:A},{default:n(()=>[e(D,{label:"和孚护理查房记录",name:"peaceNursing"},{default:n(()=>[g("div",Ye,[e(N,{inline:!0,model:a.value,class:"demo-form-inline"},{default:n(()=>[e(d,{label:"楼栋信息",prop:"buildingId"},{default:n(()=>[e(X,{modelValue:a.value.buildingId,"onUpdate:modelValue":t[0]||(t[0]=l=>a.value.buildingId=l),placeholder:"全部",style:{width:"200px"},onChange:ne},{default:n(()=>[e(L,{label:"全部",value:""}),(x(!0),B(W,null,Z($.value,l=>(x(),ee(L,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"楼层层数",prop:"floorId"},{default:n(()=>[e(X,{modelValue:a.value.floorId,"onUpdate:modelValue":t[1]||(t[1]=l=>a.value.floorId=l),placeholder:"全部",style:{width:"200px"},disabled:!a.value.buildingId},{default:n(()=>[e(L,{label:"全部",value:""}),(x(!0),B(W,null,Z(S.value,l=>(x(),ee(L,{key:l.value,label:l.floorName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(d,{label:"房间号",prop:"roomNumber"},{default:n(()=>[e(b,{modelValue:a.value.roomNumber,"onUpdate:modelValue":t[2]||(t[2]=l=>a.value.roomNumber=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,{label:"查房日期",prop:"roundDate"},{default:n(()=>[e(C,{modelValue:a.value.roundDate,"onUpdate:modelValue":t[3]||(t[3]=l=>a.value.roundDate=l),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(d,{label:"老人姓名",prop:"elderName"},{default:n(()=>[e(b,{modelValue:a.value.elderName,"onUpdate:modelValue":t[4]||(t[4]=l=>a.value.elderName=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,null,{default:n(()=>[e(i,{icon:"Search",type:"primary",onClick:v},{default:n(()=>[u("查询")]),_:1}),e(i,{onClick:_,icon:"Refresh"},{default:n(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),e(R,{data:T.value,style:{width:"100%"},border:""},{default:n(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"}),e(r,{prop:"avatar",label:"老人头像",width:"100",align:"center"},{default:n(l=>[e(oe,{shape:"circle",size:60,fit:"fill",src:l.row.avatar},null,8,["src"])]),_:1}),e(r,{prop:"roundDate",label:"查房日期","min-width":"180",align:"center"}),e(r,{prop:"elderName",label:"老人姓名",width:"120",align:"center"}),e(r,{prop:"bedNumber",label:"床位号",width:"100",align:"center"}),e(r,{prop:"roomNumber",label:"房间号",width:"100",align:"center"}),e(r,{prop:"buildingName",label:"楼栋信息",width:"120",align:"center"}),e(r,{prop:"floorNumber",label:"楼层层数",width:"120",align:"center"}),e(r,{prop:"roundNameStr",label:"查房人",width:"120",align:"center"}),e(r,{fixed:"right",label:"操作","min-width":"100",align:"center"},{default:n(l=>[e(i,{link:"",type:"primary",onClick:f=>h(l.row)},{default:n(()=>[u("详情")]),_:2},1032,["onClick"]),e(i,{type:"primary",onClick:f=>w(l.row,"hfDelete"),link:""},{default:n(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),g("div",Pe,[e(V,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[5]||(t[5]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[6]||(t[6]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1}),e(D,{label:"机构综合查房记录",name:"institutionalRecord"},{default:n(()=>[g("div",Me,[e(N,{inline:!0,model:a.value,class:"demo-form-inline"},{default:n(()=>[e(d,{label:"查房日期",prop:"roundTime"},{default:n(()=>[e(C,{modelValue:a.value.roundTime,"onUpdate:modelValue":t[7]||(t[7]=l=>a.value.roundTime=l),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(d,{label:"查房人",prop:"roundPerson"},{default:n(()=>[e(b,{modelValue:a.value.roundPerson,"onUpdate:modelValue":t[8]||(t[8]=l=>a.value.roundPerson=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,null,{default:n(()=>[e(i,{type:"primary",onClick:v,icon:"Search"},{default:n(()=>[u("查询")]),_:1}),e(i,{onClick:_,icon:"Refresh"},{default:n(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),e(R,{data:U.value,style:{width:"100%"},border:""},{default:n(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"}),e(r,{prop:"roundTime",label:"查房日期",width:"180",align:"center"}),e(r,{prop:"roundPerson",label:"查房人",width:"150",align:"center"}),e(r,{prop:"nurseName",label:"提交人",width:"150",align:"center"}),e(r,{prop:"createTime",label:"提交时间","min-width":"150",align:"center"}),e(r,{fixed:"right",label:"操作",width:"150",align:"center"},{default:n(l=>[e(i,{link:"",type:"primary",onClick:f=>h(l.row)},{default:n(()=>[u("详情")]),_:2},1032,["onClick"]),e(i,{type:"primary",onClick:f=>w(l.row,"jgDelete"),link:""},{default:n(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),g("div",je,[e(V,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[9]||(t[9]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[10]||(t[10]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1}),e(D,{label:"行政查房记录",name:"administrative"},{default:n(()=>[g("div",Be,[e(N,{inline:!0,model:a.value,class:"demo-form-inline"},{default:n(()=>[e(d,{label:"查房日期",prop:"roundTime"},{default:n(()=>[e(C,{modelValue:a.value.roundTime,"onUpdate:modelValue":t[11]||(t[11]=l=>a.value.roundTime=l),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(d,{label:"查房院长",prop:"director"},{default:n(()=>[e(b,{modelValue:a.value.director,"onUpdate:modelValue":t[12]||(t[12]=l=>a.value.director=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,{label:"检查部门",prop:"department"},{default:n(()=>[e(b,{modelValue:a.value.department,"onUpdate:modelValue":t[13]||(t[13]=l=>a.value.department=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,null,{default:n(()=>[e(i,{type:"primary",onClick:v,icon:"Search"},{default:n(()=>[u("查询")]),_:1}),e(i,{onClick:_,icon:"Refresh"},{default:n(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),e(R,{data:Y.value,style:{width:"100%"},border:""},{default:n(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"}),e(r,{prop:"roundTime",label:"查房日期","min-width":"180",align:"center"}),e(r,{prop:"director",label:"查房院长","min-width":"150",align:"center"}),e(r,{prop:"department",label:"检查部门","min-width":"150",align:"center"}),e(r,{prop:"recorder",label:"记录人","min-width":"120",align:"center"}),e(r,{fixed:"right",label:"操作","min-width":"100",align:"center"},{default:n(l=>[e(i,{link:"",type:"primary",onClick:f=>h(l.row)},{default:n(()=>[u("详情")]),_:2},1032,["onClick"]),e(i,{type:"primary",onClick:f=>w(l.row,"xzDelete"),link:""},{default:n(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),g("div",$e,[e(V,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[14]||(t[14]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[15]||(t[15]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1}),e(D,{label:"护理查房记录",name:"nursingRecord"},{default:n(()=>[g("div",He,[e(N,{inline:!0,model:a.value,class:"demo-form-inline"},{default:n(()=>[e(d,{label:"查房日期",prop:"roundDate"},{default:n(()=>[e(C,{modelValue:a.value.roundDate,"onUpdate:modelValue":t[16]||(t[16]=l=>a.value.roundDate=l),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(d,{label:"查房人",prop:"roundPerson"},{default:n(()=>[e(b,{modelValue:a.value.roundPerson,"onUpdate:modelValue":t[17]||(t[17]=l=>a.value.roundPerson=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,null,{default:n(()=>[e(i,{type:"primary",onClick:v,icon:"Search"},{default:n(()=>[u("查询")]),_:1}),e(i,{onClick:_,icon:"Refresh"},{default:n(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),e(R,{data:P.value,style:{width:"100%"},border:""},{default:n(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"}),e(r,{prop:"roundDate",label:"查房日期","min-width":"180",align:"center"}),e(r,{prop:"morningTime",label:"查房时间","min-width":"150",align:"center"},{default:n(l=>[u(we(l.row.morningTime&&l.row.afternoonTime?l.row.morningTime+"~"+l.row.afternoonTime:""),1)]),_:1}),e(r,{prop:"roundPerson",label:"查房人","min-width":"150",align:"center"}),e(r,{fixed:"right",label:"操作","min-width":"100",align:"center"},{default:n(l=>[e(i,{link:"",type:"primary",onClick:f=>h(l.row)},{default:n(()=>[u("详情")]),_:2},1032,["onClick"]),e(i,{type:"primary",onClick:f=>w(l.row,"hlDelete"),link:""},{default:n(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),g("div",Ie,[e(V,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[18]||(t[18]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[19]||(t[19]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1}),e(D,{label:"护理组长查房记录",name:"nursingTeamerLeaderRecord"},{default:n(()=>[g("div",Fe,[e(N,{inline:!0,model:a.value,class:"demo-form-inline"},{default:n(()=>[e(d,{label:"查房日期",prop:"checkDate"},{default:n(()=>[e(C,{modelValue:a.value.checkDate,"onUpdate:modelValue":t[20]||(t[20]=l=>a.value.checkDate=l),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(d,{label:"查房人",prop:"roundPerson"},{default:n(()=>[e(b,{modelValue:a.value.roundPerson,"onUpdate:modelValue":t[21]||(t[21]=l=>a.value.roundPerson=l),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),e(d,null,{default:n(()=>[e(i,{type:"primary",onClick:v,icon:"Search"},{default:n(()=>[u("查询")]),_:1}),e(i,{onClick:_,icon:"Refresh"},{default:n(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),e(R,{data:M.value,style:{width:"100%"},border:""},{default:n(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"}),e(r,{prop:"checkDate",label:"查房日期","min-width":"180",align:"center"}),e(r,{prop:"roundPerson",label:"查房人","min-width":"150",align:"center"}),e(r,{prop:"nurseName",label:"提交人",width:"150",align:"center"}),e(r,{prop:"createTime",label:"提交时间","min-width":"150",align:"center"}),e(r,{fixed:"right",label:"操作","min-width":"100",align:"center"},{default:n(l=>[e(i,{link:"",type:"primary",onClick:f=>h(l.row)},{default:n(()=>[u("详情")]),_:2},1032,["onClick"]),e(i,{type:"primary",onClick:f=>w(l.row,"hlzzDelete"),link:""},{default:n(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),g("div",Ee,[e(V,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[22]||(t[22]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[23]||(t[23]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1})]),_:1},8,["modelValue"]),e(ie,{ref_key:"hfRecordRef",ref:I},null,512),e(de,{ref_key:"institutionalRecordRef",ref:F},null,512),e(ue,{ref_key:"xzRecordRef",ref:E},null,512),e(se,{ref_key:"hlcxRecordRef",ref:O},null,512),e(pe,{ref_key:"hlzzRecordRef",ref:q},null,512)])}}},el=ce(Oe,[["__scopeId","data-v-28f26dee"]]);export{el as default};
