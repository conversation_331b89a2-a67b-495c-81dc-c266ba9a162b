import{_ as q,d as W,r as p,z as I,e as i,c as N,o as f,f as t,h as a,i as n,k as x,K as X,L as Z,Q as ee,j as se,t as w,l as te,ax as oe,n as T,v as ae,x as le,G as C}from"./index-B0qHf98Y.js";import{a as ce}from"./assessmentRecord-4xWX4TZA.js";import{e as re}from"./eventBus-BDDolVUG.js";const y=m=>(ae("data-v-6087c11d"),m=m(),le(),m),ne={class:"braden-assessment-container"},ie=y(()=>n("div",{class:"card-header"},[n("h1",{class:"title"},"Braden压疮评估量")],-1)),de=["innerHTML"],ue=["onClick"],pe={class:"score-title"},_e={class:"score-description"},me={class:"risk-level-info"},ve=y(()=>n("p",null," 严重危险：≤9分；高度危险：10分～12分；中度危险：13分～14分；轻度危险：15分～18分 ",-1)),fe={class:"total-score-display"},ye={class:"score-content"},he=y(()=>n("span",{class:"score-label"},"评分：",-1)),ge={class:"score-value"},Se=y(()=>n("span",{class:"score-label"},"分",-1)),be={class:"form-footer"},ke={class:"assessment-comments"},Ve=y(()=>n("div",{class:"comments-header"},"评估意见：",-1)),Ne={key:0,class:"action-buttons"},we={__name:"bradenPressureUlcerAssessmentScale",props:{elderId:{type:String,default:null},isShow:{type:String,default:null},data:{type:Object,default:null}},emits:["updateList"],setup(m,{emit:Ee}){const{proxy:$}=W(),v=p(1),h=p(1),g=p(1),S=p(1),b=p(1),k=p(1),V=p(1),r=m,d=p([{category:`感觉
(对压力导致的不适感觉的反应能力)`,categorytype:1,score1:{title:"完全丧失1分",description:"由于知觉减退或使用镇静剂而对疼痛刺激无反应，或大部分体表对疼痛感觉能力丧损。",score1:1},score2:{title:"非常受损2分",description:"仅对疼痛有反应，能于呻吟或躁动不能表达不适，或有1/2由于感觉障碍而限制了感觉传递或表达能力。",score1:2},score3:{title:"轻微受损3分",description:"对言语指令有反应，但不是总能表达不适，或需要翻身成1-2个肢体有感觉障碍，感觉传递或表达的能力受限。",score1:3},score4:{title:"无受损4分",description:"对言语指令反应良好，无感觉障碍，能表达不适感觉下达的能力不受限。",score1:4},selectedScore:0},{category:`湿度
(皮肤潮湿的程度)`,categorytype:2,score1:{title:"持续潮湿1分",description:"皮肤持续被汗液或尿液浸湿，每次翻身或移动时都发现病人湿润。"},score2:{title:"经常潮湿2分",description:"皮肤经常但不是始终潮湿，每班需要更换床单。"},score3:{title:"偶尔潮湿3分",description:"皮肤偶尔潮湿，每天大需更换一次床单。"},score4:{title:"很少潮湿4分",description:"皮肤一般是干燥的，只需常规换床单。"},selectedScore:1},{category:`活动
(身体的活动程度)`,categorytype:3,score1:{title:"卧床1分",description:"限制卧床"},score2:{title:"坐位2分",description:"不能行走或行走严重受限，不能负荷自身重量，必须借助椅子或轮椅。"},score3:{title:"偶尔行走3分",description:"白天可短距离行走，伴或不伴辅助，大部分时间在床内或坐轮椅活动。"},score4:{title:"经常行走4分",description:"每天至少可在室外行走 2 次，在室内2小时活动一次。"},selectedScore:1},{category:`移动
(身体的活动程度)`,categorytype:4,score1:{title:"完全不自主1分",description:"没有辅助时，病人体不能够改变位置。"},score2:{title:"非常受限2分",description:"不能行走或行走严重受限，不能负荷自身重量，必须借助椅子或轮椅。"},score3:{title:"轻微受限3分",description:"白天可短距离行走，伴或不伴辅助，大部分时间在床内或坐轮椅活动。"},score4:{title:"不受限4分",description:"每天至少可在室外行走 2 次，在室内2小时活动一次。"},selectedScore:1},{category:`移动
(改变和控制身体位置的能力)`,categorytype:5,score1:{title:"完全不自主1分",description:"没有辅助时体不能够改变位置。"},score2:{title:"非常受限2分",description:"可以示微改变身体或肢体位置，但不能独立，经常或大幅度地改变。"},score3:{title:"轻微受限3分",description:"可独立，经常或经常改变身体或肢体位置。"},score4:{title:"不受限4分",description:"没有辅助可以经常大幅度地改变身体或肢体位置改变。"},selectedScore:1},{category:`营养
(日常进食方式)`,categorytype:6,score1:{title:"非常缺乏 1 分",description:"从未吃过完整的一餐；包餐很少吃完 1/3 的食物；蛋白质摄入（肉或乳制品）很少，偶尔吃点心，或静脉液体输入；不能进食或保持清流食，禁食进食全流或静脉输液 5 天以上。"},score2:{title:"可能缺乏 2 分",description:"很少吃完一餐，通常每餐只能吃完 1/2的食物；蛋白质摄入仅是每日三餐中的两餐；偶尔进食，或进食不满要求的流食或管饲。"},score3:{title:"充足 3 分",description:"每餐能够完成大多数食物；每日总共四餐蛋白质（肉，鱼，奶制品），偶尔拒绝一餐，但通常会吃点心，不需要补充。"},score4:{title:"营养丰富 4 分",description:"吃完每餐食物；从不拒绝任何一餐；通常每日吃四餐或更多次含肉和奶制品的食物；偶尔在两餐之间加餐；不需要额外补充营养。"},selectedScore:1},{category:"摩擦力和剪切力",categorytype:7,score1:{title:"有问题1分",description:""},score2:{title:"潜在的问题2分",description:""},score3:{title:"无明显问题3分",description:""},selectedScore:1}]),l=p({assessorName:"",assessmentTime:"",assessmentOpinion:"",totalScoreValue:"",assessmentMethod:""}),A=p("");function U(){r.isShow=="add"?(console.log("add"),d.value.forEach(o=>{o.selectedScore=0})):r.isShow=="edit"?(l.value=r.data,u.value=r.data.totalScoreValue,console.log("edit")):r.isShow=="show"&&(console.log("show"),l.value=r.data,console.log(r.data,"data============"),JSON.parse(r.data.itemName).forEach(e=>{console.log(e,"item"),e.type==1?(v.value=e.score,d.value.forEach(s=>{s.categorytype==1&&(s.selectedScore=v.value)})):e.type==2?(h.value=e.score,d.value.forEach(s=>{s.categorytype==2&&(s.selectedScore=h.value)})):e.type==3?(g.value=e.score,d.value.forEach(s=>{s.categorytype==3&&(s.selectedScore=g.value)})):e.type==4?(S.value=e.score,d.value.forEach(s=>{s.categorytype==4&&(s.selectedScore=S.value)})):e.type==5?(b.value=e.score,d.value.forEach(s=>{s.categorytype==5&&(s.selectedScore=b.value)})):e.type==6?(k.value=e.score,d.value.forEach(s=>{s.categorytype==6&&(s.selectedScore=k.value)})):e.type==7&&(V.value=e.score,d.value.forEach(s=>{s.categorytype==7&&(s.selectedScore=V.value)}))}),u.value=r.data.totalScoreValue),console.log(v.value,"555")}const D=(o,e)=>{o.selectedScore=e===o.selectedScore?0:e,console.log(o,e,"changrow---"),o.categorytype==1?(console.log("1111111111"),v.value=e):o.categorytype==2?h.value=e:o.categorytype==3?g.value=e:o.categorytype==4?S.value=e:o.categorytype==5?b.value=e:o.categorytype==6?k.value=e:o.categorytype==7&&(V.value=e)},u=I(()=>d.value.reduce((o,e)=>o+(e.selectedScore||0),0)),F=I(()=>u.value<=9?"严重危险":u.value<=12?"高度危险":u.value<=14?"中度危险":"轻度危险"),Y=I(()=>u.value<=9?"danger":u.value<=12?"warning":u.value<=14?"":"success"),z=()=>{if(r.elderId===null){C.error("请选择老人信息");return}let o=[{type:1,score:v.value},{type:2,score:h.value},{type:3,score:g.value},{type:4,score:S.value},{type:5,score:b.value},{type:6,score:k.value},{type:7,score:V.value}];l.value.itemName=JSON.stringify(o),l.value.totalScoreValue=u.value,l.value.assessmentMethod="01",l.value.assessmentFormId="30";let e={elderId:r.elderId,assessmentFormId:30,assessmentOrgName:"和孚养老机构",assessmentMethod:"01",assessmentScores:[]};if(!l.value.assessorName||!l.value.assessmentTime){C.error("请填写评估师姓名和日期！");return}e.assessmentScores.push(l.value),console.log(e,"formval=="),ce(e).then(s=>{C.success("评估表提交成功！"),re.emit("uploadListEvent",{data:"some data"}),$.$tab.closeOpenPage({path:"/assessment/assessmentRecord"})})},P=()=>{d.value.forEach(o=>{o.selectedScore=0}),l.value={assessorName:"",assessmentTime:"",assessmentOpinion:"",totalScoreValue:"",assessmentMethod:""},A.value=""};return U(),(o,e)=>{const s=i("el-table-column"),R=i("el-icon"),j=i("el-table"),H=i("el-alert"),J=i("el-tag"),E=i("el-card"),M=i("el-input"),O=i("el-form-item"),L=i("el-col"),G=i("el-date-picker"),K=i("el-row"),Q=i("el-form"),B=i("el-button");return f(),N("div",ne,[t(E,{class:"assessment-card",shadow:"hover"},{header:a(()=>[ie]),default:a(()=>[t(j,{data:d.value,border:"",style:{width:"100%"},class:"assessment-table"},{default:a(()=>[t(s,{prop:"category",label:"项目",width:"180"},{default:a(({row:c})=>[n("div",{innerHTML:c.category.replace(/\n/g,"<br/>")},null,8,de)]),_:1}),t(s,{label:"评分标准",align:"center"},{default:a(()=>[(f(),N(X,null,Z([1,2,3,4],c=>t(s,{key:c,label:`${c}分`,align:"center",width:"230"},{default:a(({row:_})=>[_[`score${c}`]?(f(),N("div",{key:0,class:ee(["score-cell",{selected:_.selectedScore===c}]),onClick:Ie=>D(_,c)},[n("div",pe,w(_[`score${c}`].title),1),n("div",_e,w(_[`score${c}`].description),1),_.selectedScore===c?(f(),se(R,{key:0,class:"checkmark"},{default:a(()=>[t(te(oe))]),_:1})):x("",!0)],10,ue)):x("",!0)]),_:2},1032,["label"])),64))]),_:1})]),_:1},8,["data"]),n("div",me,[t(H,{title:"评分标准",type:"info",closable:!1},{default:a(()=>[ve]),_:1})]),n("div",fe,[t(E,{shadow:"never"},{default:a(()=>[n("div",ye,[he,n("span",ge,w(u.value),1),Se,t(J,{type:Y.value,effect:"dark",class:"risk-tag"},{default:a(()=>[T(w(F.value),1)]),_:1},8,["type"])])]),_:1})]),n("div",be,[t(Q,{model:l.value,"label-width":"100px"},{default:a(()=>[n("div",ke,[t(E,{shadow:"never"},{header:a(()=>[Ve]),default:a(()=>[t(M,{modelValue:l.value.assessmentOpinion,"onUpdate:modelValue":e[0]||(e[0]=c=>l.value.assessmentOpinion=c),type:"textarea",rows:4,placeholder:"请输入评估意见...",resize:"none",disabled:r.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),t(K,{gutter:20},{default:a(()=>[t(L,{span:12},{default:a(()=>[t(O,{label:"评估师姓名："},{default:a(()=>[t(M,{modelValue:l.value.assessorName,"onUpdate:modelValue":e[1]||(e[1]=c=>l.value.assessorName=c),placeholder:"请输入评估师姓名",disabled:r.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(L,{span:12},{default:a(()=>[t(O,{label:"日期："},{default:a(()=>[t(G,{modelValue:l.value.assessmentTime,"onUpdate:modelValue":e[2]||(e[2]=c=>l.value.assessmentTime=c),type:"date",placeholder:"选择评估日期","value-format":"YYYY-MM-DD",style:{width:"100%"},disabled:r.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),r.isShow!="show"?(f(),N("div",Ne,[t(B,{type:"primary",onClick:z},{default:a(()=>[T(" 提交 ")]),_:1}),t(B,{onClick:P},{default:a(()=>[T(" 重置 ")]),_:1})])):x("",!0)]),_:1})])}}},Me=q(we,[["__scopeId","data-v-6087c11d"]]);export{Me as default};
