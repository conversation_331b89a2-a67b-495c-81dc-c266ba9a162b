import{B as O,d as P,r as f,C as Q,e as s,j as A,o as E,h as l,f as e,l as n,m as y,n as _,J as F,i as N,t as J,O as G,D as H}from"./index-B0qHf98Y.js";import{u as M,d as W}from"./role-CeuFPMBB.js";const X={class:"dialog-footer"},Y=O({name:"SelectUser"}),oe=Object.assign(Y,{props:{roleId:{type:[Number,String]}},emits:["ok"],setup(S,{expose:C,emit:I}){const V=S,{proxy:p}=P(),{sys_normal_disable:k}=p.useDict("sys_normal_disable"),h=f([]),i=f(!1),g=f(0),v=f([]),a=Q({pageNum:1,pageSize:10,roleId:void 0,userName:void 0,phonenumber:void 0});function x(){a.roleId=V.roleId,b(),i.value=!0}function U(r){p.$refs.refTable.toggleRowSelection(r)}function R(r){v.value=r.map(o=>o.userId)}function b(){M(a).then(r=>{h.value=r.rows,g.value=r.total})}function d(){a.pageNum=1,b()}function T(){p.resetForm("queryRef"),d()}const B=I;function D(){const r=a.roleId,o=v.value.join(",");if(o==""){p.$modal.msgError("请选择要分配的用户");return}W({roleId:r,userIds:o}).then(m=>{p.$modal.msgSuccess(m.msg),i.value=!1,B("ok")})}return C({show:x}),(r,o)=>{const m=s("el-input"),w=s("el-form-item"),c=s("el-button"),$=s("el-form"),u=s("el-table-column"),j=s("dict-tag"),q=s("el-table"),z=s("pagination"),K=s("el-row"),L=s("el-dialog");return E(),A(L,{title:"选择用户",modelValue:n(i),"onUpdate:modelValue":o[5]||(o[5]=t=>H(i)?i.value=t:null),width:"800px",top:"5vh","append-to-body":""},{footer:l(()=>[N("div",X,[e(c,{type:"primary",onClick:D},{default:l(()=>[_("确 定")]),_:1}),e(c,{onClick:o[4]||(o[4]=t=>i.value=!1)},{default:l(()=>[_("取 消")]),_:1})])]),default:l(()=>[e($,{model:n(a),ref:"queryRef",inline:!0},{default:l(()=>[e(w,{label:"用户名称",prop:"userName"},{default:l(()=>[e(m,{modelValue:n(a).userName,"onUpdate:modelValue":o[0]||(o[0]=t=>n(a).userName=t),placeholder:"请输入用户名称",clearable:"",style:{width:"180px"},onKeyup:y(d,["enter"])},null,8,["modelValue"])]),_:1}),e(w,{label:"手机号码",prop:"phonenumber"},{default:l(()=>[e(m,{modelValue:n(a).phonenumber,"onUpdate:modelValue":o[1]||(o[1]=t=>n(a).phonenumber=t),placeholder:"请输入手机号码",clearable:"",style:{width:"180px"},onKeyup:y(d,["enter"])},null,8,["modelValue"])]),_:1}),e(w,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:d},{default:l(()=>[_("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:T},{default:l(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(K,null,{default:l(()=>[e(q,{onRowClick:U,ref:"refTable",data:n(h),onSelectionChange:R,height:"260px"},{default:l(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),e(u,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),e(u,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),e(u,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),e(u,{label:"状态",align:"center",prop:"status"},{default:l(t=>[e(j,{options:n(k),value:t.row.status},null,8,["options","value"])]),_:1}),e(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(t=>[N("span",null,J(r.parseTime(t.row.createTime)),1)]),_:1})]),_:1},8,["data"]),F(e(z,{total:n(g),page:n(a).pageNum,"onUpdate:page":o[2]||(o[2]=t=>n(a).pageNum=t),limit:n(a).pageSize,"onUpdate:limit":o[3]||(o[3]=t=>n(a).pageSize=t),onPagination:b},null,8,["total","page","limit"]),[[G,n(g)>0]])]),_:1})]),_:1},8,["modelValue"])}}});export{oe as default};
