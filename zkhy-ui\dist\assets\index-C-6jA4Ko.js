import{X as J,_ as rt,r as N,z as H,w as Ce,C as Te,P as Ve,F as ut,aZ as it,e as b,c as y,o as p,i as d,f as c,h as v,l as B,dy as ct,dz as dt,dA as ft,bo as vt,n as z,t as k,bc as pt,dB as mt,dC as ht,K as A,L as j,au as G,Q as ae,k as ue,j as le,p as Ne,dD as _t,b0 as gt,a1 as yt,v as wt,x as bt,G as Q}from"./index-B0qHf98Y.js";import{i as Dt}from"./index-a8qYZQmS.js";function kt($){return J({url:"/nurseworkschedule/nurseShiftType/list",method:"get",params:$})}function Ie($){return J({url:"/nurseworkschedule/nurseSchedule",method:"post",data:$})}function Le($){return J({url:"/nurseworkschedule/nurseSchedule",method:"put",data:$})}function St($){return J({url:"/system/role/authUser/allocatedList",method:"get",params:$})}function Yt($){return J({url:"/nurseworkschedule/nurseSchedule/listMonth",method:"get",params:$})}function $t($){return J({url:"/nurseworkschedule/nurseSchedule/listWeek",method:"get",params:$})}const K=$=>(wt("data-v-35076ecf"),$=$(),bt(),$),Mt={class:"scheduling-wrapper"},xt={class:"scheduling-header"},Ct={class:"header-left"},Tt=K(()=>d("span",{style:{"margin-left":"4px"}},"月视图",-1)),Vt=K(()=>d("span",{style:{"margin-left":"4px"}},"周视图",-1)),Nt={class:"header-center"},It={class:"date-navigation"},Lt={class:"quick-nav"},Et={class:"date-display"},Ut={class:"date-picker"},Bt={class:"header-right"},At={class:"shift-tags"},jt=["onClick"],zt={key:0,class:"shift-time"},Ft={key:0,class:"calendar-view"},Ot={class:"calendar-title"},Wt={class:"calendar-table"},Rt={class:"calendar-row calendar-header"},Pt={class:"week-name"},qt={class:"cell-date"},Ht={class:"cell-shifts-container"},Kt=["onClick"],Xt={class:"cell-shift-left"},Zt={class:"cell-nurse-name"},Gt={key:1,class:"week-table-view"},Qt={class:"week-title"},Jt={class:"nurse-name-cell"},ea=["onClick"],ta={class:"week-header"},aa={class:"week-day"},la={class:"week-date"},na={class:"shift-selector"},oa=K(()=>d("div",{class:"shift-selector-title"},"选择班次",-1)),sa={class:"shift-options"},ra=["onClick"],ua=["onClick","onContextmenu"],ia=["onClick","onContextmenu"],ca=K(()=>d("span",null,null,-1)),da=K(()=>d("span",{style:{"margin-left":"5px"}},"高级设置",-1)),fa=K(()=>d("span",{style:{"margin-left":"5px"}},"清除排班",-1)),va={class:"stat-dialog-content"},pa=K(()=>d("div",{id:"stat-echart",style:{width:"100%",height:"340px"}},null,-1)),ma={class:"dialog-footer"},ha={style:{display:"flex","align-items":"center"}},_a={class:"dialog-footer"},ga={__name:"index",setup($){const X=new URL("/assets/profile-DruvkrZU.jpg",import.meta.url).href,F=N([]);async function Ee(){const t=await St({roleKeys:["nurse"],pageSize:1e3});t.rows&&(F.value=t.rows.map(e=>({name:e.nickName,id:e.userId,avatar:e.avatar&&"/prod-api"+e.avatar||X})),console.log("nurseList",F.value))}const w=N([]);async function Ue(){const t=await kt({status:"1"});t.code===200&&(w.value=t.rows.map(e=>({label:e.shiftName,value:e.id,color:e.color,...e})))}const ie=["星期一","星期二","星期三","星期四","星期五","星期六","星期日"],S=N("month"),O=N(""),E=N(""),U=N(""),P=new Date,h=N(S.value==="month"?C(P,"YYYY-MM"):oe(P));function C(t,e){const l=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0");return e==="YYYY-MM"?`${l}-${a}`:e==="YYYY-MM-DD"?`${l}-${a}-${o}`:e==="YYYY年MM月"?`${l}年${a}月`:`${l}-${a}-${o}`}function oe(t){const e=new Date(t),l=e.getDay()||7;return e.setDate(e.getDate()-l+1),C(e,"YYYY-MM-DD")}function ce(t){const e=[],l=new Date(t);for(let a=0;a<7;a++)e.push(C(new Date(l.getFullYear(),l.getMonth(),l.getDate()+a),"YYYY-MM-DD"));return e}const Be=H(()=>{const[t,e]=h.value.split("-").map(Number),a=(new Date(t,e-1,1).getDay()||7)-1,o=[];let n=1-a;for(let r=0;r<6;r++){const f=[];for(let i=0;i<7;i++){const u=new Date(t,e-1,n),x=u.getMonth()===e-1;let T=[];if(x){const te=C(u,"YYYY-MM-DD");T=Fe(te),O.value&&(T=T.filter(V=>V.nurse.includes(O.value))),E.value&&(T=T.filter(V=>V.nurse===E.value))}f.push({day:u.getDate().toString().padStart(2,"0"),inMonth:x,isToday:x&&C(u,"YYYY-MM-DD")===C(P,"YYYY-MM-DD"),shifts:T}),n++}o.push(f)}return o}),de=H(()=>{const[t,e]=h.value.split("-");return`${t}年${e}月`}),M=H(()=>ce(h.value)),fe=H(()=>{const t=new Date(h.value),e=t.getFullYear(),l=(t.getMonth()+1).toString().padStart(2,"0"),a=new Date(h.value),o=new Date(a);return o.setDate(a.getDate()+6),`${e}年${l}月${a.getDate()}日 - ${o.getMonth()+1}月${o.getDate()}日`}),Ae=H(()=>{let t=F.value;O.value&&(t=t.filter(l=>l.name.includes(O.value))),E.value&&(t=t.filter(l=>l.name===E.value));let e=t.map(l=>{const a=M.value.map(o=>{var r,f;const n=Y.value.find(i=>i.nurse===l.name&&i.date===o);return n&&(!U.value||n.type===U.value)?(console.log("头像路径",n.avatar&&"/prod-api"+n.avatar||X),{type:n.type,label:n.label||((r=w.value.find(i=>i.value===n.type))==null?void 0:r.label)||n.type,color:n.color||((f=w.value.find(i=>i.value===n.type))==null?void 0:f.color),avatar:n.avatar&&"/prod-api"+n.avatar||X}):null});return{...l,weekShifts:a}});return U.value&&(e=e.filter(l=>l.weekShifts.some(a=>a&&a.type===U.value))),e}),Y=N([]);async function je(){const[t,e]=h.value.split("-").map(Number),l=`${t}-${e.toString().padStart(2,"0")}-01 00:00:00`,a=new Date(t,e,0),o=`${t}-${e.toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} 23:59:59`,n=await Yt({"params[beginScheduleDate]":l,"params[endScheduleDate]":o});if(n.code===200&&Array.isArray(n.data)){let r=[];n.data.forEach(f=>{f.shifts.forEach(i=>{const u=w.value.find(x=>x.label===i.type);r.push({id:i.id,nurse:i.nurse,date:`${t}-${e.toString().padStart(2,"0")}-${f.day.padStart(2,"0")}`,type:u?u.value:i.type,label:i.type,color:u?u.color:void 0,avatar:i.avatar&&"/prod-api"+i.avatar||X})})}),console.log("用班次名称找到ID",r),Y.value=r}else Y.value=[]}async function ze(){const t=ce(h.value),e=`${t[0]} 00:00:00`,l=`${t[6]} 23:59:59`,a=await $t({"params[beginScheduleDate]":e,"params[endScheduleDate]":l});if(a.code===200&&Array.isArray(a.data)){console.log("res.data",a.data);let o=[];a.data.forEach(n=>{t.forEach(r=>{const f=Number(r.slice(-2)),i=n.weekShifts.find(u=>Number(u.day)===f);if(i){const u=w.value.find(x=>x.label===i.type);o.push({id:i.id,nurse:n.nursename,date:r,type:u?u.value:i.type,label:i.type,color:u?u.color:void 0,avatar:i.avatar&&"/prod-api"+i.avatar||X})}})}),Y.value=o,console.log("scheduleData.value",o)}else Y.value=[]}async function W(){S.value==="month"?await je():await ze(),ee()}function Fe(t){let e=Y.value.filter(l=>l.date===t);return U.value&&(e=e.filter(l=>l.type===U.value)),e.map(l=>{const a=F.value.find(o=>o.name===l.nurse);return{nurse:l.nurse,avatar:l.avatar&&l.avatar.trim()||(a==null?void 0:a.avatar)&&a.avatar.trim()||X,type:l.type,label:l.label,color:l.color}})}function ve(t){let e=w.value.find(l=>l.value===t);return e||(e=w.value.find(l=>l.label===t),e)?e.color:"#ccc"}function pe(t){var e;return((e=w.value.find(l=>l.value===t))==null?void 0:e.label)||t}function me(t){return E.value&&E.value===t}function he(t){E.value=E.value===t?"":t,ee()}function Oe(t){U.value=U.value===t?"":t,ee()}function We(){if(S.value==="month"){const[t,e]=h.value.split("-").map(Number);let l=e===1?t-1:t,a=e===1?12:e-1;h.value=`${l}-${a.toString().padStart(2,"0")}`}else{const t=new Date(h.value);t.setDate(t.getDate()-7),h.value=C(t,"YYYY-MM-DD")}W()}function Re(){if(S.value==="month"){const[t,e]=h.value.split("-").map(Number);let l=e===12?t+1:t,a=e===12?1:e+1;h.value=`${l}-${a.toString().padStart(2,"0")}`}else{const t=new Date(h.value);t.setDate(t.getDate()+7),h.value=C(t,"YYYY-MM-DD")}W()}function Pe(){S.value==="month"?h.value=C(P,"YYYY-MM"):h.value=oe(P),W()}function qe(){W()}function He(t){return t.getFullYear()!==P.getFullYear()}Ce([S,h],async([t,e],[l,a])=>{t==="week"&&l!=="week"&&(h.value=oe(P)),E.value="",await W()}),Ce([O,U],()=>{ee()});const L=N({}),_e=N({}),q=N(!1),I=Te({nurseName:"",date:"",x:0,y:0}),Ke=H(()=>({position:"fixed",left:I.x+"px",top:I.y+"px",zIndex:3e3}));function ge(t,e,l){if(t){const a=`${e}-${l}`;_e.value[a]=t}}function ye(t,e,l){Object.keys(L.value).forEach(o=>{o!==`${e}-${l}`&&(L.value[o]=!1)});const a=`${e}-${l}`;L.value[a]=!L.value[a],t.stopPropagation()}async function Xe(t,e,l){const a=F.value.find(u=>u.name===t),o=w.value.find(u=>u.value===l);if(!a||!o)return;const n=new Date,r=Y.value.find(u=>u.nurse===t&&u.date===e),f=r&&r.id,i={nurseId:a.id,nurseName:a.name,scheduleDate:`${e} 00:00:00`,shiftType:o.label,shiftTypeId:o.value,status:"0",updateBy:"",updateTime:ke(n),remark:"",id:f?r.id:void 0,params:{},searchValue:""};try{f?await Le(i):await Ie(i),L.value[`${t}-${e}`]=!1,await W(),Q.success(f?"排班更新成功！":"排班添加成功！")}catch(u){console.error("排班操作失败:",u),Q.error(f?"排班更新失败":"排班添加失败")}}function we(t,e,l){Object.keys(L.value).forEach(a=>{L.value[a]=!1}),I.nurseName=e,I.date=l,I.x=t.clientX,I.y=t.clientY,q.value=!0,t.preventDefault()}function Ze(t){t||(q.value=!1)}function Ge(t,e){const l=Y.value.findIndex(a=>a.nurse===t&&a.date===e);l>-1&&(Y.value.splice(l,1),ee(),Q.success("已清除排班")),q.value=!1}function Qe(t,e){q.value=!1,Z.value=!0;const l=new Date(e);D.dates=[l,l],D.nurses=[t];const a=Y.value.find(o=>o.nurse===t&&o.date===e);D.shift=a?a.type:""}function be(t,e){const l=Y.value.find(a=>a.nurse===t&&a.date===e);if(!l)return!1;if(l.type==="night"){const a=new Date(e);a.setDate(a.getDate()-1);const o=C(a,"YYYY-MM-DD"),n=new Date(e);n.setDate(n.getDate()-2);const r=C(n,"YYYY-MM-DD"),f=Y.value.find(u=>u.nurse===t&&u.date===o),i=Y.value.find(u=>u.nurse===t&&u.date===r);if(f&&i&&f.type==="night"&&i.type==="night")return!0}if(l.type==="night"){const a=new Date(e);a.setDate(a.getDate()-1);const o=C(a,"YYYY-MM-DD"),n=Y.value.find(r=>r.nurse===t&&r.date===o);if(n&&(n.type==="off"||n.type==="rest"))return!0}return!1}const Z=N(!1),D=Te({dates:[],nurses:[],shift:""});function Je(){Z.value=!0,D.dates=[],D.nurses=[],D.shift=""}async function et(){if(!D.dates.length||!D.nurses.length||!D.shift){Q.warning("请完整选择日期、护士和班次类型");return}const[t,e]=D.dates,l=new Date(t),a=new Date(e);let o=[];console.log("scheduleData",Y);for(let n=new Date(l);n<=a;n.setDate(n.getDate()+1)){const r=C(n,"YYYY-MM-DD");D.nurses.forEach(f=>{const i=F.value.find(V=>V.name===f),u=w.value.find(V=>V.value===D.shift);if(!i||!u)return;const x=new Date,T=Y.value.find(V=>V.nurse===i.name&&V.date===r),te=T&&T.id;console.log("existingSchedule",T),o.push({nurseId:i.id,nurseName:i.name,scheduleDate:`${r} 00:00:00`,shiftType:u.label,shiftTypeId:u.value,status:"0",updateBy:"",updateTime:ke(x),remark:"",id:te?T.id:void 0,params:{},searchValue:""})})}try{for(const n of o)n.id&&n.id!==void 0?await Le(n):await Ie(n);Z.value=!1,await W(),Q.success("排班设置成功！")}catch{Q.error("排班设置失败")}}const ne=N(!1);function tt(){ne.value=!0,Ve(lt)}function at(t){const e=w.value.find(f=>f.value===t||f.label===t);if(!e)return 0;if(e.label==="休息"||e.label==="调休"&&(!e.startTime||!e.endTime))return 8;if(e.label==="请假")return 4;if(!e.startTime||!e.endTime)return 0;const[l,a]=e.startTime.split(":").map(Number),[o,n]=e.endTime.split(":").map(Number);let r=o+n/60-(l+a/60);return r<0&&(r+=24),r}const se=H(()=>{const t=new Date;return F.value.map(e=>{const l=Y.value.filter(o=>{if(o.nurse!==e.name)return!1;const n=w.value.find(u=>u.value===o.type||u.label===o.type);let r=(n==null?void 0:n.endTime)||"23:59",f=(o.date||"").slice(0,10),i;if(n&&n.startTime&&n.endTime&&n.endTime<n.startTime){const u=new Date(f);u.setDate(u.getDate()+1),i=new Date(u.toISOString().slice(0,10)+"T"+r)}else i=new Date(f+"T"+r);return i<t}),a={name:e.name,total:0};return w.value.forEach(o=>{a[o.label]=0}),l.forEach(o=>{const n=w.value.find(i=>i.value===o.type||i.label===o.type),r=(n==null?void 0:n.label)||o.type,f=at(o.type);a[r]=(a[r]||0)+f,a.total+=f}),a.morning=a.早班||0,a.middle=a.中班||0,a.night=a.晚班||0,a.rest=a.调休||0,a.leave=a.请假||0,a.off=a.休息||0,a})});function lt(){const t=document.getElementById("stat-echart");if(!t)return;const e=Dt(t),l=se.value.map(n=>n.name),a=w.value.map(n=>n.label),o=a.map(n=>{const r=w.value.find(f=>f.label===n);return{name:n,type:"bar",data:se.value.map(f=>f[n]||0),itemStyle:{color:(r==null?void 0:r.color)||void 0},barWidth:30}});e.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:n=>{var i;const r=(i=n[0])==null?void 0:i.dataIndex;let f=`${l[r]}<br/>`;return n.forEach(u=>{f+=`${u.marker}${u.seriesName}: <span style='color:${u.color}'>${u.value}</span> 小时<br/>`}),f}},legend:{data:a,top:10},grid:{left:40,right:20,bottom:40,top:50},xAxis:{type:"category",data:l,axisLabel:{fontSize:15}},yAxis:{type:"value",minInterval:1,name:"小时"},series:o})}function ee(){Ve(()=>{console.log("视图已更新，搜索条件:",O.value,"选中护士:",E.value)})}ut(async()=>{await Ue(),await Ee(),await W(),document.addEventListener("click",De)}),it(()=>{document.removeEventListener("click",De)});function De(t){t&&t.target&&t.target.closest(".shift-popover, .week-shift-cell, .week-empty-cell, .el-dropdown-menu")||(Object.keys(L.value).forEach(e=>{L.value[e]=!1}),q.value=!1)}function ke(t){const e=new Date(t),l=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),o=String(e.getDate()).padStart(2,"0"),n=String(e.getHours()).padStart(2,"0"),r=String(e.getMinutes()).padStart(2,"0"),f=String(e.getSeconds()).padStart(2,"0");return`${l}-${a}-${o} ${n}:${r}:${f}`}function Se(t){if(!t)return"";const e=new Date(`2000-01-01T${t}`);return isNaN(e.getTime())?t:`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`}return(t,e)=>{const l=b("el-icon"),a=b("el-radio-button"),o=b("el-radio-group"),n=b("el-input"),r=b("el-button"),f=b("el-button-group"),i=b("el-date-picker"),u=b("el-avatar"),x=b("el-table-column"),T=b("el-popover"),te=b("el-tooltip"),V=b("el-dropdown-item"),nt=b("el-dropdown-menu"),ot=b("el-dropdown"),Ye=b("el-table"),$e=b("el-dialog"),re=b("el-form-item"),Me=b("el-option"),xe=b("el-select"),st=b("el-form");return p(),y("div",Mt,[d("div",xt,[d("div",Ct,[c(o,{modelValue:S.value,"onUpdate:modelValue":e[0]||(e[0]=s=>S.value=s),class:"view-switch"},{default:v(()=>[c(a,{label:"month"},{default:v(()=>[c(l,null,{default:v(()=>[c(B(ct))]),_:1}),Tt]),_:1}),c(a,{label:"week"},{default:v(()=>[c(l,null,{default:v(()=>[c(B(dt))]),_:1}),Vt]),_:1})]),_:1},8,["modelValue"]),c(n,{modelValue:O.value,"onUpdate:modelValue":e[1]||(e[1]=s=>O.value=s),placeholder:"请输入护士姓名",clearable:"",class:"nurse-search","prefix-icon":B(ft)},null,8,["modelValue","prefix-icon"])]),d("div",Nt,[d("div",It,[d("div",Lt,[c(f,null,{default:v(()=>[c(r,{onClick:We,icon:B(vt)},{default:v(()=>[z("上一"+k(S.value==="month"?"月":"周"),1)]),_:1},8,["icon"]),c(r,{onClick:Pe},{default:v(()=>[z("本"+k(S.value==="month"?"月":"周"),1)]),_:1}),c(r,{onClick:Re,icon:B(pt)},{default:v(()=>[z("下一"+k(S.value==="month"?"月":"周"),1)]),_:1},8,["icon"])]),_:1})]),d("div",Et,k(S.value==="month"?de.value:fe.value),1),d("div",Ut,[c(i,{modelValue:h.value,"onUpdate:modelValue":e[2]||(e[2]=s=>h.value=s),type:S.value==="month"?"month":"week",format:S.value==="month"?"YYYY年MM月":"YYYY 第 ww 周","value-format":S.value==="month"?"YYYY-MM":"YYYY-MM-DD",clearable:!1,onChange:qe,"disabled-date":S.value==="week"?He:void 0},null,8,["modelValue","type","format","value-format","disabled-date"])])])]),d("div",Bt,[c(r,{type:"primary",onClick:tt,icon:B(mt)},{default:v(()=>[z(" 工时统计 ")]),_:1},8,["icon"]),c(r,{type:"success",onClick:Je,style:{"margin-left":"12px"},icon:B(ht)},{default:v(()=>[z(" 排班设置 ")]),_:1},8,["icon"])])]),d("div",At,[(p(!0),y(A,null,j(w.value,s=>(p(),y("span",{key:s.value,class:ae(["shift-tag-item",{"shift-tag-selected":U.value===s.value}]),style:G({background:s.color}),onClick:_=>Oe(s.value)},[z(k(s.label)+" ",1),s.startTime&&s.endTime?(p(),y("span",zt,k(Se(s.startTime))+"-"+k(Se(s.endTime)),1)):ue("",!0)],14,jt))),128))]),S.value==="month"?(p(),y("div",Ft,[d("div",Ot,k(de.value),1),d("div",Wt,[d("div",Rt,[(p(),y(A,null,j(ie,s=>d("div",{key:s,class:"calendar-header-cell"},[d("span",Pt,k(s),1)])),64))]),(p(!0),y(A,null,j(Be.value,(s,_)=>(p(),y("div",{key:_,class:"calendar-row"},[(p(!0),y(A,null,j(s,(g,m)=>(p(),y("div",{key:m,class:ae(["calendar-cell",{"calendar-cell-today":g.isToday,"calendar-cell-other":!g.inMonth}])},[d("div",qt,k(g.day),1),d("div",Ht,[(p(!0),y(A,null,j(g.shifts,R=>(p(),y("div",{key:R.nurse,class:ae(["cell-shift",{"cell-shift-highlight":me(R.nurse)}]),onClick:ya=>he(R.nurse)},[d("div",Xt,[c(u,{src:R.avatar,size:"small",style:{"margin-right":"6px"}},null,8,["src"]),d("span",Zt,k(R.nurse),1)]),d("span",{class:"cell-shift-tag",style:G({background:ve(R.type)})},k(pe(R.type)),5)],10,Kt))),128))])],2))),128))]))),128))])])):(p(),y("div",Gt,[d("div",Qt,k(fe.value),1),c(Ye,{data:Ae.value,border:"",stripe:"",class:"week-table","header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"600",fontSize:"14px",height:"50px",padding:"0",textAlign:"center"},"cell-style":{padding:"8px 0",height:"60px"}},{default:v(()=>[c(x,{label:"护士姓名",width:"160"},{default:v(s=>[d("div",Jt,[c(u,{src:s.row.avatar,size:"small",style:{"margin-right":"8px"}},null,8,["src"]),d("span",{class:ae(["week-nurse-name",{"selected-nurse":me(s.row.name)}]),onClick:_=>he(s.row.name)},k(s.row.name),11,ea)])]),_:1}),(p(),y(A,null,j(ie,(s,_)=>c(x,{key:s,align:"center"},{header:v(()=>[d("div",ta,[d("div",aa,k(s),1),d("div",la,k(M.value[_].substring(5).replace("-","/")),1)])]),default:v(g=>[c(T,{visible:L.value[`${g.row.name}-${M.value[_]}`],"onUpdate:visible":m=>L.value[`${g.row.name}-${M.value[_]}`]=m,placement:"bottom",width:200,trigger:"manual","popper-class":"shift-popover","virtual-ref":_e.value[`${g.row.name}-${M.value[_]}`],"virtual-triggering":""},{default:v(()=>[d("div",na,[oa,d("div",sa,[(p(!0),y(A,null,j(w.value,m=>(p(),y("div",{key:m.value,class:"shift-option-item",style:G({borderLeft:`4px solid ${m.color}`}),onClick:R=>Xe(g.row.name,M.value[_],m.value)},[d("span",null,k(m.label),1)],12,ra))),128))])])]),_:2},1032,["visible","onUpdate:visible","virtual-ref"]),g.row.weekShifts[_]?(p(),y("div",{key:0,class:ae(["week-shift-cell",{"conflict-cell":be(g.row.name,M.value[_])}]),onClick:m=>ye(m,g.row.name,M.value[_]),onContextmenu:Ne(m=>we(m,g.row.name,M.value[_]),["prevent"]),ref_for:!0,ref:m=>ge(m,g.row.name,M.value[_])},[d("span",{class:"week-shift-tag",style:G({background:ve(g.row.weekShifts[_].type)})},k(pe(g.row.weekShifts[_].type)),5),be(g.row.name,M.value[_])?(p(),le(te,{key:0,effect:"dark",content:"排班冲突：该护士在此时段已有其他任务安排",placement:"top"},{default:v(()=>[c(l,{class:"conflict-icon"},{default:v(()=>[c(B(_t))]),_:1})]),_:1})):ue("",!0)],42,ua)):(p(),y("div",{key:1,class:"week-empty-cell",onClick:m=>ye(m,g.row.name,M.value[_]),onContextmenu:Ne(m=>we(m,g.row.name,M.value[_]),["prevent"]),ref_for:!0,ref:m=>ge(m,g.row.name,M.value[_])}," - ",40,ia)),q.value&&I.nurseName===g.row.name&&I.date===M.value[_]?(p(),le(ot,{key:2,visible:q.value,trigger:"contextmenu",placement:"bottom-start",teleported:!0,style:G(Ke.value),onVisibleChange:Ze},{dropdown:v(()=>[c(nt,null,{default:v(()=>[c(V,{onClick:e[3]||(e[3]=m=>Qe(I.nurseName,I.date))},{default:v(()=>[c(l,null,{default:v(()=>[c(B(gt))]),_:1}),da]),_:1}),c(V,{divided:"",onClick:e[4]||(e[4]=m=>Ge(I.nurseName,I.date))},{default:v(()=>[c(l,null,{default:v(()=>[c(B(yt))]),_:1}),fa]),_:1})]),_:1})]),default:v(()=>[ca]),_:1},8,["visible","style"])):ue("",!0)]),_:2},1024)),64))]),_:1},8,["data"])])),c($e,{modelValue:ne.value,"onUpdate:modelValue":e[6]||(e[6]=s=>ne.value=s),title:"工时统计",width:"900px",top:"5vh","destroy-on-close":"","close-on-click-modal":!1,class:"custom-dialog"},{footer:v(()=>[d("div",ma,[c(r,{onClick:e[5]||(e[5]=s=>ne.value=!1)},{default:v(()=>[z("关闭")]),_:1})])]),default:v(()=>[d("div",va,[c(Ye,{data:se.value,border:"",stripe:"",style:{"margin-bottom":"20px"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"600"}},{default:v(()=>[c(x,{prop:"name",label:"护士姓名",align:"center",width:"120"}),c(x,{prop:"total",label:"总工时",align:"center",width:"80"}),(p(!0),y(A,null,j(w.value,s=>(p(),le(x,{key:s.label,prop:s.label,label:s.label,align:"center",width:"80"},null,8,["prop","label"]))),128))]),_:1},8,["data"]),pa])]),_:1},8,["modelValue"]),c($e,{modelValue:Z.value,"onUpdate:modelValue":e[11]||(e[11]=s=>Z.value=s),title:"排班设置",width:"520px",top:"10vh","destroy-on-close":"","close-on-click-modal":!1,class:"custom-dialog"},{footer:v(()=>[d("div",_a,[c(r,{onClick:e[10]||(e[10]=s=>Z.value=!1)},{default:v(()=>[z("取消")]),_:1}),c(r,{type:"primary",onClick:et},{default:v(()=>[z("确定")]),_:1})])]),default:v(()=>[c(st,{model:D,"label-width":"90px",class:"setting-form"},{default:v(()=>[c(re,{label:"选择日期"},{default:v(()=>[c(i,{modelValue:D.dates,"onUpdate:modelValue":e[7]||(e[7]=s=>D.dates=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),c(re,{label:"选择护士"},{default:v(()=>[c(xe,{modelValue:D.nurses,"onUpdate:modelValue":e[8]||(e[8]=s=>D.nurses=s),multiple:"",filterable:"",placeholder:"请选择护士",style:{width:"100%"},"collapse-tags":"","collapse-tags-tooltip":""},{default:v(()=>[(p(!0),y(A,null,j(F.value,s=>(p(),le(Me,{key:s.name,label:s.name,value:s.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),c(re,{label:"班次类型"},{default:v(()=>[c(xe,{modelValue:D.shift,"onUpdate:modelValue":e[9]||(e[9]=s=>D.shift=s),placeholder:"请选择班次",style:{width:"100%"}},{default:v(()=>[(p(!0),y(A,null,j(w.value,s=>(p(),le(Me,{key:s.value,label:s.label,value:s.value},{default:v(()=>[d("div",ha,[d("span",{style:G({display:"inline-block",width:"16px",height:"16px",borderRadius:"3px",background:s.color,marginRight:"8px"})},null,4),d("span",null,k(s.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Da=rt(ga,[["__scopeId","data-v-35076ecf"]]);export{Da as default};
