import{h as $,i as Y}from"./wmscheckinOut-C2B7FNiS.js";import{_ as q,d as F,a as L,r as b,F as j,e as s,c as h,o as g,f as e,k as A,h as a,l as o,K as Q,L as R,j as G,n as c,t as K,E as H,G as J}from"./index-B0qHf98Y.js";const O={class:"warehousing-query"},W={key:0,class:"pagination"},X={__name:"index",setup(Z){const{proxy:C}=F(),{stock_in_type:V}=C.useDict("stock_in_type"),N=L(),l=b({stockInNo:"",stockInType:"",stockInPerson:"",stockInDate:"",manufacturer:"",creator:"",pageNum:1,pageSize:10}),y=b([]),f=b(0),x=()=>{l.value.pageNum=1,d()},D=()=>{l.value={stockInNo:"",stockInType:"",stockInPerson:"",stockInDate:"",manufacturer:"",creator:"",pageNum:1,pageSize:10},d()},z=r=>{l.value.pageSize=r,d()},S=r=>{l.value.pageNum=r,d()},d=()=>{console.log("Fetching data with params:",l.value),$({...l.value}).then(r=>{y.value=r.rows,f.value=r.total})},k=(r,n)=>{N.push({path:"/warehousingAddorEdit",query:{type:r,id:r==="add"?void 0:n}})},T=r=>{H.confirm("确认删除该入库单吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Y(r.id).then(()=>{J.success("删除成功"),l.value.pageNum=1,d()})})};return j(()=>{d()}),(r,n)=>{const _=s("el-input"),i=s("el-form-item"),p=s("el-col"),I=s("el-option"),P=s("el-select"),w=s("el-row"),U=s("el-date-picker"),m=s("el-button"),B=s("el-form"),u=s("el-table-column"),E=s("el-table"),M=s("el-pagination");return g(),h("div",O,[e(B,{model:o(l),"label-width":"100px"},{default:a(()=>[e(w,{gutter:20},{default:a(()=>[e(p,{span:6},{default:a(()=>[e(i,{label:"入库单号",prop:"stockInNo"},{default:a(()=>[e(_,{modelValue:o(l).stockInNo,"onUpdate:modelValue":n[0]||(n[0]=t=>o(l).stockInNo=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:a(()=>[e(i,{label:"入库类型",prop:"stockInType"},{default:a(()=>[e(P,{modelValue:o(l).stockInType,"onUpdate:modelValue":n[1]||(n[1]=t=>o(l).stockInType=t),placeholder:"全部",clearable:""},{default:a(()=>[e(I,{label:"全部",value:""}),(g(!0),h(Q,null,R(o(V),t=>(g(),G(I,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:a(()=>[e(i,{label:"入库人员",prop:"stockInPerson"},{default:a(()=>[e(_,{modelValue:o(l).stockInPerson,"onUpdate:modelValue":n[2]||(n[2]=t=>o(l).stockInPerson=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(w,{gutter:20},{default:a(()=>[e(p,{span:6},{default:a(()=>[e(i,{label:"入库日期",prop:"stockInDate"},{default:a(()=>[e(U,{modelValue:o(l).stockInDate,"onUpdate:modelValue":n[3]||(n[3]=t=>o(l).stockInDate=t),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD",clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:a(()=>[e(i,{label:"生产厂家",prop:"manufacturer"},{default:a(()=>[e(_,{modelValue:o(l).manufacturer,"onUpdate:modelValue":n[4]||(n[4]=t=>o(l).manufacturer=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:a(()=>[e(i,{label:"制单人",prop:"creator"},{default:a(()=>[e(_,{modelValue:o(l).creator,"onUpdate:modelValue":n[5]||(n[5]=t=>o(l).creator=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6,class:"button-group"},{default:a(()=>[e(m,{type:"primary",onClick:x,icon:"Search"},{default:a(()=>[c("查询")]),_:1}),e(m,{onClick:D,icon:"Refresh"},{default:a(()=>[c("重置")]),_:1}),e(m,{type:"primary",onClick:n[6]||(n[6]=t=>k("add")),icon:"Plus",plain:""},{default:a(()=>[c("新增入库")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e(E,{data:o(y),border:"",style:{width:"100%"}},{default:a(()=>[e(u,{prop:"index",label:"序号",width:"60",align:"center"},{default:a(t=>[c(K(t.$index+1),1)]),_:1}),e(u,{prop:"stockInNo",label:"入库单号",width:"150",align:"center"}),e(u,{prop:"stockInDate",label:"入库日期",width:"120",align:"center"}),e(u,{prop:"stockInType",label:"入库类型",width:"120",align:"center"}),e(u,{prop:"manufacturer",label:"生产厂家",width:"180",align:"center"}),e(u,{prop:"orderNo",label:"订单编号",width:"150",align:"center"}),e(u,{prop:"creator",label:"制单人",width:"120",align:"center"}),e(u,{prop:"purchaseAmount",label:"采购金额",width:"120",align:"center"}),e(u,{prop:"stockInPerson",label:"入库人员",width:"120",align:"center"}),e(u,{prop:"createTime",label:"创建时间","min-width":"180",align:"center"}),e(u,{label:"操作","min-width":"220",align:"center",fixed:"right"},{default:a(t=>[e(m,{link:"",type:"primary",icon:"Search",onClick:v=>k("view",t.row.id)},{default:a(()=>[c("详情")]),_:2},1032,["onClick"]),e(m,{link:"",type:"primary",icon:"Edit",onClick:v=>k("edit",t.row.id)},{default:a(()=>[c("修改")]),_:2},1032,["onClick"]),e(m,{link:"",type:"primary",icon:"Delete",onClick:v=>T(t.row)},{default:a(()=>[c("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o(f)>0?(g(),h("div",W,[e(M,{background:"","current-page":o(l).pageNum,"onUpdate:currentPage":n[7]||(n[7]=t=>o(l).pageNum=t),"page-size":o(l).pageSize,"onUpdate:pageSize":n[8]||(n[8]=t=>o(l).pageSize=t),total:o(f),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:z,onCurrentChange:S},null,8,["current-page","page-size","total"])])):A("",!0)])}}},ae=q(X,[["__scopeId","data-v-e3d22bad"]]);export{ae as default};
