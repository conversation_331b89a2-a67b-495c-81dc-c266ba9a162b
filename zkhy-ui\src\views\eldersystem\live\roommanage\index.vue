<template>
  <div class="dashboard-container">
    <!-- 顶部筛选条件 -->
    <div class="filters">
      <div class="filter-group">
        <el-form-item label="房间状态：">
        <el-button-group>
          <el-button
            v-for="status in roomStatusList"
            :key="status"
            :class="{
              'is-active': (status === '外出' ? filters.bedStatus === '外出' : filters.status === status)
            }"
            @click="handleFilterChange('status', status)"
          >{{ status }}</el-button>
        </el-button-group>
        </el-form-item>
      </div>
      <div class="filter-group">
        <el-form-item label="房间朝向：">
        <el-button-group>
          <el-button
            v-for="dir in directions"
            :key="dir"
            :class="{ 'is-active': filters.roomOrientation === dir }"
            @click="handleFilterChange('roomOrientation', dir)"
          >{{ dir }}</el-button>
        </el-button-group>
        </el-form-item>
      </div>
      <div class="filter-group">
        <el-form-item label="老人性别：">
        <el-button-group>
          <el-button
            v-for="gender in genders"
            :key="gender"
            :class="{ 'is-active': filters.gender === gender }"
            @click="handleFilterChange('gender', gender)"
          >{{ gender }}</el-button>
        </el-button-group>
      </el-form-item>
      </div>
      <div class="filter-group">
        <el-form-item label="房间区域：">
        <el-button-group>
          <el-button
            v-for="area in areas"
            :key="area"
            :class="{ 'is-active': filters.areaName === area }"
            @click="handleFilterChange('areaName', area)"
          >{{ area }}</el-button>
        </el-button-group>
        </el-form-item>
      </div>
      <div class="filter-group">
        <el-form-item label="房间类型：">
        <el-button-group>
          <el-button
            v-for="type in roomTypes"
            :key="type"
            :class="{ 'is-active': filters.roomType === type }"
            @click="handleFilterChange('roomType', type)"
          >{{ type }}</el-button>
        </el-button-group>
        </el-form-item>
      </div>
      <div class="filter-actions">
        <el-button @click="resetFilters">重置筛选</el-button>
      </div>
    </div>

    <!-- 统计条 -->
    <div class="summary-bar">
      <span>楼栋: <strong>{{ currentBuildingName }}</strong></span>
      <span>楼层: <strong>{{ currentFloorName }}</strong></span>
      <span>房间: <strong>{{ roomCards.length }}</strong></span>
      <span>床位: <strong>{{ totalBeds }}</strong></span>
      <span>空床: <strong>{{ vacantBeds }}</strong></span>
    </div>

    <!-- 主体内容 -->
    <div class="main-content">
      <!-- 左侧楼栋/楼层 -->
      <div class="sidebar">
        <div class="building-nav">
          <div
            v-for="building in buildings"
            :key="building.id"
            class="nav-item"
            :class="{ active: building.id === currentBuildingId }"
            @click="selectBuilding(building.id, building.buildingName)"
          >
            <i class="el-icon-office-building"></i>{{ building.buildingName }}
          </div>
        </div>
        <div class="building-graphic">
          <div
            v-for="floor in floors"
            :key="floor.id"
            class="building-floor"
            :style="{ background: floor.id === currentFloorId ? '#409eff' : 'rgba(255,255,255,0.6)', color: floor.id === currentFloorId ? '#fff' : '#303133' }"
            @click="selectFloor(floor.id, floor.floorName)"
          >{{ floor.floorName }}</div>
          <div class="building-base"></div>
          <div class="building-trees">🌲</div>
        </div>
      </div>

      <!-- 右侧房间网格 -->
      <div class="room-grid">
        <div v-if="roomCards.length === 0" class="no-data">暂无数据</div>
        <div
          v-for="room in roomCards"
          :key="room.roomId || room.id"
          class="room-card"
          v-else
        >
          <div class="room-header">
            <div class="room-info">
              <span>{{ room.roomNumber }}</span>
              <span class="camera-icon" style="margin-left:6px;vertical-align:middle;display:inline-block;width:18px;height:18px;">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" width="18" height="18">
                  <path d="M17 10.5V7C17 5.89543 16.1046 5 15 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19H15C16.1046 19 17 18.1046 17 17V13.5L21 17V7L17 10.5Z" stroke="#409eff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </span>
              {{ room.areaName }} {{ room.roomOrientation }}
            </div>
            <div class="room-status">
              <el-tag
                :type="room.status === '满员' ? 'primary' : room.status === '空闲' ? 'success' : room.status === '外出' ? 'warning' : 'info'"
                size="small"
              >
                {{ (room.occupiedCount || 0) + '/' + (room.capacity || 0) }}
              </el-tag>
            </div>
          </div>
          <div class="bed-grid">
            <div
              v-for="(bed, index) in room.beds || []"
              :key="bed.id || index"
              class="bed-card"
              :class="bedClass(bed)"
            >
              <div class="bed-icon">
                <img :src="bed.elderName ? (bed.gender === '男' ? bedmanImg : bedwomanImg) : bedfreeImg" alt="床位图标" class="bed-image">
                <span class="bed-number">{{ bed.bedNumber || (index + 1) }}</span>
              </div>
              <div class="bed-info" v-if="bed.elderName">
                <span class="name">{{ bed.elderName }}</span> {{ bed.age }}岁 <br />
                <span class="details"><span>{{ bed.careLevel }}</span> <br/><span>{{ bed.abilityLevel }}</span></span>
              </div>
              <div class="bed-info" v-else>可入住</div>
              <span v-if="bed.status === '外出'" class="away-tag">外</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getBuildingList, getFloorList, getRoomCardList } from '@/api/live/roommanage'
import bedmanImg from '@/assets/images/bedman.png'
import bedwomanImg from '@/assets/images/bedwoman.png'
import bedfreeImg from '@/assets/images/bedfree.png'

// 常量定义
const roomStatusList = ['满员', '空闲', '外出']
const directions = ['东', '南', '西', '北', '其他']
const genders = ['男', '女', '未知']
const areas = ['自理区', '介护区', '介助区', '康复区', '其他区']
const roomTypes = ['单人间', '双人间', '三人间', '四人间']

// 状态变量
const buildings = ref([])
const floors = ref([])
const roomCards = ref([])
const loading = ref(false)
const availableFilters = ref({
  roomTypes: [],
  areas: [],
  directions: [],
  statuses: [],
  genders: []
})

const currentBuildingId = ref(null)
const currentBuildingName = ref('全部')
const currentFloorId = ref(null)
const currentFloorName = ref('全部')

// 筛选条件
const filters = ref({
  buildingName: '',
  floorName: '',
  roomNumber: '',
  areaName: '',
  roomType: '',
  roomOrientation: '',
  status: '',
  bedStatus: '', // 添加床位状态字段，用于"外出"筛选
  elderName: '',
  gender: '',
  age: '',
  careLevel: '',
  abilityLevel: ''
})

// 生命周期钩子
onMounted(() => {
  fetchBuildingList()
})

// API调用函数
const fetchBuildingList = async () => {
  try {
    loading.value = true
    const res = await getBuildingList()
    if (res.code === 200) {
      buildings.value = res.rows || []
      if (buildings.value.length > 0) {
        selectBuilding(buildings.value[0].id, buildings.value[0].buildingName)
      } else {
        fetchRoomCardList()
      }
    }
  } catch (error) {
    console.error('获取楼栋列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchFloorList = async (buildingId) => {
  try {
    loading.value = true
    const res = await getFloorList(buildingId)
    if (res.code === 200) {
      floors.value = res.rows || []
    }
  } catch (error) {
    console.error('获取楼层列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchRoomCardList = async () => {
  try {
    loading.value = true
    const queryParams = { ...filters.value }

    // 保存性别筛选条件，但从API请求参数中移除
    const genderFilter = queryParams.gender
    delete queryParams.gender

    // 移除空值参数
    Object.keys(queryParams).forEach(key => {
      if (!queryParams[key]) {
        delete queryParams[key]
      }
    })

    console.log('API请求参数:', queryParams)
    const res = await getRoomCardList(queryParams)
    if (res.code === 200) {
      // 清空现有数据
      roomCards.value = []

      // 按房间唯一key分组（楼栋+楼层+房间号）
      const roomMap = new Map()
      const data = res.data || []
      data.forEach(room => {
        // 生成唯一key
        const roomKey = `${room.buildingName || ''}_${room.floorName || ''}_${room.roomNumber || room.id || room.roomId}`
        if (!roomMap.has(roomKey)) {
          roomMap.set(roomKey, {
            ...room,
            beds: []
          })
        }

        // 合并床位信息
        const existingRoom = roomMap.get(roomKey)
        if (room.elderName) {
          // 检查该老人是否已存在于当前房间
          const isDuplicate = existingRoom.beds.some(bed => bed.elderName === room.elderName)
          if (!isDuplicate) {
            // 处理性别数据
            const gender = room.gender === '0' ? '女' : room.gender === '1' ? '男' : '未知'

            // 处理床位状态
            let bedStatus = 'occupied'
            if (room.bedStatus === '外出') {
              bedStatus = '外出'
            }

            // 按bedNumber插入
            if (room.bedNumber) {
              existingRoom.beds[room.bedNumber - 1] = {
                elderName: room.elderName,
                age: room.age,
                gender: gender,
                careLevel: room.careLevel || '未设置',
                abilityLevel: room.abilityLevel || '未设置',
                status: bedStatus,
                bedNumber: room.bedNumber
              }
            } else {
              existingRoom.beds.push({
                elderName: room.elderName,
                age: room.age,
                gender: gender,
                careLevel: room.careLevel || '未设置',
                abilityLevel: room.abilityLevel || '未设置',
                status: bedStatus
              })
            }
        }}
      })

      // 转换为数组
      roomCards.value = Array.from(roomMap.values())

      // 处理房间数据，为每个房间添加床位信息
      roomCards.value = roomCards.value.map(room => {
        // 如果没有床位信息，添加空数组
        if (!room.beds) {
          room.beds = []
        }

        // 直接使用房间容量capacity作为床位数
        const bedCount = room.capacity || 0

        // 如果床位数小于容量，补空床位
        for (let i = 0; i < bedCount; i++) {
          if (!room.beds[i]) {
            room.beds[i] = { bedNumber: i + 1 }
          } else if (!room.beds[i].bedNumber) {
            room.beds[i].bedNumber = i + 1
          }
        }
        // 如果床位数大于容量，截断床位数组
        if (room.beds.length > bedCount) {
          room.beds = room.beds.slice(0, bedCount)
        }

        // 计算已占用床位数和房间状态
        const occupiedCount = room.beds.filter(bed => bed.elderName).length
        room.occupiedCount = occupiedCount

        if (occupiedCount === 0) {
          room.status = '空闲'
        } else if (occupiedCount === bedCount) {
          room.status = '满员'
        } else {
          room.status = '部分入住'
        }

        return room
      })

      // 前端处理性别筛选
      if (genderFilter) {
        console.log('应用性别筛选:', genderFilter)
        roomCards.value = roomCards.value.filter(room => {
          // 检查房间中是否有符合性别筛选条件的床位
          const hasMatchingBed = room.beds.some(bed => bed.gender === genderFilter)

          if (hasMatchingBed) {
            // 保留房间，但只显示符合条件的床位
            room.beds = room.beds.filter(bed => !bed.elderName || bed.gender === genderFilter)

            // 更新房间的占用信息
            const occupiedCount = room.beds.filter(bed => bed.elderName).length
            room.occupiedCount = occupiedCount

            return true
          }

          return false
        })
      }

      // 更新可用的筛选选项
      updateAvailableFilters()
    }
  } catch (error) {
    console.error('获取房间卡片列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新可用的筛选选项
const updateAvailableFilters = () => {
  // 从当前结果中提取可用的筛选选项
  const roomTypesSet = new Set()
  const areasSet = new Set()
  const directionsSet = new Set()
  const statusesSet = new Set()
  const gendersSet = new Set()

  roomCards.value.forEach(room => {
    if (room.roomType) roomTypesSet.add(room.roomType)
    if (room.areaName) areasSet.add(room.areaName)
    if (room.roomOrientation) directionsSet.add(room.roomOrientation)
    if (room.status) statusesSet.add(room.status)

    // 从床位中提取性别信息
    if (room.beds && room.beds.length > 0) {
      room.beds.forEach(bed => {
        if (bed.gender) gendersSet.add(bed.gender)
      })
    }
  })

  // 更新可用筛选选项
  availableFilters.value = {
    roomTypes: Array.from(roomTypesSet),
    areas: Array.from(areasSet),
    directions: Array.from(directionsSet),
    statuses: Array.from(statusesSet),
    genders: Array.from(gendersSet)
  }
}

// 筛选条件处理函数
const handleFilterChange = (key, value) => {
  if (key === 'status' && value === '外出') {
    // 外出特殊处理
    if (filters.value.bedStatus === '外出') {
      filters.value.bedStatus = ''
    } else {
      filters.value.bedStatus = '外出'
      filters.value.status = ''
    }
  } else if (key === 'status') {
    if (filters.value.status === value) {
      filters.value.status = ''
    } else {
      filters.value.status = value
    }
    filters.value.bedStatus = ''
  } else {
    if (filters.value[key] === value) {
      filters.value[key] = ''
    } else {
      filters.value[key] = value
    }
  }
  fetchRoomCardList()
}

// 重置筛选条件
const resetFilters = () => {
  // 保存当前的楼栋和楼层信息
  const currentBuildingName = filters.value.buildingName
  const currentFloorName = filters.value.floorName

  // 重置所有筛选条件
  Object.keys(filters.value).forEach(key => {
    filters.value[key] = ''
  })

  // 恢复楼栋和楼层信息
  filters.value.buildingName = currentBuildingName
  filters.value.floorName = currentFloorName

  // 确保bedStatus也被重置
  filters.value.bedStatus = ''

  // 获取房间卡片列表
  fetchRoomCardList()
}

// 事件处理函数
const selectBuilding = (id, name) => {
  currentBuildingId.value = id
  currentBuildingName.value = name || '全部'
  currentFloorId.value = null
  currentFloorName.value = '全部'

  // 更新筛选条件
  filters.value.buildingName = name || ''
  filters.value.floorName = ''

  // 获取楼层列表
  if (id) {
    fetchFloorList(id)
  } else {
    floors.value = []
  }

  // 获取房间卡片列表
  fetchRoomCardList()
}

const selectFloor = (id, name) => {
  currentFloorId.value = id
  currentFloorName.value = name || '全部'

  // 更新筛选条件
  filters.value.floorName = name || ''

  // 获取房间卡片列表
  fetchRoomCardList()
}

// 计算属性
const totalBeds = computed(() => {
  return roomCards.value.reduce((sum, room) => {
    return sum + (room.beds?.length || 0)
  }, 0)
})

const vacantBeds = computed(() => {
  return roomCards.value.reduce((sum, room) => {
    if (!room.beds) return sum
    return sum + room.beds.filter(bed => !bed.elderName).length
  }, 0)
})

// 工具函数
function bedClass(bed) {
  if (!bed.elderName) return 'vacant'
  if (bed.gender === '男') return 'occupied-male'
  if (bed.gender === '女') return 'occupied-female'
  return 'occupied-other'
}
</script>

<style scoped>
@import url('https://unpkg.com/element-plus/dist/index.css');

/* 全局样式 */
.dashboard-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  height: 100vh;
  min-height: 100vh;
  overflow: auto;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

/* 筛选条件样式 */
.filters, .summary-bar {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
}

.filters .el-button-group {
  margin-right: 20px;
}

.filters .filter-label {
  color: #606266;
  margin-right: 8px;
  font-weight: bold;
  white-space: nowrap;
}

.filters .el-form-item {
  margin-bottom: 0;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.filter-actions .el-button {
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 500;
}

/* 统计条样式 */
.summary-bar {
  margin-top: 10px;
  margin-bottom: 15px;
  background-color: #f5f7fa;
  padding: 12px 15px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.summary-bar span {
  margin-right: 20px;
  color: #606266;
  font-size: 14px;
}

.summary-bar span strong {
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 4px;
}

/* 主体内容样式 */
.main-content {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

/* 侧边栏样式 */
.sidebar {
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: flex-start;
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  width: 280px;
}

.building-nav {
  width: 120px;
  flex-shrink: 0;
}

.building-nav .nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px 5px;
  margin-bottom: 8px;
  border-radius: 6px;
  cursor: pointer;
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.building-nav .nav-item.active {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.building-nav .nav-item:hover:not(.active) {
  background-color: #d9ecff;
  transform: translateY(-2px);
}

.building-nav .nav-item i {
  margin-right: 5px;
  font-size: 14px;
}

/* 楼层图形样式 */
.building-graphic {
  background-color: #e9eef3;
  border-radius: 8px;
  padding: 10px;
  position: relative;
  height: 300px;
  width: 120px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  background: linear-gradient(to bottom, #a0cfff, #d9ecff);
  border: 1px solid #b3d8ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), inset 0 0 10px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.building-floor {
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid #a0cfff;
  width: 90%;
  height: 16%;
  margin-bottom: 4%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #303133;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-size: 13px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 5px;
  box-sizing: border-box;
}

.building-floor:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateX(3px);
}

.building-base {
  width: 95%;
  height: 6%;
  background-color: #909399;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.building-trees {
  position: absolute;
  bottom: 8px;
  left: 8px;
  width: 30px;
  height: auto;
  color: #67c23a;
  font-size: 26px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* 房间网格样式 */
.room-grid {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.room-card {
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.room-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #ecf5ff;
  border-bottom: 1px solid #dcdfe6;
  color: #303133;
}

.room-header .room-info {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.room-header .room-info span {
  margin-right: 10px;
  font-weight: bold;
  font-size: 16px;
  color: #303133;
}

.room-header .room-status {
  display: flex;
  align-items: center;
}

.room-header .room-status .el-tag {
  margin-left: 5px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

.bed-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  padding: 15px;
  flex-grow: 1;
}

.bed-card {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  border: 1px solid #e9ecef;
  position: relative;
  transition: all 0.2s ease;
}

.bed-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bed-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bed-image {
  width: 36px;
  height: 36px;
  object-fit: contain;
}

.bed-number {
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #909399;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bed-card.occupied-male .bed-number {
  background-color: #409eff;
}

.bed-card.occupied-female .bed-number {
  background-color: #f56c6c;
}

.bed-card.occupied-other .bed-number {
  background-color: #909399;
}

.bed-card.vacant .bed-number {
  background-color: #67c23a;
}

.no-data {
  font-size: 16px;
  line-height: 1.5;
  color: #909399;
  text-align: center;
  width: 100%;
  padding: 30px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.bed-info {
  font-size: 13px;
  line-height: 1.6;
  color: #606266;
}

.bed-info .name {
  font-weight: bold;
  color: #303133;
  margin-right: 5px;
  font-size: 14px;
}

.bed-info .details span {
  margin-right: 8px;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.bed-card.vacant .bed-info {
  color: #67c23a;
  font-weight: bold;
}

.away-tag {
  position: absolute;
  bottom: 6px;
  right: 6px;
  background-color: #e6a23c;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-details {
  text-align: center;
  padding: 10px;
  color: #909399;
  font-size: 13px;
  border-top: 1px solid #ebeef5;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: auto;
}

.view-details:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

/* 按钮样式 */
.filters .el-button {
  padding: 8px 12px;
  font-size: 13px;
}

.filters .el-button.is-active {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.filters .el-button:not(.is-active) {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.filters .el-button:hover:not(.is-active) {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .room-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 992px) {
  .room-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    flex-direction: row;
    justify-content: center;
    margin-bottom: 20px;
    gap: 15px;
  }

  .building-nav {
    width: 45%;
  }

  .building-graphic {
    width: 45%;
    height: 250px;
  }

  .filters {
    flex-direction: column;
    align-items: flex-start;
  }

  .filters .el-button-group {
    margin-right: 0;
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .sidebar {
    flex-direction: column;
    align-items: center;
  }

  .building-nav,
  .building-graphic {
    width: 100%;
  }

  .bed-grid {
    grid-template-columns: 1fr;
  }

  .room-grid {
    grid-template-columns: 1fr;
  }

  .filters .filter-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    width: 100%;
  }

  .filters .filter-label {
    width: 100%;
    margin-bottom: 5px;
  }

  .summary-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .summary-bar span {
    margin-right: 0;
  }

  .dashboard-container {
    padding: 15px;
  }
}
</style>
