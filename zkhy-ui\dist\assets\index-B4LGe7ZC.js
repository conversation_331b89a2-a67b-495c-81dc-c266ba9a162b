import{B as Z,d as ee,a as le,r as s,C as ae,N as oe,e as i,I as ne,c as k,o as v,J as w,f as e,O as D,l as a,h as t,m as g,K as S,L as M,j as P,n as U,i as Q,D as te}from"./index-B0qHf98Y.js";import{l as ue,u as de,a as re}from"./tWarehouseMedication-Ycu1QDaW.js";const ie={class:"app-container"},pe=["innerHTML"],me={class:"dialog-footer"},se=Z({name:"Medication"}),xe=Object.assign(se,{setup(ce){const{proxy:f}=ee(),B=le(),x=s([]),V=s(!1),_=s(!0),O=s(!0);s([]),s(!0),s(!0);const C=s(0),R=s(""),{medication_type:fe,medication_dosage:Ve,is_otc:ge,invoice_items:be,packing_unit:ye,dosage_unit:ve,usage_type:Ue,goods_status:_e,goods_type:K}=f.useDict("medication_type","medication_dosage","is_otc","invoice_items","packing_unit","dosage_unit","usage_type","goods_status","goods_type"),T=[{id:1,label:"正常",value:"正常"},{id:2,label:"预警",value:"预警"},{id:3,label:"空盘",value:"空盘"}],L=ae({form:{},queryParams:{pageNum:1,pageSize:10,medicineCode:null,barcode:null,medicineName:null,pinyinCode:null,category:null,specification:null,dosageForm:null,isOtc:null,invoiceItem:null,approvalNumber:null,manufacturer:null,status:null,packageUnit:null,baseFactor:null,baseUnit:null,dosageFactor:null,dosageUnit:null,purchasePrice:null,retailPrice:null,usageMethod:null,singleDose:null,maxInventory:null,minInventory:null,warehouse:null,locationCode:null,expiryWarningDays:null,currentQuantity:null},rules:{}}),{queryParams:r,form:n,rules:W}=oe(L);function b(){_.value=!0,ue(r.value).then(p=>{x.value=p.rows,C.value=p.total,_.value=!1})}function h(){V.value=!1,q()}function q(){n.value={id:null,medicineCode:null,barcode:null,medicineName:null,pinyinCode:null,category:null,specification:null,dosageForm:null,isOtc:null,invoiceItem:null,approvalNumber:null,manufacturer:null,status:null,packageUnit:null,baseFactor:null,baseUnit:null,dosageFactor:null,dosageUnit:null,purchasePrice:null,retailPrice:null,usageMethod:null,singleDose:null,maxInventory:null,minInventory:null,warehouse:null,locationCode:null,expiryWarningDays:null,currentQuantity:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null},f.resetForm("medicationRef")}function c(){r.value.pageNum=1,b()}function Y(){f.resetForm("queryRef"),c()}function $(p){B.push("/wmsmedication/addManagement/show/"+p.id)}function j(p,o){return o<=0?"<span style='color: #D9001B'>空盘</span>":p>o?"<span style='color: #bfbf00'>预警</span>":"<span style='color: #09d971fe'>正常</span>"}function z(){f.$refs.medicationRef.validate(p=>{p&&(n.value.id!=null?de(n.value).then(o=>{f.$modal.msgSuccess("修改成功"),V.value=!1,b()}):re(n.value).then(o=>{f.$modal.msgSuccess("新增成功"),V.value=!1,b()}))})}return b(),(p,o)=>{const d=i("el-input"),u=i("el-form-item"),N=i("el-option"),F=i("el-select"),I=i("el-form"),y=i("el-button"),H=i("el-row"),m=i("el-table-column"),E=i("el-table"),J=i("pagination"),A=i("el-date-picker"),G=i("el-dialog"),X=ne("loading");return v(),k("div",ie,[w(e(I,{model:a(r),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[e(u,{label:"物品编码",prop:"medicineCode"},{default:t(()=>[e(d,{modelValue:a(r).medicineCode,"onUpdate:modelValue":o[0]||(o[0]=l=>a(r).medicineCode=l),placeholder:"请输入物品编码",clearable:"",onKeyup:g(c,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"物品名称",prop:"medicineName"},{default:t(()=>[e(d,{modelValue:a(r).medicineName,"onUpdate:modelValue":o[1]||(o[1]=l=>a(r).medicineName=l),placeholder:"请输入物品名称",clearable:"",onKeyup:g(c,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"物品类型",prop:"goodsCategory"},{default:t(()=>[e(F,{modelValue:a(r).goodsCategory,"onUpdate:modelValue":o[2]||(o[2]=l=>a(r).goodsCategory=l),placeholder:"请选择",clearable:"",style:{width:"200px"},onKeyup:g(c,["enter"])},{default:t(()=>[(v(!0),k(S,null,M(a(K),l=>(v(),P(N,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"库存状态",prop:"inventoryStatusStr"},{default:t(()=>[e(F,{modelValue:a(r).inventoryStatusStr,"onUpdate:modelValue":o[3]||(o[3]=l=>a(r).inventoryStatusStr=l),placeholder:"请选择",clearable:"",style:{width:"200px"},onKeyup:g(c,["enter"])},{default:t(()=>[(v(),k(S,null,M(T,l=>e(N,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"货位号",prop:"locationCode"},{default:t(()=>[e(d,{modelValue:a(r).locationCode,"onUpdate:modelValue":o[4]||(o[4]=l=>a(r).locationCode=l),placeholder:"请输入货位号",clearable:"",onKeyup:g(c,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"制单人",prop:"currentQuantity"},{default:t(()=>[e(d,{modelValue:a(r).currentQuantity,"onUpdate:modelValue":o[5]||(o[5]=l=>a(r).currentQuantity=l),placeholder:"请输入制单人",clearable:"",onKeyup:g(c,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),[[D,a(O)]]),e(H,{gutter:10,class:"mb8",justify:"end"},{default:t(()=>[e(y,{type:"primary",icon:"Search",onClick:c},{default:t(()=>[U("搜索")]),_:1}),e(y,{icon:"Refresh",onClick:Y},{default:t(()=>[U("重置")]),_:1})]),_:1}),w((v(),P(E,{data:a(x),border:"",stripe:""},{default:t(()=>[e(m,{type:"index",label:"序号",width:"55",align:"center"}),e(m,{label:"物品编码",align:"center",prop:"medicineCode"}),e(m,{label:"物品名称",align:"center",prop:"medicineName"}),e(m,{label:"物品规格",align:"center",prop:"specification"}),e(m,{label:"物品类型",align:"center",prop:"goodsCategory"}),e(m,{label:"货位号",align:"center",prop:"locationCode"}),e(m,{label:"库存状态",align:"center",prop:"locationCode"},{default:t(l=>[Q("span",{innerHTML:j(l.row.minInventory,l.row.currentQuantity)},null,8,pe)]),_:1}),e(m,{label:"库存数量",align:"center",prop:"currentQuantity"}),e(m,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(l=>[e(y,{link:"",type:"primary",icon:"Search",onClick:Ce=>$(l.row)},{default:t(()=>[U("出入明细")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,a(_)]]),w(e(J,{total:a(C),page:a(r).pageNum,"onUpdate:page":o[6]||(o[6]=l=>a(r).pageNum=l),limit:a(r).pageSize,"onUpdate:limit":o[7]||(o[7]=l=>a(r).pageSize=l),onPagination:b},null,8,["total","page","limit"]),[[D,a(C)>0]]),e(G,{title:a(R),modelValue:a(V),"onUpdate:modelValue":o[35]||(o[35]=l=>te(V)?V.value=l:null),width:"500px","append-to-body":""},{footer:t(()=>[Q("div",me,[e(y,{type:"primary",onClick:z},{default:t(()=>[U("确 定")]),_:1}),e(y,{onClick:h},{default:t(()=>[U("取 消")]),_:1})])]),default:t(()=>[e(I,{ref:"medicationRef",model:a(n),rules:a(W),"label-width":"80px"},{default:t(()=>[e(u,{label:"药品编码",prop:"medicineCode"},{default:t(()=>[e(d,{modelValue:a(n).medicineCode,"onUpdate:modelValue":o[8]||(o[8]=l=>a(n).medicineCode=l),placeholder:"请输入药品编码"},null,8,["modelValue"])]),_:1}),e(u,{label:"条形码",prop:"barcode"},{default:t(()=>[e(d,{modelValue:a(n).barcode,"onUpdate:modelValue":o[9]||(o[9]=l=>a(n).barcode=l),placeholder:"请输入条形码"},null,8,["modelValue"])]),_:1}),e(u,{label:"药品名称",prop:"medicineName"},{default:t(()=>[e(d,{modelValue:a(n).medicineName,"onUpdate:modelValue":o[10]||(o[10]=l=>a(n).medicineName=l),placeholder:"请输入药品名称"},null,8,["modelValue"])]),_:1}),e(u,{label:"拼音码",prop:"pinyinCode"},{default:t(()=>[e(d,{modelValue:a(n).pinyinCode,"onUpdate:modelValue":o[11]||(o[11]=l=>a(n).pinyinCode=l),placeholder:"请输入拼音码"},null,8,["modelValue"])]),_:1}),e(u,{label:"药品分类",prop:"category"},{default:t(()=>[e(d,{modelValue:a(n).category,"onUpdate:modelValue":o[12]||(o[12]=l=>a(n).category=l),placeholder:"请输入药品分类"},null,8,["modelValue"])]),_:1}),e(u,{label:"药品规格",prop:"specification"},{default:t(()=>[e(d,{modelValue:a(n).specification,"onUpdate:modelValue":o[13]||(o[13]=l=>a(n).specification=l),placeholder:"请输入药品规格"},null,8,["modelValue"])]),_:1}),e(u,{label:"药品剂型",prop:"dosageForm"},{default:t(()=>[e(d,{modelValue:a(n).dosageForm,"onUpdate:modelValue":o[14]||(o[14]=l=>a(n).dosageForm=l),placeholder:"请输入药品剂型"},null,8,["modelValue"])]),_:1}),e(u,{label:"OTC药品(0否1是)",prop:"isOtc"},{default:t(()=>[e(d,{modelValue:a(n).isOtc,"onUpdate:modelValue":o[15]||(o[15]=l=>a(n).isOtc=l),placeholder:"请输入OTC药品(0否1是)"},null,8,["modelValue"])]),_:1}),e(u,{label:"发票项目",prop:"invoiceItem"},{default:t(()=>[e(d,{modelValue:a(n).invoiceItem,"onUpdate:modelValue":o[16]||(o[16]=l=>a(n).invoiceItem=l),placeholder:"请输入发票项目"},null,8,["modelValue"])]),_:1}),e(u,{label:"批准文号",prop:"approvalNumber"},{default:t(()=>[e(d,{modelValue:a(n).approvalNumber,"onUpdate:modelValue":o[17]||(o[17]=l=>a(n).approvalNumber=l),placeholder:"请输入批准文号"},null,8,["modelValue"])]),_:1}),e(u,{label:"生产厂家",prop:"manufacturer"},{default:t(()=>[e(d,{modelValue:a(n).manufacturer,"onUpdate:modelValue":o[18]||(o[18]=l=>a(n).manufacturer=l),placeholder:"请输入生产厂家"},null,8,["modelValue"])]),_:1}),e(u,{label:"包装单位",prop:"packageUnit"},{default:t(()=>[e(d,{modelValue:a(n).packageUnit,"onUpdate:modelValue":o[19]||(o[19]=l=>a(n).packageUnit=l),placeholder:"请输入包装单位"},null,8,["modelValue"])]),_:1}),e(u,{label:"基本系数",prop:"baseFactor"},{default:t(()=>[e(d,{modelValue:a(n).baseFactor,"onUpdate:modelValue":o[20]||(o[20]=l=>a(n).baseFactor=l),placeholder:"请输入基本系数"},null,8,["modelValue"])]),_:1}),e(u,{label:"基本单位",prop:"baseUnit"},{default:t(()=>[e(d,{modelValue:a(n).baseUnit,"onUpdate:modelValue":o[21]||(o[21]=l=>a(n).baseUnit=l),placeholder:"请输入基本单位"},null,8,["modelValue"])]),_:1}),e(u,{label:"剂量系数",prop:"dosageFactor"},{default:t(()=>[e(d,{modelValue:a(n).dosageFactor,"onUpdate:modelValue":o[22]||(o[22]=l=>a(n).dosageFactor=l),placeholder:"请输入剂量系数"},null,8,["modelValue"])]),_:1}),e(u,{label:"剂量单位",prop:"dosageUnit"},{default:t(()=>[e(d,{modelValue:a(n).dosageUnit,"onUpdate:modelValue":o[23]||(o[23]=l=>a(n).dosageUnit=l),placeholder:"请输入剂量单位"},null,8,["modelValue"])]),_:1}),e(u,{label:"采购价(元)",prop:"purchasePrice"},{default:t(()=>[e(d,{modelValue:a(n).purchasePrice,"onUpdate:modelValue":o[24]||(o[24]=l=>a(n).purchasePrice=l),placeholder:"请输入采购价(元)"},null,8,["modelValue"])]),_:1}),e(u,{label:"零售价(元)",prop:"retailPrice"},{default:t(()=>[e(d,{modelValue:a(n).retailPrice,"onUpdate:modelValue":o[25]||(o[25]=l=>a(n).retailPrice=l),placeholder:"请输入零售价(元)"},null,8,["modelValue"])]),_:1}),e(u,{label:"用法",prop:"usageMethod"},{default:t(()=>[e(d,{modelValue:a(n).usageMethod,"onUpdate:modelValue":o[26]||(o[26]=l=>a(n).usageMethod=l),placeholder:"请输入用法"},null,8,["modelValue"])]),_:1}),e(u,{label:"单次用量",prop:"singleDose"},{default:t(()=>[e(d,{modelValue:a(n).singleDose,"onUpdate:modelValue":o[27]||(o[27]=l=>a(n).singleDose=l),placeholder:"请输入单次用量"},null,8,["modelValue"])]),_:1}),e(u,{label:"库存上限",prop:"maxInventory"},{default:t(()=>[e(d,{modelValue:a(n).maxInventory,"onUpdate:modelValue":o[28]||(o[28]=l=>a(n).maxInventory=l),placeholder:"请输入库存上限"},null,8,["modelValue"])]),_:1}),e(u,{label:"库存下限",prop:"minInventory"},{default:t(()=>[e(d,{modelValue:a(n).minInventory,"onUpdate:modelValue":o[29]||(o[29]=l=>a(n).minInventory=l),placeholder:"请输入库存下限"},null,8,["modelValue"])]),_:1}),e(u,{label:"仓库",prop:"warehouse"},{default:t(()=>[e(d,{modelValue:a(n).warehouse,"onUpdate:modelValue":o[30]||(o[30]=l=>a(n).warehouse=l),placeholder:"请输入仓库"},null,8,["modelValue"])]),_:1}),e(u,{label:"货位号",prop:"locationCode"},{default:t(()=>[e(d,{modelValue:a(n).locationCode,"onUpdate:modelValue":o[31]||(o[31]=l=>a(n).locationCode=l),placeholder:"请输入货位号"},null,8,["modelValue"])]),_:1}),e(u,{label:"有效期预警(天)",prop:"expiryWarningDays"},{default:t(()=>[e(A,{clearable:"",modelValue:a(n).expiryWarningDays,"onUpdate:modelValue":o[32]||(o[32]=l=>a(n).expiryWarningDays=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择有效期预警(天)"},null,8,["modelValue"])]),_:1}),e(u,{label:"库存数量",prop:"currentQuantity"},{default:t(()=>[e(d,{modelValue:a(n).currentQuantity,"onUpdate:modelValue":o[33]||(o[33]=l=>a(n).currentQuantity=l),placeholder:"请输入库存数量"},null,8,["modelValue"])]),_:1}),e(u,{label:"备注",prop:"remark"},{default:t(()=>[e(d,{modelValue:a(n).remark,"onUpdate:modelValue":o[34]||(o[34]=l=>a(n).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{xe as default};
