import{B as W,d as X,r as u,C as Y,N as Z,e as r,I as ee,c as le,o as x,J as w,f as e,O as D,l as t,h as s,m as N,k as T,n as v,j as te,i as b,t as I,D as ae}from"./index-B0qHf98Y.js";import{l as se,d as oe,u as ne,a as re}from"./assessmentRecord-4xWX4TZA.js";const de={class:"app-container"},ue=b("span",null,"查看",-1),me={class:"dialog-footer"},ie=W({name:"AssessmentRecord"}),ge=Object.assign(ie,{props:{elderId:{type:String,default:null},isShow:{type:Boolean,default:!1}},setup(U){const{proxy:m}=X(),{assessment_manager:F}=m.useDict("assessment_manager"),R=u([]),p=u(!1),V=u(!0),A=u(!0),M=u([]);u(!0),u(!0);const h=u(0),B=u(""),O=Y({form:{},queryParams:{pageNum:1,pageSize:10,elderId:null,assessmentFormId:null,assessmentOrgName:null,assessmentMethod:null},rules:{elderId:[{required:!0,message:"关联的老人ID不能为空",trigger:"blur"}],assessmentFormId:[{required:!0,message:"评估表单id不能为空",trigger:"blur"}]}}),{queryParams:n,form:o,rules:q}=Z(O),$=U;function f(){V.value=!0,n.value.elderId=$.elderId,se(n.value).then(i=>{R.value=i.rows,h.value=i.total,V.value=!1})}function K(){p.value=!1,S()}function S(){o.value={id:null,elderId:null,assessmentFormId:null,assessmentOrgName:null,assessmentMethod:null,remark:null,createTime:null,updateTime:null,createBy:null,updateBy:null},m.resetForm("assessmentRecordRef")}function g(){n.value.pageNum=1,f()}function P(){m.resetForm("queryRef"),g()}function pe(){S(),p.value=!0,B.value="添加评估信息记录"}function z(){m.$refs.assessmentRecordRef.validate(i=>{i&&(o.value.id!=null?ne(o.value).then(a=>{m.$modal.msgSuccess("修改成功"),p.value=!1,f()}):re(o.value).then(a=>{m.$modal.msgSuccess("新增成功"),p.value=!1,f()}))})}function ce(i){const a=i.id||M.value;m.$modal.confirm('是否确认删除评估信息记录编号为"'+a+'"的数据项？').then(function(){return oe(a)}).then(()=>{f(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}return f(),(i,a)=>{const j=r("el-date-picker"),d=r("el-form-item"),c=r("el-input"),y=r("el-button"),C=r("el-form"),_=r("el-table-column"),E=r("dict-span"),L=r("router-link"),Q=r("el-table"),J=r("pagination"),G=r("el-dialog"),H=ee("loading");return x(),le("div",de,[w(e(C,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:s(()=>[e(d,{label:"评估时间",prop:"createTime"},{default:s(()=>[e(j,{modelValue:t(n).createTime,"onUpdate:modelValue":a[0]||(a[0]=l=>t(n).createTime=l),type:"date",placeholder:"请输入评估时间",clearable:"",onKeyup:N(g,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"评估标题",prop:"formName"},{default:s(()=>[e(c,{modelValue:t(n).formName,"onUpdate:modelValue":a[1]||(a[1]=l=>t(n).formName=l),placeholder:"请输入评估标题",clearable:"",onKeyup:N(g,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"评估人",prop:"createBy"},{default:s(()=>[e(c,{modelValue:t(n).createBy,"onUpdate:modelValue":a[2]||(a[2]=l=>t(n).createBy=l),placeholder:"请输入评估人",clearable:"",onKeyup:N(g,["enter"])},null,8,["modelValue"])]),_:1}),e(d,null,{default:s(()=>[e(y,{type:"primary",icon:"Search",onClick:g},{default:s(()=>[v("搜索")]),_:1}),e(y,{icon:"Refresh",onClick:P},{default:s(()=>[v("重置")]),_:1}),T("",!0)]),_:1})]),_:1},8,["model"]),[[D,t(A)]]),w((x(),te(Q,{data:t(R),border:"",stripe:""},{default:s(()=>[e(_,{type:"index",width:"55",align:"center",label:"序号"}),e(_,{label:"评估时间",align:"center",prop:"createTime"},{default:s(l=>[b("span",null,I(i.parseTime(l.row.assessmentScores[0].assessmentTime,"{y}-{m}-{d}")),1)]),_:1}),e(_,{label:"评估标题",align:"center",prop:"formName"},{default:s(l=>[b("span",null,I(l.row.assessmentForm.formName),1)]),_:1}),e(_,{label:"评估人",align:"center",prop:"createBy"},{default:s(l=>{var k;return[b("span",null,I(((k=l.row.assessmentScores[0])==null?void 0:k.assessorName)||""),1)]}),_:1}),e(_,{label:"评估方式",align:"center",prop:"assessmentMethod"},{default:s(l=>[e(E,{options:t(F),value:l.row.assessmentMethod},null,8,["options","value"])]),_:1}),e(_,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:s(l=>[e(y,{link:"",type:"primary",icon:"Edit"},{default:s(()=>[e(L,{to:"/eldercheckin/showAssessmentDetails/detail/"+l.row.id+"/show"},{default:s(()=>[ue]),_:2},1032,["to"])]),_:2},1024),T("",!0)]),_:1})]),_:1},8,["data"])),[[H,t(V)]]),w(e(J,{total:t(h),page:t(n).pageNum,"onUpdate:page":a[3]||(a[3]=l=>t(n).pageNum=l),limit:t(n).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>t(n).pageSize=l),onPagination:f},null,8,["total","page","limit"]),[[D,t(h)>0]]),e(G,{title:t(B),modelValue:t(p),"onUpdate:modelValue":a[10]||(a[10]=l=>ae(p)?p.value=l:null),width:"500px","append-to-body":""},{footer:s(()=>[b("div",me,[e(y,{type:"primary",onClick:z},{default:s(()=>[v("确 定")]),_:1}),e(y,{onClick:K},{default:s(()=>[v("取 消")]),_:1})])]),default:s(()=>[e(C,{ref:"assessmentRecordRef",model:t(o),rules:t(q),"label-width":"80px"},{default:s(()=>[e(d,{label:"关联的老人ID",prop:"elderId"},{default:s(()=>[e(c,{modelValue:t(o).elderId,"onUpdate:modelValue":a[5]||(a[5]=l=>t(o).elderId=l),placeholder:"请输入关联的老人ID"},null,8,["modelValue"])]),_:1}),e(d,{label:"评估表单id",prop:"assessmentFormId"},{default:s(()=>[e(c,{modelValue:t(o).assessmentFormId,"onUpdate:modelValue":a[6]||(a[6]=l=>t(o).assessmentFormId=l),placeholder:"请输入评估表单id"},null,8,["modelValue"])]),_:1}),e(d,{label:"评估机构名称",prop:"assessmentOrgName"},{default:s(()=>[e(c,{modelValue:t(o).assessmentOrgName,"onUpdate:modelValue":a[7]||(a[7]=l=>t(o).assessmentOrgName=l),placeholder:"请输入评估机构名称"},null,8,["modelValue"])]),_:1}),e(d,{label:"评估方式(如: 在线评估, 纸质评估)",prop:"assessmentMethod"},{default:s(()=>[e(c,{modelValue:t(o).assessmentMethod,"onUpdate:modelValue":a[8]||(a[8]=l=>t(o).assessmentMethod=l),placeholder:"请输入评估方式(如: 在线评估, 纸质评估)"},null,8,["modelValue"])]),_:1}),e(d,{label:"备注",prop:"remark"},{default:s(()=>[e(c,{modelValue:t(o).remark,"onUpdate:modelValue":a[9]||(a[9]=l=>t(o).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ge as default};
