<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" class="filter-form">
      <el-form-item label="月份">
        <el-select
          v-model="queryParams.month"
          placeholder="请选择月份"
          style="width: 130px"
        >
          <el-option
            v-for="(item, index) in monthOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="老人姓名">
        <el-input v-model="queryParams.elderName" placeholder="请输入老人姓名" />
      </el-form-item>
      <el-form-item label="床号">
        <el-input v-model="queryParams.bedNumber" placeholder="请输入床号" />
      </el-form-item>
      <el-form-item label="护理等级">
        <el-select
          v-model="queryParams.nursingLevel"
          multiple
          placeholder="请选择护理等级"
          style="width: 130px"
        >
          <el-option
            v-for="(item, index) in nursingLevelOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetQueryParams">重置</el-button>
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="elderName" label="老人姓名" align="center" />
      <el-table-column prop="bedNumber" label="床号" align="center" />
      <el-table-column prop="nursingLevel" label="护理等级" align="center" />
      <el-table-column prop="month" label="月份" align="center" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="text" @click="handleViewReport(row)">查看月报表</el-button>
          <el-button v-if="hasEditPermission" type="text" @click="handleEditReport(row)">
            修改月报表
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增/修改月报表"
      width="60%"
      :before-close="handleCloseDialog"
    >
      <el-form :model="form" label-width="100px">
        <el-form-item label="老人姓名">
          <el-select v-model="form.elderName" placeholder="请选择老人">
            <el-option
              v-for="(item, index) in elderOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="床号" prop="bedNumber">
          <el-input v-model="form.bedNumber" placeholder="请输入床号" />
        </el-form-item>
        <el-form-item label="护理等级" prop="nursingLevel">
          <el-input v-model="form.nursingLevel" placeholder="请输入护理等级" />
        </el-form-item>
        <el-form-item label="月份">
          <el-select v-model="form.month" placeholder="请选择月份">
            <el-option
              v-for="(item, index) in monthOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="护理类型">
          <el-table :data="careItems" border style="width: 100%">
            <el-table-column prop="type" label="护理类型" width="150px" fixed />
            <el-table-column prop="item" label="护理项" width="150px" fixed />
            <el-table-column
              v-for="(day, index) in daysInMonth"
              :key="index"
              :label="day"
            >
              <template #default="{ $index }">
                <el-checkbox
                  v-model="careItems[$index].executed[day]"
                  @change="handleCheckboxChange($index, day)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";

// 查询参数
const queryParams = reactive({
  month: new Date().getMonth() + 1,
  elderName: "",
  bedNumber: "",
  nursingLevel: [],
});

// 表格数据
const tableData = ref([
  {
    id: 1,
    elderName: "张三",
    bedNumber: "A001",
    nursingLevel: "一级护理",
    month: "2025-07",
  },
  {
    id: 2,
    elderName: "李四",
    bedNumber: "A002",
    nursingLevel: "三级护理",
    month: "2025-07",
  },
  {
    id: 3,
    elderName: "王五",
    bedNumber: "A201",
    nursingLevel: "一级护理",
    month: "2025-07",
  },
  {
    id: 4,
    elderName: "老六",
    bedNumber: "A214",
    nursingLevel: "二级护理",
    month: "2025-07",
  },
]);

// 弹窗状态
const dialogVisible = ref(false);

// 表单数据
const data = reactive({
  form: {
    elderName: "",
    bedNumber: "",
    nursingLevel: "",
    month: "",
  },
});

const { form } = toRefs(data);

// 护理项数据
const careItems = ref([
  {
    type: "护理类型1",
    item: "护理项1",
    executed: {
      1: false,
      2: false,
      3: false,
      4: false,
      5: false,
      6: false,
      7: false,
      8: false,
      9: false,
      10: false,
      11: false,
      12: false,
    },
  },
  {
    type: "护理类型2",
    item: "护理项2",
    executed: {
      1: false,
      2: false,
      3: false,
      4: false,
      5: false,
      6: false,
      7: false,
      8: false,
      9: false,
      10: false,
      11: false,
      12: false,
    },
  },
]);

// 权限控制
const hasEditPermission = computed(() => {
  // 假设管理员有编辑权限
  return true;
});

// 月份选项
const monthOptions = [
  { label: "一月", value: 1 },
  { label: "二月", value: 2 },
  { label: "三月", value: 3 },
  { label: "四月", value: 4 },
  { label: "五月", value: 5 },
  { label: "六月", value: 6 },
  { label: "七月", value: 7 },
  { label: "八月", value: 8 },
  { label: "九月", value: 9 },
  { label: "十月", value: 10 },
  { label: "十一月", value: 11 },
  { label: "十二月", value: 12 },
];

// 护理等级选项
const nursingLevelOptions = [
  { label: "一级护理", value: "level1" },
  { label: "二级护理", value: "level2" },
  { label: "三级护理", value: "level3" },
];

// 老人选项
const elderOptions = [
  { id: 1, name: "张三" },
  { id: 2, name: "李四" },
];

// 当前月份天数
const daysInMonth = computed(() => {
  const year = new Date().getFullYear();
  const month = form.month || new Date().getMonth() + 1;
  return new Date(year, month, 0).getDate();
});

// 查询方法
const handleSearch = () => {
  console.log("查询条件:", queryParams);
  // 模拟查询数据
  tableData.value = [
    { elderName: "张三", bedNumber: "A001", nursingLevel: "一级护理", month: "2023-10" },
    { elderName: "李四", bedNumber: "B002", nursingLevel: "二级护理", month: "2023-10" },
  ];
};

// 重置查询条件
const resetQueryParams = () => {
  Object.assign(queryParams, {
    month: new Date().getMonth() + 1,
    elderName: "",
    bedNumber: "",
    nursingLevel: [],
  });
};

// 查看月报表
const handleViewReport = (row) => {
  console.log("查看月报表:", row);
  dialogVisible.value = true;
  form.value = row;
};

const handleAdd = () => {
  console.log("新增");
  dialogVisible.value = true;
};

// 修改月报表
const handleEditReport = (row) => {
  console.log("修改月报表:", row);
  dialogVisible.value = true;
  form.value = row;
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
};

// 提交表单
const handleSubmit = () => {
  console.log("提交表单:", form);
  ElMessage.success("保存成功");
  dialogVisible.value = false;
};

// 处理复选框变化
const handleCheckboxChange = (index, day) => {
  console.log(`护理项[${index}]在${day}的变化`);
};
</script>

<style scoped>
.filter-form {
  margin-bottom: 20px;
}
</style>
