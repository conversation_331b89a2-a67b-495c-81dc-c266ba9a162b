import{X as e}from"./index-B0qHf98Y.js";function m(s){return e({url:"/assessment/assessmentForm/list",method:"get",params:s})}function n(s){return e({url:"/assessment/assessmentForm/"+s,method:"get"})}function r(s){return e({url:"/assessment/assessmentForm",method:"post",data:s})}function a(s){return e({url:"/assessment/assessmentForm",method:"put",data:s})}function o(s){return e({url:"/assessment/assessmentForm/"+s,method:"delete"})}export{r as a,o as d,n as g,m as l,a as u};
