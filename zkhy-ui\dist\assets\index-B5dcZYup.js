import{_ as u,r as m,u as f,e as c,c as h,o as C,i as s,n as g,f as e,h as n,l as o,A as x,k as _,v as V,x as N}from"./index-B0qHf98Y.js";const w="/assets/assessment2-DNn1A_vG.png",I="/assets/assessment1-De8jqWCD.png",R="/assets/assessOk-edN_TEUz.png",i=a=>(V("data-v-d3e398c2"),a=a(),N(),a),k={class:"app-container",style:{width:"100%",margin:"auto"}},D={style:{"font-size":"18px",color:"#9c9a9a","font-weight":"600"}},y={class:"res"},S=x('<div class="assessResCss" data-v-d3e398c2> 评估意见：<span class="assessValCss" data-v-d3e398c2> 经评估老人能力基本符合入住条件，评估通过</span></div><div class="assessResCss" data-v-d3e398c2>能力等级：<span class="assessValCss" data-v-d3e398c2>能力完好</span></div><div class="assessResCss" data-v-d3e398c2>评估结果：<span class="assessValCss" data-v-d3e398c2>评估通过</span></div>',3),B={class:"resImages"},z={class:"res"},A=i(()=>s("div",{class:"assessResDateCss",style:{"margin-left":"78%"}},[s("span",null,"泰康评估机构 张铭 ")],-1)),E=i(()=>s("div",{class:"assessResDateCss",style:{"margin-left":"82%"}},[s("span",null,"2025-03-12")],-1)),O={__name:"index",setup(a){const d=m(),r=f();function p(){d.value=r.params.id,console.log(d.value,"getid")}return p(),(T,j)=>{const t=c("el-image"),l=c("el-col"),v=c("el-row");return C(),h("div",k,[s("div",D,[g(" 张铭评估结果： "),e(v,null,{default:n(()=>[e(l,{span:6},{default:n(()=>[e(t,{src:o(w),class:"assessCss"},null,8,["src"])]),_:1}),e(l,{span:6},{default:n(()=>[e(t,{src:o(I),class:"assessCss"},null,8,["src"])]),_:1})]),_:1})]),s("div",y,[S,s("div",B,[e(t,{src:o(R)},null,8,["src"])])]),s("div",z,[A,E,_("",!0),_("",!0)])])}}},G=u(O,[["__scopeId","data-v-d3e398c2"]]);export{G as default};
