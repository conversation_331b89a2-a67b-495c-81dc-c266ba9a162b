import{X as t}from"./index-B0qHf98Y.js";function r(e){return t({url:"/process/processout/list",method:"get",params:e})}function s(e){return t({url:`/process/processout/${e}`,method:"get"})}function c(e){return t({url:"/process/processout/apply",method:"post",data:e})}function u(e){return t({url:`/process/processout/${e}`,method:"delete"})}function a(e){return t({url:"/process/processout/audit",method:"post",data:e})}function n(e){return t({url:"/process/processout/cancelation",method:"post",data:e})}function p(e){return t({url:"/elderinfo/basicInfo/listinfo",method:"get",params:e})}export{r as a,s as b,c,u as d,n as e,a as f,p as g};
