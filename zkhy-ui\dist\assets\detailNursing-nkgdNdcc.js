import{g as T}from"./tNursingHandover-ugsVwCUd.js";import{_ as L,d as F,r as p,e as i,j as y,o as m,h as o,i as t,f as s,k as x,c as H,t as e,n as r,K as A,L as E,l as O,dw as P,v as U,x as j}from"./index-B0qHf98Y.js";const a=u=>(U("data-v-d9ee34e4"),u=u(),j(),u),G={class:"room-info"},K=a(()=>t("h3",null,"房间信息",-1)),q={class:"top_info"},z={class:"nursing_detail"};const J={class:"left_title"},M=a(()=>t("span",null,"楼栋信息：",-1)),Q={class:"left_title"},R=a(()=>t("span",null,"楼层信息：",-1));const W={class:"handoverInfo"},X=a(()=>t("h3",null,"交接信息",-1)),Y={style:{float:"right"}},Z=a(()=>t("strong",null,"状态：",-1)),$={style:{"margin-left":"43%"}},tt=a(()=>t("strong",null,"交接日期：",-1)),et=a(()=>t("h3",{class:"title_day",style:{color:"rgba(50, 109, 254, 0.607843137254902)"}}," 白班信息 ",-1)),st={style:{margin:"10px"}},ot=a(()=>t("span",null,"白班护士：",-1)),at={style:{"margin-top":"6px"}},lt=a(()=>t("span",null,"交接时间：",-1)),nt={class:"back1 backDiv"},dt=a(()=>t("div",{class:"h3_title"},"交接人数",-1)),_t={class:"textCenter"},it={class:"back2 backDiv"},ct=a(()=>t("div",{class:"h3_title"},"外出人数",-1)),rt={class:"textCenter"},ut={class:"back3 backDiv"},pt=a(()=>t("div",{class:"h3_title"},"离院人数",-1)),ht={class:"textCenter"},vt={class:"back5 backDiv"},mt=a(()=>t("div",{class:"h3_title"},"死亡人数",-1)),gt={class:"textCenter"},bt=a(()=>t("h3",{class:"title_day",style:{color:"rgba(245, 154, 35, 0.607843137254902)"}}," 夜班信息 ",-1)),ft={style:{margin:"10px"}},yt=a(()=>t("span",null,"夜班护士：",-1)),xt={style:{"margin-top":"6px"}},kt=a(()=>t("span",null,"交接时间：",-1)),Nt={class:"back1 backDiv"},Ct=a(()=>t("div",{class:"h3_title"},"交接人数",-1)),Dt={class:"textCenter"},Vt={class:"back2 backDiv"},Bt=a(()=>t("div",{class:"h3_title"},"外出人数",-1)),St={class:"textCenter"},wt={class:"back3 backDiv"},It=a(()=>t("div",{class:"h3_title"},"离院人数",-1)),Tt={class:"textCenter"},Lt={class:"back5 backDiv"},Ft=a(()=>t("div",{class:"h3_title"},"死亡人数",-1)),Ht={class:"textCenter"},At={class:"bottom_card"},Et=a(()=>t("div",{class:"bed_detail"},"床位交接详情",-1)),Ot={class:"collapse_card"},Pt={class:"title_bg"},Ut={class:"describe_look"},jt=a(()=>t("div",{class:"title_dayShift"},[t("span",{class:"circle"}),r(" 白班 ")],-1)),Gt={class:"describe"},Kt={class:"describe",style:{"margin-top":"3px"}},qt={class:"describe_look"},zt={class:"title_dayShift"},Jt={class:"describe"},Mt={class:"describe",style:{"margin-top":"3px"}},Qt={__name:"detailNursing",props:{detailId:{type:String,default:null}},setup(u,{expose:k}){const{proxy:N}=F(),{room_type:Rt,room_area:Wt}=N.useDict("room_type","room_area"),h=p(!1),C=p("详情"),v=p([]),l=p({});function D(g){T(g).then(_=>{console.log(_,"res===="),_.code===200&&(h.value=!0,l.value=_.data,v.value=_.data.tNursingHandoverBedList.map(b=>b.id)||[])})}return k({init:D}),(g,_)=>{const b=i("el-tag"),c=i("el-row"),d=i("el-col"),V=i("Place"),f=i("el-icon"),B=i("el-collapse-item"),S=i("el-collapse"),w=i("el-dialog");return m(),y(w,{title:C.value,modelValue:h.value,"onUpdate:modelValue":_[1]||(_[1]=n=>h.value=n),width:"50%","append-to-body":""},{default:o(()=>[t("div",G,[K,t("div",q,[t("div",z,[x("",!0),t("div",J,[M,t("span",null,e(l.value.buildingName||"-"),1)]),t("div",Q,[R,t("span",null,e(l.value.floorNumber||"-"),1)])]),x("",!0)])]),t("div",W,[X,t("p",Y,[Z,r(e(l.value.status==="complete"?"已完成":"未完成"),1)])]),t("div",$,[t("p",null,[tt,r(e(l.value.handoverDate||"-"),1)])]),s(c,{gutter:"20"},{default:o(()=>[s(d,{span:12,style:{"background-color":"rgb(242, 242, 242)","border-radius":"10px"}},{default:o(()=>[s(c,null,{default:o(()=>[t("div",null,[et,t("div",st,[t("div",null,[ot,t("span",null,e(l.value.dayNurse||"-"),1)]),t("div",at,[lt,t("span",null,e(l.value.dayHandoverTime||"-"),1)])])])]),_:1}),s(c,{gutter:15},{default:o(()=>[s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",nt,[dt,t("div",_t,e(l.value.dayTotalCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",it,[ct,t("span",rt,e(l.value.dayOutCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",ut,[pt,t("span",ht,e(l.value.dayLeaveCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",vt,[mt,t("span",gt,e(l.value.dayDeathCount||"0"),1)])]),_:1}),s(d,{span:12})]),_:1})]),_:1}),s(d,{span:12,style:{"background-color":"rgb(242, 242, 242)","border-radius":"10px"}},{default:o(()=>[s(c,null,{default:o(()=>[t("div",null,[bt,t("div",ft,[t("div",null,[yt,t("span",null,e(l.value.nightNurse||"-"),1)]),t("div",xt,[kt,t("span",null,e(l.value.nightHandoverTime||"-"),1)])])])]),_:1}),s(c,{gutter:15},{default:o(()=>[s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",Nt,[Ct,t("span",Dt,e(l.value.nightTotalCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",Vt,[Bt,t("span",St,e(l.value.nightOutCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",wt,[It,t("span",Tt,e(l.value.nightLeaveCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[t("div",Lt,[Ft,t("span",Ht,e(l.value.nightDeathCount||"0"),1)])]),_:1}),s(d,{span:12})]),_:1})]),_:1})]),_:1}),s(c,{gutter:20},{default:o(()=>[s(d,{span:24},{default:o(()=>[t("div",At,[Et,t("div",Ot,[s(S,{class:"collapse_card_list",modelValue:v.value,"onUpdate:modelValue":_[0]||(_[0]=n=>v.value=n)},{default:o(()=>[(m(!0),H(A,null,E(l.value.tNursingHandoverBedList,(n,I)=>(m(),y(B,{name:n.id,class:"collapse_card_list_item",key:I},{title:o(({isActive:Xt})=>[t("div",Pt,[s(f,null,{default:o(()=>[s(V)]),_:1}),r(" "+e(n.roomNumber||"-")+" - "+e(n.bedNumber>10?n.bedNumber:"0"+n.bedNumber||"-")+"床 "+e(n.elderName||"-")+"（"+e(n.elderGender=="0"?"女":"男")+" "+e(n.elderAge||"-")+"岁） ",1)])]),default:o(()=>[t("div",Ut,[jt,t("div",Gt,e(n.handoverContent1Str||"-"),1),t("div",Kt,[t("div",null,"备注："+e(n.remark1||"-"),1)])]),t("div",qt,[t("div",zt,[s(f,{color:"#FF00FF"},{default:o(()=>[s(O(P))]),_:1}),r("夜班 ")]),t("div",Jt,e(n.handoverContent2Str||"-"),1),t("div",Mt,[t("div",null,"备注："+e(n.remark2||"-"),1)])])]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])])])]),_:1})]),_:1})]),_:1},8,["title","modelValue"])}}},$t=L(Qt,[["__scopeId","data-v-d9ee34e4"]]);export{$t as default};
