import{B as _e,u as ge,d as be,r as p,C as ve,N as he,cF as ye,e as r,I as $,c as K,o as m,J as d,f as e,O as L,l,h as a,m as Y,D as A,n as b,j as v,i as G,t as Q,K as we,L as Ce,dP as Ne}from"./index-B0qHf98Y.js";import{a as ke,b as Se,d as xe,p as Ve,s as $e}from"./gen-4wQ_Uuqb.js";import Re from"./importTable-Dfo6dy0j.js";import Te from"./createTable-Dyvix-u_.js";const De={class:"app-container"},Ie=_e({name:"Gen"}),ze=Object.assign(Ie,{setup(qe){const q=ge(),{proxy:s}=be(),P=p([]),R=p(!0),S=p(!0),T=p([]),U=p(!0),D=p(!0),I=p(0),O=p([]),y=p([]),B=p(""),w=p({prop:"createTime",order:"descending"}),M=ve({queryParams:{pageNum:1,pageSize:10,tableName:void 0,tableComment:void 0,orderByColumn:w.value.prop,isAsc:w.value.order},preview:{open:!1,title:"代码预览",data:{},activeName:"domain.java"}}),{queryParams:i,preview:f}=he(M);ye(()=>{const n=q.query.t;n!=null&&n!=B.value&&(B.value=n,i.value.pageNum=Number(q.query.pageNum),y.value=[],s.resetForm("queryForm"),h())});function h(){R.value=!0,ke(s.addDateRange(i.value,y.value)).then(n=>{P.value=n.rows,I.value=n.total,R.value=!1})}function C(){i.value.pageNum=1,h()}function z(n){const t=n.tableName||O.value;if(t==""){s.$modal.msgError("请选择要生成的数据");return}n.genType==="1"?Se(n.tableName).then(x=>{s.$modal.msgSuccess("成功生成到自定义路径："+n.genPath)}):s.$download.zip("/tool/gen/batchGenCode?tables="+t,"ruoyi.zip")}function J(n){const t=n.tableName;s.$modal.confirm('确认要强制同步"'+t+'"表结构吗？').then(function(){return $e(t)}).then(()=>{s.$modal.msgSuccess("同步成功")}).catch(()=>{})}function H(){s.$refs.importRef.show()}function W(){s.$refs.createRef.show()}function X(){y.value=[],s.resetForm("queryRef"),i.value.pageNum=1,s.$refs.genRef.sort(w.value.prop,w.value.order)}function Z(n){Ve(n.tableId).then(t=>{f.value.data=t.data,f.value.open=!0,f.value.activeName="domain.java"})}function ee(){s.$modal.msgSuccess("复制成功")}function te(n){T.value=n.map(t=>t.tableId),O.value=n.map(t=>t.tableName),U.value=n.length!=1,D.value=!n.length}function le(n,t,x){i.value.orderByColumn=n.prop,i.value.isAsc=n.order,h()}function E(n){const t=n.tableId||T.value[0];Ne.push({path:"/tool/gen-edit/index/"+t,query:{pageNum:i.value.pageNum}})}function F(n){const t=n.tableId||T.value;s.$modal.confirm('是否确认删除表编号为"'+t+'"的数据项？').then(function(){return xe(t)}).then(()=>{h(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return h(),(n,t)=>{const x=r("el-input"),V=r("el-form-item"),ae=r("el-date-picker"),u=r("el-button"),ne=r("el-form"),N=r("el-col"),oe=r("right-toolbar"),ie=r("el-row"),g=r("el-table-column"),k=r("el-tooltip"),re=r("el-table"),se=r("pagination"),de=r("el-link"),ue=r("el-tab-pane"),ce=r("el-tabs"),pe=r("el-dialog"),_=$("hasPermi"),me=$("hasRole"),fe=$("loading"),j=$("copyText");return m(),K("div",De,[d(e(ne,{model:l(i),ref:"queryRef",inline:!0},{default:a(()=>[e(V,{label:"表名称",prop:"tableName"},{default:a(()=>[e(x,{modelValue:l(i).tableName,"onUpdate:modelValue":t[0]||(t[0]=o=>l(i).tableName=o),placeholder:"请输入表名称",clearable:"",style:{width:"200px"},onKeyup:Y(C,["enter"])},null,8,["modelValue"])]),_:1}),e(V,{label:"表描述",prop:"tableComment"},{default:a(()=>[e(x,{modelValue:l(i).tableComment,"onUpdate:modelValue":t[1]||(t[1]=o=>l(i).tableComment=o),placeholder:"请输入表描述",clearable:"",style:{width:"200px"},onKeyup:Y(C,["enter"])},null,8,["modelValue"])]),_:1}),e(V,{label:"创建时间",style:{width:"308px"}},{default:a(()=>[e(ae,{modelValue:l(y),"onUpdate:modelValue":t[2]||(t[2]=o=>A(y)?y.value=o:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(V,null,{default:a(()=>[e(u,{type:"primary",icon:"Search",onClick:C},{default:a(()=>[b("搜索")]),_:1}),e(u,{icon:"Refresh",onClick:X},{default:a(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[L,l(S)]]),e(ie,{gutter:10,class:"mb8"},{default:a(()=>[e(N,{span:1.5},{default:a(()=>[d((m(),v(u,{type:"primary",plain:"",icon:"Download",disabled:l(D),onClick:z},{default:a(()=>[b("生成")]),_:1},8,["disabled"])),[[_,["tool:gen:code"]]])]),_:1}),e(N,{span:1.5},{default:a(()=>[d((m(),v(u,{type:"primary",plain:"",icon:"Plus",onClick:W},{default:a(()=>[b("创建")]),_:1})),[[me,["admin"]]])]),_:1}),e(N,{span:1.5},{default:a(()=>[d((m(),v(u,{type:"info",plain:"",icon:"Upload",onClick:H},{default:a(()=>[b("导入")]),_:1})),[[_,["tool:gen:import"]]])]),_:1}),e(N,{span:1.5},{default:a(()=>[d((m(),v(u,{type:"success",plain:"",icon:"Edit",disabled:l(U),onClick:E},{default:a(()=>[b("修改")]),_:1},8,["disabled"])),[[_,["tool:gen:edit"]]])]),_:1}),e(N,{span:1.5},{default:a(()=>[d((m(),v(u,{type:"danger",plain:"",icon:"Delete",disabled:l(D),onClick:F},{default:a(()=>[b("删除")]),_:1},8,["disabled"])),[[_,["tool:gen:remove"]]])]),_:1}),e(oe,{showSearch:l(S),"onUpdate:showSearch":t[3]||(t[3]=o=>A(S)?S.value=o:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),d((m(),v(re,{ref:"genRef",data:l(P),onSelectionChange:te,"default-sort":l(w),onSortChange:le},{default:a(()=>[e(g,{type:"selection",align:"center",width:"55"}),e(g,{label:"序号",type:"index",width:"50",align:"center"},{default:a(o=>[G("span",null,Q((l(i).pageNum-1)*l(i).pageSize+o.$index+1),1)]),_:1}),e(g,{label:"表名称",align:"center",prop:"tableName","show-overflow-tooltip":!0}),e(g,{label:"表描述",align:"center",prop:"tableComment","show-overflow-tooltip":!0}),e(g,{label:"实体",align:"center",prop:"className","show-overflow-tooltip":!0}),e(g,{label:"创建时间",align:"center",prop:"createTime",width:"160",sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"更新时间",align:"center",prop:"updateTime",width:"160",sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"操作",align:"center",width:"330","class-name":"small-padding fixed-width"},{default:a(o=>[e(k,{content:"预览",placement:"top"},{default:a(()=>[d(e(u,{link:"",type:"primary",icon:"View",onClick:c=>Z(o.row)},null,8,["onClick"]),[[_,["tool:gen:preview"]]])]),_:2},1024),e(k,{content:"编辑",placement:"top"},{default:a(()=>[d(e(u,{link:"",type:"primary",icon:"Edit",onClick:c=>E(o.row)},null,8,["onClick"]),[[_,["tool:gen:edit"]]])]),_:2},1024),e(k,{content:"删除",placement:"top"},{default:a(()=>[d(e(u,{link:"",type:"primary",icon:"Delete",onClick:c=>F(o.row)},null,8,["onClick"]),[[_,["tool:gen:remove"]]])]),_:2},1024),e(k,{content:"同步",placement:"top"},{default:a(()=>[d(e(u,{link:"",type:"primary",icon:"Refresh",onClick:c=>J(o.row)},null,8,["onClick"]),[[_,["tool:gen:edit"]]])]),_:2},1024),e(k,{content:"生成代码",placement:"top"},{default:a(()=>[d(e(u,{link:"",type:"primary",icon:"Download",onClick:c=>z(o.row)},null,8,["onClick"]),[[_,["tool:gen:code"]]])]),_:2},1024)]),_:1})]),_:1},8,["data","default-sort"])),[[fe,l(R)]]),d(e(se,{total:l(I),page:l(i).pageNum,"onUpdate:page":t[4]||(t[4]=o=>l(i).pageNum=o),limit:l(i).pageSize,"onUpdate:limit":t[5]||(t[5]=o=>l(i).pageSize=o),onPagination:h},null,8,["total","page","limit"]),[[L,l(I)>0]]),e(pe,{title:l(f).title,modelValue:l(f).open,"onUpdate:modelValue":t[7]||(t[7]=o=>l(f).open=o),width:"80%",top:"5vh","append-to-body":"",class:"scrollbar"},{default:a(()=>[e(ce,{modelValue:l(f).activeName,"onUpdate:modelValue":t[6]||(t[6]=o=>l(f).activeName=o)},{default:a(()=>[(m(!0),K(we,null,Ce(l(f).data,(o,c)=>(m(),v(ue,{label:c.substring(c.lastIndexOf("/")+1,c.indexOf(".vm")),name:c.substring(c.lastIndexOf("/")+1,c.indexOf(".vm")),key:o},{default:a(()=>[d((m(),v(de,{underline:!1,icon:"DocumentCopy",style:{float:"right"}},{default:a(()=>[b(" 复制")]),_:2},1024)),[[j,o],[j,ee,"callback"]]),G("pre",null,Q(o),1)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"]),e(l(Re),{ref:"importRef",onOk:C},null,512),e(l(Te),{ref:"createRef",onOk:C},null,512)])}}});export{ze as default};
