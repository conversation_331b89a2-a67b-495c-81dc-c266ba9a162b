import{X as w,B as _e,d as ge,r as _,C as ve,N as be,e as n,I as L,c as $,o as d,J as g,f as e,O as z,l,h as o,m as Q,K as j,L as O,j as v,n as p,D as A,i as J,t as X}from"./index-B0qHf98Y.js";function ye(i){return w({url:"/system/post/list",method:"get",params:i})}function he(i){return w({url:"/system/post/"+i,method:"get"})}function Ve(i){return w({url:"/system/post",method:"post",data:i})}function Ce(i){return w({url:"/system/post",method:"put",data:i})}function we(i){return w({url:"/system/post/"+i,method:"delete"})}const ke={class:"app-container"},Se={class:"dialog-footer"},Ne=_e({name:"Post"}),Ue=Object.assign(Ne,{setup(i){const{proxy:c}=ge(),{sys_normal_disable:x}=c.useDict("sys_normal_disable"),q=_([]),b=_(!1),U=_(!0),k=_(!0),P=_([]),T=_(!0),B=_(!0),D=_(0),I=_(""),G=ve({form:{},queryParams:{pageNum:1,pageSize:10,postCode:void 0,postName:void 0,status:void 0},rules:{postName:[{required:!0,message:"岗位名称不能为空",trigger:"blur"}],postCode:[{required:!0,message:"岗位编码不能为空",trigger:"blur"}],postSort:[{required:!0,message:"岗位顺序不能为空",trigger:"blur"}]}}),{queryParams:u,form:s,rules:H}=be(G);function h(){U.value=!0,ye(u.value).then(r=>{q.value=r.rows,D.value=r.total,U.value=!1})}function M(){b.value=!1,R()}function R(){s.value={postId:void 0,postCode:void 0,postName:void 0,postSort:0,status:"0",remark:void 0},c.resetForm("postRef")}function S(){u.value.pageNum=1,h()}function W(){c.resetForm("queryRef"),S()}function Y(r){P.value=r.map(a=>a.postId),T.value=r.length!=1,B.value=!r.length}function Z(){R(),b.value=!0,I.value="添加岗位"}function E(r){R();const a=r.postId||P.value;he(a).then(V=>{s.value=V.data,b.value=!0,I.value="修改岗位"})}function ee(){c.$refs.postRef.validate(r=>{r&&(s.value.postId!=null?Ce(s.value).then(a=>{c.$modal.msgSuccess("修改成功"),b.value=!1,h()}):Ve(s.value).then(a=>{c.$modal.msgSuccess("新增成功"),b.value=!1,h()}))})}function F(r){const a=r.postId||P.value;c.$modal.confirm('是否确认删除岗位编号为"'+a+'"的数据项？').then(function(){return we(a)}).then(()=>{h(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function te(){c.download("system/post/export",{...u.value},`post_${new Date().getTime()}.xlsx`)}return h(),(r,a)=>{const V=n("el-input"),f=n("el-form-item"),le=n("el-option"),oe=n("el-select"),m=n("el-button"),K=n("el-form"),N=n("el-col"),ae=n("right-toolbar"),ne=n("el-row"),y=n("el-table-column"),se=n("dict-tag"),ue=n("el-table"),re=n("pagination"),de=n("el-input-number"),pe=n("el-radio"),ie=n("el-radio-group"),me=n("el-dialog"),C=L("hasPermi"),ce=L("loading");return d(),$("div",ke,[g(e(K,{model:l(u),ref:"queryRef",inline:!0},{default:o(()=>[e(f,{label:"岗位编码",prop:"postCode"},{default:o(()=>[e(V,{modelValue:l(u).postCode,"onUpdate:modelValue":a[0]||(a[0]=t=>l(u).postCode=t),placeholder:"请输入岗位编码",clearable:"",style:{width:"200px"},onKeyup:Q(S,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位名称",prop:"postName"},{default:o(()=>[e(V,{modelValue:l(u).postName,"onUpdate:modelValue":a[1]||(a[1]=t=>l(u).postName=t),placeholder:"请输入岗位名称",clearable:"",style:{width:"200px"},onKeyup:Q(S,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"状态",prop:"status"},{default:o(()=>[e(oe,{modelValue:l(u).status,"onUpdate:modelValue":a[2]||(a[2]=t=>l(u).status=t),placeholder:"岗位状态",clearable:"",style:{width:"200px"}},{default:o(()=>[(d(!0),$(j,null,O(l(x),t=>(d(),v(le,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:o(()=>[e(m,{type:"primary",icon:"Search",onClick:S},{default:o(()=>[p("搜索")]),_:1}),e(m,{icon:"Refresh",onClick:W},{default:o(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[z,l(k)]]),e(ne,{gutter:10,class:"mb8"},{default:o(()=>[e(N,{span:1.5},{default:o(()=>[g((d(),v(m,{type:"primary",plain:"",icon:"Plus",onClick:Z},{default:o(()=>[p("新增")]),_:1})),[[C,["system:post:add"]]])]),_:1}),e(N,{span:1.5},{default:o(()=>[g((d(),v(m,{type:"success",plain:"",icon:"Edit",disabled:l(T),onClick:E},{default:o(()=>[p("修改")]),_:1},8,["disabled"])),[[C,["system:post:edit"]]])]),_:1}),e(N,{span:1.5},{default:o(()=>[g((d(),v(m,{type:"danger",plain:"",icon:"Delete",disabled:l(B),onClick:F},{default:o(()=>[p("删除")]),_:1},8,["disabled"])),[[C,["system:post:remove"]]])]),_:1}),e(N,{span:1.5},{default:o(()=>[g((d(),v(m,{type:"warning",plain:"",icon:"Download",onClick:te},{default:o(()=>[p("导出")]),_:1})),[[C,["system:post:export"]]])]),_:1}),e(ae,{showSearch:l(k),"onUpdate:showSearch":a[3]||(a[3]=t=>A(k)?k.value=t:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),g((d(),v(ue,{data:l(q),onSelectionChange:Y},{default:o(()=>[e(y,{type:"selection",width:"55",align:"center"}),e(y,{label:"岗位编号",align:"center",prop:"postId"}),e(y,{label:"岗位编码",align:"center",prop:"postCode"}),e(y,{label:"岗位名称",align:"center",prop:"postName"}),e(y,{label:"岗位排序",align:"center",prop:"postSort"}),e(y,{label:"状态",align:"center",prop:"status"},{default:o(t=>[e(se,{options:l(x),value:t.row.status},null,8,["options","value"])]),_:1}),e(y,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:o(t=>[J("span",null,X(r.parseTime(t.row.createTime)),1)]),_:1}),e(y,{label:"操作",width:"180",align:"center","class-name":"small-padding fixed-width"},{default:o(t=>[g((d(),v(m,{link:"",type:"primary",icon:"Edit",onClick:fe=>E(t.row)},{default:o(()=>[p("修改")]),_:2},1032,["onClick"])),[[C,["system:post:edit"]]]),g((d(),v(m,{link:"",type:"primary",icon:"Delete",onClick:fe=>F(t.row)},{default:o(()=>[p("删除")]),_:2},1032,["onClick"])),[[C,["system:post:remove"]]])]),_:1})]),_:1},8,["data"])),[[ce,l(U)]]),g(e(re,{total:l(D),page:l(u).pageNum,"onUpdate:page":a[4]||(a[4]=t=>l(u).pageNum=t),limit:l(u).pageSize,"onUpdate:limit":a[5]||(a[5]=t=>l(u).pageSize=t),onPagination:h},null,8,["total","page","limit"]),[[z,l(D)>0]]),e(me,{title:l(I),modelValue:l(b),"onUpdate:modelValue":a[11]||(a[11]=t=>A(b)?b.value=t:null),width:"500px","append-to-body":""},{footer:o(()=>[J("div",Se,[e(m,{type:"primary",onClick:ee},{default:o(()=>[p("确 定")]),_:1}),e(m,{onClick:M},{default:o(()=>[p("取 消")]),_:1})])]),default:o(()=>[e(K,{ref:"postRef",model:l(s),rules:l(H),"label-width":"80px"},{default:o(()=>[e(f,{label:"岗位名称",prop:"postName"},{default:o(()=>[e(V,{modelValue:l(s).postName,"onUpdate:modelValue":a[6]||(a[6]=t=>l(s).postName=t),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位编码",prop:"postCode"},{default:o(()=>[e(V,{modelValue:l(s).postCode,"onUpdate:modelValue":a[7]||(a[7]=t=>l(s).postCode=t),placeholder:"请输入编码名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位顺序",prop:"postSort"},{default:o(()=>[e(de,{modelValue:l(s).postSort,"onUpdate:modelValue":a[8]||(a[8]=t=>l(s).postSort=t),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),e(f,{label:"岗位状态",prop:"status"},{default:o(()=>[e(ie,{modelValue:l(s).status,"onUpdate:modelValue":a[9]||(a[9]=t=>l(s).status=t)},{default:o(()=>[(d(!0),$(j,null,O(l(x),t=>(d(),v(pe,{key:t.value,value:t.value},{default:o(()=>[p(X(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"备注",prop:"remark"},{default:o(()=>[e(V,{modelValue:l(s).remark,"onUpdate:modelValue":a[10]||(a[10]=t=>l(s).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ue as default};
