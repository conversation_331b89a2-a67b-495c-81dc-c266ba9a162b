import{X as t}from"./index-B0qHf98Y.js";function r(e){return t({url:"/warehouse/inventoryCheck/list",method:"get",params:e})}function o(e){return t({url:"/warehouse/inventoryCheck/"+e,method:"get"})}function a(e){return t({url:"/warehouse/inventoryCheck",method:"post",data:e})}function u(e){return t({url:"/warehouse/inventoryCheck",method:"put",data:e})}function h(e){return t({url:"/warehouse/inventoryCheck/listChange",method:"get",params:e})}export{a,r as b,o as g,h as l,u};
