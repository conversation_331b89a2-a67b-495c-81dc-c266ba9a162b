import{b as He,_ as Le,B as qe,a as Ke,d as Fe,r as d,C as je,N as Ee,e as f,I as de,c as U,o as c,J as G,f as e,O as ie,h as a,m as pe,l as n,K as Y,L as x,j as b,k as g,n as h,i as w,t as $,v as Ge,x as Oe,M as ce}from"./index-B0qHf98Y.js";import{l as Je,g as me,d as Qe,u as We,a as Xe,b as Ze}from"./tReception-DwUHUj_X.js";import{e as el}from"./user-u7DySmj3.js";function oe(V){if(V&&V instanceof Array&&V.length>0){const v=He().roles;console.log(v,"checkRole");const ne=V,A="admin";return!!v.some(O=>A===O||ne.includes(O))}else return console.error(`need roles! Like checkRole="['admin','editor']"`),!1}const fe=V=>(Ge("data-v-4b05407a"),V=V(),Oe(),V),ll={class:"app-container"},al=fe(()=>w("span",null,"回访记录",-1)),ol={class:"dc-dialog-header-16"},nl=fe(()=>w("div",{style:{width:"100px",height:"3px","background-color":"rgb(50, 109, 234)"}},null,-1)),tl={class:"dialog-footer"},ul={class:"dialog-footer"},rl=qe({name:"reception"}),sl=Object.assign(rl,{setup(V){Ke();const{proxy:v}=Fe(),{sys_yes_no:ne,sys_user_sex:A,consultation_type:ve,relationship_elderly:O,channel_type:J,follow_results:te,consultation_type2:Q}=v.useDict("sys_yes_no","sys_user_sex","consultation_type","relationship_elderly","channel_type","follow_results","consultation_type2"),{reception_review:dl}=v.useDict("reception_review"),ue=d([]),N=d(!1),S=d(!1),k=d([]),i=d(!1),_e=d(""),W=d(!0),ge=d(!0),I=d([]),ye=d(!0),be=d(!0),X=d(0),D=d(""),H=d([]),L=d([]),B=d(JSON.parse(localStorage.getItem("userInfo")));console.log(B.value,"users");const Z=d([]),q=d([]),R=d([]),ee=d(!1),C=d(!1);d(!1);const he=je({form:{},formSale:{},queryParams:{pageNum:1,pageSize:10,elderName:void 0,channel:"",status:"",ReceptionType:void 0},rules:{elderName:[{required:!0,message:"老人姓名不能为空",trigger:"blur"}],elderAge:[{required:!0,message:"老人年龄不能为空",trigger:"blur"}],consultPersonName:[{required:!0,message:"咨询人姓名不能为空",trigger:"blur"}],consultPersonPhone:[{required:!0,message:"咨询人电话不能为空",trigger:"blur"}]},rulesSale:{}}),{queryParams:m,form:t,rules:Ve,formSale:ke,rulesSale:we}=Ee(he);function T(){ee.value=C.value=!1,B.value.roles.map(u=>{var o=[];o.push(u.roleKey),L.value=[...new Set(o)]}),console.log(L.value,"userRules.value---"),oe(["sales"])&&(console.log("销售"),C.value=!1,m.value.createUser=B.value.userName),oe(["salesmanager"])&&(C.value=!0,ee.value=!1),oe(["dataanalysis"])&&(C.value=!0,ee.value=!1),console.log(C.value,"userRules222"),L.value!=null&&(m.value.rules=L.value.join(",")),W.value=!0,Je(v.addDateRange(m.value,H.value)).then(u=>{ue.value=u.rows,X.value=u.total,W.value=!1})}function Ne(){N.value=!1,K()}function K(){t.value={consultTime:null,consultType:null,elderName:null,elderGender:0,elderAge:null,elderPhone:null,consultPersonName:null,consultPersonPhone:null,relation:null,receptionPerson:null,status:"01",visitCount:null,channel:null,channelContent:null,consultContent:null,createBy:null,createTime:ce().format("YYYY-MM-DD HH:mm:ss"),updateBy:null,updateTime:null,remark:null},v.resetForm("ReceptionRef"),i.value=!1}function F(){m.value.pageNum=1,T()}function Re(){H.value=[],v.resetForm("queryRef"),F()}function Ce(u){k.value=u,console.log(k.value,"selection"),I.value=u.map(o=>o.ReceptionId),ye.value=u.length!=1,be.value=!u.length}function Te(){K(),N.value=!0,D.value="新增"}function Pe(u){K();const o=u.id||I.value;me(o).then(_=>{t.value=_.data,N.value=!0,D.value="修改"})}function Ue(u){K();const o=u.id||I.value;me(o).then(_=>{t.value=_.data,i.value=!0,N.value=!0,D.value=_e.value})}function Be(){v.$refs.ReceptionRef.validate(u=>{u&&(t.value.createBy=B.value.userName,t.value.id!=null?We(t.value).then(o=>{v.$modal.msgSuccess("修改成功"),N.value=!1,T()}):Xe(t.value).then(o=>{v.$modal.msgSuccess("新增成功"),N.value=!1,T()}))})}function il(u){const o=u.id||I.value;v.$modal.confirm('是否确认删除参数编号为"'+o+'"的数据项？').then(function(){return Qe(o)}).then(()=>{T(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Ye(){if(console.log(k.value,"selectOption"),k.value.length==0){v.$modal.msgError("请选择要分配的参数");return}S.value=!0,el("sales").then(u=>{console.log(u,"getUsersByRuleKey"),Z.value=u.data})}function xe(){console.log(R.value,"salers"),q.value=[],Z.value.forEach(u=>{R.value.includes(u.nickName)&&q.value.push(u.userName)}),console.log(q.value,"saleUsercheckList"),v.$refs.ReceptionAllRef.validate(u=>{u&&(k.value.map(o=>{o.allocationCode=q.value.join(",")||"",o.allocationName=R.value.join(",")||"",o.saleManageCode=B.value.userName,o.saleManageName=B.value.nickName,o.allocationTime=ce().format("YYYY-MM-DD HH:mm:ss")}),Ze(k.value).then(o=>{v.$modal.msgSuccess("分配成功"),S.value=!1,R.value=[],k.value=[],T()}))})}function Se(){S.value=!1,R.value=[],k.value=[]}return T(),(u,o)=>{const _=f("el-input"),s=f("el-form-item"),le=f("el-date-picker"),P=f("el-option"),M=f("el-select"),ae=f("el-form"),y=f("el-button"),j=f("el-row"),r=f("el-table-column"),z=f("dict-tag"),De=f("router-link"),re=f("el-table"),Me=f("pagination"),p=f("el-col"),ze=f("el-radio-button"),$e=f("el-radio-group"),se=f("el-dialog"),Ae=de("hasPermi"),Ie=de("loading");return c(),U("div",ll,[G(e(ae,{ref:"queryRef",inline:!0,model:n(m),"label-width":"68px"},{default:a(()=>[e(s,{label:"老人姓名",prop:"elderName"},{default:a(()=>[e(_,{modelValue:n(m).elderName,"onUpdate:modelValue":o[0]||(o[0]=l=>n(m).elderName=l),clearable:"",placeholder:"请输入老人姓名",onKeyup:pe(F,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"接待人",prop:"receptionPerson"},{default:a(()=>[e(_,{modelValue:n(m).receptionPerson,"onUpdate:modelValue":o[1]||(o[1]=l=>n(m).receptionPerson=l),clearable:"",placeholder:"请输入接待人",onKeyup:pe(F,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"咨询时间",style:{width:"308px"}},{default:a(()=>[e(le,{modelValue:H.value,"onUpdate:modelValue":o[2]||(o[2]=l=>H.value=l),"end-placeholder":"结束日期",format:"YYYY-MM-DD","range-separator":"至","start-placeholder":"开始日期",type:"daterange","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(s,{label:"状态",prop:"status"},{default:a(()=>[e(M,{modelValue:n(m).status,"onUpdate:modelValue":o[3]||(o[3]=l=>n(m).status=l),clearable:"",placeholder:"状态",style:{width:"120px"}},{default:a(()=>[e(P,{value:"",label:"全部"}),(c(!0),U(Y,null,x(n(te),l=>(c(),b(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"渠道",prop:"channel"},{default:a(()=>[e(M,{modelValue:n(m).channel,"onUpdate:modelValue":o[4]||(o[4]=l=>n(m).channel=l),clearable:"",placeholder:"请选择渠道",style:{width:"120px"}},{default:a(()=>[e(P,{value:"",label:"全部"}),(c(!0),U(Y,null,x(n(J),l=>(c(),b(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),[[ie,ge.value]]),e(j,{justify:"end"},{default:a(()=>[e(s,null,{default:a(()=>[e(y,{icon:"Search",type:"primary",onClick:F},{default:a(()=>[h("搜索")]),_:1}),e(y,{icon:"Refresh",onClick:Re},{default:a(()=>[h("重置")]),_:1}),C.value?g("",!0):(c(),b(y,{key:0,icon:"Plus",plain:"",type:"primary",onClick:Te},{default:a(()=>[h("新增")]),_:1})),G((c(),b(y,{icon:"Plus",plain:"",type:"primary",onClick:Ye},{default:a(()=>[h("分配")]),_:1})),[[Ae,["eldersystem:reception:allocation"]]])]),_:1})]),_:1}),G((c(),b(re,{data:ue.value,border:"",stripe:"",onSelectionChange:Ce},{default:a(()=>[e(r,{type:"selection",width:"55",align:"center"}),e(r,{align:"center",label:"序号",type:"index",width:"55"}),e(r,{align:"center",label:"咨询时间","min-width":"120",prop:"consultTime"},{default:a(l=>[w("span",null,$(u.parseTime(l.row.consultTime,"{y}-{m}-{d} {h}:{m}")),1)]),_:1}),e(r,{align:"center",label:"咨询类型",prop:"consultType"},{default:a(l=>[e(z,{options:n(Q),value:l.row.consultType},null,8,["options","value"])]),_:1}),e(r,{align:"center",label:"老人姓名","min-width":"110px",prop:"elderName"}),e(r,{align:"center",label:"老人性别","min-width":"80px",prop:"elderGender"},{default:a(l=>[e(z,{options:n(A),value:l.row.elderGender},null,8,["options","value"])]),_:1}),e(r,{align:"center",label:"老人年龄",prop:"elderAge"}),e(r,{align:"center",label:"咨询人姓名","min-width":"110px",prop:"consultPersonName"}),e(r,{align:"center",label:"咨询人电话","min-width":"120",prop:"consultPersonPhone"}),e(r,{align:"center",label:"与老人关系","min-width":"100px",prop:"relation"}),e(r,{align:"center",label:"接待人",prop:"receptionPerson"}),e(r,{align:"center",label:"状态",prop:"status"},{default:a(l=>[e(z,{options:n(te),value:l.row.status},null,8,["options","value"])]),_:1}),e(r,{align:"center",label:"回访次数",prop:"visitCount"}),e(r,{align:"center",label:"渠道",prop:"channel","min-width":"100"},{default:a(l=>[e(z,{options:n(J),value:l.row.channel},null,8,["options","value"])]),_:1}),g("",!0),e(r,{align:"center",label:"销售姓名",prop:"allocationName","min-width":"100"}),e(r,{align:"center",label:"销售经理",prop:"saleManageName"}),e(r,{align:"center",label:"分配时间",prop:"allocationTime",width:"120"},{default:a(l=>[w("span",null,$(u.parseTime(l.row.allocationTime,"{y}-{m}-{d}")),1)]),_:1}),g("",!0),g("",!0),g("",!0),e(r,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作","min-width":"200"},{default:a(l=>[e(y,{icon:"Edit",link:"",type:"primary",onClick:E=>Ue(l.row)},{default:a(()=>[h("查看")]),_:2},1032,["onClick"]),C.value?g("",!0):(c(),b(y,{key:0,icon:"Edit",link:"",type:"primary",onClick:E=>Pe(l.row)},{default:a(()=>[h("修改")]),_:2},1032,["onClick"])),g("",!0),e(y,{icon:"ChatLineSquare",link:"",type:"primary"},{default:a(()=>[e(De,{to:"/ReceptionManagement/receptionVisit/detail/"+l.row.id},{default:a(()=>[al]),_:2},1032,["to"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Ie,W.value]]),G(e(Me,{limit:n(m).pageSize,"onUpdate:limit":o[5]||(o[5]=l=>n(m).pageSize=l),page:n(m).pageNum,"onUpdate:page":o[6]||(o[6]=l=>n(m).pageNum=l),total:X.value,onPagination:T},null,8,["limit","page","total"]),[[ie,X.value>0]]),e(se,{modelValue:N.value,"onUpdate:modelValue":o[24]||(o[24]=l=>N.value=l),title:D.value,"append-to-body":"",width:"50%"},{header:a(()=>[w("h2",ol,$(D.value)+"咨询信息",1),nl]),footer:a(()=>[w("div",tl,[i.value?g("",!0):(c(),b(y,{key:0,size:"large",type:"primary",onClick:Be},{default:a(()=>[h("确 定")]),_:1})),e(y,{size:"large",onClick:Ne},{default:a(()=>[h("取 消")]),_:1})])]),default:a(()=>[e(j,{gutter:20},{default:a(()=>[e(p,{span:24},{default:a(()=>[e(ae,{ref:"ReceptionRef",model:n(t),rules:n(Ve),"label-width":"120px"},{default:a(()=>[e(j,{gutter:20},{default:a(()=>[e(p,{span:12},{default:a(()=>[e(s,{label:"咨询时间",prop:"consultTime"},{default:a(()=>[e(le,{modelValue:n(t).consultTime,"onUpdate:modelValue":o[7]||(o[7]=l=>n(t).consultTime=l),readonly:i.value,clearable:"",format:"YYYY-MM-DD HH:mm",placeholder:"请选择咨询时间",size:"large",style:{width:"100%"},type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"咨询类型",prop:"consultType"},{default:a(()=>[e(M,{modelValue:n(t).consultType,"onUpdate:modelValue":o[8]||(o[8]=l=>n(t).consultType=l),disabled:i.value,placeholder:"请选择咨询类型",size:"large",style:{width:"100%"}},{default:a(()=>[(c(!0),U(Y,null,x(n(Q),l=>(c(),b(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),g("",!0)]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"老人姓名",prop:"elderName"},{default:a(()=>[e(_,{modelValue:n(t).elderName,"onUpdate:modelValue":o[10]||(o[10]=l=>n(t).elderName=l),readonly:i.value,placeholder:"请输入老人姓名",size:"large"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"老人性别",prop:"elderGender"},{default:a(()=>[e($e,{modelValue:n(t).elderGender,"onUpdate:modelValue":o[11]||(o[11]=l=>n(t).elderGender=l),disabled:i.value,placeholder:"请输入老人性别",size:"large"},{default:a(()=>[(c(!0),U(Y,null,x(n(A),l=>(c(),b(ze,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"老人年龄",prop:"elderAge"},{default:a(()=>[e(_,{modelValue:n(t).elderAge,"onUpdate:modelValue":o[12]||(o[12]=l=>n(t).elderAge=l),readonly:i.value,placeholder:"请输入老人年龄",size:"large"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"老人电话",prop:"elderPhone"},{default:a(()=>[e(_,{modelValue:n(t).elderPhone,"onUpdate:modelValue":o[13]||(o[13]=l=>n(t).elderPhone=l),readonly:i.value,placeholder:"请输入老人电话",size:"large"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"咨询人姓名",prop:"consultPersonName"},{default:a(()=>[e(_,{modelValue:n(t).consultPersonName,"onUpdate:modelValue":o[14]||(o[14]=l=>n(t).consultPersonName=l),readonly:i.value,placeholder:"请输入咨询人姓名",size:"large"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"与老人关系",prop:"relation"},{default:a(()=>[e(_,{modelValue:n(t).relation,"onUpdate:modelValue":o[15]||(o[15]=l=>n(t).relation=l),readonly:i.value,placeholder:"请输入与老人关系",size:"large"},null,8,["modelValue","readonly"]),g("",!0)]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"咨询人电话",prop:"consultPersonPhone"},{default:a(()=>[e(_,{modelValue:n(t).consultPersonPhone,"onUpdate:modelValue":o[17]||(o[17]=l=>n(t).consultPersonPhone=l),readonly:i.value,placeholder:"请输入咨询人电话",size:"large"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"渠道",prop:"channel"},{default:a(()=>[e(M,{modelValue:n(t).channel,"onUpdate:modelValue":o[18]||(o[18]=l=>n(t).channel=l),disabled:i.value,placeholder:"请选择渠道",size:"large",style:{width:"100%"}},{default:a(()=>[(c(!0),U(Y,null,x(n(J),l=>(c(),b(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(s,{label:"渠道内容",prop:"channelContent"},{default:a(()=>[e(_,{modelValue:n(t).channelContent,"onUpdate:modelValue":o[19]||(o[19]=l=>n(t).channelContent=l),readonly:i.value,placeholder:"请输入渠道内容",rows:"3",size:"large",type:"textarea"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(s,{label:"咨询内容"},{default:a(()=>[e(_,{modelValue:n(t).consultContent,"onUpdate:modelValue":o[20]||(o[20]=l=>n(t).consultContent=l),readonly:i.value,placeholder:"请输入咨询内容",rows:"3",size:"large",type:"textarea"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"接待人",prop:"receptionPerson"},{default:a(()=>[e(_,{modelValue:n(t).receptionPerson,"onUpdate:modelValue":o[21]||(o[21]=l=>n(t).receptionPerson=l),readonly:i.value,placeholder:"请输入接待人",size:"large"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:12},{default:a(()=>[e(s,{label:"记录时间",prop:"createTime"},{default:a(()=>[e(le,{modelValue:n(t).createTime,"onUpdate:modelValue":o[22]||(o[22]=l=>n(t).createTime=l),readonly:i.value,clearable:"",format:"YYYY-MM-DD HH:mm",placeholder:"请选择记录时间",size:"large",style:{width:"100%"},type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(s,{label:"备注",prop:"remark"},{default:a(()=>[e(_,{modelValue:n(t).remark,"onUpdate:modelValue":o[23]||(o[23]=l=>n(t).remark=l),readonly:i.value,"V-IF":"false",placeholder:"请输入内容",size:"large",type:"textarea"},null,8,["modelValue","readonly"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1},8,["modelValue","title"]),e(se,{modelValue:S.value,"onUpdate:modelValue":o[26]||(o[26]=l=>S.value=l),title:"分配销售","append-to-body":"",width:"50%"},{footer:a(()=>[w("div",ul,[e(y,{type:"primary",onClick:xe},{default:a(()=>[h("确 定")]),_:1}),e(y,{onClick:Se},{default:a(()=>[h("取 消")]),_:1})])]),default:a(()=>[e(ae,{ref:"ReceptionAllRef",model:n(ke),rules:n(we),"label-width":"120px"},{default:a(()=>[e(j,{gutter:15},{default:a(()=>[e(p,{span:24},{default:a(()=>[e(s,{label:"待分配项",prop:"noticeType"},{default:a(()=>[e(re,{data:k.value,border:"",stripe:""},{default:a(()=>[e(r,{align:"center",label:"序号",type:"index",width:"55"}),e(r,{align:"center",label:"咨询时间","min-width":"120",prop:"consultTime"},{default:a(l=>[w("span",null,$(u.parseTime(l.row.consultTime,"{y}-{m}-{d} {h}:{m}")),1)]),_:1}),e(r,{align:"center",label:"咨询类型",prop:"consultType"},{default:a(l=>[e(z,{options:n(Q),value:l.row.consultType},null,8,["options","value"])]),_:1}),e(r,{align:"center",label:"老人姓名","min-width":"110px",prop:"elderName"}),g("",!0),e(r,{align:"center",label:"销售姓名",prop:"allocationName"}),e(r,{align:"center",label:"销售经理",prop:"saleManageName"}),g("",!0),e(r,{align:"center",label:"分配时间",prop:"allocationTime"},{default:a(l=>[w("span",null,$(u.parseTime(l.row.allocationTime,"{y}-{m}-{d}")),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(s,{label:"销售人员",prop:"salers"},{default:a(()=>[e(M,{modelValue:R.value,"onUpdate:modelValue":o[25]||(o[25]=l=>R.value=l),multiple:"",placeholder:"请选择销售人员",style:{width:"600px"}},{default:a(()=>[(c(!0),U(Y,null,x(Z.value,(l,E)=>(c(),b(P,{key:E,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),fl=Le(sl,[["__scopeId","data-v-4b05407a"]]);export{fl as default};
