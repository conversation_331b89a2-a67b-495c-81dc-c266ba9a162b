import ce from"./detailNurse-DTp0b36L.js";import{_ as pe,d as me,r as u,a as fe,C as _e,N as ge,F as be,e as r,I as he,c as f,o as d,f as e,h as a,i as c,k as ve,K as b,t as w,l as i,ao as ye,J as Ne,L as P,j as V,n as k,v as we,x as Ve,P as ke,G as xe}from"./index-B0qHf98Y.js";import{g as Ce}from"./buildmanage-CIqJJJF0.js";import{c as De}from"./index-DCxZ1IEc.js";import{a as Ie,g as Se}from"./roommanage-DBG5TiIR.js";const Le=h=>(we("data-v-7d7a19de"),h=h(),Ve(),h),ze={class:"app-container"},Be={class:"treeStyle"},Pe=Le(()=>c("div",{class:"panel-header"},[c("span",{class:"title"},"楼层信息")],-1)),Re={class:"rightTable"},Te={class:"btn-group"},Ue={class:"pagination-container"},Fe={__name:"index",setup(h){const{proxy:v}=me(),{nursing_handover_status:M,sys_notice_type:qe,room_type:Me,room_area:Ye}=v.useDict("nursing_handover_status","sys_notice_type","room_type","room_area"),x=u(!1),{handover_status:Y}=v.useDict("handover_status"),$=fe(),C=u(!1);u(""),u(!1);const D=u([]),I=u([]),S=u([]),G={children:"children",label:"label"},H=u(""),R=u(0),T=u([]),j=_e({queryParams:{pageNum:1,pageSize:10,status:void 0,handoverDate:void 0,dayNurse:void 0,nightNurse:void 0,buildingName:void 0,floorNumber:void 0},form:{},rules:{}}),{queryParams:t,form:$e,rules:Ge}=ge(j);function A(n){n.type=="building"?(t.value.buildingName=n.label,t.value.buildingId=n.id,U(n.label)):n.type=="floor"&&(t.value.floorNumber=n.floorNumber,t.value.floorId=n.id),L()}const E=n=>{t.value.pageNum=n,y()},O=()=>{$.push({path:"/nursecheckin/nurseCheckinAdd/add/0/add"})},Q=n=>{C.value=!0,ke(()=>{v.$refs.detailNurseRef.sendParams(n)})},J=async()=>{try{const n=await Ce();n.code===200&&(T.value=n.data.map(o=>{var _;return{...o,children:(_=o.children)==null?void 0:_.map(p=>({...p,children:[]}))}}))}catch(n){console.error("获取房间树形数据失败:",n),xe.error("获取楼栋信息失败")}},y=()=>{x.value=!0,De({...t.value}).then(n=>{D.value=n.rows,R.value=n.total}).finally(()=>{x.value=!1})};function L(){D.value=[],t.pageNum=1,y()}const U=async n=>{S.value=[],t.value.floorNumber="";const o=I.value.filter(p=>p.buildingName==n),_=await Ie(o[0].id);S.value=_.rows};function K(){v.$refs.queryForm.resetFields(),t.value={pageNum:1,pageSize:10,status:void 0,handoverDate:void 0,dayNurse:void 0,nightNurse:void 0,buildingName:void 0,buildingId:void 0,floorNumber:void 0,floorId:void 0},L()}const W=n=>{t.value.pageSize=n,y()};function X(){J(),Z(),y()}const Z=async()=>{const n=await Se();I.value=n.rows||[]};return be(()=>{X()}),(n,o)=>{const _=r("OfficeBuilding"),p=r("el-icon"),ee=r("Grid"),le=r("el-tree"),F=r("el-col"),ae=r("el-date-picker"),g=r("el-form-item"),q=r("el-input"),z=r("el-option"),B=r("el-select"),N=r("el-button"),te=r("el-form"),s=r("el-table-column"),ne=r("dict-tag"),oe=r("View"),re=r("el-table"),ie=r("el-pagination"),de=r("el-row"),se=r("el-dialog"),ue=he("loading");return d(),f("div",ze,[e(de,{gutter:24},{default:a(()=>[e(F,{span:4},{default:a(()=>[c("div",Be,[Pe,e(le,{data:T.value,props:G,"node-key":"id","highlight-current":"","default-expand-all":"","expand-on-click-node":!1,"current-node-key":H.value,onNodeClick:A},{default:a(({node:l,data:m})=>[m.type==="building"?(d(),f(b,{key:0},[e(p,{style:{color:"#409eff","margin-right":"4px"}},{default:a(()=>[e(_)]),_:1}),c("span",null,w(m.label),1)],64)):m.type==="floor"?(d(),f(b,{key:1},[e(p,{style:{color:"#409eff","margin-right":"4px"}},{default:a(()=>[e(ee)]),_:1}),c("span",null,w(m.label),1)],64)):m.type==="bed"?(d(),f(b,{key:2},[e(p,{style:{color:"#1890ff","margin-right":"4px"}},{default:a(()=>[e(i(ye))]),_:1}),c("span",null,w(m.label),1)],64)):ve("",!0)]),_:1},8,["data","current-node-key"])])]),_:1}),e(F,{span:20},{default:a(()=>[c("div",Re,[e(te,{inline:!0,model:i(t),class:"search-form","label-width":"100px",ref:"queryForm"},{default:a(()=>[e(g,{label:"交接班日期",prop:"handoverDate"},{default:a(()=>[e(ae,{modelValue:i(t).handoverDate,"onUpdate:modelValue":o[0]||(o[0]=l=>i(t).handoverDate=l),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"160px"},clearable:""},null,8,["modelValue"])]),_:1}),e(g,{label:"白班交接人",prop:"dayNurse"},{default:a(()=>[e(q,{modelValue:i(t).dayNurse,"onUpdate:modelValue":o[1]||(o[1]=l=>i(t).dayNurse=l),placeholder:"请输入交接人",style:{width:"160px"},clearable:""},null,8,["modelValue"])]),_:1}),e(g,{label:"夜班交接人",prop:"nightNurse"},{default:a(()=>[e(q,{modelValue:i(t).nightNurse,"onUpdate:modelValue":o[2]||(o[2]=l=>i(t).nightNurse=l),placeholder:"请输入交接人",style:{width:"160px"},clearable:""},null,8,["modelValue"])]),_:1}),e(g,{label:"楼栋信息",prop:"buildingName"},{default:a(()=>[e(B,{modelValue:i(t).buildingName,"onUpdate:modelValue":o[3]||(o[3]=l=>i(t).buildingName=l),style:{width:"160px"},placeholder:"全部",clearable:"",onChange:U},{default:a(()=>[(d(!0),f(b,null,P(I.value,l=>(d(),V(z,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"楼栋层数",prop:"floorNumber"},{default:a(()=>[e(B,{modelValue:i(t).floorNumber,"onUpdate:modelValue":o[4]||(o[4]=l=>i(t).floorNumber=l),style:{width:"160px"},placeholder:"全部",clearable:"",disabled:!i(t).buildingName},{default:a(()=>[(d(!0),f(b,null,P(S.value,l=>(d(),V(z,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(g,{label:"状 态",prop:"status"},{default:a(()=>[e(B,{modelValue:i(t).status,"onUpdate:modelValue":o[5]||(o[5]=l=>i(t).status=l),style:{width:"160px"},placeholder:"全部",clearable:""},{default:a(()=>[(d(!0),f(b,null,P(i(Y),l=>(d(),V(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),c("div",Te,[e(N,{type:"primary",icon:"Search",onClick:L,style:{"margin-left":"60px"}},{default:a(()=>[k("查询")]),_:1}),e(N,{icon:"Refresh",onClick:K},{default:a(()=>[k("重置")]),_:1}),e(N,{onClick:O,type:"primary",icon:"Plus",plain:""},{default:a(()=>[k(" 新增交接 ")]),_:1})])]),_:1},8,["model"]),Ne((d(),V(re,{data:D.value,style:{width:"100%"},border:"",stripe:""},{default:a(()=>[e(s,{prop:"index",label:"序号",width:"60",align:"center"},{default:a(l=>[c("span",null,w(l.$index+1),1)]),_:1}),e(s,{prop:"handoverDate",label:"交接班日期",width:"100",align:"center"}),e(s,{prop:"floorNumber",label:"楼栋层数",width:"80",align:"center"}),e(s,{prop:"buildingName",label:"楼栋信息",width:"120",align:"center"}),e(s,{prop:"dayNurse",label:"白班护士",width:"100",align:"center"}),e(s,{prop:"dayHandoverTime",label:"白班交接时间","min-width":"180",align:"center"}),e(s,{prop:"nightNurse",label:"夜班护士",width:"100",align:"center"}),e(s,{prop:"nightHandoverTime",label:"夜班交接时间","min-width":"180",align:"center"}),e(s,{prop:"status",label:"状态","min-width":"180",align:"center"},{default:a(l=>[e(ne,{options:i(M),value:l.row.status},null,8,["options","value"])]),_:1}),e(s,{label:"操作",width:"180",fixed:"right",align:"center"},{default:a(l=>[e(N,{type:"primary",onClick:m=>Q(l.row),link:""},{default:a(()=>[e(p,null,{default:a(()=>[e(oe)]),_:1}),k("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ue,x.value]]),c("div",Ue,[e(ie,{layout:"prev, pager, next, sizes, jumper",total:R.value,background:"","page-size":i(t).pageSize,"current-page":i(t).pageNum,onSizeChange:W,onCurrentChange:E,"page-sizes":[10,20,50]},null,8,["total","page-size","current-page"])])])]),_:1})]),_:1}),e(se,{title:"交接详情",modelValue:C.value,"onUpdate:modelValue":o[6]||(o[6]=l=>C.value=l),width:"70%"},{default:a(()=>[e(ce,{ref:"detailNurseRef"},null,512)]),_:1},8,["modelValue"])])}}},Qe=pe(Fe,[["__scopeId","data-v-7d7a19de"]]);export{Qe as default};
