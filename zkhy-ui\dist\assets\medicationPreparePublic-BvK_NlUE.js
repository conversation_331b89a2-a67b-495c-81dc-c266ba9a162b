import{_ as Ae,d as He,r as I,z as P,M as V,C as Ke,e as y,I as We,J as Xe,l as a,c as v,o as p,f as t,h as s,i as g,k as W,t as T,n as D,j as O,K as U,L as k,ap as oe,Q as Ze,D as ea,w as de,G as N,v as aa,x as na,E as se}from"./index-B0qHf98Y.js";import ta from"./index-CCXF19OR.js";import{q as la,i as ve,s as ia}from"./index-2bfkpdNb.js";import{a as X}from"./index-e0lvOvDC.js";import"./leave-Dd4WELmg.js";const ce=H=>(aa("data-v-50e3340d"),H=H(),na(),H),oa={class:"addMedicationReceive"},da={class:"medicine-dialog"},sa={class:"section"},ca=ce(()=>g("h3",null,"老人信息",-1)),ra={class:"value"},ua={key:0,class:"avatar-container"},ma={class:"value"},pa={class:"value"},ga={class:"value"},va={class:"value"},fa={class:"value"},_a={class:"value"},ha={class:"value"},ba={key:0,class:"section"},Ma=ce(()=>g("h3",null,"药品信息",-1)),Da={class:"section"},Va=ce(()=>g("h3",null,"摆药计划",-1)),ya={class:"time-period"},Sa={class:"time-header"},Ta={key:0,class:"medication-list"},za={class:"medication-name"},Ca={class:"medication-details"},Ua={key:1,style:{"text-align":"center",padding:"10px",color:"#909399"}},ka={class:"time-period"},Na={class:"time-header"},Ya={key:0,class:"medication-list"},Ia={class:"medication-name"},$a={class:"medication-details"},Ea={key:1,style:{"text-align":"center",padding:"10px",color:"#909399"}},La={class:"time-period"},wa={class:"time-header"},xa={key:0,class:"medication-list"},Pa={class:"medication-name"},Oa={class:"medication-details"},Ba={key:1,style:{"text-align":"center",padding:"10px",color:"#909399"}},ja={key:1,style:{"text-align":"center",padding:"20px",color:"#909399"}},qa={class:"dataTimeList"},Fa=["onClick"],Qa={class:"personalized-settings"},Ga={class:"time-period"},Ja={class:"time-header"},Ra={key:0,class:"medication-list"},Aa={class:"medication-name"},Ha={class:"medication-details"},Ka={key:1,style:{"text-align":"center",padding:"10px",color:"#909399"}},Wa={class:"time-period"},Xa={class:"time-header"},Za={key:0,class:"medication-list"},en={class:"medication-name"},an={class:"medication-details"},nn={key:1,style:{"text-align":"center",padding:"10px",color:"#909399"}},tn={class:"time-period"},ln={class:"time-header"},on={key:0,class:"medication-list"},dn={class:"medication-name"},sn={class:"medication-details"},cn={key:1,style:{"text-align":"center",padding:"10px",color:"#909399"}},rn={class:"preparer-info"},un={class:"dialog-footer"},mn={__name:"medicationPreparePublic",emits:["success"],setup(H,{expose:fe,emit:_e}){const{proxy:re}=He(),$=I(!1),{sys_user_sex:he,inventory_results:be}=re.useDict("inventory_results","sys_user_sex"),B=I(!1),K=I(null),z=I("add"),Me=I(null),f=P(()=>z.value==="view"),De=P(()=>({view:"查看预备信息",add:"新增预备",edit:"修改预备"})[z.value]),F=I([{value:"毫升",label:"毫升"},{value:"毫克",label:"毫克"},{value:"克",label:"克"}]),j=I([]),e=I({medicationTime:{morning:{medication:"0",planMedication:[]},noon:{medication:"0",planMedication:[]},evening:{medication:"0",planMedication:[]}},period:[],isPersonalized:!1,personalizedSettings:{},selectedDate:V().format("YYYY-MM-DD"),dateTimeList:[]}),Ve=_e,ye=I(JSON.parse(localStorage.getItem("userInfo"))),Se=n=>n.getTime()<new Date(new Date().setHours(0,0,0,0)).getTime(),Te=Ke({elderName:[{required:!0,message:"请选择老人",trigger:""}],preparer:[{required:!0,message:"请输入核对人",trigger:"blur"}],period:[{required:!0,message:"请选择摆药周期",trigger:"change"}]}),ze=I([]),Ce=I([]),ue=n=>({餐前:"0",餐中:"1",餐后:"2",睡前:"3"})[n],Ue=n=>{const l={medicationId:n.medicationId,medicationName:n.medicationName,dosage:n.dosage||0,beforeMeal:ue(n.usePeriod),dosageUnit:n.dosageUnit},c=["morning","noon","evening"];let o=!1;c.forEach(r=>{e.value.medicationTime[r].planMedication.some(h=>h.medicationId===n.medicationId)||(e.value.medicationTime[r].planMedication.push({...l}),e.value.medicationTime[r].medication="1",o=!0)}),e.value.isPersonalized&&o&&Object.keys(e.value.personalizedSettings).forEach(r=>{c.forEach(m=>{e.value.personalizedSettings[r][m].planMedication.some(S=>S.medicationId===n.medicationId)||(e.value.personalizedSettings[r][m].planMedication.push({...l}),e.value.personalizedSettings[r][m].medication="1")})}),o?N.success("药品添加成功"):N.warning("该药品已在所有时间段存在")},Z=(n,l)=>{const c=e.value.medicationTime[n].planMedication[l];if(e.value.medicationTime[n].planMedication.splice(l,1),e.value.medicationTime[n].planMedication.length===0&&(e.value.medicationTime[n].medication="0"),e.value.isPersonalized){const o=V().format("YYYY-MM-DD");Object.keys(e.value.personalizedSettings).forEach(r=>{if(r>=o){const m=e.value.personalizedSettings[r][n],h=m.planMedication.findIndex(S=>S.medicationId===c.medicationId);h!==-1&&(m.planMedication.splice(h,1),m.planMedication.length===0&&(m.medication="0"))}})}N.success("删除成功")},ke=n=>{if(!n||n.length!==2){e.value.isPersonalized=!1,e.value.dateTimeList=[],e.value.personalizedSettings={};return}const[l,c]=n;if(V(c).isBefore(l)){N.warning("结束日期不能早于开始日期"),e.value.period=[],e.value.isPersonalized=!1,e.value.dateTimeList=[],e.value.personalizedSettings={};return}e.value.dateTimeList=ee(l,c),ae()},ee=(n,l)=>{const c=V(n),r=V(l).diff(c,"days")+1,m=V().startOf("day");return Array.from({length:r},(h,S)=>{const u=V(c).add(S,"days"),M=(z.value==="edit",u.isBefore(m));return{date:u.format("YYYY-MM-DD"),day:u.format("dddd"),fullDate:`${u.format("YYYY-MM-DD")}(${me(u)})`,isPastDate:M}})},me=n=>`周${["日","一","二","三","四","五","六"][n.day()]}`,ae=()=>{var h,S;if(!e.value.period||e.value.period.length!==2){e.value.personalizedSettings={};return}const n=V().format("YYYY-MM-DD"),[l,c]=e.value.period;let o=[];try{o=typeof e.value.dateTimeList=="string"?JSON.parse(e.value.dateTimeList):e.value.dateTimeList}catch{o=ee(l,c)}e.value.dateTimeList=o.map(u=>({date:u.date,day:u.day||V(u.date).format("dddd"),fullDate:u.fullDate||`${u.date}(${me(V(u.date))})`,isPastDate:u.isPastDate!==void 0?u.isPastDate:V(u.date).isBefore(n)}));let r={};try{r=typeof e.value.personalizedSettings=="string"?JSON.parse(e.value.personalizedSettings):e.value.personalizedSettings||{}}catch{r={}}const m={};if(o.forEach(u=>{var q,Y,b;const M=u.date,w=u.isPastDate,x=r[M]||{};if(z.value==="edit"&&w){m[M]=X(x);return}m[M]={morning:{id:((q=x.morning)==null?void 0:q.id)||null,medication:e.value.medicationTime.morning.medication,planMedication:X(e.value.medicationTime.morning.planMedication)},noon:{id:((Y=x.noon)==null?void 0:Y.id)||null,medication:e.value.medicationTime.noon.medication,planMedication:X(e.value.medicationTime.noon.planMedication)},evening:{id:((b=x.evening)==null?void 0:b.id)||null,medication:e.value.medicationTime.evening.medication,planMedication:X(e.value.medicationTime.evening.planMedication)}}}),e.value.personalizedSettings=m,!e.value.selectedDate||!m[e.value.selectedDate]){const u=((h=o.find(M=>!M.isPastDate))==null?void 0:h.date)||((S=o[0])==null?void 0:S.date);u&&(e.value.selectedDate=u)}},Ne=()=>{if(f.value)return;if(!e.value.period||e.value.period.length!==2){N.warning("请先选择摆药周期"),e.value.isPersonalized=!1;return}if(!Object.values(e.value.medicationTime).some(l=>l.medication==="1"&&l.planMedication.length>0)){N.warning("请至少设置一个时间段的服药计划后才能开启个性化"),e.value.isPersonalized=!1;return}ae()},A=I(!1);(()=>{de(()=>e.value.medicationTime.morning.planMedication,(n,l)=>{e.value.isPersonalized&&!A.value&&ne("morning")},{deep:!0,immediate:!0}),de(()=>e.value.medicationTime.noon.planMedication,(n,l)=>{e.value.isPersonalized&&!A.value&&ne("noon")},{deep:!0,immediate:!0}),de(()=>e.value.medicationTime.evening.planMedication,(n,l)=>{e.value.isPersonalized&&!A.value&&ne("evening")},{deep:!0,immediate:!0})})();const ne=n=>{const l=V().format("YYYY-MM-DD");Object.keys(e.value.personalizedSettings).forEach(c=>{c>=l&&e.value.medicationTime[n].planMedication.forEach((o,r)=>{const m=e.value.personalizedSettings[c][n].planMedication[r];m&&m.medicationId===o.medicationId&&(m.dosage=Number(o.dosage),m.beforeMeal=o.beforeMeal,m.dosageUnit=o.dosageUnit)})})},Ye=P(()=>Object.values(e.value.medicationTime).some(n=>n.medication==="1"&&n.planMedication.length>0)),Ie=n=>{e.value.selectedDate=n},C=n=>{const l=e.value.dateTimeList.find(c=>c.date===n);return l?l.isPastDate:!1},$e=P(()=>e.value.medicationTime.morning.planMedication.length>0),Ee=P(()=>e.value.medicationTime.noon.planMedication.length>0),Le=P(()=>e.value.medicationTime.evening.planMedication.length>0),we=P(()=>{var n;return((n=e.value.personalizedSettings[e.value.selectedDate])==null?void 0:n.morning.planMedication.length)>0}),xe=P(()=>{var n;return((n=e.value.personalizedSettings[e.value.selectedDate])==null?void 0:n.noon.planMedication.length)>0}),Pe=P(()=>{var n;return((n=e.value.personalizedSettings[e.value.selectedDate])==null?void 0:n.evening.planMedication.length)>0}),E=async(n,l,c)=>{if(typeof c=="number"){const o=e.value.medicationTime[n].planMedication[c],r=V().format("YYYY-MM-DD");Object.keys(e.value.personalizedSettings).forEach(m=>{if(m>=r){const h=e.value.personalizedSettings[m][n].planMedication.findIndex(S=>S.medicationId===o.medicationId);h!==-1&&(e.value.personalizedSettings[m][n].planMedication[h][l]=o[l])}});return}},te=(n,l)=>{if(e.value.medicationTime[n][l]==="0")se.confirm("取消选中将删除此时间段下的摆药数据，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.value.medicationTime[n].planMedication=[];const c=V().format("YYYY-MM-DD");Object.keys(e.value.personalizedSettings).forEach(o=>{o>=c&&(e.value.personalizedSettings[o][n].planMedication=[],e.value.personalizedSettings[o][n].medication="0")}),e.value.medicationTime[n].medication="0"}).catch(()=>{e.value.medicationTime[n].medication="1"});else{const c=e.value.medicationTime[n][l],o=V().format("YYYY-MM-DD");Object.keys(e.value.personalizedSettings).forEach(r=>{r>=o&&(e.value.personalizedSettings[r][n][l]=c)})}},le=async(n,l)=>{const c=e.value.personalizedSettings[e.value.selectedDate][n][l];c==="0"&&!C(e.value.selectedDate)?se.confirm("确定要删除此日期下该时间段的摆药数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.value.personalizedSettings[e.value.selectedDate][n].planMedication=[],e.value.personalizedSettings[e.value.selectedDate][n].medication="0"}).catch(()=>{e.value.personalizedSettings[e.value.selectedDate][n].medication="1"}):e.value.personalizedSettings[e.value.selectedDate][n][l]=c},L=(n,l,c)=>{const o=e.value.personalizedSettings[e.value.selectedDate][n].planMedication[c];e.value.personalizedSettings[e.value.selectedDate][n].planMedication[c][l]=o[l]},Oe=()=>{re.$refs.elderSelectComponentRef.openElderSelect()},pe=n=>{j.value=[],n&&(e.value={...e.value,elderName:n.elderName,elderId:n.id,elderCode:n.elderCode,gender:n.gender,avatar:n.avatar,bedNumber:n.bedNumber,roomNumber:n.roomNumber,age:n.age,buildingName:n.buildingName,buildingId:n.buildingId,floorNumber:n.floorNumber,floorId:n.floorId,nursingLevel:n.nursingLevel,checkInDate:n.checkInDate,roomId:n.roomId,bedId:n.bedId},$.value=!0,ve({elderId:e.value.elderId,pageSize:1e4}).then(l=>{var c;j.value=((c=l.data)==null?void 0:c.filter(o=>o.medicationStatus=="01"||o.medicationStatus=="02"))||[],z.value==="add"&&j.value.length>0&&(e.value.medicationTime.morning.planMedication=[],e.value.medicationTime.noon.planMedication=[],e.value.medicationTime.evening.planMedication=[],j.value.forEach(o=>{const r={medicationId:o.medicationId,medicationName:o.medicationName,dosage:Number(o.dosage),beforeMeal:ue(o.usePeriod),dosageUnit:o.dosageUnit};e.value.medicationTime.morning.planMedication.push({...r}),e.value.medicationTime.morning.medication="1",e.value.medicationTime.noon.planMedication.push({...r}),e.value.medicationTime.noon.medication="1",e.value.medicationTime.evening.planMedication.push({...r}),e.value.medicationTime.evening.medication="1"}),ae())}).finally(()=>{$.value=!1}))},ie=()=>{e.value={medicationTime:{morning:{medication:"0",planMedication:[]},noon:{medication:"0",planMedication:[]},evening:{medication:"0",planMedication:[]}},period:[],isPersonalized:!1,personalizedSettings:{},selectedDate:V().format("YYYY-MM-DD"),dateTimeList:[]},ze.value=[],Ce.value=[],j.value=[],K.value&&K.value.resetFields()},Be=()=>{var S;if(!e.value.period||e.value.period.length!==2)return[];const n=V().startOf("day"),l=V(e.value.period[0]).startOf("day"),c=V(e.value.period[1]).startOf("day"),o=V.max(l,n),r=c;Math.max(0,r.diff(o,"days")+1);const m={},h=(u,M=1)=>{m[u.medicationName]||(m[u.medicationName]={name:u.medicationName,totalDosage:0,unit:u.dosageUnit,logicQuantity:0}),m[u.medicationName].totalDosage+=(Number(u.dosage)||0)*M};return e.value.personalizedSettings&&Object.entries(e.value.personalizedSettings).forEach(([u,M])=>{V(u).startOf("day").isBetween(o,r,null,"[]")&&["morning","noon","evening"].forEach(x=>{var q,Y;((q=M[x])==null?void 0:q.medication)==="1"&&((Y=M[x].planMedication)==null||Y.forEach(b=>{h(b,1)}))})}),(S=j.value)==null||S.forEach(u=>{u.medicationName&&m[u.medicationName]&&(m[u.medicationName].logicQuantity=u.logicQuantity||0)}),Object.entries(m).filter(([u,M])=>M.totalDosage>M.logicQuantity).map(([u,M])=>({id:u,name:M.name,required:M.totalDosage,available:M.logicQuantity,unit:M.unit,shortage:M.totalDosage-M.logicQuantity,dateRange:[o.format("YYYY-MM-DD"),r.format("YYYY-MM-DD")]}))},je=async()=>{try{if(await K.value.validate(),!Object.values(e.value.medicationTime).some(o=>o.medication==="1"&&o.planMedication.length>0)){N.warning("请至少设置一个时间段的服药计划");return}if(!e.value.period||e.value.period.length!==2){N.warning("请设置摆药周期");return}const l=Be();if(l.length>0){let o="以下药品的累计用量超过药品数量：<br/><br/>";l.forEach(r=>{o+=`⚠️药品名称: ${r.name}<br/>`,o+=`需要用量: ${r.required}<br/>`,o+=`当前药品数量: ${r.available}<br/>`,o+=`缺少数量: ${r.shortage}<br/><br/>`}),o+="请调整药品用量后再提交！",se.alert(o,"⚠️药品数量不足",{confirmButtonText:"确定",dangerouslyUseHTMLString:!0});return}const c={...e.value,preparationStartTime:e.value.period[0],preparationEndTime:e.value.period[1],period:void 0,dateTimeList:JSON.stringify(e.value.dateTimeList),recorder:ye.value.userName};if(console.log(c,"submitData"),z.value==="add"||z.value==="edit"){$.value=!0;try{const o=await ia(c);o.code==200?($.value=!1,N.success(z.value==="add"?"新增成功":"修改成功"),B.value=!1,Ve("success")):($.value=!1,N.error(o.msg))}catch(o){$.value=!1,N.error("操作失败，请重试"),console.error("提交失败:",o)}}}catch(n){N.warning("请填写完整信息"),console.error("表单验证失败:",n)}},qe=async n=>{var c;const l=await ve({elderId:n,pageSize:1e4});j.value=((c=l.data)==null?void 0:c.filter(o=>o.medicationStatus=="01"||o.medicationStatus=="02"))||[]},ge=n=>{n.id&&(A.value=!0,$.value=!0,la({id:n.id}).then(l=>{const c=l.data;z.value==="edit"&&qe(c.elderId);let o={};try{o=typeof c.personalizedSettings=="string"?JSON.parse(c.personalizedSettings):c.personalizedSettings||{}}catch(m){console.error("Error parsing personalizedSettings:",m),o={}}let r={};try{r=typeof c.medicationTime=="string"?JSON.parse(c.medicationTime):c.medicationTime||{morning:{medication:"0",planMedication:[]},noon:{medication:"0",planMedication:[]},evening:{medication:"0",planMedication:[]}}}catch(m){console.error("Error parsing medicationTime:",m),r={morning:{medication:"0",planMedication:[]},noon:{medication:"0",planMedication:[]},evening:{medication:"0",planMedication:[]}}}["morning","noon","evening"].forEach(m=>{var h;(h=r[m])!=null&&h.planMedication&&(r[m].planMedication=r[m].planMedication.map(S=>({...S,dosage:Number(S.dosage)||0})))}),e.value={...c,period:c.preparationStartTime&&c.preparationEndTime?[c.preparationStartTime,c.preparationEndTime]:[],medicationTime:r,isPersonalized:c.isPersonalized||!1,selectedDate:Object.keys(o).length>0?Object.keys(o).reduce((m,h)=>h<m?h:m):V().format("YYYY-MM-DD"),personalizedSettings:o,dateTimeList:ee(c.preparationStartTime,c.preparationEndTime)},$.value=!1}).catch(l=>{console.error("获取详情失败:",l),N.error("获取详情失败")}).finally(()=>{$.value=!1,A.value=!1}))};return fe({openView:async n=>{z.value="view",B.value=!0,ie(),ge(n)},openAdd:n=>{z.value="add",Me.value=null,B.value=!0,ie(),n&&pe(n)},openEdit:async n=>{z.value="edit",B.value=!0,ie(),ge(n)}}),(n,l)=>{const c=y("el-input"),o=y("el-form-item"),r=y("el-col"),m=y("dict-tag-span"),h=y("el-row"),S=y("el-avatar"),u=y("el-table-column"),M=y("dict-tag"),w=y("el-button"),x=y("el-table"),q=y("el-date-picker"),Y=y("el-checkbox"),b=y("el-radio"),Q=y("el-radio-group"),G=y("el-input-number"),J=y("el-option"),R=y("el-select"),Fe=y("el-switch"),Qe=y("el-scrollbar"),Ge=y("el-form"),Je=y("el-dialog"),Re=We("loading");return Xe((p(),v("div",oa,[t(Je,{title:a(De),modelValue:a(B),"onUpdate:modelValue":l[17]||(l[17]=i=>ea(B)?B.value=i:null),width:"80%","close-on-click-modal":!1},{footer:s(()=>[g("span",un,[t(w,{onClick:l[16]||(l[16]=i=>B.value=!1)},{default:s(()=>[D("关闭")]),_:1}),a(f)?W("",!0):(p(),O(w,{key:0,type:"primary",onClick:je,loading:a($)},{default:s(()=>[D(" 提交 ")]),_:1},8,["loading"]))])]),default:s(()=>[t(Ge,{ref_key:"medicineForm",ref:K,model:a(e),"label-width":"120px",rules:a(Te),"label-position":"left"},{default:s(()=>[g("div",da,[g("div",sa,[ca,t(h,{gutter:24,class:"elder-info"},{default:s(()=>[t(r,{span:8},{default:s(()=>[t(o,{label:"老人姓名",prop:"elderName"},{default:s(()=>[t(c,{modelValue:a(e).elderName,"onUpdate:modelValue":l[0]||(l[0]=i=>a(e).elderName=i),placeholder:"请选择老人",readonly:"",onClick:Oe,disabled:a(f)||a(z)=="edit"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(r,{span:8},{default:s(()=>[t(o,{label:"老人编号",prop:"elderCode"},{default:s(()=>[g("span",ra,T(a(e).elderCode),1)]),_:1})]),_:1}),t(r,{span:8},{default:s(()=>[t(o,{label:"性别",prop:"gender"},{default:s(()=>[t(m,{options:a(he),value:a(e).gender},null,8,["options","value"])]),_:1})]),_:1})]),_:1}),a(e).avatar?(p(),v("div",ua,[t(S,{shape:"square",size:140,fit:"fill",src:a(e).avatar},null,8,["src"])])):W("",!0),t(h,{gutter:24,class:"elder-info"},{default:s(()=>[t(r,{span:8},{default:s(()=>[t(o,{label:"床位编号",prop:"bedNumber"},{default:s(()=>[g("span",ma,T(a(e).roomNumber?a(e).roomNumber+"-"+a(e).bedNumber:a(e).bedNumber),1)]),_:1})]),_:1}),t(r,{span:8},{default:s(()=>[t(o,{label:"房间信息",prop:"roomNumber"},{default:s(()=>[g("span",pa,T(a(e).roomNumber),1)]),_:1})]),_:1}),t(r,{span:8},{default:s(()=>[t(o,{label:"年龄",prop:"age"},{default:s(()=>[g("span",ga,T(a(e).age),1)]),_:1})]),_:1})]),_:1}),t(h,{gutter:24,class:"elder-info"},{default:s(()=>[t(r,{span:8},{default:s(()=>[t(o,{label:"楼栋信息",prop:"buildingName"},{default:s(()=>[g("span",va,T(a(e).buildingName),1)]),_:1})]),_:1}),t(r,{span:8},{default:s(()=>[t(o,{label:"楼层信息",prop:"floorNumber"},{default:s(()=>[g("span",fa,T(a(e).floorNumber),1)]),_:1})]),_:1}),t(r,{span:8},{default:s(()=>[t(o,{label:"护理等级",prop:"nursingLevel"},{default:s(()=>[g("span",_a,T(a(e).nursingLevel),1)]),_:1})]),_:1})]),_:1}),t(h,{gutter:24,class:"elder-info"},{default:s(()=>[t(r,{span:8},{default:s(()=>[t(o,{label:"入住时间",prop:"checkInDate"},{default:s(()=>[g("span",ha,T(a(e).checkInDate),1)]),_:1})]),_:1})]),_:1})]),a(z)=="add"?(p(),v("div",ba,[Ma,t(h,{gutter:24},{default:s(()=>[t(x,{data:a(j),border:"",style:{width:"100%"},"empty-text":"暂无药品信息，请先选择老人！"},{default:s(()=>[t(u,{prop:"id",label:"序号",width:"60",align:"center"},{default:s(i=>[D(T(i.$index+1),1)]),_:1}),t(u,{prop:"collectionTime",label:"收药时间",align:"center","min-width":"120"}),t(u,{prop:"medicationId",label:"药品编号",align:"center"}),t(u,{prop:"medicationName",label:"药品名称",align:"center","min-width":"180"}),t(u,{prop:"dosage",label:"用量",align:"center"}),t(u,{prop:"quantity",label:"数量",align:"center"}),t(u,{prop:"logicQuantity",label:"摆药剩余量",align:"center"}),t(u,{prop:"expiryDate",label:"有效期",align:"center","min-width":"140"}),t(u,{prop:"medicationStatus",label:"状态",align:"center"},{default:s(i=>[t(M,{options:a(be),value:i.row.medicationStatus},null,8,["options","value"])]),_:1}),t(u,{label:"操作",width:"150",fixed:"right",align:"center"},{default:s(i=>[t(w,{link:"",type:"primary",onClick:_=>Ue(i.row)},{default:s(()=>[D("添加")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])):W("",!0),g("div",Da,[Va,a(e).medicationTime?(p(),O(h,{key:0,gutter:24},{default:s(()=>[t(r,{span:12},{default:s(()=>[t(o,{label:"摆药周期",prop:"period"},{default:s(()=>[t(q,{modelValue:a(e).period,"onUpdate:modelValue":l[1]||(l[1]=i=>a(e).period=i),type:"daterange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",disabled:a(f)||a(z)=="edit","disabled-date":Se,onChange:ke,"value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(r,{span:24},{default:s(()=>[g("div",ya,[g("div",Sa,[t(Y,{modelValue:a(e).medicationTime.morning.medication,"onUpdate:modelValue":l[2]||(l[2]=i=>a(e).medicationTime.morning.medication=i),label:"早晨",size:"large","true-value":"1","false-value":"0",disabled:a(f)||!a($e),onChange:l[3]||(l[3]=i=>te("morning","medication"))},null,8,["modelValue","disabled"])]),a(e).medicationTime.morning.planMedication.length>0?(p(),v("div",Ta,[(p(!0),v(U,null,k(a(e).medicationTime.morning.planMedication,(i,_)=>(p(),v("div",{class:"medication-item",key:_},[g("div",za,T(i.medicationName),1),g("div",Ca,[t(Q,{modelValue:i.beforeMeal,"onUpdate:modelValue":d=>i.beforeMeal=d,class:"radioGroupClass",disabled:a(f),onChange:d=>E("morning","beforeMeal",_)},{default:s(()=>[t(b,{value:"0"},{default:s(()=>[D("餐前")]),_:1}),t(b,{value:"1"},{default:s(()=>[D("餐中")]),_:1}),t(b,{value:"2"},{default:s(()=>[D("餐后")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(G,{min:0,step:.1,modelValue:i.dosage,"onUpdate:modelValue":d=>i.dosage=d,placeholder:"剂量",disabled:a(f),onChange:d=>E("morning","dosage",_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(R,{modelValue:i.dosageUnit,"onUpdate:modelValue":d=>i.dosageUnit=d,placeholder:"单位",disabled:a(f),onChange:d=>E("morning","dosageUnit",_)},{default:s(()=>[(p(!0),v(U,null,k(a(F),d=>(p(),O(J,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(w,{type:"danger",icon:a(oe),circle:"",onClick:d=>Z("morning",_),disabled:a(f)},null,8,["icon","onClick","disabled"])])]))),128))])):(p(),v("div",Ua," 暂无摆药数据 "))])]),_:1}),t(r,{span:24},{default:s(()=>[g("div",ka,[g("div",Na,[t(Y,{modelValue:a(e).medicationTime.noon.medication,"onUpdate:modelValue":l[4]||(l[4]=i=>a(e).medicationTime.noon.medication=i),label:"中午",size:"large","true-value":"1","false-value":"0",disabled:a(f)||!a(Ee),onChange:l[5]||(l[5]=i=>te("noon","medication"))},null,8,["modelValue","disabled"])]),a(e).medicationTime.noon.planMedication.length>0?(p(),v("div",Ya,[(p(!0),v(U,null,k(a(e).medicationTime.noon.planMedication,(i,_)=>(p(),v("div",{class:"medication-item",key:_},[g("div",Ia,T(i.medicationName),1),g("div",$a,[t(Q,{modelValue:i.beforeMeal,"onUpdate:modelValue":d=>i.beforeMeal=d,class:"radioGroupClass",disabled:a(f),onChange:d=>E("noon","beforeMeal",_)},{default:s(()=>[t(b,{value:"0"},{default:s(()=>[D("餐前")]),_:1}),t(b,{value:"1"},{default:s(()=>[D("餐中")]),_:1}),t(b,{value:"2"},{default:s(()=>[D("餐后")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(G,{min:0,step:.1,modelValue:i.dosage,"onUpdate:modelValue":d=>i.dosage=d,placeholder:"剂量",disabled:a(f),onChange:d=>E("noon","dosage",_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(R,{modelValue:i.dosageUnit,"onUpdate:modelValue":d=>i.dosageUnit=d,placeholder:"单位",disabled:a(f),onChange:d=>E("noon","dosageUnit",_)},{default:s(()=>[(p(!0),v(U,null,k(a(F),d=>(p(),O(J,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(w,{type:"danger",icon:a(oe),circle:"",onClick:d=>Z("noon",_),disabled:a(f)},null,8,["icon","onClick","disabled"])])]))),128))])):(p(),v("div",Ea," 暂无摆药数据 "))])]),_:1}),t(r,{span:24},{default:s(()=>[g("div",La,[g("div",wa,[t(Y,{modelValue:a(e).medicationTime.evening.medication,"onUpdate:modelValue":l[6]||(l[6]=i=>a(e).medicationTime.evening.medication=i),label:"晚上",size:"large","true-value":"1","false-value":"0",disabled:a(f)||!a(Le),onChange:l[7]||(l[7]=i=>te("evening","medication"))},null,8,["modelValue","disabled"])]),a(e).medicationTime.evening.planMedication.length>0?(p(),v("div",xa,[(p(!0),v(U,null,k(a(e).medicationTime.evening.planMedication,(i,_)=>(p(),v("div",{class:"medication-item",key:_},[g("div",Pa,T(i.medicationName),1),g("div",Oa,[t(Q,{modelValue:i.beforeMeal,"onUpdate:modelValue":d=>i.beforeMeal=d,class:"radioGroupClass",disabled:a(f),onChange:d=>E("evening","beforeMeal",_)},{default:s(()=>[t(b,{value:"0"},{default:s(()=>[D("餐前")]),_:1}),t(b,{value:"1"},{default:s(()=>[D("餐中")]),_:1}),t(b,{value:"2"},{default:s(()=>[D("餐后")]),_:1}),t(b,{value:"3"},{default:s(()=>[D("睡前")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(G,{min:0,step:.1,modelValue:i.dosage,"onUpdate:modelValue":d=>i.dosage=d,placeholder:"剂量",disabled:a(f),onChange:d=>E("evening","dosage",_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(R,{modelValue:i.dosageUnit,"onUpdate:modelValue":d=>i.dosageUnit=d,placeholder:"单位",disabled:a(f),onChange:d=>E("evening","dosageUnit",_)},{default:s(()=>[(p(!0),v(U,null,k(a(F),d=>(p(),O(J,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(w,{type:"danger",icon:a(oe),circle:"",onClick:d=>Z("evening",_),disabled:a(f)},null,8,["icon","onClick","disabled"])])]))),128))])):(p(),v("div",Ba," 暂无摆药数据 "))])]),_:1})]),_:1})):(p(),v("div",ja," 暂无摆药计划! ")),t(o,{label:"开启个性化",prop:"isPersonalized"},{default:s(()=>[t(Fe,{modelValue:a(e).isPersonalized,"onUpdate:modelValue":l[8]||(l[8]=i=>a(e).isPersonalized=i),class:"drawer-switch",onChange:Ne,disabled:a(f)||!a(e).period||a(e).period.length!==2||!a(Ye)},null,8,["modelValue","disabled"])]),_:1}),a(e).isPersonalized&&a(e).period&&a(e).period.length===2?(p(),O(h,{key:2,gutter:24},{default:s(()=>[t(r,{span:6},{default:s(()=>[g("div",qa,[t(Qe,{"max-height":"400"},{default:s(()=>[(p(!0),v(U,null,k(a(e).dateTimeList,i=>(p(),v("div",{class:Ze(["dateTime",{selected:a(e).selectedDate===i.date,"disabled-date":a(f).value||i.isPastDate}]),key:i.date,onClick:_=>Ie(i.date)},T(i.fullDate),11,Fa))),128))]),_:1})])]),_:1}),t(r,{span:18},{default:s(()=>[g("div",Qa,[g("div",Ga,[g("div",Ja,[t(Y,{modelValue:a(e).personalizedSettings[a(e).selectedDate].morning.medication,"onUpdate:modelValue":l[9]||(l[9]=i=>a(e).personalizedSettings[a(e).selectedDate].morning.medication=i),label:"早晨",size:"large","true-value":"1","false-value":"0",disabled:a(f)||C(a(e).selectedDate)||!a(we),onChange:l[10]||(l[10]=i=>le("morning","medication"))},null,8,["modelValue","disabled"])]),a(e).personalizedSettings[a(e).selectedDate].morning.medication==="1"&&a(e).personalizedSettings[a(e).selectedDate].morning.planMedication.length>0?(p(),v("div",Ra,[(p(!0),v(U,null,k(a(e).personalizedSettings[a(e).selectedDate].morning.planMedication,(i,_)=>(p(),v("div",{class:"medication-item",key:_},[g("div",Aa,T(i.medicationName),1),g("div",Ha,[t(Q,{modelValue:i.beforeMeal,"onUpdate:modelValue":d=>i.beforeMeal=d,class:"radioGroupClass",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("morning","beforeMeal",_)},{default:s(()=>[t(b,{value:"0"},{default:s(()=>[D("餐前")]),_:1}),t(b,{value:"1"},{default:s(()=>[D("餐中")]),_:1}),t(b,{value:"2"},{default:s(()=>[D("餐后")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(G,{min:0,step:.1,modelValue:i.dosage,"onUpdate:modelValue":d=>i.dosage=d,placeholder:"剂量",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("morning","dosage",_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(R,{modelValue:i.dosageUnit,"onUpdate:modelValue":d=>i.dosageUnit=d,placeholder:"单位",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("morning","dosageUnit",_)},{default:s(()=>[(p(!0),v(U,null,k(a(F),d=>(p(),O(J,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]))),128))])):(p(),v("div",Ka," 暂无摆药数据 "))]),g("div",Wa,[g("div",Xa,[t(Y,{modelValue:a(e).personalizedSettings[a(e).selectedDate].noon.medication,"onUpdate:modelValue":l[11]||(l[11]=i=>a(e).personalizedSettings[a(e).selectedDate].noon.medication=i),label:"中午",size:"large","true-value":"1","false-value":"0",disabled:a(f)||C(a(e).selectedDate)||!a(xe),onChange:l[12]||(l[12]=i=>le("noon","medication"))},null,8,["modelValue","disabled"])]),a(e).personalizedSettings[a(e).selectedDate].noon.medication==="1"&&a(e).personalizedSettings[a(e).selectedDate].noon.planMedication.length>0?(p(),v("div",Za,[(p(!0),v(U,null,k(a(e).personalizedSettings[a(e).selectedDate].noon.planMedication,(i,_)=>(p(),v("div",{class:"medication-item",key:_},[g("div",en,T(i.medicationName),1),g("div",an,[t(Q,{modelValue:i.beforeMeal,"onUpdate:modelValue":d=>i.beforeMeal=d,class:"radioGroupClass",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("noon","beforeMeal",_)},{default:s(()=>[t(b,{value:"0"},{default:s(()=>[D("餐前")]),_:1}),t(b,{value:"1"},{default:s(()=>[D("餐中")]),_:1}),t(b,{value:"2"},{default:s(()=>[D("餐后")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(G,{min:0,step:.1,modelValue:i.dosage,"onUpdate:modelValue":d=>i.dosage=d,placeholder:"剂量",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("noon","dosage",_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(R,{modelValue:i.dosageUnit,"onUpdate:modelValue":d=>i.dosageUnit=d,placeholder:"单位",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("noon","dosageUnit",_)},{default:s(()=>[(p(!0),v(U,null,k(a(F),d=>(p(),O(J,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]))),128))])):(p(),v("div",nn," 暂无摆药数据 "))]),g("div",tn,[g("div",ln,[t(Y,{modelValue:a(e).personalizedSettings[a(e).selectedDate].evening.medication,"onUpdate:modelValue":l[13]||(l[13]=i=>a(e).personalizedSettings[a(e).selectedDate].evening.medication=i),label:"晚上",size:"large","true-value":"1","false-value":"0",disabled:a(f)||C(a(e).selectedDate)||!a(Pe),onChange:l[14]||(l[14]=i=>le("evening","medication"))},null,8,["modelValue","disabled"])]),a(e).personalizedSettings[a(e).selectedDate].evening.medication==="1"&&a(e).personalizedSettings[a(e).selectedDate].evening.planMedication.length>0?(p(),v("div",on,[(p(!0),v(U,null,k(a(e).personalizedSettings[a(e).selectedDate].evening.planMedication,(i,_)=>(p(),v("div",{class:"medication-item",key:_},[g("div",dn,T(i.medicationName),1),g("div",sn,[t(Q,{modelValue:i.beforeMeal,"onUpdate:modelValue":d=>i.beforeMeal=d,class:"radioGroupClass",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("evening","beforeMeal",_)},{default:s(()=>[t(b,{value:"0"},{default:s(()=>[D("餐前")]),_:1}),t(b,{value:"1"},{default:s(()=>[D("餐中")]),_:1}),t(b,{value:"2"},{default:s(()=>[D("餐后")]),_:1}),t(b,{value:"3"},{default:s(()=>[D("睡前")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(G,{min:0,step:.1,modelValue:i.dosage,"onUpdate:modelValue":d=>i.dosage=d,placeholder:"剂量",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("evening","dosage",_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),t(R,{modelValue:i.dosageUnit,"onUpdate:modelValue":d=>i.dosageUnit=d,placeholder:"单位",disabled:a(f)||C(a(e).selectedDate),onChange:d=>L("evening","dosageUnit",_)},{default:s(()=>[(p(!0),v(U,null,k(a(F),d=>(p(),O(J,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]))),128))])):(p(),v("div",cn," 暂无摆药数据 "))])])]),_:1})]),_:1})):W("",!0)]),g("div",rn,[t(o,{label:"核对人",prop:"preparer"},{default:s(()=>[t(c,{modelValue:a(e).preparer,"onUpdate:modelValue":l[15]||(l[15]=i=>a(e).preparer=i),placeholder:"请输入",disabled:a(f),style:{width:"200px"}},null,8,["modelValue","disabled"])]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(a(ta),{ref:"elderSelectComponentRef",onSelectLerder:pe},null,512)])),[[Re,a($)]])}}},bn=Ae(mn,[["__scopeId","data-v-50e3340d"]]);export{bn as default};
