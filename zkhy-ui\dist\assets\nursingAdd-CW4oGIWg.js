import{a as ce,g as _e}from"./roommanage-DBG5TiIR.js";import{l as fe}from"./tLiveRoom-DmSXfHxo.js";import{l as ge}from"./tLiveBed-B9bJPM9s.js";import{g as ve,b as be}from"./index-DCxZ1IEc.js";import{X as he,_ as Ve,B as Ne,r as v,d as ye,C as Ce,N as we,e as c,j as f,o as m,h as o,f as l,l as n,i as _,k as x,c as N,K as y,L as C,n as L,a1 as ke,v as xe,x as Be,E as Ie,G as $e,dx as G}from"./index-B0qHf98Y.js";import{a as Le}from"./tNursingHandover-ugsVwCUd.js";function Ue(w){return he({url:"/handover/nursingItem/list",method:"get",params:w})}const U=w=>(xe("data-v-ad5e511b"),w=w(),Be(),w),He={class:"backdiv"},Te=U(()=>_("h3",{class:"titleCss"},"房间信息",-1));const qe=U(()=>_("h3",{class:"titleCss"},"人员交接信息",-1)),De={class:"backdiv"},Ye=U(()=>_("div",{class:"title_room_h4"},[_("span",null,"白班交接信息")],-1)),Me={style:{"margin-left":"25px"}},Ae={class:"backdiv"},Re={class:"title_room_h5"},Se={style:{"margin-left":"25px"}},Fe={class:"bottom_room_table"},Oe={class:"flexDiv"},Ee=U(()=>_("div",{class:"title_room"},[_("h3",null,"床位交接详情")],-1)),Ge={class:"add_room_table"},je={class:"form-actions"},Pe=Ne({name:"nursingAdd"}),ze=Object.assign(Pe,{props:{isShow:{type:String,default:"add"},data:{type:Object,default:()=>{}}},emits:"closeEvent",setup(w,{expose:j,emit:P}){const z=P,K=v("新增"),B=v(!1),H=v([]),I=v([]),T=v([]),X=v(!1),{proxy:q}=ye(),{room_type:Ke,room_area:Xe}=q.useDict("room_type","room_area"),D=v([]),Y=v([]),M=v([]),J=Ce({form:{tNursingHandoverBedList:[]},queryParams:{pageNum:1,pageSize:20,contentType:"护理"}}),{form:a,queryParams:Q}=we(J),b=v({building:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼栋层数",trigger:"change"}],roomId:[{required:!0,message:"请选择房间号",trigger:"change"}],areaName:[{required:!0,message:"请选择区域",trigger:"change"}],roomType:[{required:!0,message:"请选择房间类型",trigger:"change"}],handoverDate:[{required:!0,message:"请选择交接日期",trigger:"change"}],dayNurse:[{required:!0,message:"请选择白班交接人",trigger:"change"}],dayHandoverTime:[{required:!0,message:"请选择白班交接时间",trigger:"change"}],nightNurse:[{required:!0,message:"请选择夜班交接人",trigger:"change"}],nightHandoverTime:[{required:!0,message:"请选择夜班交接时间",trigger:"change"}],dayTotalCount:[{required:!0,message:"请输入白班交接总人数",trigger:"blur"}],dayOutCount:[{required:!0,message:"请输入白班外出人数",trigger:"blur"}],dayLeaveCount:[{required:!0,message:"请输入白班离院人数",trigger:"blur"}],dayDeathCount:[{required:!0,message:"请输入白班死亡人数",trigger:"blur"}],nightTotalCount:[{required:!0,message:"请输入夜班交接总人数",trigger:"blur"}],nightOutCount:[{required:!0,message:"请输入夜班外出人数",trigger:"blur"}],nightLeaveCount:[{required:!0,message:"请输入夜班离院人数",trigger:"blur"}],nightDeathCount:[{required:!0,message:"请输入夜班死亡人数",trigger:"blur"}]});function W(){console.log("init"),B.value=!0,Z(),oe(),Ue(Q.value).then(r=>{console.log(r,"listNursingItem"),Y.value=r.rows})}function Z(){_e().then(r=>{H.value=r.rows})}function ee(r){ce(r).then(t=>{I.value=t.rows,H.value.map(s=>{s.id==r&&(a.value.buildingName=s.buildingName)})})}function le(r){const t=I.value.filter(s=>s.floorNumber==r);fe({floorId:t[0].id}).then(s=>{console.log(s,"getRoomListByBuild"),T.value=s.rows}),I.value.map(s=>{s.id==r&&(a.value.floorNumber=s.floorName)})}function oe(){be({roleKeys:["nurse"],pageSize:1e3}).then(r=>{D.value=r.rows})}const te=async r=>{var s,p;r.bedNumber="",r.elderName="";const t=await ge({roomId:r.roomId});M.value=t.rows,r.roomNumber=((p=(s=T.value)==null?void 0:s.filter(u=>u.id===r.roomId)[0])==null?void 0:p.roomName)||""},ae=async r=>{var s;r.elderName="";const t=(s=M.value.filter(p=>p.bedNumber===r.bedNumber)[0])==null?void 0:s.id;await ve({bedId:t}).then(p=>{var u,i,k,g;console.log(p,"res111"),r.elderId=((u=p.rows[0])==null?void 0:u.id)||"",r.elderName=((i=p.rows[0])==null?void 0:i.elderName)||"",r.elderAge=((k=p.rows[0])==null?void 0:k.age)||"",r.elderGender=((g=p.rows[0])==null?void 0:g.gender)||"",r.bedId=t}),console.log(r,"row")};function ne(){q.$refs.formRef.validate(r=>{r&&(a.value.tNursingHandoverBedList.map(t=>{t.elderId=t.elderId,t.elderAge=t.elderAge,t.elderGender=t.elderGender,t.bedId=t.bedId,t.roomNumber=t.roomNumber,t.roomId=t.roomId,t.bedNumber=t.bedNumber,t.elderName=t.elderName,G(t.handoverContent1)&&(t.handoverContent1=t.handoverContent1.join(",")),G(t.handoverContent2)&&(t.handoverContent2=t.handoverContent2.join(","))}),Le(a.value).then(t=>{q.$message({message:"保存成功",type:"success"}),F(),z("closeEvent"),B.value=!1}))})}function S(){B.value=!1,F()}function F(){a.value={building:null,floorId:null,roomId:null,areaName:null,roomName:null,roomType:null,roomStatus:null,roomLevel:null,roomArea:null,roomPrice:null,roomRemark:null,createTime:null,updateTime:null,createBy:null,updateBy:null,roomId:null,roomName:null,roomType:null,roomStatus:null,tNursingHandoverBedList:[]}}function de(){a.value.tNursingHandoverBedList.push({roomId:"",elderId:"",elderAge:"",elderGender:"",bedNumber:"",bedId:"",elderName:"",handoverContent1:[],handoverContent2:[],remark1:"",remark2:""}),console.log(a.value,"form.value")}const re=r=>{Ie.confirm("确认删除该条记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{a.value.tNursingHandoverBedList.splice(r,1),$e.success("删除成功")})};return j({init:W}),(r,t)=>{const s=c("el-option"),p=c("el-select"),u=c("el-form-item"),i=c("el-col"),k=c("el-date-picker"),g=c("el-row"),A=c("el-input"),h=c("el-input-number"),ue=c("Moon"),se=c("el-icon"),$=c("el-button"),V=c("el-table-column"),O=c("el-checkbox"),E=c("el-checkbox-group"),ie=c("el-table"),me=c("el-form"),pe=c("el-dialog");return m(),f(pe,{title:K.value,modelValue:B.value,"onUpdate:modelValue":t[20]||(t[20]=e=>B.value=e),width:"90%","append-to-body":"",onClose:S},{default:o(()=>[l(me,{model:n(a),ref:"formRef","label-width":"120px","label-position":"left",rules:b.value},{default:o(()=>[_("div",He,[Te,l(g,{gutter:20},{default:o(()=>[l(i,{span:8},{default:o(()=>[l(u,{label:"楼栋信息",prop:"buildingId"},{default:o(()=>[l(p,{modelValue:n(a).buildingId,"onUpdate:modelValue":t[0]||(t[0]=e=>n(a).buildingId=e),style:{width:"200px"},onChange:ee},{default:o(()=>[(m(!0),N(y,null,C(H.value,e=>(m(),f(s,{key:e.value,label:e.buildingName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{span:8},{default:o(()=>[l(u,{label:"楼栋层数",prop:"floorId"},{default:o(()=>[l(p,{modelValue:n(a).floorId,"onUpdate:modelValue":t[1]||(t[1]=e=>n(a).floorId=e),style:{width:"200px"},onChange:le},{default:o(()=>[(m(!0),N(y,null,C(I.value,e=>(m(),f(s,{key:e.value,label:e.floorName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),x("",!0),l(i,{span:8},{default:o(()=>[l(u,{label:"交接日期",prop:"handoverDate"},{default:o(()=>[l(k,{modelValue:n(a).handoverDate,"onUpdate:modelValue":t[3]||(t[3]=e=>n(a).handoverDate=e),type:"date",style:{width:"200px"},placeholder:"选择日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),x("",!0),qe,_("div",De,[Ye,_("div",Me,[l(g,{gutter:20},{default:o(()=>[l(i,{span:12},{default:o(()=>[l(u,{label:"白班交接人",prop:"dayNurse"},{default:o(()=>[l(p,{modelValue:n(a).dayNurse,"onUpdate:modelValue":t[8]||(t[8]=e=>n(a).dayNurse=e),style:{width:"200px"}},{default:o(()=>[(m(!0),N(y,null,C(D.value,e=>(m(),f(s,{key:e.userId,label:e.nickName,value:e.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{span:12},{default:o(()=>[l(u,{label:"交接班日期",prop:"dayHandoverTime"},{default:o(()=>[l(k,{modelValue:n(a).dayHandoverTime,"onUpdate:modelValue":t[9]||(t[9]=e=>n(a).dayHandoverTime=e),type:"datetime",placeholder:"选择日期时间","value-format":"YYYY-MM-DD HH:mm",value:"YYYY-MM-DD HH:mm"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(g,null,{default:o(()=>[l(i,{span:6},{default:o(()=>[l(u,{label:"交接人数",prop:"dayTotalCount"},{default:o(()=>[l(h,{modelValue:n(a).dayTotalCount,"onUpdate:modelValue":t[10]||(t[10]=e=>n(a).dayTotalCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:6},{default:o(()=>[l(u,{label:"外出人数",prop:"dayOutCount"},{default:o(()=>[l(h,{modelValue:n(a).dayOutCount,"onUpdate:modelValue":t[11]||(t[11]=e=>n(a).dayOutCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:6},{default:o(()=>[l(u,{label:"离院人数",prop:"dayLeaveCount"},{default:o(()=>[l(h,{modelValue:n(a).dayLeaveCount,"onUpdate:modelValue":t[12]||(t[12]=e=>n(a).dayLeaveCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:6},{default:o(()=>[l(u,{label:"死亡人数",prop:"dayDeathCount"},{default:o(()=>[l(h,{modelValue:n(a).dayDeathCount,"onUpdate:modelValue":t[13]||(t[13]=e=>n(a).dayDeathCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),_("div",Ae,[_("div",Re,[_("span",null,[l(se,{color:"#FF00FF"},{default:o(()=>[l(ue)]),_:1}),L(" 夜班交接信息")])]),_("div",Se,[l(g,{gutter:20},{default:o(()=>[l(i,{span:12},{default:o(()=>[l(u,{label:"夜班交接人：",prop:"nightNurse"},{default:o(()=>[l(p,{modelValue:n(a).nightNurse,"onUpdate:modelValue":t[14]||(t[14]=e=>n(a).nightNurse=e),style:{width:"200px"}},{default:o(()=>[(m(!0),N(y,null,C(D.value,e=>(m(),f(s,{key:e.userId,label:e.nickName,value:e.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{span:12},{default:o(()=>[l(u,{label:"交接班日期：",prop:"nightHandoverTime"},{default:o(()=>[l(k,{modelValue:n(a).nightHandoverTime,"onUpdate:modelValue":t[15]||(t[15]=e=>n(a).nightHandoverTime=e),type:"datetime",placeholder:"选择日期时间","value-format":"YYYY-MM-DD HH:mm",value:"YYYY-MM-DD HH:mm"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(g,{gutter:20},{default:o(()=>[l(i,{span:6},{default:o(()=>[l(u,{label:"交接人数：",prop:"nightTotalCount"},{default:o(()=>[l(h,{modelValue:n(a).nightTotalCount,"onUpdate:modelValue":t[16]||(t[16]=e=>n(a).nightTotalCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:6},{default:o(()=>[l(u,{label:"外出人数：",prop:"nightOutCount"},{default:o(()=>[l(h,{modelValue:n(a).nightOutCount,"onUpdate:modelValue":t[17]||(t[17]=e=>n(a).nightOutCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:6},{default:o(()=>[l(u,{label:"离院人数：",prop:"nightLeaveCount"},{default:o(()=>[l(h,{modelValue:n(a).nightLeaveCount,"onUpdate:modelValue":t[18]||(t[18]=e=>n(a).nightLeaveCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:6},{default:o(()=>[l(u,{label:"死亡人数：",prop:"nightDeathCount"},{default:o(()=>[l(h,{modelValue:n(a).nightDeathCount,"onUpdate:modelValue":t[19]||(t[19]=e=>n(a).nightDeathCount=e),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),_("div",Fe,[_("div",Oe,[Ee,_("div",Ge,[l($,{type:"primary",onClick:de,icon:"plus",disabled:!n(a).floorId},{default:o(()=>[L("添加床位")]),_:1},8,["disabled"])])]),l(ie,{data:n(a).tNursingHandoverBedList,style:{width:"100%"},class:"tNursingCss",border:"",disabled:!n(a).floorId},{default:o(()=>[l(V,{label:"房间号","min-width":"180",align:"left"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.roomId`,rules:b.value.requiredSelect,style:{width:"100%"}},{default:o(()=>[l(p,{modelValue:e.row.roomId,"onUpdate:modelValue":d=>e.row.roomId=d,placeholder:"请选择",onChange:d=>te(e.row)},{default:o(()=>[(m(!0),N(y,null,C(T.value,d=>(m(),f(s,{key:d.id,label:d.roomName,value:d.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),l(V,{label:"床位号",width:"180",align:"center"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.bedNumber`,rules:b.value.requiredSelect,style:{width:"100%"}},{default:o(()=>[l(p,{modelValue:e.row.bedNumber,"onUpdate:modelValue":d=>e.row.bedNumber=d,placeholder:"请选择床位",disabled:!e.row.roomId,loading:X.value,onChange:d=>ae(e.row)},{default:o(()=>[(m(!0),N(y,null,C(M.value,d=>(m(),f(s,{key:d.id,label:d.bedNumber,value:d.bedNumber},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","loading","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),l(V,{label:"老人姓名",width:"180",align:"center"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.elderName`,rules:b.value.requiredInput,style:{width:"100%"}},{default:o(()=>[l(A,{modelValue:e.row.elderName,"onUpdate:modelValue":d=>e.row.elderName=d,disabled:!e.row.bedNumber},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop","rules"])]),_:1}),l(V,{label:"白班交接内容","min-width":"450",align:"center"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.handoverContent1`,rules:b.value.requiredInput,style:{width:"100%"}},{default:o(()=>[l(E,{modelValue:e.row.handoverContent1,"onUpdate:modelValue":d=>e.row.handoverContent1=d},{default:o(()=>[l(g,null,{default:o(()=>[(m(!0),N(y,null,C(Y.value,(d,R)=>(m(),f(i,{span:8},{default:o(()=>[(m(),f(O,{key:R,label:d.contentDetail,value:d.id,class:"ckeckboxCss"},null,8,["label","value"]))]),_:2},1024))),256))]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),l(V,{label:"白班备注","min-width":"200",align:"center"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.remark1`,rules:b.value.requiredInput,style:{width:"100%"}},{default:o(()=>[l(A,{modelValue:e.row.remark1,"onUpdate:modelValue":d=>e.row.remark1=d,placeholder:"请输入白班备注",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),l(V,{label:"夜班交接内容","min-width":"450",align:"center"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.handoverContent2`,rules:b.value.requiredInput,style:{width:"100%"}},{default:o(()=>[l(E,{modelValue:e.row.handoverContent2,"onUpdate:modelValue":d=>e.row.handoverContent2=d},{default:o(()=>[l(g,null,{default:o(()=>[(m(!0),N(y,null,C(Y.value,(d,R)=>(m(),f(i,{span:8},{default:o(()=>[(m(),f(O,{key:R,label:d.contentDetail,value:d.id,class:"ckeckboxCss"},null,8,["label","value"]))]),_:2},1024))),256))]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),l(V,{label:"夜班备注","min-width":"200",align:"center"},{default:o(e=>[l(u,{prop:`tNursingHandoverBedList.${e.$index}.remark2`,rules:b.value.requiredInput,style:{width:"100%"}},{default:o(()=>[l(A,{modelValue:e.row.remark2,"onUpdate:modelValue":d=>e.row.remark2=d,placeholder:"请输入夜班备注",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),x("",!0),x("",!0),x("",!0),x("",!0),l(V,{label:"操作",width:"100",align:"center",fixed:"right"},{default:o(e=>[l($,{type:"danger",icon:n(ke),circle:"",onClick:d=>re(e.$index)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data","disabled"])]),_("div",je,[l($,{type:"primary",onClick:ne},{default:o(()=>[L("提交")]),_:1}),l($,{onClick:S},{default:o(()=>[L("取消")]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])}}}),ol=Ve(ze,[["__scopeId","data-v-ad5e511b"]]);export{ol as default};
