import{a as ee,g as le}from"./roommanage-DBG5TiIR.js";import{l as oe}from"./tLiveRoom-DmSXfHxo.js";import{_ as ae,B as te,r as v,d as ue,e as s,j as _,o as i,h as o,f as e,i as m,c as h,K as y,L as V,k as f,n as Y,v as ne,x as re}from"./index-B0qHf98Y.js";import{g as de,b as se}from"./index-DCxZ1IEc.js";import{a as me}from"./tNursingHandover-ugsVwCUd.js";const w=C=>(ne("data-v-28ee2b00"),C=C(),re(),C),ie={class:"backdiv"},pe=w(()=>m("h3",{class:"titleCss"},"房间信息",-1)),ve=w(()=>m("h3",{class:"titleCss"},"人员交接信息",-1)),ge={class:"backdiv"},_e=w(()=>m("div",{class:"title_room_h4"},[m("span",null,"白班交接信息")],-1)),fe={style:{"margin-left":"25px"}},ce={class:"backdiv"},be={class:"title_room_h5"},he={style:{"margin-left":"25px"}},ye=w(()=>m("h3",{class:"titleCss"},"床位交接详情",-1)),Ve={class:"form-actions"},Ce=te({name:"nursingAdd"}),Ne=Object.assign(Ce,{props:{isShow:{type:String,default:"add"},data:{type:Object,default:()=>{}}},emits:"closeEvent",setup(C,{expose:O,emit:F}){const $=F,R=v("新增"),c=v(!1),I=v([]),N=v([]),L=v([]),{proxy:T}=ue(),{room_type:ke,room_area:Be}=T.useDict("room_type","room_area"),x=v([]),U=v({building:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼栋层数",trigger:"change"}],roomId:[{required:!0,message:"请选择房间号",trigger:"change"}],areaName:[{required:!0,message:"请选择区域",trigger:"change"}],roomType:[{required:!0,message:"请选择房间类型",trigger:"change"}],handoverDate:[{required:!0,message:"请选择交接日期",trigger:"change"}],dayNurse:[{required:!0,message:"请选择白班交接人",trigger:"change"}],dayHandoverTime:[{required:!0,message:"请选择白班交接时间",trigger:"change"}],nightNurse:[{required:!0,message:"请选择夜班交接人",trigger:"change"}],nightHandoverTime:[{required:!0,message:"请选择夜班交接时间",trigger:"change"}],dayTotalCount:[{required:!0,message:"请输入白班交接总人数",trigger:"blur"}],dayOutCount:[{required:!0,message:"请输入白班外出人数",trigger:"blur"}],dayLeaveCount:[{required:!0,message:"请输入白班离院人数",trigger:"blur"}],dayDeathCount:[{required:!0,message:"请输入白班死亡人数",trigger:"blur"}],nightTotalCount:[{required:!0,message:"请输入夜班交接总人数",trigger:"blur"}],nightOutCount:[{required:!0,message:"请输入夜班外出人数",trigger:"blur"}],nightLeaveCount:[{required:!0,message:"请输入夜班离院人数",trigger:"blur"}],nightDeathCount:[{required:!0,message:"请输入夜班死亡人数",trigger:"blur"}]});function S(){console.log("init"),c.value=!0,A(),K()}function A(){le().then(u=>{I.value=u.rows})}function E(u){ee(u).then(a=>{N.value=a.rows,I.value.map(d=>{d.id==u&&(t.value.buildingName=d.buildingName)})})}function j(u){const a=N.value.filter(d=>d.floorNumber==u);oe({floorId:a[0].id}).then(d=>{console.log(d,"getRoomListByBuild"),L.value=d.rows}),N.value.map(d=>{d.id==u&&(t.value.floorNumber=d.floorName)})}function K(){se({roleKeys:["nurse"],pageSize:1e3}).then(u=>{x.value=u.rows})}function z(u){de({roomId:u}).then(a=>{console.log(a,"getUserByRoomId"),t.value.tNursingHandoverBedList=a.rows}),L.value.map(a=>{console.log(a,"roomitem"),a.id==u&&(t.value.roomNumber=a.roomName,t.value.areaName=a.areaName,t.value.roomType=a.roomType),console.log(t.value.roomNumber,"form.value.roomName")})}function G(){console.log(t.value,"form.value"),t.value.tNursingHandoverBedList.map(u=>{u.elderAge=u.age,u.elderGender=u.gender}),console.log(t.value,"form.value"),T.$refs.formRef.validate(u=>{u&&me(t.value).then(a=>{T.$message({message:"保存成功",type:"success"}),console.log(a,"addNursing"),q(),$("closeEvent"),c.value=!1})})}const t=v({});function P(){c.value=!1,q()}function q(){t.value={building:null,floorId:null,roomId:null,areaName:null,roomName:null,roomType:null,roomStatus:null,roomLevel:null,roomArea:null,roomPrice:null,roomRemark:null,createTime:null,updateTime:null,createBy:null,updateBy:null,roomId:null,roomName:null,roomType:null,roomStatus:null}}return O({init:S}),(u,a)=>{const d=s("el-option"),b=s("el-select"),n=s("el-form-item"),r=s("el-col"),g=s("el-row"),k=s("el-input"),D=s("el-date-picker"),p=s("el-input-number"),J=s("Moon"),Q=s("el-icon"),B=s("el-table-column"),W=s("el-table"),M=s("el-button"),X=s("el-form"),Z=s("el-dialog");return i(),_(Z,{title:R.value,modelValue:c.value,"onUpdate:modelValue":a[20]||(a[20]=l=>c.value=l),width:"70%","append-to-body":""},{default:o(()=>[e(X,{model:t.value,ref:"formRef","label-width":"120px","label-position":"left",rules:U.value},{default:o(()=>[m("div",ie,[pe,e(g,{gutter:20},{default:o(()=>[e(r,{span:8},{default:o(()=>[e(n,{label:"楼栋信息",prop:"buildingId"},{default:o(()=>[e(b,{modelValue:t.value.buildingId,"onUpdate:modelValue":a[0]||(a[0]=l=>t.value.buildingId=l),style:{width:"200px"},onChange:E},{default:o(()=>[(i(!0),h(y,null,V(I.value,l=>(i(),_(d,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:o(()=>[e(n,{label:"楼栋层数",prop:"floorId"},{default:o(()=>[e(b,{modelValue:t.value.floorId,"onUpdate:modelValue":a[1]||(a[1]=l=>t.value.floorId=l),style:{width:"200px"},onChange:j},{default:o(()=>[(i(!0),h(y,null,V(N.value,l=>(i(),_(d,{key:l.value,label:l.floorName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:o(()=>[e(n,{label:"房间号",prop:"roomId"},{default:o(()=>[e(b,{modelValue:t.value.roomId,"onUpdate:modelValue":a[2]||(a[2]=l=>t.value.roomId=l),placeholder:"请选择",style:{width:"200px"},onChange:z},{default:o(()=>[(i(!0),h(y,null,V(L.value,l=>(i(),_(d,{key:l.id,label:l.roomName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),m("div",null,[e(g,{gutter:20},{default:o(()=>[e(r,{span:8},{default:o(()=>[e(n,{label:"区域",prop:"area"},{default:o(()=>[e(k,{modelValue:t.value.areaName,"onUpdate:modelValue":a[3]||(a[3]=l=>t.value.areaName=l),clearable:"",style:{width:"200px"},disabled:""},null,8,["modelValue"]),f("",!0)]),_:1})]),_:1}),e(r,{span:8},{default:o(()=>[e(n,{label:"房间类型",prop:"room_area"},{default:o(()=>[e(k,{modelValue:t.value.roomType,"onUpdate:modelValue":a[5]||(a[5]=l=>t.value.roomType=l),clearable:"",style:{width:"200px"},disabled:""},null,8,["modelValue"]),f("",!0)]),_:1})]),_:1}),e(r,{span:8},{default:o(()=>[e(n,{label:"交接日期",prop:"handoverDate"},{default:o(()=>[e(D,{modelValue:t.value.handoverDate,"onUpdate:modelValue":a[7]||(a[7]=l=>t.value.handoverDate=l),type:"date",style:{width:"200px"},placeholder:"选择日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),ve,m("div",ge,[_e,m("div",fe,[e(g,{gutter:20},{default:o(()=>[e(r,{span:12},{default:o(()=>[e(n,{label:"白班交接人",prop:"dayNurse"},{default:o(()=>[e(b,{modelValue:t.value.dayNurse,"onUpdate:modelValue":a[8]||(a[8]=l=>t.value.dayNurse=l),style:{width:"200px"}},{default:o(()=>[(i(!0),h(y,null,V(x.value,l=>(i(),_(d,{key:l.userId,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:o(()=>[e(n,{label:"交接班日期",prop:"dayHandoverTime"},{default:o(()=>[e(D,{modelValue:t.value.dayHandoverTime,"onUpdate:modelValue":a[9]||(a[9]=l=>t.value.dayHandoverTime=l),type:"datetime",placeholder:"选择日期时间","value-format":"YYYY-MM-DD HH:mm",value:"YYYY-MM-DD HH:mm"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,null,{default:o(()=>[e(r,{span:6},{default:o(()=>[e(n,{label:"交接人数",prop:"dayTotalCount"},{default:o(()=>[e(p,{modelValue:t.value.dayTotalCount,"onUpdate:modelValue":a[10]||(a[10]=l=>t.value.dayTotalCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:6},{default:o(()=>[e(n,{label:"外出人数",prop:"dayOutCount"},{default:o(()=>[e(p,{modelValue:t.value.dayOutCount,"onUpdate:modelValue":a[11]||(a[11]=l=>t.value.dayOutCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:6},{default:o(()=>[e(n,{label:"离院人数",prop:"dayLeaveCount"},{default:o(()=>[e(p,{modelValue:t.value.dayLeaveCount,"onUpdate:modelValue":a[12]||(a[12]=l=>t.value.dayLeaveCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:6},{default:o(()=>[e(n,{label:"死亡人数",prop:"dayDeathCount"},{default:o(()=>[e(p,{modelValue:t.value.dayDeathCount,"onUpdate:modelValue":a[13]||(a[13]=l=>t.value.dayDeathCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),m("div",ce,[m("div",be,[m("span",null,[e(Q,{color:"#FF00FF"},{default:o(()=>[e(J)]),_:1}),Y(" 夜班交接信息")])]),m("div",he,[e(g,{gutter:20},{default:o(()=>[e(r,{span:12},{default:o(()=>[e(n,{label:"夜班交接人：",prop:"nightNurse"},{default:o(()=>[e(b,{modelValue:t.value.nightNurse,"onUpdate:modelValue":a[14]||(a[14]=l=>t.value.nightNurse=l),style:{width:"200px"}},{default:o(()=>[(i(!0),h(y,null,V(x.value,l=>(i(),_(d,{key:l.userId,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:o(()=>[e(n,{label:"交接班日期：",prop:"nightHandoverTime"},{default:o(()=>[e(D,{modelValue:t.value.nightHandoverTime,"onUpdate:modelValue":a[15]||(a[15]=l=>t.value.nightHandoverTime=l),type:"datetime",placeholder:"选择日期时间","value-format":"YYYY-MM-DD HH:mm",value:"YYYY-MM-DD HH:mm"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:o(()=>[e(r,{span:6},{default:o(()=>[e(n,{label:"交接人数：",prop:"nightTotalCount"},{default:o(()=>[e(p,{modelValue:t.value.nightTotalCount,"onUpdate:modelValue":a[16]||(a[16]=l=>t.value.nightTotalCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:6},{default:o(()=>[e(n,{label:"外出人数：",prop:"nightOutCount"},{default:o(()=>[e(p,{modelValue:t.value.nightOutCount,"onUpdate:modelValue":a[17]||(a[17]=l=>t.value.nightOutCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:6},{default:o(()=>[e(n,{label:"离院人数：",prop:"nightLeaveCount"},{default:o(()=>[e(p,{modelValue:t.value.nightLeaveCount,"onUpdate:modelValue":a[18]||(a[18]=l=>t.value.nightLeaveCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:6},{default:o(()=>[e(n,{label:"死亡人数：",prop:"nightDeathCount"},{default:o(()=>[e(p,{modelValue:t.value.nightDeathCount,"onUpdate:modelValue":a[19]||(a[19]=l=>t.value.nightDeathCount=l),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),ye,e(W,{data:t.value.tNursingHandoverBedList,border:"",style:{width:"100%"}},{default:o(()=>[f("",!0),e(B,{prop:"bedNumber",label:"床位号",width:"120"}),f("",!0),e(B,{prop:"elderName",label:"老人姓名",width:"120"}),f("",!0),f("",!0),e(B,{prop:"handoverContent1",label:"白班交接内容"},{default:o(l=>[e(n,{prop:`tNursingHandoverBedList.${l.$index}.handoverContent1`,rules:U.value.handoverContent1,style:{width:"100%"}},{default:o(()=>[e(k,{modelValue:l.row.handoverContent1,"onUpdate:modelValue":H=>l.row.handoverContent1=H,type:"textarea",rows:2,placeholder:"请输入白班交接内容"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(B,{prop:"handoverContent2",label:"夜班交接内容"},{default:o(l=>[e(n,{prop:`tNursingHandoverBedList.${l.$index}.handoverContent2`,rules:U.value.handoverContent2,style:{width:"100%"}},{default:o(()=>[e(k,{modelValue:l.row.handoverContent2,"onUpdate:modelValue":H=>l.row.handoverContent2=H,type:"textarea",rows:2,placeholder:"请输入夜班交接内容"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1})]),_:1},8,["data"]),m("div",Ve,[e(M,{type:"primary",onClick:G},{default:o(()=>[Y("提交")]),_:1}),e(M,{onClick:P},{default:o(()=>[Y("取消")]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])}}}),De=ae(Ne,[["__scopeId","data-v-28ee2b00"]]);export{De as default};
