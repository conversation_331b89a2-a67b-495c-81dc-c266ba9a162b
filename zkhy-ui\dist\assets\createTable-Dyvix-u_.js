import{c as h}from"./gen-4wQ_Uuqb.js";import{r as i,d as y,e as s,j as C,o as w,h as a,i as f,f as r,D as p,l as m,n as c}from"./index-B0qHf98Y.js";const T=f("span",null,"创建表语句(支持多个建表语句)：",-1),B={class:"dialog-footer"},U={__name:"createTable",emits:["ok"],setup(N,{expose:_,emit:v}){const e=i(!1),t=i(""),{proxy:u}=y(),V=v;function x(){e.value=!0}function b(){if(t.value===""){u.$modal.msgError("请输入建表语句");return}h({sql:t.value}).then(n=>{u.$modal.msgSuccess(n.msg),n.code===200&&(e.value=!1,V("ok"))})}return _({show:x}),(n,o)=>{const g=s("el-input"),d=s("el-button"),k=s("el-dialog");return w(),C(k,{title:"创建表",modelValue:m(e),"onUpdate:modelValue":o[2]||(o[2]=l=>p(e)?e.value=l:null),width:"800px",top:"5vh","append-to-body":""},{footer:a(()=>[f("div",B,[r(d,{type:"primary",onClick:b},{default:a(()=>[c("确 定")]),_:1}),r(d,{onClick:o[1]||(o[1]=l=>e.value=!1)},{default:a(()=>[c("取 消")]),_:1})])]),default:a(()=>[T,r(g,{type:"textarea",rows:10,placeholder:"请输入文本",modelValue:m(t),"onUpdate:modelValue":o[0]||(o[0]=l=>p(t)?t.value=l:null)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}};export{U as default};
