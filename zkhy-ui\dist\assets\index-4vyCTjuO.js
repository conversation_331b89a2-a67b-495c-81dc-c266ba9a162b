import{_ as je,B as Ee,d as Qe,r as c,C as Ae,N as He,e as v,I as Oe,c as x,o as h,f as e,h as o,J as R,l as t,m as C,K as P,L,j as V,O as J,n as T,k as q,i as r,t as b,v as Je,x as We}from"./index-B0qHf98Y.js";import{g as Ge,l as ge,a as Xe,u as Ze,b as el}from"./tMedicationUseRecord-Cm3HZByX.js";import{g as Ne}from"./telderinfo-BSpoeVyZ.js";import{l as ll}from"./tLiveRoom-DmSXfHxo.js";import{g as ne,a as ye}from"./roommanage-DBG5TiIR.js";const f=M=>(Je("data-v-3ba640f8"),M=M(),We(),M),al={class:"app-container"},tl={class:"section"},ol=f(()=>r("div",{class:"section-title"},"老人信息",-1)),dl={class:"tbcss"},nl=f(()=>r("th",{class:"tbTr"},"老人姓名",-1)),rl={class:"tbTrVal"},ul=f(()=>r("th",{class:"tbTr"},"老人编号",-1)),il={class:"tbTrVal"},sl=f(()=>r("th",{class:"tbTr"},"性别",-1)),ml={class:"tbTrVal"},pl=f(()=>r("th",{class:"tbTr"},"床位编号",-1)),cl={class:"tbTrVal"},bl=f(()=>r("th",{class:"tbTr"},"房间信息",-1)),fl={class:"tbTrVal"},hl=f(()=>r("th",{class:"tbTr"},"年龄",-1)),vl={class:"tbTrVal"},_l=f(()=>r("th",{class:"tbTr"},"楼栋信息",-1)),gl={class:"tbTrVal"},Nl=f(()=>r("th",{class:"tbTr"},"楼层信息",-1)),yl={class:"tbTrVal"},Vl=f(()=>r("th",{class:"tbTr"},"护理等级",-1)),wl={class:"tbTrVal"},Il=f(()=>r("th",{class:"tbTr"},"入住时间",-1)),Tl={class:"tbTrVal"},kl={class:"section"},xl=f(()=>r("div",{class:"section-title"},"服药信息",-1)),Cl={class:"dialog-footer"},Ul={class:"section"},Dl=f(()=>r("div",{class:"section-title"},"老人信息",-1)),Rl={class:"tbcss"},Pl=f(()=>r("th",{class:"tbTr"},"老人姓名",-1)),Ll={class:"tbTrVal"},Sl=f(()=>r("th",{class:"tbTr"},"老人编号",-1)),Bl={class:"tbTrVal"},Yl=f(()=>r("th",{class:"tbTr"},"年       龄",-1)),$l={class:"tbTrVal"},ql=f(()=>r("th",{class:"tbTr"},"性       别",-1)),Ml={class:"tbTrVal"},Fl=f(()=>r("th",{class:"tbTr"},"楼栋信息",-1)),Kl={class:"tbTrVal"},zl=f(()=>r("th",{class:"tbTr"},"楼层信息",-1)),jl={class:"tbTrVal"},El=f(()=>r("th",{class:"tbTr"},"房间信息",-1)),Ql={class:"tbTrVal"},Al=f(()=>r("th",{class:"tbTr"},"床位编号",-1)),Hl={class:"tbTrVal"},Ol={class:"section"},Jl=f(()=>r("div",{class:"section-title"},"服药明细记录",-1)),Wl={style:{"font-weight":"600","margin-bottom":"5px"}},Gl={class:"dialog-footer"},Xl=Ee({name:"UseRecord"}),Zl=Object.assign(Xl,{setup(M){const{proxy:y}=Qe(),{medication_status:B,sys_user_sex:W,medication_period:F}=y.useDict("medication_status","sys_user_sex","medication_period"),K=c([]),z=c([]),Ve=c([]),G=c([]),j=c([]);c([]);const re=c([]),ue=c([]),X=c([]),S=c(!1),E=c(!1),Y=c(!0),ie=c(!0),se=c(!0),we=c([]);c(!0),c(!0);const Z=c(0),ee=c(0),le=c(""),N=c(!1),me=c("useRecord"),pe=c(""),Ie=Ae({form:{},queryParams:{pageNum:1,pageSize:10,elderId:null,elderName:null,elderCode:null,buildingId:null,buildingName:null,floorId:null,floorNumber:null,roomId:null,roomNumber:null,bedId:null,bedNumber:null,medicationDate:null,status:null,medicineId:null,medicineName:null,timePeriod:null,dosage:null,medicationTime:null,deliverer:null,supervisor:null,reaction:null,type:null,recorder:null},queryParams2:{pageNum:1,pageSize:10},queryParams3:{pageNum:1,pageSize:1e5},rules:{}}),{queryParams:m,queryParams2:p,queryParams3:Q,form:d,rules:ce}=He(Ie);function $(){be(),ne().then(u=>{K.value=u.rows||[]})}function Te(u){var n;m.value.buildingName=u;const a=K.value.filter(s=>s.buildingName==u);ye((n=a[0])==null?void 0:n.id).then(s=>{z.value=s.rows})}function ke(u){p.value.buildingName=u;const a=G.value.filter(n=>n.buildingName==u);ye(a[0].id).then(n=>{j.value=n.rows})}function xe(u){m.value.floorNumber=u;const a=z.value.filter(n=>n.floorName==u);ll({floorId:a[0].id}).then(n=>{Ve.value=n.rows})}function Ce(u){var n;p.value.floorNumber=u,console.log(p.value.floorNumber,"handleFloorChange2");const a=j.value.filter(s=>s.floorName==u);p.value.floorId=(n=a[0])==null?void 0:n.id,console.log(a,p.value.floorId,"floorId")}function Ue(){console.log("chearfloor2"),p.value.floorNumber=null,p.value.floorId=null,console.log(p.value,"chearfloor22222")}function be(){Y.value=!0,ge(m.value).then(u=>{re.value=u.rows,Z.value=u.total,Y.value=!1})}function ae(){ie.value=!0,Xe(p.value).then(u=>{ue.value=u.rows,ee.value=u.total,ie.value=!1})}function De(){S.value=!1,fe()}function fe(){d.value={id:null,elderId:null,elderName:null,elderCode:null,buildingId:null,buildingName:null,floorId:null,floorNumber:null,roomId:null,roomNumber:null,bedId:null,bedNumber:null,medicationDate:null,status:null,medicineId:null,medicineName:null,timePeriod:null,dosage:null,medicationTime:null,deliverer:null,supervisor:null,reaction:null,type:null,recorder:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null},y.resetForm("useRecordRef")}function k(){m.value.pageNum=1,$()}function A(){p.value.pageNum=1,ae()}function Re(){y.resetForm("queryRef"),k()}function Pe(){console.log("清空楼栋"),z.value=[],m.value.floorNumber=null}function Le(){console.log("清空楼栋"),j.value=[],p.value.floorNumber=null}function Se(){y.resetForm("queryRef2"),qyery2.value.floorId=null,k()}function Be(u){u=="useRecord"?(be(),ne().then(a=>{K.value=a.rows||[]})):u=="useRecordTotal"&&(ae(),ne().then(a=>{G.value=a.rows||[]}))}function Ye(){E.value=!1}function $e(){var u=X.value.map(n=>({medicationDate:y.parseTime(n.medicationDate,"{y}-{m}-{d}"),medicationTime:y.parseTime(n.medicationTime,"{h}:{m}"),timePeriod:n.timePeriod,medicineId:n.medicineId,medicineName:n.medicineName,dosage:n.dosage,timePeriod:y.selectDictLabel(F.value,n.timePeriod),status:y.selectDictLabel(B.value,n.status),type:n.type,deliverer:n.deliverer,supervisor:n.supervisor,reaction:n.reaction}));console.log(u,"data");const a=window.open("","_blank");a.document.write(`
            <html>
                <head>
                    <title>服药明细记录打印</title>
                <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                        }

                        h1 {
                            text-align: center;
                            margin-bottom: 20px;
                        }

                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }

                        th,
                        td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                            color: #666;
                        }

                        .print-date {
                            text-align: right;
                            margin-bottom: 20px;
                        }
                </style>
                </head>
                <body>
                    <h1>服药明细记录表</h1>
                    <div class="print-date">打印日期: ${new Date().toLocaleDateString()}</div>
                    <table>
                        <thead>
                            <tr>
                                <th style="width:55px">序号</th>
                                <th style="width:8px">服药日期</th>
                                <th style="width:80px">服药时间</th>
                                <th style="width:80px">药品编号</th>
                                <th style="width:140px">药品名称</th>
                                <th style="width:120px">服药剂量</th>
                                <th style="width:80px">时段</th>
                                <th style="width:80px">服药状态</th>
                                <th style="width:80px">类型</th>
                                <th style="width:80px">送药人</th>
                                <th style="width:80px">监督人</th>
                                <th style="width:80px">服用反应</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${u.map((n,s)=>`
                                <tr>
                                    <td>${s+1}</td>
                                    <td>${n.medicationDate}</td>
                                    <td>${n.medicationTime}</td>
                                    <td>${n.medicineId?n.medicineId:"-"}</td>
                                    <td>${n.medicineName?n.medicineName:"-"}</td>
                                    <td>${n.dosage?n.dosage:"-"}</td>
                                    <td>${n.timePeriod?n.timePeriod:"-"}</td>
                                    <td>${n.status?n.status:"-"}</td>
                                    <td>${n.type?n.type:"-"}</td>
                                    <td>${n.deliverer?n.deliverer:"-"}</td>
                                    <td>${n.supervisor?n.supervisor:"-"}</td>
                                    <td>${n.reaction?n.reaction:"-"}</td>
                                </tr>
                            `).join("")}
                        </tbody>
                    </table>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    <\/script>
                </body>
            </html>
        `),a.document.close()}function qe(u){fe();const a=u.id||we.value;N.value=!0,Ge(a).then(n=>{d.value=n.data,S.value=!0,le.value="查看"}),Ne(u.elderId).then(n=>{d.value.elderName=n.data.elderInfo.elderName,d.value.elderCode=n.data.elderInfo.elderCode,d.value.gender=n.data.elderInfo.gender,d.value.bedNumber=n.data.checkIn.bedNumber,d.value.roomNumber=n.data.checkIn.roomNumber,d.value.age=n.data.elderInfo.age,d.value.buildingName=n.data.checkIn.buildingName,d.value.floorNumber=n.data.checkIn.floorName,d.value.nursingLevel=n.data.checkIn.nursingLevel,d.value.checkInDate=n.data.checkIn.checkInDate,d.value.avatar=n.data.elderInfo.avatar})}function Me(u){console.log(u,"rows"),E.value=!0,pe.value=u.medicationDate?u.medicationDate.substring(0,7):"",Ne(u.elderId).then(a=>{d.value.elderName=a.data.elderInfo.elderName,d.value.elderCode=a.data.elderInfo.elderCode,d.value.gender=a.data.elderInfo.gender,d.value.bedNumber=a.data.checkIn.bedNumber,d.value.roomNumber=a.data.checkIn.roomNumber,d.value.age=a.data.elderInfo.age,d.value.buildingName=a.data.checkIn.buildingName,d.value.floorNumber=a.data.checkIn.floorName,d.value.nursingLevel=a.data.checkIn.nursingLevel,d.value.checkInDate=a.data.checkIn.checkInDate,d.value.avatar=a.data.elderInfo.avatar}),Q.value.elderId=u.elderId,Q.value.queryMonth=u.medicationDate,console.log(Q.value,"111"),ge(Q.value).then(a=>{console.log(a,"response"),X.value=a.rows})}function ea(){y.$refs.useRecordRef.validate(u=>{u&&(d.value.id!=null?Ze(d.value).then(a=>{y.$modal.msgSuccess("修改成功"),S.value=!1,$()}):el(d.value).then(a=>{y.$modal.msgSuccess("新增成功"),S.value=!1,$()}))})}return $(),(u,a)=>{const n=v("el-date-picker"),s=v("el-form-item"),_=v("el-input"),U=v("el-option"),D=v("el-select"),w=v("el-row"),H=v("el-form"),I=v("el-button"),i=v("el-table-column"),O=v("dict-tag"),te=v("el-table"),he=v("pagination"),ve=v("el-tab-pane"),oe=v("dict-tag-span"),Fe=v("el-tabs"),g=v("el-col"),Ke=v("el-avatar"),_e=v("el-dialog"),de=Oe("loading");return h(),x("div",al,[e(Fe,{modelValue:me.value,"onUpdate:modelValue":a[19]||(a[19]=l=>me.value=l),onTabChange:Be,style:{"padding-right":"10px"}},{default:o(()=>[e(ve,{label:"老人服用记录",name:"useRecord"},{default:o(()=>[R(e(H,{model:t(m),ref:"queryRef",inline:!0,"label-width":"88px"},{default:o(()=>[e(w,{gutter:10},{default:o(()=>[e(s,{label:"服药日期",prop:"medicationDate"},{default:o(()=>[e(n,{clearable:"",modelValue:t(m).medicationDate,"onUpdate:modelValue":a[0]||(a[0]=l=>t(m).medicationDate=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"200px"},placeholder:"请选择服药日期"},null,8,["modelValue"])]),_:1}),e(s,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(_,{modelValue:t(m).elderName,"onUpdate:modelValue":a[1]||(a[1]=l=>t(m).elderName=l),placeholder:"请输入老人姓名",clearable:"",style:{width:"200px"},onKeyup:C(k,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"楼栋名称",prop:"buildingName"},{default:o(()=>[e(D,{modelValue:t(m).buildingName,"onUpdate:modelValue":a[2]||(a[2]=l=>t(m).buildingName=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:Te,onClear:Pe},{default:o(()=>[(h(!0),x(P,null,L(K.value,l=>(h(),V(U,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"楼栋层号",prop:"floorNumber"},{default:o(()=>[e(D,{modelValue:t(m).floorNumber,"onUpdate:modelValue":a[3]||(a[3]=l=>t(m).floorNumber=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:xe},{default:o(()=>[(h(!0),x(P,null,L(z.value,l=>(h(),V(U,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"房  间  号",prop:"roomNumber"},{default:o(()=>[e(_,{modelValue:t(m).roomNumber,"onUpdate:modelValue":a[4]||(a[4]=l=>t(m).roomNumber=l),placeholder:"请输入房间号",clearable:"",style:{width:"200px"},onKeyup:C(k,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"药品名称",prop:"medicineName"},{default:o(()=>[e(_,{modelValue:t(m).medicineName,"onUpdate:modelValue":a[5]||(a[5]=l=>t(m).medicineName=l),placeholder:"请输入药品名称",clearable:"",style:{width:"200px"},onKeyup:C(k,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"送  药  人",prop:"deliverer"},{default:o(()=>[e(_,{modelValue:t(m).deliverer,"onUpdate:modelValue":a[6]||(a[6]=l=>t(m).deliverer=l),placeholder:"请输入送药人",clearable:"",style:{width:"200px"},onKeyup:C(k,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"监  督  人",prop:"supervisor"},{default:o(()=>[e(_,{modelValue:t(m).supervisor,"onUpdate:modelValue":a[7]||(a[7]=l=>t(m).supervisor=l),placeholder:"请输入监督人",clearable:"",style:{width:"200px"},onKeyup:C(k,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"服药状态",prop:"status"},{default:o(()=>[e(D,{modelValue:t(m).status,"onUpdate:modelValue":a[8]||(a[8]=l=>t(m).status=l),placeholder:"请选择服药状态",clearable:"",style:{width:"200px"}},{default:o(()=>[(h(!0),x(P,null,L(t(B),l=>(h(),V(U,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"]),[[J,se.value]]),e(w,{gutter:10,class:"mb8",justify:"end",style:{"margin-right":"3px"}},{default:o(()=>[e(I,{type:"primary",icon:"Search",onClick:k},{default:o(()=>[T("查询")]),_:1}),e(I,{icon:"Refresh",onClick:Re},{default:o(()=>[T("重置")]),_:1})]),_:1}),R((h(),V(te,{data:re.value,border:"",stripe:""},{default:o(()=>[e(i,{type:"index",width:"55",label:"序号",align:"center"}),e(i,{label:"服药日期",align:"center",prop:"medicationDate",width:"120"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationDate,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"老人姓名",align:"center",prop:"elderName",width:"120"}),e(i,{label:"楼层信息",align:"center",prop:"floorNumber",width:"100"}),e(i,{label:"房间号",align:"center",prop:"roomNumber",width:"100"}),e(i,{label:"床位号",align:"center",prop:"bedNumber",width:"100"},{default:o(l=>[r("span",null,b(l.row.roomNumber+"-"+l.row.bedNumber),1)]),_:1}),e(i,{label:"楼栋名称",align:"center",prop:"buildingName",width:"100"}),e(i,{label:"药品编号",align:"center",prop:"medicineId",width:"140"}),e(i,{label:"药品名称",align:"center",prop:"medicineName",width:"160"}),e(i,{label:"时段",align:"center",prop:"timePeriod",width:"60"},{default:o(l=>[e(O,{options:t(F),value:l.row.timePeriod},null,8,["options","value"])]),_:1}),e(i,{label:"服药剂量",align:"center",prop:"dosage",width:"160"}),e(i,{label:"服药时间",align:"center",prop:"timePeriod",width:"100"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationTime,"{h}:{m}")),1)]),_:1}),e(i,{label:"服药状态",align:"center",prop:"status",width:"100"},{default:o(l=>[e(O,{options:t(B),value:l.row.status},null,8,["options","value"])]),_:1}),e(i,{label:"送药人",align:"center",prop:"deliverer",width:"100"}),e(i,{label:"监督人",align:"center",prop:"supervisor",width:"100"}),q("",!0),e(i,{label:"类型",align:"center",prop:"type",width:"100"}),q("",!0),q("",!0),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},{default:o(l=>[e(I,{link:"",type:"primary",icon:"Edit",onClick:ze=>qe(l.row)},{default:o(()=>[T("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,Y.value]]),R(e(he,{total:Z.value,page:t(m).pageNum,"onUpdate:page":a[9]||(a[9]=l=>t(m).pageNum=l),limit:t(m).pageSize,"onUpdate:limit":a[10]||(a[10]=l=>t(m).pageSize=l),onPagination:$},null,8,["total","page","limit"]),[[J,Z.value>0]])]),_:1}),e(ve,{label:"服药汇总记录",name:"useRecordTotal"},{default:o(()=>[R(e(H,{model:t(p),ref:"queryRef2",inline:!0,"label-width":"88px"},{default:o(()=>[e(w,{gutter:10},{default:o(()=>[e(s,{label:"汇总月份",prop:"queryMonth"},{default:o(()=>[e(n,{clearable:"",modelValue:t(p).queryMonth,"onUpdate:modelValue":a[11]||(a[11]=l=>t(p).queryMonth=l),type:"month","value-format":"YYYY-MM",format:"YYYY-MM",style:{width:"200px"},placeholder:"请选择汇总月份"},null,8,["modelValue"])]),_:1}),e(s,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(_,{modelValue:t(p).elderName,"onUpdate:modelValue":a[12]||(a[12]=l=>t(p).elderName=l),placeholder:"请输入老人姓名",clearable:"",style:{width:"200px"},onKeyup:C(A,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"楼栋名称",prop:"buildingName"},{default:o(()=>[e(D,{modelValue:t(p).buildingName,"onUpdate:modelValue":a[13]||(a[13]=l=>t(p).buildingName=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:ke,onClear:Le},{default:o(()=>[(h(!0),x(P,null,L(G.value,l=>(h(),V(U,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"楼栋层号",prop:"floorNumber"},{default:o(()=>[e(D,{modelValue:t(p).floorNumber,"onUpdate:modelValue":a[14]||(a[14]=l=>t(p).floorNumber=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onClear:Ue,onChange:Ce},{default:o(()=>[(h(!0),x(P,null,L(j.value,l=>(h(),V(U,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"房  间  号",prop:"roomNumber"},{default:o(()=>[e(_,{modelValue:t(p).roomNumber,"onUpdate:modelValue":a[15]||(a[15]=l=>t(p).roomNumber=l),placeholder:"请输入房间号",clearable:"",style:{width:"200px"},onKeyup:C(A,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"床  位  号",prop:"bedNumber"},{default:o(()=>[e(_,{modelValue:t(p).bedNumber,"onUpdate:modelValue":a[16]||(a[16]=l=>t(p).bedNumber=l),placeholder:"请输入床位号",clearable:"",style:{width:"200px"},onKeyup:C(A,["enter"])},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"]),[[J,se.value]]),e(w,{gutter:10,class:"mb8",justify:"end",style:{"margin-right":"3px"}},{default:o(()=>[e(I,{type:"primary",icon:"Search",onClick:A},{default:o(()=>[T("查询")]),_:1}),e(I,{icon:"Refresh",onClick:Se},{default:o(()=>[T("重置")]),_:1})]),_:1}),R((h(),V(te,{data:ue.value,border:"",stripe:""},{default:o(()=>[e(i,{type:"index",width:"55",label:"序号",align:"center"}),e(i,{label:"服药日期",align:"center",prop:"medicationDate"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationDate,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"老人姓名",align:"center",prop:"elderName"}),e(i,{label:"老人编码",align:"center",prop:"elderCode"}),e(i,{label:"年龄",align:"center",prop:"age"}),e(i,{label:"性别",align:"center",prop:"age"},{default:o(l=>[e(oe,{options:t(W),value:l.row.gender},null,8,["options","value"])]),_:1}),e(i,{label:"楼层号",align:"center",prop:"floorNumber"}),e(i,{label:"房间号",align:"center",prop:"roomNumber"}),e(i,{label:"床位号",align:"center",prop:"bedNumber"},{default:o(l=>[r("span",null,b(l.row.roomNumber+"-"+l.row.bedNumber),1)]),_:1}),e(i,{label:"楼栋名称",align:"center",prop:"buildingName"}),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},{default:o(l=>[e(I,{link:"",type:"primary",icon:"Edit",onClick:ze=>Me(l.row)},{default:o(()=>[T("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,Y.value]]),R(e(he,{total:ee.value,page:t(p).pageNum,"onUpdate:page":a[17]||(a[17]=l=>t(p).pageNum=l),limit:t(p).pageSize,"onUpdate:limit":a[18]||(a[18]=l=>t(p).pageSize=l),onPagination:ae},null,8,["total","page","limit"]),[[J,ee.value>0]])]),_:1})]),_:1},8,["modelValue"]),e(_e,{title:le.value,modelValue:S.value,"onUpdate:modelValue":a[31]||(a[31]=l=>S.value=l),width:"1100px","append-to-body":""},{footer:o(()=>[r("div",Cl,[q("",!0),e(I,{onClick:De},{default:o(()=>[T("返 回")]),_:1})])]),default:o(()=>[e(H,{ref:"visitRecordRef",model:t(d),rules:t(ce),"label-width":"90px"},{default:o(()=>[r("div",tl,[ol,e(w,null,{default:o(()=>[e(g,{span:20},{default:o(()=>[e(w,{gutter:20},{default:o(()=>[r("table",dl,[r("tr",null,[nl,r("th",rl,[e(_,{modelValue:t(d).elderName,"onUpdate:modelValue":a[20]||(a[20]=l=>t(d).elderName=l),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},disabled:N.value},null,8,["modelValue","disabled"])]),ul,r("th",il,b(t(d).elderCode),1),sl,r("th",ml,[e(oe,{options:t(W),value:t(d).gender},null,8,["options","value"])])]),r("tr",null,[pl,r("th",cl,b(t(d).roomNumber+"-"+t(d).bedNumber),1),bl,r("th",fl,b(t(d).roomNumber),1),hl,r("th",vl,b(t(d).age),1)]),r("tr",null,[_l,r("th",gl,b(t(d).buildingName),1),Nl,r("th",yl,b(t(d).floorNumber),1),Vl,r("th",wl,b(t(d).nursingLevel),1)]),r("tr",null,[Il,r("th",Tl,b(t(d).checkInDate),1)])])]),_:1})]),_:1}),e(g,{span:4},{default:o(()=>[t(d).avatar?(h(),V(Ke,{key:0,shape:"square",size:140,fit:"fill",src:t(d).avatar},null,8,["src"])):q("",!0)]),_:1})]),_:1})]),r("div",kl,[xl,e(w,null,{default:o(()=>[e(g,{span:8},{default:o(()=>[e(s,{label:"服药日期",prop:"medicationDate"},{default:o(()=>[e(n,{clearable:"",modelValue:t(d).medicationDate,"onUpdate:modelValue":a[21]||(a[21]=l=>t(d).medicationDate=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择服药日期",disabled:N.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"服药状态",prop:"status"},{default:o(()=>[e(D,{modelValue:t(d).status,"onUpdate:modelValue":a[22]||(a[22]=l=>t(d).status=l),placeholder:"请选择服药状态",clearable:"",disabled:N.value},{default:o(()=>[(h(!0),x(P,null,L(t(B),l=>(h(),V(U,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"药品编号",prop:"medicineId"},{default:o(()=>[e(_,{modelValue:t(d).medicineId,"onUpdate:modelValue":a[23]||(a[23]=l=>t(d).medicineId=l),placeholder:"请输入药品编号",disabled:N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"药品名称",prop:"medicineName"},{default:o(()=>[e(_,{modelValue:t(d).medicineName,"onUpdate:modelValue":a[24]||(a[24]=l=>t(d).medicineName=l),placeholder:"请输入药品名称",disabled:N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"时       段",prop:"timePeriod"},{default:o(()=>[e(D,{modelValue:t(d).timePeriod,"onUpdate:modelValue":a[25]||(a[25]=l=>t(d).timePeriod=l),disabled:N.value,placeholder:"请选择",clearable:""},{default:o(()=>[(h(!0),x(P,null,L(t(F),l=>(h(),V(U,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"服药剂量",prop:"dosage"},{default:o(()=>[e(_,{modelValue:t(d).dosage,"onUpdate:modelValue":a[26]||(a[26]=l=>t(d).dosage=l),placeholder:"请输入服药剂量",disabled:N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"服药时间",prop:"medicationTime"},{default:o(()=>[e(_,{modelValue:t(d).medicationTime,"onUpdate:modelValue":a[27]||(a[27]=l=>t(d).medicationTime=l),placeholder:"请输入服药时间",disabled:N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"送  药  人",prop:"deliverer"},{default:o(()=>[e(_,{modelValue:t(d).deliverer,"onUpdate:modelValue":a[28]||(a[28]=l=>t(d).deliverer=l),placeholder:"请输入送药人",disabled:N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:o(()=>[e(s,{label:"监  督  人",prop:"supervisor"},{default:o(()=>[e(_,{modelValue:t(d).supervisor,"onUpdate:modelValue":a[29]||(a[29]=l=>t(d).supervisor=l),placeholder:"请输入监督人",disabled:N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:24},{default:o(()=>[e(s,{label:"服用反应",prop:"reaction"},{default:o(()=>[e(_,{modelValue:t(d).reaction,"onUpdate:modelValue":a[30]||(a[30]=l=>t(d).reaction=l),type:"textarea",disabled:N.value,placeholder:"请输入内容"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(_e,{title:le.value,modelValue:E.value,"onUpdate:modelValue":a[32]||(a[32]=l=>E.value=l),width:"65%","append-to-body":""},{footer:o(()=>[r("div",Gl,[e(I,{type:"primary",onClick:$e},{default:o(()=>[T("打 印")]),_:1}),e(I,{onClick:Ye},{default:o(()=>[T("返 回")]),_:1})])]),default:o(()=>[e(H,{ref:"visitRecordRef",model:t(d),rules:t(ce),"label-width":"140px"},{default:o(()=>[r("div",Ul,[Dl,e(w,null,{default:o(()=>[e(g,{span:24},{default:o(()=>[e(w,{gutter:24},{default:o(()=>[r("table",Rl,[r("tr",null,[Pl,r("th",Ll,b(t(d).elderName),1),Sl,r("th",Bl,b(t(d).elderCode),1),Yl,r("th",$l,b(t(d).age),1),ql,r("th",Ml,[e(oe,{options:t(W),value:t(d).gender},null,8,["options","value"])])]),r("tr",null,[Fl,r("th",Kl,b(t(d).buildingName),1),zl,r("th",jl,b(t(d).floorNumber),1),El,r("th",Ql,b(t(d).roomNumber),1),Al,r("th",Hl,b(t(d).bedNumber),1)])])]),_:1})]),_:1})]),_:1})]),r("div",Ol,[Jl,r("div",Wl,b(u.parseTime(pe.value,"{y}年{m}月")),1),e(w,null,{default:o(()=>[R((h(),V(te,{data:X.value,border:"",stripe:""},{default:o(()=>[e(i,{type:"index",width:"55",label:"序号",align:"center"}),e(i,{label:"服药日期",align:"center",prop:"medicationDate",width:"100"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationDate,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"服药时间",align:"center",prop:"medicationTime",width:"80"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationTime,"{h}:{m}")),1)]),_:1}),e(i,{label:"药品编号",align:"center",prop:"medicineId"}),e(i,{label:"药品名称",align:"center",prop:"medicineName"}),e(i,{label:"服药剂量",align:"center",prop:"dosage"}),e(i,{label:"时段",align:"center",prop:"timePeriod",width:"60"},{default:o(l=>[e(O,{options:t(F),value:l.row.timePeriod},null,8,["options","value"])]),_:1}),e(i,{label:"服药状态",align:"center",prop:"status",width:"80"},{default:o(l=>[e(O,{options:t(B),value:l.row.status},null,8,["options","value"])]),_:1}),e(i,{label:"类型",align:"center",prop:"type",width:"80"}),e(i,{label:"送药人",align:"center",prop:"deliverer",width:"100"}),e(i,{label:"监督人",align:"center",prop:"supervisor",width:"100"}),e(i,{label:"服用反应",align:"center",prop:"reaction"})]),_:1},8,["data"])),[[de,Y.value]])]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),na=je(Zl,[["__scopeId","data-v-3ba640f8"]]);export{na as default};
