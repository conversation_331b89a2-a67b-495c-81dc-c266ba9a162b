import{X as F,_ as Ee,B as Qe,d as Ae,r as c,C as He,N as Oe,e as v,I as Je,c as C,o as h,f as e,h as o,J as P,l as t,m as U,K as L,L as S,j as w,O as W,n as k,k as q,i as r,t as b,v as We,x as Xe}from"./index-B0qHf98Y.js";import{g as Ne}from"./telderinfo-BSpoeVyZ.js";import{l as Ge}from"./tLiveRoom-DmSXfHxo.js";import{g as re,a as ye}from"./roommanage-DBG5TiIR.js";function Ve(g){return F({url:"/vhf/medication/useRecord/list",method:"get",params:g})}function Ze(g){return F({url:"/vhf/medication/useRecord/listMonth",method:"get",params:g})}function el(g){return F({url:"/vhf/medication/useRecord/"+g,method:"get"})}function ll(g){return F({url:"/vhf/medication/useRecord",method:"post",data:g})}function al(g){return F({url:"/vhf/medication/useRecord",method:"put",data:g})}const f=g=>(We("data-v-37d66b5a"),g=g(),Xe(),g),tl={class:"app-container"},ol={class:"section"},dl=f(()=>r("div",{class:"section-title"},"老人信息",-1)),nl={class:"tbcss"},rl=f(()=>r("th",{class:"tbTr"},"老人姓名",-1)),ul={class:"tbTrVal"},il=f(()=>r("th",{class:"tbTr"},"老人编号",-1)),sl={class:"tbTrVal"},ml=f(()=>r("th",{class:"tbTr"},"性别",-1)),pl={class:"tbTrVal"},cl=f(()=>r("th",{class:"tbTr"},"床位编号",-1)),bl={class:"tbTrVal"},fl=f(()=>r("th",{class:"tbTr"},"房间信息",-1)),hl={class:"tbTrVal"},vl=f(()=>r("th",{class:"tbTr"},"年龄",-1)),_l={class:"tbTrVal"},gl=f(()=>r("th",{class:"tbTr"},"楼栋信息",-1)),Nl={class:"tbTrVal"},yl=f(()=>r("th",{class:"tbTr"},"楼层信息",-1)),Vl={class:"tbTrVal"},wl=f(()=>r("th",{class:"tbTr"},"护理等级",-1)),Il={class:"tbTrVal"},Tl=f(()=>r("th",{class:"tbTr"},"入住时间",-1)),kl={class:"tbTrVal"},xl={class:"section"},Cl=f(()=>r("div",{class:"section-title"},"服药信息",-1)),Ul={class:"dialog-footer"},Dl={class:"section"},Rl=f(()=>r("div",{class:"section-title"},"老人信息",-1)),Pl={class:"tbcss"},Ll=f(()=>r("th",{class:"tbTr"},"老人姓名",-1)),Sl={class:"tbTrVal"},Bl=f(()=>r("th",{class:"tbTr"},"老人编号",-1)),Yl={class:"tbTrVal"},$l=f(()=>r("th",{class:"tbTr"},"年       龄",-1)),Ml={class:"tbTrVal"},ql=f(()=>r("th",{class:"tbTr"},"性       别",-1)),Fl={class:"tbTrVal"},Kl=f(()=>r("th",{class:"tbTr"},"楼栋信息",-1)),zl={class:"tbTrVal"},jl=f(()=>r("th",{class:"tbTr"},"楼层信息",-1)),El={class:"tbTrVal"},Ql=f(()=>r("th",{class:"tbTr"},"房间信息",-1)),Al={class:"tbTrVal"},Hl=f(()=>r("th",{class:"tbTr"},"床位编号",-1)),Ol={class:"tbTrVal"},Jl={class:"section"},Wl=f(()=>r("div",{class:"section-title"},"服药明细记录",-1)),Xl={style:{"font-weight":"600","margin-bottom":"5px"}},Gl={class:"dialog-footer"},Zl=Qe({name:"UseRecord"}),ea=Object.assign(Zl,{setup(g){const{proxy:V}=Ae(),{medication_status:Y,sys_user_sex:X,medication_period:K}=V.useDict("medication_status","sys_user_sex","medication_period"),z=c([]),j=c([]),we=c([]),G=c([]),E=c([]);c([]);const ue=c([]),ie=c([]),Z=c([]),B=c(!1),Q=c(!1),$=c(!0),se=c(!0),me=c(!0),Ie=c([]);c(!0),c(!0);const ee=c(0),le=c(0),ae=c(""),y=c(!1),pe=c("useRecord"),ce=c(""),Te=He({form:{},queryParams:{pageNum:1,pageSize:10,elderId:null,elderName:null,elderCode:null,buildingId:null,buildingName:null,floorId:null,floorNumber:null,roomId:null,roomNumber:null,bedId:null,bedNumber:null,medicationDate:null,status:null,medicineId:null,medicineName:null,timePeriod:null,dosage:null,medicationTime:null,deliverer:null,supervisor:null,reaction:null,type:null,recorder:null},queryParams2:{pageNum:1,pageSize:10},queryParams3:{pageNum:1,pageSize:1e5},rules:{}}),{queryParams:m,queryParams2:p,queryParams3:A,form:d,rules:be}=Oe(Te);function M(){fe(),re().then(u=>{z.value=u.rows||[]})}function ke(u){var n;m.value.buildingName=u;const a=z.value.filter(s=>s.buildingName==u);ye((n=a[0])==null?void 0:n.id).then(s=>{j.value=s.rows})}function xe(u){p.value.buildingName=u;const a=G.value.filter(n=>n.buildingName==u);ye(a[0].id).then(n=>{E.value=n.rows})}function Ce(u){m.value.floorNumber=u;const a=j.value.filter(n=>n.floorName==u);Ge({floorId:a[0].id}).then(n=>{we.value=n.rows})}function Ue(u){var n;p.value.floorNumber=u,console.log(p.value.floorNumber,"handleFloorChange2");const a=E.value.filter(s=>s.floorName==u);p.value.floorId=(n=a[0])==null?void 0:n.id,console.log(a,p.value.floorId,"floorId")}function De(){console.log("chearfloor2"),p.value.floorNumber=null,p.value.floorId=null,console.log(p.value,"chearfloor22222")}function fe(){$.value=!0,Ve(m.value).then(u=>{ue.value=u.rows,ee.value=u.total,$.value=!1})}function te(){se.value=!0,Ze(p.value).then(u=>{ie.value=u.rows,le.value=u.total,se.value=!1})}function Re(){B.value=!1,he()}function he(){d.value={id:null,elderId:null,elderName:null,elderCode:null,buildingId:null,buildingName:null,floorId:null,floorNumber:null,roomId:null,roomNumber:null,bedId:null,bedNumber:null,medicationDate:null,status:null,medicineId:null,medicineName:null,timePeriod:null,dosage:null,medicationTime:null,deliverer:null,supervisor:null,reaction:null,type:null,recorder:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null},V.resetForm("useRecordRef")}function x(){m.value.pageNum=1,M()}function H(){p.value.pageNum=1,te()}function Pe(){V.resetForm("queryRef"),x()}function Le(){console.log("清空楼栋"),j.value=[],m.value.floorNumber=null}function Se(){console.log("清空楼栋"),E.value=[],p.value.floorNumber=null}function Be(){V.resetForm("queryRef2"),qyery2.value.floorId=null,x()}function Ye(u){u=="useRecord"?(fe(),re().then(a=>{z.value=a.rows||[]})):u=="useRecordTotal"&&(te(),re().then(a=>{G.value=a.rows||[]}))}function $e(){Q.value=!1}function Me(){var u=Z.value.map(n=>({medicationDate:V.parseTime(n.medicationDate,"{y}-{m}-{d}"),medicationTime:V.parseTime(n.medicationTime,"{h}:{m}"),timePeriod:n.timePeriod,medicineId:n.medicineId,medicineName:n.medicineName,dosage:n.dosage,timePeriod:V.selectDictLabel(K.value,n.timePeriod),status:V.selectDictLabel(Y.value,n.status),type:n.type,deliverer:n.deliverer,supervisor:n.supervisor,reaction:n.reaction}));console.log(u,"data");const a=window.open("","_blank");a.document.write(`
            <html>
                <head>
                    <title>服药明细记录打印</title>
                <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                        }

                        h1 {
                            text-align: center;
                            margin-bottom: 20px;
                        }

                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }

                        th,
                        td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                            color: #666;
                        }

                        .print-date {
                            text-align: right;
                            margin-bottom: 20px;
                        }
                </style>
                </head>
                <body>
                    <h1>服药明细记录表</h1>
                    <div class="print-date">打印日期: ${new Date().toLocaleDateString()}</div>
                    <table>
                        <thead>
                            <tr>
                                <th style="width:55px">序号</th>
                                <th style="width:8px">服药日期</th>
                                <th style="width:80px">服药时间</th>
                                <th style="width:80px">药品编号</th>
                                <th style="width:140px">药品名称</th>
                                <th style="width:120px">服药剂量</th>
                                <th style="width:80px">时段</th>
                                <th style="width:80px">服药状态</th>
                                <th style="width:80px">类型</th>
                                <th style="width:80px">送药人</th>
                                <th style="width:80px">监督人</th>
                                <th style="width:80px">服用反应</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${u.map((n,s)=>`
                                <tr>
                                    <td>${s+1}</td>
                                    <td>${n.medicationDate}</td>
                                    <td>${n.medicationTime}</td>
                                    <td>${n.medicineId?n.medicineId:"-"}</td>
                                    <td>${n.medicineName?n.medicineName:"-"}</td>
                                    <td>${n.dosage?n.dosage:"-"}</td>
                                    <td>${n.timePeriod?n.timePeriod:"-"}</td>
                                    <td>${n.status?n.status:"-"}</td>
                                    <td>${n.type?n.type:"-"}</td>
                                    <td>${n.deliverer?n.deliverer:"-"}</td>
                                    <td>${n.supervisor?n.supervisor:"-"}</td>
                                    <td>${n.reaction?n.reaction:"-"}</td>
                                </tr>
                            `).join("")}
                        </tbody>
                    </table>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    <\/script>
                </body>
            </html>
        `),a.document.close()}function qe(u){he();const a=u.id||Ie.value;y.value=!0,el(a).then(n=>{d.value=n.data,B.value=!0,ae.value="查看"}),Ne(u.elderId).then(n=>{d.value.elderName=n.data.elderInfo.elderName,d.value.elderCode=n.data.elderInfo.elderCode,d.value.gender=n.data.elderInfo.gender,d.value.bedNumber=n.data.checkIn.bedNumber,d.value.roomNumber=n.data.checkIn.roomNumber,d.value.age=n.data.elderInfo.age,d.value.buildingName=n.data.checkIn.buildingName,d.value.floorNumber=n.data.checkIn.floorName,d.value.nursingLevel=n.data.checkIn.nursingLevel,d.value.checkInDate=n.data.checkIn.checkInDate,d.value.avatar=n.data.elderInfo.avatar})}function Fe(u){console.log(u,"rows"),Q.value=!0,ce.value=u.medicationDate?u.medicationDate.substring(0,7):"",Ne(u.elderId).then(a=>{d.value.elderName=a.data.elderInfo.elderName,d.value.elderCode=a.data.elderInfo.elderCode,d.value.gender=a.data.elderInfo.gender,d.value.bedNumber=a.data.checkIn.bedNumber,d.value.roomNumber=a.data.checkIn.roomNumber,d.value.age=a.data.elderInfo.age,d.value.buildingName=a.data.checkIn.buildingName,d.value.floorNumber=a.data.checkIn.floorName,d.value.nursingLevel=a.data.checkIn.nursingLevel,d.value.checkInDate=a.data.checkIn.checkInDate,d.value.avatar=a.data.elderInfo.avatar}),A.value.elderId=u.elderId,A.value.queryMonth=u.medicationDate,console.log(A.value,"111"),Ve(A.value).then(a=>{console.log(a,"response"),Z.value=a.rows})}function la(){V.$refs.useRecordRef.validate(u=>{u&&(d.value.id!=null?al(d.value).then(a=>{V.$modal.msgSuccess("修改成功"),B.value=!1,M()}):ll(d.value).then(a=>{V.$modal.msgSuccess("新增成功"),B.value=!1,M()}))})}return M(),(u,a)=>{const n=v("el-date-picker"),s=v("el-form-item"),_=v("el-input"),D=v("el-option"),R=v("el-select"),I=v("el-row"),O=v("el-form"),T=v("el-button"),i=v("el-table-column"),J=v("dict-tag"),oe=v("el-table"),ve=v("pagination"),_e=v("el-tab-pane"),de=v("dict-tag-span"),Ke=v("el-tabs"),N=v("el-col"),ze=v("el-avatar"),ge=v("el-dialog"),ne=Je("loading");return h(),C("div",tl,[e(Ke,{modelValue:pe.value,"onUpdate:modelValue":a[19]||(a[19]=l=>pe.value=l),onTabChange:Ye,style:{"padding-right":"10px"}},{default:o(()=>[e(_e,{label:"老人服用记录",name:"useRecord"},{default:o(()=>[P(e(O,{model:t(m),ref:"queryRef",inline:!0,"label-width":"88px"},{default:o(()=>[e(I,{gutter:10},{default:o(()=>[e(s,{label:"服药日期",prop:"medicationDate"},{default:o(()=>[e(n,{clearable:"",modelValue:t(m).medicationDate,"onUpdate:modelValue":a[0]||(a[0]=l=>t(m).medicationDate=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"200px"},placeholder:"请选择服药日期"},null,8,["modelValue"])]),_:1}),e(s,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(_,{modelValue:t(m).elderName,"onUpdate:modelValue":a[1]||(a[1]=l=>t(m).elderName=l),placeholder:"请输入老人姓名",clearable:"",style:{width:"200px"},onKeyup:U(x,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"楼栋名称",prop:"buildingName"},{default:o(()=>[e(R,{modelValue:t(m).buildingName,"onUpdate:modelValue":a[2]||(a[2]=l=>t(m).buildingName=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:ke,onClear:Le},{default:o(()=>[(h(!0),C(L,null,S(z.value,l=>(h(),w(D,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"楼栋层号",prop:"floorNumber"},{default:o(()=>[e(R,{modelValue:t(m).floorNumber,"onUpdate:modelValue":a[3]||(a[3]=l=>t(m).floorNumber=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:Ce},{default:o(()=>[(h(!0),C(L,null,S(j.value,l=>(h(),w(D,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"房  间  号",prop:"roomNumber"},{default:o(()=>[e(_,{modelValue:t(m).roomNumber,"onUpdate:modelValue":a[4]||(a[4]=l=>t(m).roomNumber=l),placeholder:"请输入房间号",clearable:"",style:{width:"200px"},onKeyup:U(x,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"药品名称",prop:"medicineName"},{default:o(()=>[e(_,{modelValue:t(m).medicineName,"onUpdate:modelValue":a[5]||(a[5]=l=>t(m).medicineName=l),placeholder:"请输入药品名称",clearable:"",style:{width:"200px"},onKeyup:U(x,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"送  药  人",prop:"deliverer"},{default:o(()=>[e(_,{modelValue:t(m).deliverer,"onUpdate:modelValue":a[6]||(a[6]=l=>t(m).deliverer=l),placeholder:"请输入送药人",clearable:"",style:{width:"200px"},onKeyup:U(x,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"监  督  人",prop:"supervisor"},{default:o(()=>[e(_,{modelValue:t(m).supervisor,"onUpdate:modelValue":a[7]||(a[7]=l=>t(m).supervisor=l),placeholder:"请输入监督人",clearable:"",style:{width:"200px"},onKeyup:U(x,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"服药状态",prop:"status"},{default:o(()=>[e(R,{modelValue:t(m).status,"onUpdate:modelValue":a[8]||(a[8]=l=>t(m).status=l),placeholder:"请选择服药状态",clearable:"",style:{width:"200px"}},{default:o(()=>[(h(!0),C(L,null,S(t(Y),l=>(h(),w(D,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"]),[[W,me.value]]),e(I,{gutter:10,class:"mb8",justify:"end",style:{"margin-right":"3px"}},{default:o(()=>[e(T,{type:"primary",icon:"Search",onClick:x},{default:o(()=>[k("查询")]),_:1}),e(T,{icon:"Refresh",onClick:Pe},{default:o(()=>[k("重置")]),_:1})]),_:1}),P((h(),w(oe,{data:ue.value,border:"",stripe:""},{default:o(()=>[e(i,{type:"index",width:"55",label:"序号",align:"center"}),e(i,{label:"服药日期",align:"center",prop:"medicationDate",width:"120"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationDate,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"老人姓名",align:"center",prop:"elderName",width:"120"}),e(i,{label:"楼层信息",align:"center",prop:"floorNumber",width:"100"}),e(i,{label:"房间号",align:"center",prop:"roomNumber",width:"100"}),e(i,{label:"床位号",align:"center",prop:"bedNumber",width:"100"},{default:o(l=>[r("span",null,b(l.row.roomNumber+"-"+l.row.bedNumber),1)]),_:1}),e(i,{label:"楼栋名称",align:"center",prop:"buildingName",width:"100"}),e(i,{label:"药品编号",align:"center",prop:"medicineId",width:"140"}),e(i,{label:"药品名称",align:"center",prop:"medicineName",width:"160"}),e(i,{label:"时段",align:"center",prop:"timePeriod",width:"60"},{default:o(l=>[e(J,{options:t(K),value:l.row.timePeriod},null,8,["options","value"])]),_:1}),e(i,{label:"服药剂量",align:"center",prop:"dosage",width:"160"}),e(i,{label:"服药时间",align:"center",prop:"timePeriod",width:"100"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationTime,"{h}:{m}")),1)]),_:1}),e(i,{label:"服药状态",align:"center",prop:"status",width:"100"},{default:o(l=>[e(J,{options:t(Y),value:l.row.status},null,8,["options","value"])]),_:1}),e(i,{label:"送药人",align:"center",prop:"deliverer",width:"100"}),e(i,{label:"监督人",align:"center",prop:"supervisor",width:"100"}),q("",!0),e(i,{label:"类型",align:"center",prop:"type",width:"100"}),q("",!0),q("",!0),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},{default:o(l=>[e(T,{link:"",type:"primary",icon:"Edit",onClick:je=>qe(l.row)},{default:o(()=>[k("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ne,$.value]]),P(e(ve,{total:ee.value,page:t(m).pageNum,"onUpdate:page":a[9]||(a[9]=l=>t(m).pageNum=l),limit:t(m).pageSize,"onUpdate:limit":a[10]||(a[10]=l=>t(m).pageSize=l),onPagination:M},null,8,["total","page","limit"]),[[W,ee.value>0]])]),_:1}),e(_e,{label:"服药汇总记录",name:"useRecordTotal"},{default:o(()=>[P(e(O,{model:t(p),ref:"queryRef2",inline:!0,"label-width":"88px"},{default:o(()=>[e(I,{gutter:10},{default:o(()=>[e(s,{label:"汇总月份",prop:"queryMonth"},{default:o(()=>[e(n,{clearable:"",modelValue:t(p).queryMonth,"onUpdate:modelValue":a[11]||(a[11]=l=>t(p).queryMonth=l),type:"month","value-format":"YYYY-MM",format:"YYYY-MM",style:{width:"200px"},placeholder:"请选择汇总月份"},null,8,["modelValue"])]),_:1}),e(s,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(_,{modelValue:t(p).elderName,"onUpdate:modelValue":a[12]||(a[12]=l=>t(p).elderName=l),placeholder:"请输入老人姓名",clearable:"",style:{width:"200px"},onKeyup:U(H,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"楼栋名称",prop:"buildingName"},{default:o(()=>[e(R,{modelValue:t(p).buildingName,"onUpdate:modelValue":a[13]||(a[13]=l=>t(p).buildingName=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onChange:xe,onClear:Se},{default:o(()=>[(h(!0),C(L,null,S(G.value,l=>(h(),w(D,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"楼栋层号",prop:"floorNumber"},{default:o(()=>[e(R,{modelValue:t(p).floorNumber,"onUpdate:modelValue":a[14]||(a[14]=l=>t(p).floorNumber=l),style:{width:"200px"},placeholder:"请选择",clearable:"",onClear:De,onChange:Ue},{default:o(()=>[(h(!0),C(L,null,S(E.value,l=>(h(),w(D,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"房  间  号",prop:"roomNumber"},{default:o(()=>[e(_,{modelValue:t(p).roomNumber,"onUpdate:modelValue":a[15]||(a[15]=l=>t(p).roomNumber=l),placeholder:"请输入房间号",clearable:"",style:{width:"200px"},onKeyup:U(H,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"床  位  号",prop:"bedNumber"},{default:o(()=>[e(_,{modelValue:t(p).bedNumber,"onUpdate:modelValue":a[16]||(a[16]=l=>t(p).bedNumber=l),placeholder:"请输入床位号",clearable:"",style:{width:"200px"},onKeyup:U(H,["enter"])},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"]),[[W,me.value]]),e(I,{gutter:10,class:"mb8",justify:"end",style:{"margin-right":"3px"}},{default:o(()=>[e(T,{type:"primary",icon:"Search",onClick:H},{default:o(()=>[k("查询")]),_:1}),e(T,{icon:"Refresh",onClick:Be},{default:o(()=>[k("重置")]),_:1})]),_:1}),P((h(),w(oe,{data:ie.value,border:"",stripe:""},{default:o(()=>[e(i,{type:"index",width:"55",label:"序号",align:"center"}),e(i,{label:"服药日期",align:"center",prop:"medicationDate"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationDate,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"老人姓名",align:"center",prop:"elderName"}),e(i,{label:"老人编码",align:"center",prop:"elderCode"}),e(i,{label:"年龄",align:"center",prop:"age"}),e(i,{label:"性别",align:"center",prop:"age"},{default:o(l=>[e(de,{options:t(X),value:l.row.gender},null,8,["options","value"])]),_:1}),e(i,{label:"楼层号",align:"center",prop:"floorNumber"}),e(i,{label:"房间号",align:"center",prop:"roomNumber"}),e(i,{label:"床位号",align:"center",prop:"bedNumber"},{default:o(l=>[r("span",null,b(l.row.roomNumber+"-"+l.row.bedNumber),1)]),_:1}),e(i,{label:"楼栋名称",align:"center",prop:"buildingName"}),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},{default:o(l=>[e(T,{link:"",type:"primary",icon:"Edit",onClick:je=>Fe(l.row)},{default:o(()=>[k("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ne,$.value]]),P(e(ve,{total:le.value,page:t(p).pageNum,"onUpdate:page":a[17]||(a[17]=l=>t(p).pageNum=l),limit:t(p).pageSize,"onUpdate:limit":a[18]||(a[18]=l=>t(p).pageSize=l),onPagination:te},null,8,["total","page","limit"]),[[W,le.value>0]])]),_:1})]),_:1},8,["modelValue"]),e(ge,{title:ae.value,modelValue:B.value,"onUpdate:modelValue":a[31]||(a[31]=l=>B.value=l),width:"1100px","append-to-body":""},{footer:o(()=>[r("div",Ul,[q("",!0),e(T,{onClick:Re},{default:o(()=>[k("返 回")]),_:1})])]),default:o(()=>[e(O,{ref:"visitRecordRef",model:t(d),rules:t(be),"label-width":"90px"},{default:o(()=>[r("div",ol,[dl,e(I,null,{default:o(()=>[e(N,{span:20},{default:o(()=>[e(I,{gutter:20},{default:o(()=>[r("table",nl,[r("tr",null,[rl,r("th",ul,[e(_,{modelValue:t(d).elderName,"onUpdate:modelValue":a[20]||(a[20]=l=>t(d).elderName=l),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},disabled:y.value},null,8,["modelValue","disabled"])]),il,r("th",sl,b(t(d).elderCode),1),ml,r("th",pl,[e(de,{options:t(X),value:t(d).gender},null,8,["options","value"])])]),r("tr",null,[cl,r("th",bl,b(t(d).roomNumber+"-"+t(d).bedNumber),1),fl,r("th",hl,b(t(d).roomNumber),1),vl,r("th",_l,b(t(d).age),1)]),r("tr",null,[gl,r("th",Nl,b(t(d).buildingName),1),yl,r("th",Vl,b(t(d).floorNumber),1),wl,r("th",Il,b(t(d).nursingLevel),1)]),r("tr",null,[Tl,r("th",kl,b(t(d).checkInDate),1)])])]),_:1})]),_:1}),e(N,{span:4},{default:o(()=>[t(d).avatar?(h(),w(ze,{key:0,shape:"square",size:140,fit:"fill",src:t(d).avatar},null,8,["src"])):q("",!0)]),_:1})]),_:1})]),r("div",xl,[Cl,e(I,null,{default:o(()=>[e(N,{span:8},{default:o(()=>[e(s,{label:"服药日期",prop:"medicationDate"},{default:o(()=>[e(n,{clearable:"",modelValue:t(d).medicationDate,"onUpdate:modelValue":a[21]||(a[21]=l=>t(d).medicationDate=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择服药日期",disabled:y.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"服药状态",prop:"status"},{default:o(()=>[e(R,{modelValue:t(d).status,"onUpdate:modelValue":a[22]||(a[22]=l=>t(d).status=l),placeholder:"请选择服药状态",clearable:"",disabled:y.value},{default:o(()=>[(h(!0),C(L,null,S(t(Y),l=>(h(),w(D,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"药品编号",prop:"medicineId"},{default:o(()=>[e(_,{modelValue:t(d).medicineId,"onUpdate:modelValue":a[23]||(a[23]=l=>t(d).medicineId=l),placeholder:"请输入药品编号",disabled:y.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"药品名称",prop:"medicineName"},{default:o(()=>[e(_,{modelValue:t(d).medicineName,"onUpdate:modelValue":a[24]||(a[24]=l=>t(d).medicineName=l),placeholder:"请输入药品名称",disabled:y.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"时       段",prop:"timePeriod"},{default:o(()=>[e(R,{modelValue:t(d).timePeriod,"onUpdate:modelValue":a[25]||(a[25]=l=>t(d).timePeriod=l),disabled:y.value,placeholder:"请选择",clearable:""},{default:o(()=>[(h(!0),C(L,null,S(t(K),l=>(h(),w(D,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"服药剂量",prop:"dosage"},{default:o(()=>[e(_,{modelValue:t(d).dosage,"onUpdate:modelValue":a[26]||(a[26]=l=>t(d).dosage=l),placeholder:"请输入服药剂量",disabled:y.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"服药时间",prop:"medicationTime"},{default:o(()=>[e(_,{modelValue:t(d).medicationTime,"onUpdate:modelValue":a[27]||(a[27]=l=>t(d).medicationTime=l),placeholder:"请输入服药时间",disabled:y.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"送  药  人",prop:"deliverer"},{default:o(()=>[e(_,{modelValue:t(d).deliverer,"onUpdate:modelValue":a[28]||(a[28]=l=>t(d).deliverer=l),placeholder:"请输入送药人",disabled:y.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:8},{default:o(()=>[e(s,{label:"监  督  人",prop:"supervisor"},{default:o(()=>[e(_,{modelValue:t(d).supervisor,"onUpdate:modelValue":a[29]||(a[29]=l=>t(d).supervisor=l),placeholder:"请输入监督人",disabled:y.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(N,{span:24},{default:o(()=>[e(s,{label:"服用反应",prop:"reaction"},{default:o(()=>[e(_,{modelValue:t(d).reaction,"onUpdate:modelValue":a[30]||(a[30]=l=>t(d).reaction=l),type:"textarea",disabled:y.value,placeholder:"请输入内容"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(ge,{title:ae.value,modelValue:Q.value,"onUpdate:modelValue":a[32]||(a[32]=l=>Q.value=l),width:"65%","append-to-body":""},{footer:o(()=>[r("div",Gl,[e(T,{type:"primary",onClick:Me},{default:o(()=>[k("打 印")]),_:1}),e(T,{onClick:$e},{default:o(()=>[k("返 回")]),_:1})])]),default:o(()=>[e(O,{ref:"visitRecordRef",model:t(d),rules:t(be),"label-width":"140px"},{default:o(()=>[r("div",Dl,[Rl,e(I,null,{default:o(()=>[e(N,{span:24},{default:o(()=>[e(I,{gutter:24},{default:o(()=>[r("table",Pl,[r("tr",null,[Ll,r("th",Sl,b(t(d).elderName),1),Bl,r("th",Yl,b(t(d).elderCode),1),$l,r("th",Ml,b(t(d).age),1),ql,r("th",Fl,[e(de,{options:t(X),value:t(d).gender},null,8,["options","value"])])]),r("tr",null,[Kl,r("th",zl,b(t(d).buildingName),1),jl,r("th",El,b(t(d).floorNumber),1),Ql,r("th",Al,b(t(d).roomNumber),1),Hl,r("th",Ol,b(t(d).bedNumber),1)])])]),_:1})]),_:1})]),_:1})]),r("div",Jl,[Wl,r("div",Xl,b(u.parseTime(ce.value,"{y}年{m}月")),1),e(I,null,{default:o(()=>[P((h(),w(oe,{data:Z.value,border:"",stripe:""},{default:o(()=>[e(i,{type:"index",width:"55",label:"序号",align:"center"}),e(i,{label:"服药日期",align:"center",prop:"medicationDate",width:"100"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationDate,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"服药时间",align:"center",prop:"medicationTime",width:"80"},{default:o(l=>[r("span",null,b(u.parseTime(l.row.medicationTime,"{h}:{m}")),1)]),_:1}),e(i,{label:"药品编号",align:"center",prop:"medicineId"}),e(i,{label:"药品名称",align:"center",prop:"medicineName"}),e(i,{label:"服药剂量",align:"center",prop:"dosage"}),e(i,{label:"时段",align:"center",prop:"timePeriod",width:"60"},{default:o(l=>[e(J,{options:t(K),value:l.row.timePeriod},null,8,["options","value"])]),_:1}),e(i,{label:"服药状态",align:"center",prop:"status",width:"80"},{default:o(l=>[e(J,{options:t(Y),value:l.row.status},null,8,["options","value"])]),_:1}),e(i,{label:"类型",align:"center",prop:"type",width:"80"}),e(i,{label:"送药人",align:"center",prop:"deliverer",width:"100"}),e(i,{label:"监督人",align:"center",prop:"supervisor",width:"100"}),e(i,{label:"服用反应",align:"center",prop:"reaction"})]),_:1},8,["data"])),[[ne,$.value]])]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),na=Ee(ea,[["__scopeId","data-v-37d66b5a"]]);export{na as default};
