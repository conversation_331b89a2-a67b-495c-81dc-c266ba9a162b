function c(t){if(t==null||t=="")return"";var e=new Date(t),n=e.getFullYear(),a=e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1,r=e.getDate()<10?"0"+e.getDate():e.getDate(),s=e.getHours()<10?"0"+e.getHours():e.getHours(),i=e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes(),o=e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds();return n+"-"+a+"-"+r+" "+s+":"+i+":"+o}function p(t,e,n){let a,r,s,i,o;const u=function(){const l=+new Date-i;l<e&&l>0?a=setTimeout(u,e-l):(a=null,o=t.apply(s,r),a||(s=r=null))};return function(...l){return s=this,i=+new Date,a||(a=setTimeout(u,e)),o}}function f(t){if(!t&&typeof t!="object")throw new Error("error arguments","deepClone");const e=t.constructor===Array?[]:{};return Object.keys(t).forEach(n=>{t[n]&&typeof t[n]=="object"?e[n]=f(t[n]):e[n]=t[n]}),e}function d(t,e){const n=Object.create(null),a=t.split(",");for(let r=0;r<a.length;r++)n[a[r]]=!0;return e?r=>n[r.toLowerCase()]:r=>n[r]}const g={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function _(t){return t.replace(/( |^)[a-z]/g,e=>e.toUpperCase())}function m(t){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(t)}export{f as a,g as b,p as d,c as f,m as i,d as m,_ as t};
