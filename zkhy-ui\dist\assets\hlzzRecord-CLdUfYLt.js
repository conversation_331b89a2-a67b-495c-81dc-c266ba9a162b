import{_ as z,r as p,e as m,I as D,J as C,l as e,c as M,o as V,f as g,h as u,i as t,t as n,n as b,D as N,aD as R,v as S,x as I}from"./index-B0qHf98Y.js";const s=r=>(S("data-v-74228498"),r=r(),I(),r),B=s(()=>t("h3",{class:"title_record"},"护理组长查房记录",-1)),E={class:"table-style"},T={style:{width:"40%"}},W={style:{"text-align":"center"},colspan:"4"},q=s(()=>t("tr",null,[t("td",{style:{"text-align":"center",width:"40%"}},"检查内容"),t("td",{style:{"text-align":"center",width:"150px"}},"存在问题"),t("td",{style:{"text-align":"center",width:"150px"}},"责任人"),t("td",{style:{"text-align":"center",width:"150px"}},"改进措施"),t("td",{style:{"text-align":"center",width:"150px"}},"反馈")],-1)),A=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1. 三短:头发、胡须、指/趾甲;"),t("p",null,"2.六洁:"),t("p",null,"(1)口腔洁:无残渣、无异味，有与病情相适应的口腔护理次数;"),t("p",null,"(2)头发洁:清洁、整齐、无异味;"),t("p",null,"(3)手足洁:干净;"),t("p",null,"(4)皮肤洁:全身皮肤清洁、无血、尿、便、胶布痕迹，无受压部痕迹，背部及骨突部位无褥疮，有预防措施(因病情不可避免除外);"),t("p",null,"(5)会阴、肛门洁:肛周及尿道口清洁、无血、尿、便迹，目卧床长者每日清洁会阴，留置尿管者保持尿道口干洁，尿管固定通畅。")],-1)),L={style:{"text-align":"center"}},H={style:{"text-align":"center"}},J={style:{"text-align":"center"}},O={style:{"text-align":"center"}},U=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.四无:无压疮、无跌倒/坠床、无烫伤、无噎食/误吸;"),t("p",null,"2.安全防护:"),t("p",null,"(1)长者衣服裤子长短、鞋子大小是否合适。"),t("p",null,"(2)轮椅、助行器刹车是否完好。(3)全护理、半护理长者不能自行打开水"),t("p",null,"(4)插座、插头、电源是否外落(5)有无危险品(如打火机、刀、剪刀、钢丝、铁片等)"),t("p",null,"(3)食品有无腐烂、霉变、药品是否安全放置。"),t("p",null,"(7)约束带使用是否正常，不用的安全放查。"),t("p",null,"(8)剃须刀、水果刀安全管理。(9)床防护栏(扶手)刹车、椅是否完好,(10)马桶、床头铃等性能。"),t("p",null,"(11)微波炉使用安全、地面清洁无水无障碍物"),t("p",null,"(12)假牙维护是否正确")],-1)),Y={style:{"text-align":"center"}},$={style:{"text-align":"center"}},j={style:{"text-align":"center"}},F={style:{"text-align":"center"}},G=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;"),t("p",null,'2.“四周到":饭前洗手，送水、送饭、送便器到床;'),t("p",null,"3.核查文书书写情况是否如实、及时等")],-1)),K={style:{"text-align":"center"}},Q={style:{"text-align":"center"}},X={style:{"text-align":"center"}},Z={style:{"text-align":"center"}},tt=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;"),t("p",null,'2.“四周到":饭前洗手，送水、送饭、送便器到床;'),t("p",null,"3.核查文书书写情况是否如实、及时等")],-1)),et={style:{"text-align":"center"}},lt={style:{"text-align":"center"}},nt={style:{"text-align":"center"}},st={style:{"text-align":"center"}},ot=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"卫生:"),t("p",null,"(1)床单位干洁平整、床上无碎屑、无杂物;床下整洁，无便器、无杂物，只有一双拖鞋，房间无异味。(2)桌面清洁，整齐，碗筷用物不乱放。长者的用物“一用一清洁一消毒”。"),t("p",null,"(3)卫生间用物用具摆放整齐，定时消毒，无臭味，室内无蚊蝇、无蟑螂(4)物品摆放:衣柜、床头柜、桌面是否整齐干净"),t("p",null,"(5)长者衣着整洁干净、无异味、无污渍")],-1)),it={style:{"text-align":"center"}},dt={style:{"text-align":"center"}},ct={style:{"text-align":"center"}},at={style:{"text-align":"center"}},rt=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.消毒隔离:房间按时开窗通风，毛巾便盆、轮椅等是否及时消毒，气垫床是否及时清洁晾晒及维护;"),t("p",null,"2.检查相关文书书写情况。")],-1)),_t={style:{"text-align":"center"}},pt={style:{"text-align":"center"}},ut={style:{"text-align":"center"}},xt={style:{"text-align":"center"}},ht=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.长者食品按有效期长短放置，保证在有效期内及时给长者食用，无过期无霉变食品。")],-1)),gt={style:{"text-align":"center"}},yt={style:{"text-align":"center"}},ft={style:{"text-align":"center"}},mt={style:{"text-align":"center"}},bt=s(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.长者十知道:姓名、性别、年龄、护理等级、生活习惯及健康状况、用药情况、饮食禁忌、大小便情况、食品衣物护理重点。")],-1)),vt={style:{"text-align":"center"}},wt={style:{"text-align":"center"}},Pt={style:{"text-align":"center"}},kt={style:{"text-align":"center"}},zt={class:"dialog-footer"},Dt={__name:"hlzzRecord",setup(r,{expose:v}){const o=p(!1),y=p(null),l=p({}),x=p(!1),w=i=>{x.value=!0,R(i.id).then(d=>{o.value=!0,l.value=d.data||{}}).finally(()=>{x.value=!1})},P=()=>{o.value=!1},k=()=>{const i=y.value.cloneNode(!0);i.querySelectorAll(".el-input, .el-textarea").forEach(_=>{var f;const h=((f=_.querySelector("input, textarea"))==null?void 0:f.value)||"",a=document.createElement("div");a.textContent=h,a.style.padding="8px",_.replaceWith(a)});const c=window.open("","_blank");c.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>护理组长查房记录</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${i.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `),c.document.close()};return v({openDialog:w}),(i,d)=>{const c=m("el-button"),_=m("el-dialog"),h=D("loading");return C((V(),M("div",null,[g(_,{modelValue:e(o),"onUpdate:modelValue":d[0]||(d[0]=a=>N(o)?o.value=a:null),title:"详情",width:"70%","close-on-click-modal":!1,"append-to-body":""},{footer:u(()=>[t("div",zt,[g(c,{onClick:P},{default:u(()=>[b("返 回")]),_:1}),g(c,{type:"primary",onClick:k},{default:u(()=>[b("打 印")]),_:1})])]),default:u(()=>[t("div",{class:"detail-content",ref_key:"printContent",ref:y},[B,t("table",E,[t("tbody",null,[t("tr",null,[t("td",T,"检查日期:"+n(e(l).checkDate||"-"),1),t("td",W,"查房人:"+n(e(l).roundPerson||"-"),1)]),q,t("tr",null,[A,t("td",L,n(e(l).existingProblems1||"-"),1),t("td",H,n(e(l).responsiblePerson1||"-"),1),t("td",J,n(e(l).improvementMeasures1||"-"),1),t("td",O,n(e(l).feedback1||"-"),1)]),t("tr",null,[U,t("td",Y,n(e(l).existingProblems2||"-"),1),t("td",$,n(e(l).responsiblePerson2||"-"),1),t("td",j,n(e(l).improvementMeasures2||"-"),1),t("td",F,n(e(l).feedback2||"-"),1)]),t("tr",null,[G,t("td",K,n(e(l).existingProblems3||"-"),1),t("td",Q,n(e(l).responsiblePerson3||"-"),1),t("td",X,n(e(l).improvementMeasures3||"-"),1),t("td",Z,n(e(l).feedback3||"-"),1)]),t("tr",null,[tt,t("td",et,n(e(l).existingProblems4||"-"),1),t("td",lt,n(e(l).responsiblePerson4||"-"),1),t("td",nt,n(e(l).improvementMeasures4||"-"),1),t("td",st,n(e(l).feedback4||"-"),1)]),t("tr",null,[ot,t("td",it,n(e(l).existingProblems5||"-"),1),t("td",dt,n(e(l).responsiblePerson5||"-"),1),t("td",ct,n(e(l).improvementMeasures5||"-"),1),t("td",at,n(e(l).feedback5||"-"),1)]),t("tr",null,[rt,t("td",_t,n(e(l).existingProblems6||"-"),1),t("td",pt,n(e(l).responsiblePerson6||"-"),1),t("td",ut,n(e(l).improvementMeasures6||"-"),1),t("td",xt,n(e(l).feedback6||"-"),1)]),t("tr",null,[ht,t("td",gt,n(e(l).existingProblems7||"-"),1),t("td",yt,n(e(l).responsiblePerson7||"-"),1),t("td",ft,n(e(l).improvementMeasures7||"-"),1),t("td",mt,n(e(l).feedback7||"-"),1)]),t("tr",null,[bt,t("td",vt,n(e(l).existingProblems8||"-"),1),t("td",wt,n(e(l).responsiblePerson8||"-"),1),t("td",Pt,n(e(l).improvementMeasures8||"-"),1),t("td",kt,n(e(l).feedback8||"-"),1)])])])],512)]),_:1},8,["modelValue"])])),[[h,e(x)]])}}},Mt=z(Dt,[["__scopeId","data-v-74228498"]]);export{Mt as default};
