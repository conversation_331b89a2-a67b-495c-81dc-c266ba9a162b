import{g as oe,a as se}from"./roommanage-DBG5TiIR.js";import{_ as ne,d as ie,r as p,u as de,a as ce,w as re,F as ue,e as _,c as V,o as w,f as t,i as e,h as n,n as h,D as I,l,K as B,L as z,j as U,t as c,aN as pe,G as D,aO as _e,E as me,aP as he,v as fe,x as ge}from"./index-B0qHf98Y.js";const u=x=>(fe("data-v-852fd90b"),x=x(),ge(),x),be={class:"log-review-container"},ve={class:"table-header-btns"},ye={class:"paginationBox"},we={class:"disinfection-detail"},xe=u(()=>e("div",{class:"title_room"}," 房间信息 ",-1)),Ne={class:"detail-content"},ke={class:"room-info"},Ce={class:"info-item"},Ve=u(()=>e("span",{class:"label"},"房间号：",-1)),De={class:"value"},Se={class:"info-items"},Re={class:"info-item"},$e=u(()=>e("span",{class:"label"},"楼栋信息：",-1)),Te={class:"value"},Le={class:"info-item"},Ie=u(()=>e("span",{class:"label"},"楼层信息：",-1)),Be={class:"value"},ze=u(()=>e("div",{class:"title_room"}," 消毒信息 ",-1)),Ue={class:"detail-contents"},Ee={class:"info_xd"},Fe={class:"info-item"},Me=u(()=>e("span",{class:"label"},"消毒日期：",-1)),Pe={class:"value"},Ye={class:"info-item"},je=u(()=>e("span",{class:"label"},"紫外线灯编号：",-1)),He={class:"value"},qe={class:"info-item"},Ae=u(()=>e("span",{class:"label"},"消毒时间：",-1)),Qe={class:"value"},Ge={class:"info-item"},Ke=u(()=>e("span",{class:"label"},"消毒时长：",-1)),Oe={class:"value"},We={class:"info-item"},Je=u(()=>e("span",{class:"label"},"辐照强度结果：",-1)),Xe={class:"value"},Ze={class:"info-item"},et=u(()=>e("span",{class:"label"},"消毒人员：",-1)),tt={class:"value"},at={class:"info-item"},lt=u(()=>e("span",{class:"label"},"监督人员：",-1)),ot={class:"value"},st={class:"info-item"},nt=u(()=>e("span",{class:"label"},"记录人：",-1)),it={class:"value"},dt={class:"info-items"},ct=u(()=>e("span",{class:"label"},"消毒区域：",-1)),rt={class:"value"},ut={class:"info-items"},pt=u(()=>e("span",{class:"label"},"备注：",-1)),_t={class:"value"},mt={__name:"uvDisinfectionRecordHistory",setup(x){const{proxy:E}=ie(),i=p({pageNum:1,pageSize:10}),S=p([]),k=p([]),d=p({}),F=de(),M=ce(),N=p([]),R=p(0),b=p(!1),v=p([]);p(null);const C=p(),y=p([]),f=()=>{pe(E.addDateRange(i.value,v.value,"RecordDate")).then(s=>{N.value=s.rows,R.value=s.total})},P=()=>{i.value.pageNum=1,f()},Y=()=>{v.value=[],i.value={pageNum:1,pageSize:10},f()},j=async()=>{const s=await oe();S.value=s.rows||[]},H=async s=>{k.value=[],i.value.floorName="";const a=await se(s);k.value=a.rows},q=s=>{i.value.pageSize=s,f()},A=s=>{i.value.pageNum=s,f()},Q=s=>{_e(s.id).then(a=>{d.value=a.data||{},b.value=!0})},G=s=>{me.confirm("注：删除紫外线记录表将失去原始数据，请慎重删除","确定删除该紫外线记录表吗?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{he(s.id).then(a=>{a.code===200?(D.success("删除成功"),i.value.pageNum=1,f()):D.error(a.msg)})})},K=()=>{M.push("/work/nurseworkstation")},O=()=>{y.value.length===N.value.length?C.value.clearSelection():N.value.forEach(s=>{C.value.toggleRowSelection(s,!0)})},W=s=>{y.value=s},J=()=>{if(y.value.length===0){D.warning("请先选择要打印的数据");return}const s=window.open("","_blank");s.document.write(`
            <html>
                <head>
                    <title>消毒记录打印</title>                    
                <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                        }

                        h1 {
                            text-align: center;
                            margin-bottom: 20px;
                        }

                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }

                        th,
                        td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                            color: #666;
                        }

                        .print-date {
                            text-align: right;
                            margin-bottom: 20px;
                        }
                </style>
                </head>
                <body>
                    <h1>紫外线消毒记录表</h1>
                    <div class="print-date">打印日期: ${new Date().toLocaleDateString()}</div>
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>消毒日期</th>
                                <th>房间号</th>
                                <th>楼栋层数</th>
                                <th>楼栋信息</th>
                                <th>紫外线灯编号</th>
                                <th>消毒区域</th>
                                <th>消毒时间</th>
                                <th>消毒时长</th>
                                <th>辐照强度结果</th>
                                <th>消毒人员</th>
                                <th>监督人员</th>
                                <th>记录人</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${y.value.map((a,m)=>`
                                <tr>
                                    <td>${m+1}</td>
                                    <td>${a.recordDate}</td>
                                    <td>${a.roomNumber}</td>
                                    <td>${a.floorNumber}层</td>
                                    <td>${a.buildingName}</td>
                                    <td>${a.uvLampCode}</td>
                                    <td>${a.disinfectionTarget}</td>
                                    <td>${a.startTime+"-"+a.endTime}</td>
                                    <td>${a.duration}</td>
                                    <td>${a.monitoringResult}</td>
                                    <td>${a.disinfectionStaffName}</td>
                                    <td>${a.supervisor}</td>
                                    <td>${a.recorder}</td>
                                </tr>
                            `).join("")}
                        </tbody>
                    </table>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    <\/script>
                </body>
            </html>
        `),s.document.close()};return re(()=>F.path,s=>{s==="/ultravioletDisinfectionLog/uvDisinfectionRecordHistory/add/0/add"&&(f(),j())},{immediate:!0}),ue(()=>{f()}),(s,a)=>{const m=_("el-button"),X=_("el-date-picker"),g=_("el-form-item"),$=_("el-option"),T=_("el-select"),L=_("el-input"),Z=_("el-form"),r=_("el-table-column"),ee=_("el-table"),te=_("el-pagination"),ae=_("el-dialog");return w(),V("div",be,[t(m,{type:"primary",onClick:K,class:"back-button"},{default:n(()=>[h(" 返回工作台 ")]),_:1}),t(Z,{model:l(i),ref:"queryForm",inline:!0,class:"search-form","label-width":"90px"},{default:n(()=>[t(g,{label:"消毒日期"},{default:n(()=>[t(X,{style:{width:"200px"},modelValue:l(v),"onUpdate:modelValue":a[0]||(a[0]=o=>I(v)?v.value=o:null),type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(g,{label:"楼栋信息",prop:"buildingId"},{default:n(()=>[t(T,{modelValue:l(i).buildingId,"onUpdate:modelValue":a[1]||(a[1]=o=>l(i).buildingId=o),placeholder:"全部",clearable:"",style:{width:"200px"},onChange:H},{default:n(()=>[(w(!0),V(B,null,z(l(S),o=>(w(),U($,{key:o.value,label:o.buildingName,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"楼层信息",prop:"floorId"},{default:n(()=>[t(T,{modelValue:l(i).floorId,"onUpdate:modelValue":a[2]||(a[2]=o=>l(i).floorId=o),placeholder:"全部",clearable:"",style:{width:"200px"},disabled:!l(i).buildingId},{default:n(()=>[(w(!0),V(B,null,z(l(k),o=>(w(),U($,{key:o.value,label:o.floorName,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(g,{label:"消毒人员",prop:"disinfectionStaffName"},{default:n(()=>[t(L,{style:{width:"200px"},modelValue:l(i).disinfectionStaffName,"onUpdate:modelValue":a[3]||(a[3]=o=>l(i).disinfectionStaffName=o),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),t(g,{label:"房间号",prop:"roomNumber"},{default:n(()=>[t(L,{style:{width:"200px"},modelValue:l(i).roomNumber,"onUpdate:modelValue":a[4]||(a[4]=o=>l(i).roomNumber=o),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),t(g,null,{default:n(()=>[t(m,{type:"primary",onClick:P,icon:"Search"},{default:n(()=>[h("查询")]),_:1}),t(m,{onClick:Y,icon:"Refresh"},{default:n(()=>[h("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e("div",ve,[t(m,{type:"primary",onClick:O},{default:n(()=>[h("全选")]),_:1}),t(m,{type:"primary",onClick:J,icon:"Printer",disabled:l(y).length===0},{default:n(()=>[h("打印")]),_:1},8,["disabled"])]),t(ee,{ref_key:"multipleTableRef",ref:C,data:l(N),border:"",style:{width:"100%"},onSelectionChange:W},{default:n(()=>[t(r,{type:"selection",width:"55"}),t(r,{prop:"index",label:"序号",width:"80",align:"center"},{default:n(o=>[h(c(o.$index+1),1)]),_:1}),t(r,{prop:"recordDate",label:"消毒日期","min-width":"120",align:"center"}),t(r,{prop:"roomNumber",label:"房间号","min-width":"120",align:"center"}),t(r,{prop:"floorName",label:"楼栋层数","min-width":"120",align:"center"}),t(r,{prop:"buildingName",label:"楼栋信息","min-width":"120",align:"center"}),t(r,{prop:"uvLampCode",label:"紫外线灯编号","min-width":"120",align:"center"}),t(r,{prop:"disinfectionTarget",label:"消毒区域","min-width":"120",align:"center"}),t(r,{prop:"startTime",label:"消毒时间","min-width":"120",align:"center"},{default:n(o=>[h(c(o.row.startTime)+" - "+c(o.row.endTime),1)]),_:1}),t(r,{prop:"duration",label:"消毒时长","min-width":"120",align:"center"}),t(r,{prop:"monitoringResult",label:"辐照强度结果","min-width":"120",align:"center"}),t(r,{prop:"disinfectionStaffName",label:"消毒人员","min-width":"120",align:"center"}),t(r,{prop:"supervisor",label:"监督人员","min-width":"120",align:"center"}),t(r,{prop:"recorder",label:"记录人","min-width":"120",align:"center"}),t(r,{label:"操作",width:"180",align:"center",fixed:"right"},{default:n(({row:o})=>[t(m,{type:"primary",link:"",onClick:le=>Q(o)},{default:n(()=>[h("详情")]),_:2},1032,["onClick"]),t(m,{type:"primary",link:"",onClick:le=>G(o)},{default:n(()=>[h("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e("div",ye,[t(te,{background:"",onSizeChange:q,onCurrentChange:A,"current-page":l(i).pageNum,"page-sizes":[10,20,30,50],"page-size":l(i).pageSize,layout:"total, sizes, prev, pager, next, jumper",total:l(R)},null,8,["current-page","page-size","total"])]),t(ae,{title:"详情",modelValue:l(b),"onUpdate:modelValue":a[6]||(a[6]=o=>I(b)?b.value=o:null),width:"50%"},{footer:n(()=>[t(m,{type:"primary",onClick:a[5]||(a[5]=o=>b.value=!1),plain:""},{default:n(()=>[h("返回")]),_:1})]),default:n(()=>[e("div",we,[xe,e("div",Ne,[e("div",ke,[e("div",Ce,[Ve,e("span",De,c(l(d).roomNumber||"-"),1)]),e("div",Se,[e("div",Re,[$e,e("span",Te,c(l(d).buildingName||"-"),1)]),e("div",Le,[Ie,e("span",Be,c(l(d).floorName||"-"),1)])])]),ze,e("div",Ue,[e("div",Ee,[e("div",Fe,[Me,e("span",Pe,c(l(d).recordDate||"-"),1)]),e("div",Ye,[je,e("span",He,c(l(d).uvLampCode||"-"),1)]),e("div",qe,[Ae,e("span",Qe,c(l(d).startTime?l(d).startTime+"-"+l(d).endTime:"-"),1)]),e("div",Ge,[Ke,e("span",Oe,c(l(d).duration||"-"),1)]),e("div",We,[Je,e("span",Xe,c(l(d).monitoringResult||"-"),1)]),e("div",Ze,[et,e("span",tt,c(l(d).disinfectionStaffName||"-"),1)]),e("div",at,[lt,e("span",ot,c(l(d).supervisor||"-"),1)]),e("div",st,[nt,e("span",it,c(l(d).recorder||"-"),1)]),e("div",dt,[ct,e("span",rt,c(l(d).disinfectionTarget||"-"),1)]),e("div",ut,[pt,e("span",_t,c(l(d).remark||"-"),1)])])])])])]),_:1},8,["modelValue"])])}}},gt=ne(mt,[["__scopeId","data-v-852fd90b"]]);export{gt as default};
