import T from"./PrintPreview-BkU_p74Q.js";import{_ as I,r as l,e as c,I as S,J as P,c as g,o as N,f as t,h as n,k as B,i as e,t as o,n as m,az as H,v as R,x as z}from"./index-B0qHf98Y.js";const i=d=>(R("data-v-a7e03844"),d=d(),z(),d),J={class:"hfrecords"},L=i(()=>e("div",{class:"title_room"}," 房间信息 ",-1)),q={key:0,class:"detail-content"},E={class:"room-info"},O={class:"info-left"},U={class:"info-item"},j=i(()=>e("span",{class:"label"},"房间号：",-1)),A={class:"value"},F={class:"info-item"},G=i(()=>e("span",{class:"label"},"老人姓名：",-1)),K={class:"value"},M={class:"info-item"},Q=i(()=>e("span",{class:"label"},"床位号：",-1)),W={class:"value"},X={class:"info-item"},Y=i(()=>e("span",{class:"label"},"楼栋信息：",-1)),Z={class:"value"},$={class:"info-item"},ee=i(()=>e("span",{class:"label"},"楼层层数：",-1)),se={class:"value"},te={class:"info-right"},oe={class:"title_room"},ae={class:"label"},le={class:"attachment-area"},ne={class:"dialog-footer"},ie={__name:"hfrecord",setup(d,{expose:C}){const h=l(null),_=l(!1),p=l(!1);l([]),l(!1);const s=l(null),y=v=>{console.log("打印预览弹窗数据",v),p.value=!0,H(v.id).then(r=>{s.value=r.data||[],_.value=!0}).finally(()=>{p.value=!1})},f=()=>{_.value=!1,s.value=null},k=()=>{h.value.openPrintDialog(s.value)};return C({openDialog:y}),(v,r)=>{const x=c("el-avatar"),u=c("el-table-column"),D=c("el-table"),b=c("el-button"),V=c("el-dialog"),w=S("loading");return P((N(),g("div",J,[t(V,{modelValue:_.value,"onUpdate:modelValue":r[0]||(r[0]=a=>_.value=a),title:"详情",width:"800px",onClose:f},{footer:n(()=>[e("span",ne,[t(b,{onClick:f},{default:n(()=>[m("返回")]),_:1}),t(b,{type:"primary",onClick:k},{default:n(()=>[m("打印")]),_:1})])]),default:n(()=>[L,s.value?(N(),g("div",q,[e("div",E,[e("div",O,[e("div",U,[j,e("span",A,o(s.value.roomNumber||"-"),1)]),e("div",F,[G,e("span",K,o(s.value.elderName||"-"),1)]),e("div",M,[Q,e("span",W,o(s.value.roomNumber||"-")+"-"+o(s.value.bedNumber||"-"),1)]),e("div",X,[Y,e("span",Z,o(s.value.buildingName||"-"),1)]),e("div",$,[ee,e("span",se,o(s.value.floorNumber||"-")+"层",1)])]),e("div",te,[t(x,{shape:"square",size:100,src:s.value.avatar},null,8,["src"])])]),e("div",oe,[e("span",ae,"查房信息-"+o(s.value.roundDate||"-"),1)]),e("div",le,[t(D,{data:s.value.visits,style:{width:"100%"},border:""},{default:n(()=>[t(u,{prop:"roundCount",label:"查房次数",width:"80",align:"center"}),t(u,{prop:"roundTime",label:"查房时间","min-width":"100",align:"center"},{default:n(({row:a})=>[m(o(a.roundTime.length>0&&JSON.stringify(a.roundTime)!='[""]'?a.roundTime[0]+"~"+a.roundTime[1]:"-"),1)]),_:1}),t(u,{prop:"roundName",label:"查房人","min-width":"100",align:"center"}),t(u,{prop:"roundContent",label:"查房内容","min-width":"180",align:"center"})]),_:1},8,["data"])])])):B("",!0)]),_:1},8,["modelValue"]),t(T,{ref_key:"prientRef",ref:h},null,512)])),[[w,p.value]])}}},_e=I(ie,[["__scopeId","data-v-a7e03844"]]);export{_e as default};
