import{_ as Ie,d as Ue,r as y,a as ke,z as Ce,M as Ne,F as Ee,e as _,I as Pe,J as Re,l as o,c as N,o as I,i as t,k as L,f as a,h as r,n as x,K as $,L as j,j as F,D as O,t as B,p as le,a2 as De,a1 as Me,a3 as He,a4 as Se,a5 as Ye,a6 as Te,G as g,v as Le,x as ze,a7 as Ae,a8 as qe,a9 as $e,aa as je,ab as Fe,ac as Oe,ad as Be,ae as Qe,af as Je,ag as We}from"./index-B0qHf98Y.js";import{a as Ge,g as Ke}from"./roommanage-DBG5TiIR.js";import{l as Xe}from"./tLiveRoom-DmSXfHxo.js";const m=z=>(Le("data-v-a97f9285"),z=z(),ze(),z),Ze={class:"container"},el={class:"topBackOrType"},ll={class:"selectTitle"},tl={key:0,class:"mt20"},ol={m:"4"},al={class:"roundCount"},nl={class:"elderInfo"},dl={class:"room_info_top"},sl=m(()=>t("div",{class:"title_room"},[t("h3",null,"房间信息")],-1)),rl={class:"room_form"},ul={class:"roomList"},il={key:1,class:"el-table-container mt20"},ml=m(()=>t("span",null,"查房时间: ",-1)),pl=m(()=>t("span",null,"查房人: ",-1)),cl={key:2,class:"el-table-container mt20"},Vl={class:"table-style"},yl=m(()=>t("td",{style:{"text-align":"center"}},"查房院长",-1)),xl=m(()=>t("td",{style:{"text-align":"center"}},"查房时间",-1)),fl=m(()=>t("td",{style:{"text-align":"center"}},"记录人",-1)),gl=m(()=>t("td",{style:{"text-align":"center"}},"检查部门",-1)),vl=m(()=>t("td",{style:{"text-align":"center"}},"参加人员",-1)),wl={colspan:"3"},hl=m(()=>t("tr",null,[t("td",{style:{"text-align":"center"}},"检查内容"),t("td",{colspan:"3",style:{"text-align":"center"}},"处置意见")],-1)),bl=m(()=>t("td",{style:{"text-align":"center"}},"生活护理",-1)),_l={colspan:"3"},Il=m(()=>t("td",{style:{"text-align":"center"}},"医疗护理",-1)),Ul={colspan:"3"},kl=m(()=>t("td",{style:{"text-align":"center"}},"后勤保障",-1)),Cl={colspan:"3"},Nl=m(()=>t("td",{style:{"text-align":"center"}},"安全隐患",-1)),El={colspan:"3"},Pl=m(()=>t("td",{style:{"text-align":"center"}},"意见或建议",-1)),Rl={colspan:"3"},Dl={key:3,class:"el-table-container mt20"},Ml={class:"table-style"},Hl={style:{"text-align":"center"}},Sl={style:{width:"40%"}},Yl={style:{display:"flex","justify-content":"space-around","align-items":"center"}},Tl=m(()=>t("p",{style:{width:"80px"}},"查房时间:",-1)),Ll=m(()=>t("tr",null,[t("td",{style:{"text-align":"center"}},"区 域"),t("td",{style:{"text-align":"center"}},"查房情况"),t("td",{style:{"text-align":"center"}},"处理办法及结果")],-1)),zl=m(()=>t("td",{style:{"text-align":"center"},rowspan:"8"},"自理区",-1)),Al={style:{display:"flex","justify-content":"space-between","align-items":"center"}},ql={colspan:"3"},$l={style:{display:"flex","justify-content":"space-between","align-items":"center"}},jl={colspan:"3"},Fl={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Ol={colspan:"3"},Bl={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Ql={colspan:"3"},Jl=m(()=>t("td",{style:{"text-align":"center"},rowspan:"8"},"介 护 区:",-1)),Wl={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Gl={colspan:"3"},Kl={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Xl={colspan:"3"},Zl={style:{display:"flex","justify-content":"space-between","align-items":"center"}},et={colspan:"3"},lt={style:{display:"flex","justify-content":"space-between","align-items":"center"}},tt={colspan:"3"},ot={key:4,class:"el-table-container mt20"},at={class:"table-style"},nt={style:{width:"40%"}},dt={style:{"text-align":"center"},colspan:"4"},st=m(()=>t("tr",null,[t("td",{style:{"text-align":"center",width:"40%"}},"检查内容"),t("td",{style:{"text-align":"center",width:"150px"}},"存在问题"),t("td",{style:{"text-align":"center",width:"150px"}},"责任人"),t("td",{style:{"text-align":"center",width:"150px"}},"改进措施"),t("td",{style:{"text-align":"center",width:"150px"}},"反馈")],-1)),rt=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1. 三短:头发、胡须、指/趾甲;"),t("p",null,"2.六洁:"),t("p",null,"(1)口腔洁:无残渣、无异味，有与病情相适应的口腔护理次数;"),t("p",null,"(2)头发洁:清洁、整齐、无异味;"),t("p",null,"(3)手足洁:干净;"),t("p",null,"(4)皮肤洁:全身皮肤清洁、无血、尿、便、胶布痕迹，无受压部痕迹，背部及骨突部位无褥疮，有预防措施(因病情不可避免除外);"),t("p",null,"(5)会阴、肛门洁:肛周及尿道口清洁、无血、尿、便迹，目卧床长者每日清洁会阴，留置尿管者保持尿道口干洁，尿管固定通畅。")],-1)),ut={style:{"text-align":"center"}},it={style:{"text-align":"center"}},mt={style:{"text-align":"center"}},pt={style:{"text-align":"center"}},ct=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.四无:无压疮、无跌倒/坠床、无烫伤、无噎食/误吸;"),t("p",null,"2.安全防护:"),t("p",null,"(1)长者衣服裤子长短、鞋子大小是否合适。"),t("p",null,"(2)轮椅、助行器刹车是否完好。(3)全护理、半护理长者不能自行打开水"),t("p",null,"(4)插座、插头、电源是否外落(5)有无危险品(如打火机、刀、剪刀、钢丝、铁片等)"),t("p",null,"(3)食品有无腐烂、霉变、药品是否安全放置。"),t("p",null,"(7)约束带使用是否正常，不用的安全放查。"),t("p",null,"(8)剃须刀、水果刀安全管理。(9)床防护栏(扶手)刹车、椅是否完好,(10)马桶、床头铃等性能。"),t("p",null,"(11)微波炉使用安全、地面清洁无水无障碍物"),t("p",null,"(12)假牙维护是否正确")],-1)),Vt={style:{"text-align":"center"}},yt={style:{"text-align":"center"}},xt={style:{"text-align":"center"}},ft={style:{"text-align":"center"}},gt=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;"),t("p",null,'2.“四周到":饭前洗手，送水、送饭、送便器到床;'),t("p",null,"3.核查文书书写情况是否如实、及时等")],-1)),vt={style:{"text-align":"center"}},wt={style:{"text-align":"center"}},ht={style:{"text-align":"center"}},bt={style:{"text-align":"center"}},_t=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.“四及时”:巡视长者及时、发现问题及时、解决问题及时、护理及时;"),t("p",null,'2.“四周到":饭前洗手，送水、送饭、送便器到床;'),t("p",null,"3.核查文书书写情况是否如实、及时等")],-1)),It={style:{"text-align":"center"}},Ut={style:{"text-align":"center"}},kt={style:{"text-align":"center"}},Ct={style:{"text-align":"center"}},Nt=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"卫生:"),t("p",null,"(1)床单位干洁平整、床上无碎屑、无杂物;床下整洁，无便器、无杂物，只有一双拖鞋，房间无异味。(2)桌面清洁，整齐，碗筷用物不乱放。长者的用物“一用一清洁一消毒”。"),t("p",null,"(3)卫生间用物用具摆放整齐，定时消毒，无臭味，室内无蚊蝇、无蟑螂(4)物品摆放:衣柜、床头柜、桌面是否整齐干净"),t("p",null,"(5)长者衣着整洁干净、无异味、无污渍")],-1)),Et={style:{"text-align":"center"}},Pt={style:{"text-align":"center"}},Rt={style:{"text-align":"center"}},Dt={style:{"text-align":"center"}},Mt=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.消毒隔离:房间按时开窗通风，毛巾便盆、轮椅等是否及时消毒，气垫床是否及时清洁晾晒及维护;"),t("p",null,"2.检查相关文书书写情况。")],-1)),Ht={style:{"text-align":"center"}},St={style:{"text-align":"center"}},Yt={style:{"text-align":"center"}},Tt={style:{"text-align":"center"}},Lt=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.长者食品按有效期长短放置，保证在有效期内及时给长者食用，无过期无霉变食品。")],-1)),zt={style:{"text-align":"center"}},At={style:{"text-align":"center"}},qt={style:{"text-align":"center"}},$t={style:{"text-align":"center"}},jt=m(()=>t("td",{style:{"text-align":"left","font-size":"12px"}},[t("p",null,"1.长者十知道:姓名、性别、年龄、护理等级、生活习惯及健康状况、用药情况、饮食禁忌、大小便情况、食品衣物护理重点。")],-1)),Ft={style:{"text-align":"center"}},Ot={style:{"text-align":"center"}},Bt={style:{"text-align":"center"}},Qt={style:{"text-align":"center"}},Jt={style:{"text-align":"center","margin-top":"20px"}},Wt={__name:"roomInspectionForm",setup(z){const{proxy:G}=Ue(),b=y(!1),h=y("1"),E=y([]),H=y(!1),K=y([]),Q=y([]),A=y([]),te=ke(),q=Ce(()=>Ne().format("YYYY-MM-DD")),oe=y([{label:"和孚护理查房记录",value:"1"},{label:"机构综合查房记录",value:"2"},{label:"行政查房记录",value:"3"},{label:"护理查房记录",value:"4"},{label:"护理组长查房记录",value:"5"}]);y([{label:"07:00 ~ 09:00",value:"1"},{label:"09:00 ~ 11:00",value:"2"},{label:"11:00 ~ 13:00",value:"3"},{label:"13:00 ~ 15:00",value:"4"},{label:"15:00 ~ 17:00",value:"5"},{label:"17:00 ~ 19:00",value:"6"},{label:"19:00 ~ 20:00",value:"7"},{label:"20:00 ~ 22:00",value:"8"},{label:"22:00 ~ 24:00",value:"9"}]);const C=y({}),ae=y({handoverDate:[{required:!0,message:"请选择日期",trigger:"change"}],buildingId:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼层",trigger:"change"}]}),P=y(""),Y=y(""),i=y(JSON.parse(localStorage.getItem("userInfo"))),R=y([{seqNo:1,checkItems:"生命体征监测",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:2,checkItems:"药物核对",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:3,checkItems:"管道护理",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:4,checkItems:"皮肤检查",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:5,checkItems:"安全及环境检查",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:6,checkItems:"饮食检查",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:7,checkItems:"心理评估",checkContent:"",nurseId:i.value.userId,nurseName:i.value.userName},{seqNo:8,checkItems:"康复指导",checkContent:"",roundTime:P.value,roundTime:P.value,nurseId:i.value.userId,nurseName:i.value.userName}]),c=y({}),d=y({}),s=y({});y(""),y("");const U=y([]),ne=u=>{u=="1"?Z():u=="2"?He(X()).then(e=>{e.rows.length>0?(R.value=e.rows||[],P.value=e.rows[0].roundTime||"",Y.value=e.rows[0].roundPerson||""):R.value=R.value}):u=="3"?Se(X()).then(e=>{c.value=e.rows[0]||{}}):u=="4"?Ye({nurseId:i.value.userId,nurseName:i.value.userName,roundDate:q.value}).then(e=>{d.value=e.rows[0]||{}}):u=="5"&&Te({nurseId:i.value.userId,nurseName:i.value.userName,checkDate:q.value}).then(e=>{s.value=e.rows[0]||{}})},X=()=>({nurseId:i.value.userId,nurseName:i.value.userName,roundTime:q.value}),de=()=>{te.push("/work/nurseworkstation")},se=async()=>{h.value=="1"?(console.log(U.value,"originalElders"),b.value=!0,(await qe(JSON.stringify(U.value))).code==200?(g.success("提交成功"),b.value=!1):(g.error("提交失败"),b.value=!1)):h.value=="2"?(b.value=!0,R.value=R.value.map(e=>({...e,roundTime:P.value,roundPerson:Y.value})),(await $e(R.value)).code==200?(g.success("提交成功"),b.value=!1):(g.error("提交失败"),b.value=!1)):h.value=="3"?(b.value=!0,(await(c.value.id?je:Fe)({...c.value,nurseId:i.value.userId,nurseName:i.value.userName})).code==200?(g.success("提交成功"),b.value=!1):(g.error("提交失败"),b.value=!1)):h.value=="4"?(b.value=!0,(await(d.value.id?Oe:Be)({...d.value,nurseId:i.value.userId,nurseName:i.value.userName})).code==200?(g.success("提交成功"),b.value=!1):(g.error("提交失败"),b.value=!1)):h.value=="5"&&(b.value=!0,(await(s.value.id?Qe:Je)({...s.value,nurseId:i.value.userId,nurseName:i.value.userName})).code==200?(g.success("提交成功"),b.value=!1):(g.error("提交失败"),b.value=!1))},re=()=>{G.$router.back()},ue=u=>{const e=E.value.findIndex(p=>p.roomId===u.id);e>-1?E.value.splice(e,1):E.value.push({roomId:u.id,roomName:u.roomName})},ie=()=>{G.$refs.formRef.validate(async u=>{if(u){if(E.value.length==0){g.error("请选择房间");return}const e=E.value.map(f=>f.roomId),p=await Ae({roomIds:e,pageSize:1e3});if(p.code==200){if(p.rows.length==0){g.warning("此房间暂无老人信息!");return}H.value=!1;const f=p.rows.map(v=>({...v,elderId:v.elderId?v.elderId:v.id,nurseId:i.value.userId,nurseName:i.value.userName,roundDate:C.value.handoverDate,visits:v.visits||[{roundCount:1,roundTime:[],roundName:"",roundContent:"均正常",elderId:v.elderId?v.elderId:v.id}]}));if(U.value.length===0)U.value=f,g.success("成功添加查房记录");else{const v=new Set(U.value.map(k=>k.elderId)),V=f.filter(k=>v.has(k.elderId));if(V.length>0){const k=V.map(D=>D.elderName).join("、");g.warning(`老人 ${k} 已存在，请勿重复添加`);const n=f.filter(D=>!v.has(D.elderId));n.length>0&&(U.value.push(...n),g.success(`成功添加${n.length}位老人的查房记录`))}else U.value.push(...f),g.success(`成功添加${f.length}位老人的查房记录`)}}else g.error(p.msg)}})},S=y([]),me=(u,e)=>{const p=S.value.indexOf(u.elderId);p>-1?S.value.splice(p,1):S.value.push(u.elderId)},pe=()=>{E.value=[],H.value=!0},ce=async u=>{const e=[...S.value],p=U.value.findIndex(f=>f.elderId===u.elderId);if(p>=0){const f=U.value[p];if(f.visits){const v=f.visits.findIndex(V=>V.roundCount===u.roundCount);if(v>=0){if(f.visits.length<=1){g.warning("至少需要保留一条查房记录");return}f.visits.splice(v,1),f.visits.forEach((V,k)=>{V.roundCount=k+1}),S.value=e,g.success("删除成功")}}}},Ve=u=>{const e=U.value.find(p=>p.elderId===u.elderId);if(e){const p={roundCount:e.visits.length+1,roundTime:[],roundName:"",roundContent:"均正常",elderId:u.elderId};e.visits||(e.visits=[]),e.visits.push(p)}},ye=()=>{xe(),Z()},xe=async()=>{const u=await Ke();K.value=u.rows||[]},fe=async u=>{Q.value=[],E.value=[],A.value=[],C.value.floorId="";const e=await Ge(u);Q.value=e.rows},ge=async u=>{A.value=[],E.value=[];const e=await Xe({floorId:u});A.value=e.rows},Z=async()=>{const u=await We({nurseId:i.value.userId,nurseName:i.value.userName,roundDate:q.value});u.code===200&&(U.value=u.rows)};return Ee(()=>{ye()}),(u,e)=>{const p=_("el-button"),f=_("el-option"),v=_("el-select"),V=_("el-table-column"),k=_("el-time-picker"),n=_("el-input"),D=_("el-table"),ve=_("el-avatar"),T=_("el-date-picker"),J=_("el-form-item"),W=_("el-col"),ee=_("el-row"),we=_("el-check-tag"),he=_("el-form"),be=_("el-dialog"),_e=Pe("loading");return Re((I(),N("div",Ze,[t("div",el,[a(p,{type:"primary",onClick:de},{default:r(()=>[x("返回工作台")]),_:1}),a(v,{modelValue:o(h),"onUpdate:modelValue":e[0]||(e[0]=l=>O(h)?h.value=l:null),placeholder:"请选择查房类型",style:{width:"50%","margin-left":"50px"},onChange:ne},{default:r(()=>[(I(!0),N($,null,j(o(oe),l=>(I(),F(f,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),t("div",ll,B(o(h)=="1"?"和孚护理查房记录":o(h)=="2"?"机构综合查房记录":o(h)=="3"?"行政查房记录":o(h)=="4"?"护理查房记录":""),1),o(h)=="1"?(I(),N("div",tl,[a(p,{type:"primary",style:{float:"right"},onClick:pe},{default:r(()=>[x("新增查房")]),_:1}),a(D,{data:o(U),border:!1,style:{width:"100%"},"row-key":l=>l.elderId,"expand-row-keys":o(S),onExpandChange:me},{default:r(()=>[a(V,{type:"expand"},{default:r(l=>[t("div",ol,[a(D,{data:l.row.visits,border:!1},{default:r(()=>[a(V,{label:"查房次数",property:"roundCount",align:"center",width:"80"},{default:r(w=>[t("div",al,B(w.row.roundCount),1)]),_:1}),a(V,{property:"roundTime",label:"查房时间",align:"center","min-width":"120"},{default:r(w=>[a(k,{modelValue:w.row.roundTime,"onUpdate:modelValue":M=>w.row.roundTime=M,"is-range":"","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"200px"},format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(V,{property:"roundName",label:"查房人",align:"center"},{default:r(w=>[a(n,{modelValue:w.row.roundName,"onUpdate:modelValue":M=>w.row.roundName=M,placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(V,{property:"roundContent",label:"查房内容",align:"center","min-width":"200"},{default:r(w=>[a(n,{modelValue:w.row.roundContent,"onUpdate:modelValue":M=>w.row.roundContent=M,type:"textarea",rows:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(V,{label:"操作",align:"center"},{default:r(w=>[a(p,{type:"primary",icon:o(De),circle:"",onClick:le(M=>Ve(w.row),["stop"])},null,8,["icon","onClick"]),a(p,{type:"danger",icon:o(Me),circle:"",onClick:le(M=>ce(w.row),["stop"])},null,8,["icon","onClick"])]),_:1})]),_:2},1032,["data"])])]),_:1}),a(V,{label:"老人头像",prop:"avatar",align:"center"},{default:r(l=>[t("div",nl,[a(ve,{shape:"circle",size:80,fit:"fill",src:l.row.avatar},null,8,["src"])])]),_:1}),a(V,{label:"老人姓名",prop:"elderName",align:"center"}),a(V,{label:"老人房间号",prop:"roomNumber",align:"center"}),a(V,{label:"老人床位号",prop:"bedNumber",align:"center"},{default:r(l=>[x(B(`${l.row.roomNumber}-${l.row.bedNumber}`),1)]),_:1})]),_:1},8,["data","row-key","expand-row-keys"]),a(be,{modelValue:o(H),"onUpdate:modelValue":e[6]||(e[6]=l=>O(H)?H.value=l:null),title:"新增查房",width:"70%","close-on-click-modal":!1,"append-to-body":""},{footer:r(()=>[a(p,{type:"primary",onClick:e[4]||(e[4]=l=>ie())},{default:r(()=>[x("提交")]),_:1}),a(p,{onClick:e[5]||(e[5]=l=>H.value=!1)},{default:r(()=>[x("取消")]),_:1})]),default:r(()=>[a(he,{ref:"formRef",model:o(C),rules:o(ae),"label-width":"120px","label-position":"left"},{default:r(()=>[t("div",dl,[sl,t("div",rl,[a(ee,{gutter:24},{default:r(()=>[a(W,{span:8},{default:r(()=>[a(J,{label:"查房日期",prop:"handoverDate"},{default:r(()=>[a(T,{modelValue:o(C).handoverDate,"onUpdate:modelValue":e[1]||(e[1]=l=>o(C).handoverDate=l),type:"date",placeholder:"选择日期",style:{width:"200px"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),a(W,{span:8},{default:r(()=>[a(J,{label:"楼栋信息",prop:"buildingId"},{default:r(()=>[a(v,{modelValue:o(C).buildingId,"onUpdate:modelValue":e[2]||(e[2]=l=>o(C).buildingId=l),style:{width:"200px"},onChange:fe},{default:r(()=>[(I(!0),N($,null,j(o(K),l=>(I(),F(f,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(W,{span:8},{default:r(()=>[a(J,{label:"楼栋层数",prop:"floorId"},{default:r(()=>[a(v,{modelValue:o(C).floorId,"onUpdate:modelValue":e[3]||(e[3]=l=>o(C).floorId=l),disabled:!o(C).buildingId,style:{width:"200px"},onChange:ge},{default:r(()=>[(I(!0),N($,null,j(o(Q),l=>(I(),F(f,{key:l.value,label:l.floorName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),a(ee,{gutter:24},{default:r(()=>[t("div",ul,[(I(!0),N($,null,j(o(A),l=>(I(),F(we,{key:l.id,checked:o(E).some(w=>w.roomId===l.id),onChange:w=>ue(l)},{default:r(()=>[x(B(l.roomName),1)]),_:2},1032,["checked","onChange"]))),128))])]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])):L("",!0),o(h)=="2"?(I(),N("div",il,[a(D,{data:o(R),border:"",style:{width:"100%"}},{default:r(()=>[a(V,{label:"查房时间",prop:"roundTime"},{header:r(()=>[ml,a(T,{modelValue:o(P),"onUpdate:modelValue":e[7]||(e[7]=l=>O(P)?P.value=l:null),type:"date",placeholder:"选择日期",style:{width:"200px",height:"35px"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),default:r(()=>[a(V,{prop:"seqNo",label:"序号",width:"80",align:"center"}),a(V,{prop:"checkItems",label:"检查项目","min-width":"180",align:"center"})]),_:1}),a(V,{label:"查房人",width:"100",prop:"roundPerson"},{header:r(()=>[pl,a(n,{modelValue:o(Y),"onUpdate:modelValue":e[8]||(e[8]=l=>O(Y)?Y.value=l:null),size:"small",placeholder:"请输入查房人",style:{width:"45%",height:"35px"}},null,8,["modelValue"])]),default:r(()=>[a(V,{label:"检查内容","min-width":"300",prop:"checkContent",align:"center"},{default:r(l=>[a(n,{modelValue:l.row.checkContent,"onUpdate:modelValue":w=>l.row.checkContent=w,placeholder:"请输入检查内容",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1})]),_:1},8,["data"])])):L("",!0),o(h)=="3"?(I(),N("div",cl,[t("table",Vl,[t("tbody",null,[t("tr",null,[yl,t("td",null,[a(n,{placeholder:"请输入",modelValue:o(c).director,"onUpdate:modelValue":e[9]||(e[9]=l=>o(c).director=l)},null,8,["modelValue"])]),xl,t("td",null,[a(T,{modelValue:o(c).roundTime,"onUpdate:modelValue":e[10]||(e[10]=l=>o(c).roundTime=l),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])])]),t("tr",null,[fl,t("td",null,[a(n,{placeholder:"请输入",modelValue:o(c).recorder,"onUpdate:modelValue":e[11]||(e[11]=l=>o(c).recorder=l)},null,8,["modelValue"])]),gl,t("td",null,[a(n,{placeholder:"请输入",modelValue:o(c).department,"onUpdate:modelValue":e[12]||(e[12]=l=>o(c).department=l)},null,8,["modelValue"])])]),t("tr",null,[vl,t("td",wl,[a(n,{placeholder:"请输入",modelValue:o(c).participants,"onUpdate:modelValue":e[13]||(e[13]=l=>o(c).participants=l),type:"textarea",rows:2},null,8,["modelValue"])])]),hl,t("tr",null,[bl,t("td",_l,[a(n,{placeholder:"请输入",modelValue:o(c).lifeCare,"onUpdate:modelValue":e[14]||(e[14]=l=>o(c).lifeCare=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[Il,t("td",Ul,[a(n,{placeholder:"请输入",modelValue:o(c).medicalCare,"onUpdate:modelValue":e[15]||(e[15]=l=>o(c).medicalCare=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[kl,t("td",Cl,[a(n,{placeholder:"请输入",modelValue:o(c).logistics,"onUpdate:modelValue":e[16]||(e[16]=l=>o(c).logistics=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[Nl,t("td",El,[a(n,{placeholder:"请输入",modelValue:o(c).safetyHazard,"onUpdate:modelValue":e[17]||(e[17]=l=>o(c).safetyHazard=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[Pl,t("td",Rl,[a(n,{placeholder:"请输入",modelValue:o(c).suggestions,"onUpdate:modelValue":e[18]||(e[18]=l=>o(c).suggestions=l),type:"textarea",rows:2},null,8,["modelValue"])])])])])])):L("",!0),o(h)=="4"?(I(),N("div",Dl,[t("table",Ml,[t("tbody",null,[t("tr",null,[t("td",Hl,[a(T,{modelValue:o(d).roundDate,"onUpdate:modelValue":e[19]||(e[19]=l=>o(d).roundDate=l),type:"date",placeholder:"选择日期",style:{width:"80%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),t("td",Sl,[x("查房人:"),a(n,{placeholder:"请输入",modelValue:o(d).roundPerson,"onUpdate:modelValue":e[20]||(e[20]=l=>o(d).roundPerson=l),style:{width:"80%"}},null,8,["modelValue"])]),t("td",Yl,[Tl,t("p",null,[x("上午  "),a(k,{modelValue:o(d).morningTime,"onUpdate:modelValue":e[21]||(e[21]=l=>o(d).morningTime=l),placeholder:"请选择",style:{width:"60%"},"value-format":"HH:mm",format:"HH:mm"},null,8,["modelValue"])]),t("p",null,[x("下午  "),a(k,{modelValue:o(d).afternoonTime,"onUpdate:modelValue":e[22]||(e[22]=l=>o(d).afternoonTime=l),placeholder:"请选择",style:{width:"60%"},"value-format":"HH:mm",format:"HH:mm"},null,8,["modelValue"])])])]),Ll,t("tr",null,[zl,t("td",Al,[x("居室卫生:"),a(n,{placeholder:"请输入",modelValue:o(d).selfcareRoomHygiene,"onUpdate:modelValue":e[23]||(e[23]=l=>o(d).selfcareRoomHygiene=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",ql,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareRoomHygieneSolution,"onUpdate:modelValue":e[24]||(e[24]=l=>o(d).selfcareRoomHygieneSolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",$l,[x("老人卫生:"),a(n,{placeholder:"请输入",modelValue:o(d).selfcareElderHygiene,"onUpdate:modelValue":e[25]||(e[25]=l=>o(d).selfcareElderHygiene=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",jl,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareElderHygieneSolution,"onUpdate:modelValue":e[26]||(e[26]=l=>o(d).selfcareElderHygieneSolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",Fl,[x("居室物品摆放:"),a(n,{placeholder:"请输入",modelValue:o(d).selfcareRoomArrangement,"onUpdate:modelValue":e[27]||(e[27]=l=>o(d).selfcareRoomArrangement=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",Ol,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareRoomArrangementSolution,"onUpdate:modelValue":e[28]||(e[28]=l=>o(d).selfcareRoomArrangementSolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",Bl,[x("服务提供质量:"),a(n,{placeholder:"请输入",modelValue:o(d).selfcareServiceQuality,"onUpdate:modelValue":e[29]||(e[29]=l=>o(d).selfcareServiceQuality=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",Ql,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareServiceQualitySolution,"onUpdate:modelValue":e[30]||(e[30]=l=>o(d).selfcareServiceQualitySolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareItemEx1,"onUpdate:modelValue":e[31]||(e[31]=l=>o(d).selfcareItemEx1=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareContentEx1,"onUpdate:modelValue":e[32]||(e[32]=l=>o(d).selfcareContentEx1=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareItemEx2,"onUpdate:modelValue":e[33]||(e[33]=l=>o(d).selfcareItemEx2=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareContentEx2,"onUpdate:modelValue":e[34]||(e[34]=l=>o(d).selfcareContentEx2=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareItemEx3,"onUpdate:modelValue":e[35]||(e[35]=l=>o(d).selfcareItemEx3=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareContentEx3,"onUpdate:modelValue":e[36]||(e[36]=l=>o(d).selfcareContentEx3=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareItemEx4,"onUpdate:modelValue":e[37]||(e[37]=l=>o(d).selfcareItemEx4=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).selfcareContentEx4,"onUpdate:modelValue":e[38]||(e[38]=l=>o(d).selfcareContentEx4=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[Jl,t("td",Wl,[x("居室卫生:"),a(n,{placeholder:"请输入",modelValue:o(d).careRoomHygiene,"onUpdate:modelValue":e[39]||(e[39]=l=>o(d).careRoomHygiene=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",Gl,[a(n,{placeholder:"请输入",modelValue:o(d).careRoomHygieneSolution,"onUpdate:modelValue":e[40]||(e[40]=l=>o(d).careRoomHygieneSolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",Kl,[x("老人卫生:"),a(n,{placeholder:"请输入",modelValue:o(d).careElderHygiene,"onUpdate:modelValue":e[41]||(e[41]=l=>o(d).careElderHygiene=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",Xl,[a(n,{placeholder:"请输入",modelValue:o(d).careElderHygieneSolution,"onUpdate:modelValue":e[42]||(e[42]=l=>o(d).careElderHygieneSolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",Zl,[x("居室物品摆放:"),a(n,{placeholder:"请输入",modelValue:o(d).careRoomArrangement,"onUpdate:modelValue":e[43]||(e[43]=l=>o(d).careRoomArrangement=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",et,[a(n,{placeholder:"请输入",modelValue:o(d).careRoomArrangementSolution,"onUpdate:modelValue":e[44]||(e[44]=l=>o(d).careRoomArrangementSolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",lt,[x("服务提供质量:"),a(n,{placeholder:"请输入",modelValue:o(d).careServiceQuality,"onUpdate:modelValue":e[45]||(e[45]=l=>o(d).careServiceQuality=l),type:"textarea",rows:2,style:{width:"80%"}},null,8,["modelValue"])]),t("td",tt,[a(n,{placeholder:"请输入",modelValue:o(d).careServiceQualitySolution,"onUpdate:modelValue":e[46]||(e[46]=l=>o(d).careServiceQualitySolution=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careItemEx1,"onUpdate:modelValue":e[47]||(e[47]=l=>o(d).careItemEx1=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careContentEx1,"onUpdate:modelValue":e[48]||(e[48]=l=>o(d).careContentEx1=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careItemEx2,"onUpdate:modelValue":e[49]||(e[49]=l=>o(d).careItemEx2=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careContentEx2,"onUpdate:modelValue":e[50]||(e[50]=l=>o(d).careContentEx2=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careItemEx3,"onUpdate:modelValue":e[51]||(e[51]=l=>o(d).careItemEx3=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careContentEx3,"onUpdate:modelValue":e[52]||(e[52]=l=>o(d).careContentEx3=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careItemEx4,"onUpdate:modelValue":e[53]||(e[53]=l=>o(d).careItemEx4=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",null,[a(n,{placeholder:"请输入",modelValue:o(d).careContentEx4,"onUpdate:modelValue":e[54]||(e[54]=l=>o(d).careContentEx4=l),type:"textarea",rows:2},null,8,["modelValue"])])])])])])):L("",!0),o(h)=="5"?(I(),N("div",ot,[t("table",at,[t("tbody",null,[t("tr",null,[t("td",nt,[x("检查日期:"),a(T,{modelValue:o(s).checkDate,"onUpdate:modelValue":e[55]||(e[55]=l=>o(s).checkDate=l),type:"date",placeholder:"选择日期",style:{width:"80%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),t("td",dt,[x("查房人:"),a(n,{placeholder:"请输入",modelValue:o(s).roundPerson,"onUpdate:modelValue":e[56]||(e[56]=l=>o(s).roundPerson=l),style:{width:"80%"}},null,8,["modelValue"])])]),st,t("tr",null,[rt,t("td",ut,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems1,"onUpdate:modelValue":e[57]||(e[57]=l=>o(s).existingProblems1=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",it,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson1,"onUpdate:modelValue":e[58]||(e[58]=l=>o(s).responsiblePerson1=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",mt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures1,"onUpdate:modelValue":e[59]||(e[59]=l=>o(s).improvementMeasures1=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",pt,[a(n,{placeholder:"请输入",modelValue:o(s).feedback1,"onUpdate:modelValue":e[60]||(e[60]=l=>o(s).feedback1=l),type:"textarea",rows:3},null,8,["modelValue"])])]),t("tr",null,[ct,t("td",Vt,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems2,"onUpdate:modelValue":e[61]||(e[61]=l=>o(s).existingProblems2=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",yt,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson2,"onUpdate:modelValue":e[62]||(e[62]=l=>o(s).responsiblePerson2=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",xt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures2,"onUpdate:modelValue":e[63]||(e[63]=l=>o(s).improvementMeasures2=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",ft,[a(n,{placeholder:"请输入",modelValue:o(s).feedback2,"onUpdate:modelValue":e[64]||(e[64]=l=>o(s).feedback2=l),type:"textarea",rows:3},null,8,["modelValue"])])]),t("tr",null,[gt,t("td",vt,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems3,"onUpdate:modelValue":e[65]||(e[65]=l=>o(s).existingProblems3=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",wt,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson3,"onUpdate:modelValue":e[66]||(e[66]=l=>o(s).responsiblePerson3=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",ht,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures3,"onUpdate:modelValue":e[67]||(e[67]=l=>o(s).improvementMeasures3=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",bt,[a(n,{placeholder:"请输入",modelValue:o(s).feedback3,"onUpdate:modelValue":e[68]||(e[68]=l=>o(s).feedback3=l),type:"textarea",rows:3},null,8,["modelValue"])])]),t("tr",null,[_t,t("td",It,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems4,"onUpdate:modelValue":e[69]||(e[69]=l=>o(s).existingProblems4=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",Ut,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson4,"onUpdate:modelValue":e[70]||(e[70]=l=>o(s).responsiblePerson4=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",kt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures4,"onUpdate:modelValue":e[71]||(e[71]=l=>o(s).improvementMeasures4=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",Ct,[a(n,{placeholder:"请输入",modelValue:o(s).feedback4,"onUpdate:modelValue":e[72]||(e[72]=l=>o(s).feedback4=l),type:"textarea",rows:3},null,8,["modelValue"])])]),t("tr",null,[Nt,t("td",Et,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems5,"onUpdate:modelValue":e[73]||(e[73]=l=>o(s).existingProblems5=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",Pt,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson5,"onUpdate:modelValue":e[74]||(e[74]=l=>o(s).responsiblePerson5=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",Rt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures5,"onUpdate:modelValue":e[75]||(e[75]=l=>o(s).improvementMeasures5=l),type:"textarea",rows:3},null,8,["modelValue"])]),t("td",Dt,[a(n,{placeholder:"请输入",modelValue:o(s).feedback5,"onUpdate:modelValue":e[76]||(e[76]=l=>o(s).feedback5=l),type:"textarea",rows:3},null,8,["modelValue"])])]),t("tr",null,[Mt,t("td",Ht,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems6,"onUpdate:modelValue":e[77]||(e[77]=l=>o(s).existingProblems6=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",St,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson6,"onUpdate:modelValue":e[78]||(e[78]=l=>o(s).responsiblePerson6=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",Yt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures6,"onUpdate:modelValue":e[79]||(e[79]=l=>o(s).improvementMeasures6=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",Tt,[a(n,{placeholder:"请输入",modelValue:o(s).feedback6,"onUpdate:modelValue":e[80]||(e[80]=l=>o(s).feedback6=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[Lt,t("td",zt,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems7,"onUpdate:modelValue":e[81]||(e[81]=l=>o(s).existingProblems7=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",At,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson7,"onUpdate:modelValue":e[82]||(e[82]=l=>o(s).responsiblePerson7=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",qt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures7,"onUpdate:modelValue":e[83]||(e[83]=l=>o(s).improvementMeasures7=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",$t,[a(n,{placeholder:"请输入",modelValue:o(s).feedback7,"onUpdate:modelValue":e[84]||(e[84]=l=>o(s).feedback7=l),type:"textarea",rows:2},null,8,["modelValue"])])]),t("tr",null,[jt,t("td",Ft,[a(n,{placeholder:"请输入",modelValue:o(s).existingProblems8,"onUpdate:modelValue":e[85]||(e[85]=l=>o(s).existingProblems8=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",Ot,[a(n,{placeholder:"请输入",modelValue:o(s).responsiblePerson8,"onUpdate:modelValue":e[86]||(e[86]=l=>o(s).responsiblePerson8=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",Bt,[a(n,{placeholder:"请输入",modelValue:o(s).improvementMeasures8,"onUpdate:modelValue":e[87]||(e[87]=l=>o(s).improvementMeasures8=l),type:"textarea",rows:2},null,8,["modelValue"])]),t("td",Qt,[a(n,{placeholder:"请输入",modelValue:o(s).feedback8,"onUpdate:modelValue":e[88]||(e[88]=l=>o(s).feedback8=l),type:"textarea",rows:2},null,8,["modelValue"])])])])])])):L("",!0),t("div",Jt,[a(p,{type:"primary",onClick:se},{default:r(()=>[x("提交")]),_:1}),a(p,{onClick:re},{default:r(()=>[x("取消")]),_:1})])])),[[_e,o(b)]])}}},Zt=Ie(Wt,[["__scopeId","data-v-a97f9285"]]);export{Zt as default};
