import{_ as ze,C as K,r as f,F as Ge,b1 as We,e as y,c as k,o as v,f as a,i as p,k as he,h as o,K as I,L as M,j as E,G as x,l as S,dA as Je,n as b,dE as Xe,a2 as Ze,dF as el,dB as ll,aU as al,bo as tl,t as X,bc as ol,Q as nl,p as sl,au as ul,dG as rl,M as h,v as dl,x as il}from"./index-B0qHf98Y.js";const ne=T=>(dl("data-v-494c659d"),T=T(),il(),T),pl={class:"app-container"},cl={class:"schedule-header"},fl=["onMousedown","onMouseover","onClick","onDblclick","onContextmenu"],vl={key:0,class:"conflict-marker"},ml=ne(()=>p("p",null,"请按照数据模板的格式准备导入数据。",-1)),hl=ne(()=>p("div",{class:"el-upload__text"},[b("将文件拖到此处，或"),p("em",null,"点击上传")],-1)),bl=ne(()=>p("div",{style:{"line-height":"2"}},[p("h3",null,"1. 单元格设置"),p("p",null,"(1) 点击单元格可选中单元格；"),p("p",null,"(2) 在单元格上点击右键可弹出菜单：设置班次、取消班次；"),p("h3",null,"2. 批量设置"),p("p",null,"(1) 按住Ctrl键点击可选择多个不连续的单元格；"),p("p",null,"(2) 按住鼠标左键拖动可选择连续的单元格"),p("p",null,"(3) 在选中的单元格上点击右键可弹出菜单：设置班次、取消班次"),p("p",null,'(4) 点击"设置班次"会弹出设置窗口，选择班次和区域后点击确定更新所有选中单元格'),p("p",null,'(5) 点击"取消班次"会清空所有选中单元格的内容')],-1)),_l={__name:"index",setup(T){const D=K({deptIds:[],areaIds:[],shiftIds:[],person:"",status:null}),w=f([]),se=f(new Date().getFullYear()),ue=f(new Date().getMonth()+1),be=f([{label:"内科",value:1},{label:"外科",value:2},{label:"护理部",value:3}]),Z=f([{label:"A区",value:"A"},{label:"B区",value:"B"},{label:"C区",value:"C"}]),L=f([{label:"早班",value:"D",hours:8},{label:"中班",value:"M",hours:8},{label:"晚班",value:"N",hours:8},{label:"夜班",value:"E",hours:8},{label:"休班",value:"R",hours:0}]),_e=f([{label:"未排班",value:0},{label:"已排班",value:1}]),_=f([]),$=f([]),A=f(!1),U=K({shift:"",area:""}),O=K({row:null,prop:""}),H=f(!1),ee=f([{id:1,name:"张三",disabled:!1},{id:2,name:"李四",disabled:!1},{id:3,name:"王五",disabled:!1},{id:4,name:"赵六",disabled:!1},{id:5,name:"孙七",disabled:!1},{id:6,name:"周八",disabled:!1},{id:7,name:"吴九",disabled:!1},{id:8,name:"郑十",disabled:!1},{id:9,name:"冯十一",disabled:!1},{id:10,name:"陈十二",disabled:!1}]),Q=f([]),F=f(!1),le=f(null),i=f([]),ae=f(!1),q=K({top:0,left:0}),N=f(!1),P=K({shift:"",area:""}),te=f(!1),B=f([]),re=f([]),z=f(!1),de=()=>{const t=h();t.date()<=15?w.value=[t.clone().startOf("month").toDate(),t.clone().startOf("month").add(14,"days").toDate()]:w.value=[t.clone().startOf("month").add(15,"days").toDate(),t.clone().endOf("month").toDate()]},ye=()=>{if(!w.value||w.value.length!==2){$.value=[];return}const t=h(w.value[0]),e=h(w.value[1]);se.value=t.year(),ue.value=t.month()+1;const s=[];let n=t.clone();for(;n.isSameOrBefore(e);)s.push({prop:n.format("YYYY-MM-DD"),label:`${n.format("MM-DD")} (${["日","一","二","三","四","五","六"][n.day()]})`}),n.add(1,"day");$.value=s},ie=()=>{const t=_.value.map(e=>e.id);ee.value.forEach(e=>{e.disabled=t.includes(e.id)})},we=()=>{const t=[{id:1,name:"张三"},{id:2,name:"李四"},{id:3,name:"王五"}],e=["D","M","N","R"],s=["A","B","C","-"],n=t.map(r=>{const u={id:r.id,name:r.name};return $.value.forEach((d,c)=>{const m=e[Math.floor(Math.random()*e.length)],g=m==="R"?"-":s[Math.floor(Math.random()*s.length)];u[d.prop]={shift:m,area:g},r.id===2&&c===1&&(u[d.prop].conflict=!0)}),u});_.value=n,console.log("generateMockData",n),ie()},pe=()=>{ye(),we()},G=()=>pe(),De=()=>{const t=h(w.value[0]),e=h(w.value[1]),s=h.duration(e.diff(t)).asDays()+1;w.value=[t.clone().subtract(s,"days").toDate(),e.clone().subtract(s,"days").toDate()],G()},ge=()=>{const t=h(w.value[0]),e=h(w.value[1]),s=h.duration(e.diff(t)).asDays()+1;w.value=[t.clone().add(s,"days").toDate(),e.clone().add(s,"days").toDate()],G()},Ve=()=>x.info("模拟搜索，参数："+JSON.stringify(D)),ke=()=>{Object.assign(D,{deptIds:[],areaIds:[],shiftIds:[],person:"",status:null}),de(),G()},xe=()=>{z.value=!0},Ce=(t,e)=>{x.success(`${e.name} 导入成功`),z.value=!1},Ie=()=>{Q.value=_.value.map(t=>t.id),H.value=!0},Me=(t,e)=>e.name.indexOf(t)>-1,Ee=()=>{Q.value.map(e=>ee.value.find(s=>s.id===e)).filter(e=>e&&!_.value.some(s=>s.id===e.id)).forEach(e=>{const s={id:e.id,name:e.name};$.value.forEach(n=>{s[n.prop]={shift:"",area:"-"}}),_.value.push(s)}),ie(),H.value=!1},Se=()=>{B.value=w.value,ce(),te.value=!0},ce=()=>{if(!B.value||B.value.length!==2)return;const t=h(B.value[0]),e=h(B.value[1]);re.value=_.value.map(s=>{let n=0,r=0,u=0,d=t.clone();for(;d.isSameOrBefore(e);){const c=d.format("YYYY-MM-DD"),m=s[c];if(m){const g=L.value.find(oe=>oe.value===m.shift);g&&(g.value==="R"?r++:(n++,u+=g.hours))}d.add(1,"day")}return{name:s.name,workDays:n,restDays:r,workHours:u}})},$e=(t,e)=>{if(!e.property||e.property==="name")return;const s=`${t.id}|${e.property}`;i.value.includes(s)||(i.value=[s]),O.row=t,O.prop=e.property;const n=t[e.property]||{};U.shift=n.shift||"",U.area=n.area||"",A.value=!0},Ue=(t,e,s,n)=>{if(!e.property||e.property==="name")return;if(h(e.property).isBefore(h(),"day")){x.warning("当前日期已过期，不允许修改");return}const u=`${t.id}|${e.property}`;if(!F.value)if(n.ctrlKey||n.metaKey){n.preventDefault();const d=i.value.indexOf(u);d>-1?i.value.splice(d,1):i.value.push(u)}else i.value=[u]},Pe=()=>{O.row&&O.prop&&(O.row[O.prop]={...U}),A.value=!1,x.success("保存成功（自动保存）")},Be=(t,e,s,n)=>{if(n.button!==0||!e.property||e.property==="name"||n.ctrlKey||n.metaKey)return;n.preventDefault(),F.value=!0,i.value=[],le.value={rowId:t.id,colProp:e.property},document.body.style.userSelect="none";const r=`${t.id}|${e.property}`;i.value=[r]},fe=(t,e,s,n)=>{if(!F.value||!e.property||e.property==="name")return;const r={rowId:t.id,colProp:e.property};Oe(le.value,r)},W=t=>{F.value&&(F.value=!1,le.value=null,document.body.style.userSelect="")},Oe=(t,e)=>{const s=[],n=[_.value.findIndex(u=>u.id===t.rowId),_.value.findIndex(u=>u.id===e.rowId)].sort((u,d)=>u-d),r=[$.value.findIndex(u=>u.prop===t.colProp),$.value.findIndex(u=>u.prop===e.colProp)].sort((u,d)=>u-d);for(let u=n[0];u<=n[1];u++)for(let d=r[0];d<=r[1];d++){const c=_.value[u],m=$.value[d];c&&m&&s.push(`${c.id}|${m.prop}`)}i.value=s},Re=(t,e,s,n)=>{if(n.preventDefault(),!e.property||e.property==="name")return;if(h(e.property).isBefore(h(),"day")){x.warning("当前日期已过期，不允许修改");return}const u=`${t.id}|${e.property}`;i.value.includes(u)||(i.value=[u]),ae.value=!0,q.top=n.clientY,q.left=n.clientX,document.addEventListener("click",j)},j=()=>{ae.value=!1,document.removeEventListener("click",j)},Ye=()=>{if(i.value.length===0){x.warning("没有选中的单元格");return}if(i.value.some(e=>{const[,s]=e.split("|");return h(s).isBefore(h(),"day")})){x.warning("选中的单元格中包含已过期日期，不允许修改");return}P.shift="",P.area="",N.value=!0,j()},Le=()=>{if(i.value.length===0){x.warning("没有选中的单元格");return}if(i.value.some(e=>{const[,s]=e.split("|");return h(s).isBefore(h(),"day")})){x.warning("选中的单元格中包含已过期日期，不允许修改");return}console.log("取消所有班次",i.value),i.value.forEach(e=>{const[s,n]=e.split("|"),r=_.value.findIndex(u=>u.id==s);if(r>-1){const d={..._.value[r],[n]:{shift:"",area:"-"}};_.value[r]=d}}),x.success(`已取消 ${i.value.length} 个单元格的班次`),i.value=[],j()},Ae=()=>{if(i.value.length===0)return;const{shift:t,area:e}=P;if(!t&&!e){x.warning("请至少选择班次或区域进行应用");return}console.log("applyBatchEdit",i.value),i.value.forEach(s=>{const[n,r]=s.split("|"),u=_.value.findIndex(d=>d.id==n);if(console.log("rowIndex",u,r,n),u>-1){const d=_.value[u],m={...d[r]||{}};t&&(m.shift=t,t==="R"&&(m.area="-")),e&&m.shift!=="R"&&(m.area=e),console.log("newData",m);const g={...d,[r]:m};_.value[u]=g}}),x.success(`已批量修改 ${i.value.length} 个单元格`),N.value=!1,i.value=[]},He=t=>{if(!t||!t.shift)return"-";const e=L.value.find(s=>s.value===t.shift);return e?e.label:"-"},Fe=t=>!t||!t.area?"-":t.area,Ne=(t,e)=>{if(!t)return"";const s=`${t.id}|${e}`,n=t[e];let r=[];return i.value.includes(s)&&r.push("cell-selected"),!n||!n.shift?r.push("cell-empty"):r.push(`cell-shift-${n.shift.toLowerCase()}`),r.join(" ")},je=(t,e)=>{if(!t)return!1;const s=t[e];return s&&s.conflict};Ge(()=>{de(),pe(),document.addEventListener("mouseup",W),document.addEventListener("mouseleave",W)}),We(()=>{document.removeEventListener("mouseup",W),document.removeEventListener("mouseleave",W),document.removeEventListener("click",j)});const J=f(!1),Ke=()=>{J.value=!0};return(t,e)=>{const s=y("el-date-picker"),n=y("el-form-item"),r=y("el-option"),u=y("el-select"),d=y("el-input"),c=y("el-button"),m=y("el-form"),g=y("el-col"),oe=y("el-row"),ve=y("el-icon"),R=y("el-table-column"),me=y("el-table"),Y=y("el-dialog"),Te=y("el-transfer"),Qe=y("el-link"),qe=y("el-upload");return v(),k("div",pl,[a(m,{model:D,ref:"queryForm",inline:!0,class:"filter-container","label-position":"right","label-width":"auto"},{default:o(()=>[a(n,{label:"排班日期"},{default:o(()=>[a(s,{style:{width:"220px"},modelValue:w.value,"onUpdate:modelValue":e[0]||(e[0]=l=>w.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:G},null,8,["modelValue"])]),_:1}),a(n,{label:"科室/部门",prop:"deptIds"},{default:o(()=>[a(u,{modelValue:D.deptIds,"onUpdate:modelValue":e[1]||(e[1]=l=>D.deptIds=l),placeholder:"请选择",clearable:"",multiple:"",style:{width:"220px"}},{default:o(()=>[(v(!0),k(I,null,M(be.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"区域",prop:"areaIds"},{default:o(()=>[a(u,{modelValue:D.areaIds,"onUpdate:modelValue":e[2]||(e[2]=l=>D.areaIds=l),placeholder:"请选择",clearable:"",multiple:"",style:{width:"220px"}},{default:o(()=>[(v(!0),k(I,null,M(Z.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"班次",prop:"shiftIds"},{default:o(()=>[a(u,{modelValue:D.shiftIds,"onUpdate:modelValue":e[3]||(e[3]=l=>D.shiftIds=l),placeholder:"请选择",clearable:"",multiple:"",style:{width:"220px"}},{default:o(()=>[(v(!0),k(I,null,M(L.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"人员",prop:"person"},{default:o(()=>[a(d,{modelValue:D.person,"onUpdate:modelValue":e[4]||(e[4]=l=>D.person=l),placeholder:"姓名/工号",clearable:"",style:{width:"220px"}},null,8,["modelValue"])]),_:1}),a(n,{label:"状态",prop:"status"},{default:o(()=>[a(u,{modelValue:D.status,"onUpdate:modelValue":e[5]||(e[5]=l=>D.status=l),placeholder:"请选择",clearable:"",style:{width:"220px"}},{default:o(()=>[(v(!0),k(I,null,M(_e.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,null,{default:o(()=>[a(c,{type:"primary",icon:S(Je),onClick:Ve},{default:o(()=>[b("搜索")]),_:1},8,["icon"]),a(c,{icon:S(Xe),onClick:ke},{default:o(()=>[b("重置")]),_:1},8,["icon"])]),_:1})]),_:1},8,["model"]),a(oe,{gutter:10,class:"mb8",justify:"end"},{default:o(()=>[a(g,{span:1.5},{default:o(()=>[a(c,{type:"primary",icon:S(Ze),onClick:Ie},{default:o(()=>[b("增加人员")]),_:1},8,["icon"])]),_:1}),a(g,{span:1.5},{default:o(()=>[a(c,{type:"info",plain:"",icon:S(el),onClick:xe},{default:o(()=>[b("导入")]),_:1},8,["icon"])]),_:1}),a(g,{span:1.5},{default:o(()=>[a(c,{type:"success",plain:"",icon:S(ll),onClick:Se},{default:o(()=>[b("工时统计")]),_:1},8,["icon"])]),_:1}),a(g,{span:1.5},{default:o(()=>[a(c,{type:"info",plain:"",icon:S(al),onClick:Ke},{default:o(()=>[b("排班使用说明")]),_:1},8,["icon"])]),_:1})]),_:1}),p("div",cl,[a(c,{type:"primary",plain:"",icon:S(tl),onClick:De},{default:o(()=>[b("上一周期")]),_:1},8,["icon"]),p("h3",null,X(se.value)+"年"+X(ue.value)+"月排班表",1),a(c,{type:"primary",plain:"",onClick:ge},{default:o(()=>[b("下一周期"),a(ve,{class:"el-icon--right"},{default:o(()=>[a(S(ol))]),_:1})]),_:1})]),a(me,{data:_.value,border:"",style:{width:"100%"},onCellMouseover:fe,ref:"scheduleTable"},{default:o(()=>[a(R,{prop:"name",label:"姓名",width:"100",fixed:""}),(v(!0),k(I,null,M($.value,l=>(v(),E(R,{key:l.prop,prop:l.prop,label:l.label,width:"120",align:"center"},{default:o(C=>[p("div",{class:nl(Ne(C.row,l.prop)),onMousedown:V=>Be(C.row,{property:l.prop},V.target,V),onMouseover:V=>fe(C.row,{property:l.prop},V.target,V),onClick:V=>Ue(C.row,{property:l.prop},V.target,V),onDblclick:V=>$e(C.row,{property:l.prop}),onContextmenu:V=>Re(C.row,{property:l.prop},V.target,V),style:{cursor:"pointer","user-select":"none"}},[p("div",null,X(He(C.row[l.prop])),1),p("div",null,X(Fe(C.row[l.prop])),1),je(C.row,l.prop)?(v(),k("div",vl)):he("",!0)],42,fl)]),_:2},1032,["prop","label"]))),128))]),_:1},8,["data"]),ae.value?(v(),k("div",{key:0,style:ul({top:q.top+"px",left:q.left+"px"}),class:"context-menu",onClick:e[6]||(e[6]=sl(()=>{},["stop"]))},[p("ul",{class:"context-menu-list"},[p("li",{onClick:Ye},"设置班次"),p("li",{onClick:Le},"取消班次")])],4)):he("",!0),a(Y,{modelValue:A.value,"onUpdate:modelValue":e[10]||(e[10]=l=>A.value=l),title:"设置排班",width:"300px"},{footer:o(()=>[p("span",null,[a(c,{onClick:e[9]||(e[9]=l=>A.value=!1)},{default:o(()=>[b("取 消")]),_:1}),a(c,{type:"primary",onClick:Pe},{default:o(()=>[b("确 定")]),_:1})])]),default:o(()=>[a(m,{model:U,"label-width":"60px"},{default:o(()=>[a(n,{label:"班次"},{default:o(()=>[a(u,{modelValue:U.shift,"onUpdate:modelValue":e[7]||(e[7]=l=>U.shift=l),placeholder:"请选择班次"},{default:o(()=>[(v(!0),k(I,null,M(L.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"区域"},{default:o(()=>[a(u,{modelValue:U.area,"onUpdate:modelValue":e[8]||(e[8]=l=>U.area=l),placeholder:"请选择区域"},{default:o(()=>[a(r,{label:"-",value:"-"}),(v(!0),k(I,null,M(Z.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(Y,{modelValue:N.value,"onUpdate:modelValue":e[14]||(e[14]=l=>N.value=l),title:"批量设置排班",width:"300px"},{footer:o(()=>[p("span",null,[a(c,{onClick:e[13]||(e[13]=l=>N.value=!1)},{default:o(()=>[b("取 消")]),_:1}),a(c,{type:"primary",onClick:Ae},{default:o(()=>[b("确 定")]),_:1})])]),default:o(()=>[a(m,{model:P,"label-width":"60px"},{default:o(()=>[a(n,{label:"班次"},{default:o(()=>[a(u,{modelValue:P.shift,"onUpdate:modelValue":e[11]||(e[11]=l=>P.shift=l),placeholder:"请选择班次",clearable:""},{default:o(()=>[(v(!0),k(I,null,M(L.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"区域"},{default:o(()=>[a(u,{modelValue:P.area,"onUpdate:modelValue":e[12]||(e[12]=l=>P.area=l),placeholder:"请选择区域",clearable:""},{default:o(()=>[a(r,{label:"-",value:"-"}),(v(!0),k(I,null,M(Z.value,l=>(v(),E(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(Y,{modelValue:H.value,"onUpdate:modelValue":e[17]||(e[17]=l=>H.value=l),title:"增加待排班人员",width:"600px"},{footer:o(()=>[p("span",null,[a(c,{onClick:e[16]||(e[16]=l=>H.value=!1)},{default:o(()=>[b("取 消")]),_:1}),a(c,{type:"primary",onClick:Ee},{default:o(()=>[b("确 定")]),_:1})])]),default:o(()=>[a(Te,{modelValue:Q.value,"onUpdate:modelValue":e[15]||(e[15]=l=>Q.value=l),data:ee.value,titles:["待选列表","已选列表"],props:{key:"id",label:"name"},"filter-method":Me,filterable:""},null,8,["modelValue","data"])]),_:1},8,["modelValue"]),a(Y,{modelValue:te.value,"onUpdate:modelValue":e[19]||(e[19]=l=>te.value=l),title:"工时统计",width:"700px"},{default:o(()=>[a(m,{inline:!0},{default:o(()=>[a(n,{label:"统计日期"},{default:o(()=>[a(s,{modelValue:B.value,"onUpdate:modelValue":e[18]||(e[18]=l=>B.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),a(n,null,{default:o(()=>[a(c,{type:"primary",onClick:ce},{default:o(()=>[b("统计")]),_:1})]),_:1})]),_:1}),a(me,{data:re.value,border:""},{default:o(()=>[a(R,{prop:"name",label:"姓名"}),a(R,{prop:"workDays",label:"出勤天数"}),a(R,{prop:"restDays",label:"休息天数"}),a(R,{prop:"workHours",label:"总工时 (小时)"})]),_:1},8,["data"])]),_:1},8,["modelValue"]),a(Y,{modelValue:z.value,"onUpdate:modelValue":e[20]||(e[20]=l=>z.value=l),title:"导入排班表",width:"400px"},{default:o(()=>[p("div",null,[ml,a(Qe,{type:"primary",href:"/template.xlsx",download:""},{default:o(()=>[b("下载模板")]),_:1}),a(qe,{class:"upload-demo",drag:"",action:"https://jsonplaceholder.typicode.com/posts/","on-success":Ce,style:{"margin-top":"20px"}},{default:o(()=>[a(ve,{class:"el-icon--upload"},{default:o(()=>[a(S(rl))]),_:1}),hl]),_:1})])]),_:1},8,["modelValue"]),a(Y,{modelValue:J.value,"onUpdate:modelValue":e[22]||(e[22]=l=>J.value=l),title:"排班使用说明",width:"600px"},{footer:o(()=>[a(c,{type:"primary",onClick:e[21]||(e[21]=l=>J.value=!1)},{default:o(()=>[b("知道了")]),_:1})]),default:o(()=>[bl]),_:1},8,["modelValue"])])}}},wl=ze(_l,[["__scopeId","data-v-494c659d"]]);export{wl as default};
