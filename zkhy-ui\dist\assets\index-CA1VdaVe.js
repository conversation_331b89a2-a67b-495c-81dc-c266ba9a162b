import{A as M,l as T,d as W}from"./addElderClothingWashRecord-DaDLXpp3.js";import{g as $}from"./roommanage-DBG5TiIR.js";import{_ as A,d as Y,r as d,F as H,e as i,c as C,o as v,f as e,k as y,h as o,i as j,n as c,t as F,j as I,E as O,G as h}from"./index-B0qHf98Y.js";import"./index-CCXF19OR.js";import"./leave-Dd4WELmg.js";const G={class:"elder-clothing-wash-record-container"},q={class:"button-group",style:{"text-align":"right"}},J={key:0,class:"pagination-container"},K={__name:"index",setup(Q){const{proxy:g}=Y(),N=d([]);d([]);const a=d({pageSize:10,pageNum:1}),_=d([]),m=d(0),k=()=>{console.log("查询",a.value),a.value.pageNum=1,p()},f=()=>{a.value={pageSize:10,pageNum:1},p()},S=()=>{g.$refs.addElderClothingWashRecordRef.openAdd()},x=t=>{g.$refs.addElderClothingWashRecordRef.openView(t)},V=t=>{g.$refs.addElderClothingWashRecordRef.openReceive(t)},R=t=>{console.log("删除",t),O.confirm("确定删除该衣物清洗记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await W(t.id)).code==200?(h.success("删除成功"),a.value.pageNum=1,p()):h.error("删除失败")}).catch(()=>{h.info("已取消删除")})},E=t=>{a.value.pageSize=t,p()},z=t=>{a.value.pageNum=t,p()},p=async()=>{D();const t=await T({...a.value});_.value=t.rows||[],m.value=t.total||0},D=async()=>{const t=await $();N.value=t.rows||[]};return H(()=>{p()}),(t,r)=>{const b=i("el-input"),u=i("el-form-item"),P=i("el-date-picker"),s=i("el-button"),B=i("el-form"),l=i("el-table-column"),U=i("el-table"),L=i("el-pagination");return v(),C("div",G,[e(B,{inline:!0,model:a.value,class:"search-form","label-width":"100px"},{default:o(()=>[e(u,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(b,{modelValue:a.value.elderName,"onUpdate:modelValue":r[0]||(r[0]=n=>a.value.elderName=n),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"清洗日期",prop:"washDate"},{default:o(()=>[e(P,{modelValue:a.value.washDate,"onUpdate:modelValue":r[1]||(r[1]=n=>a.value.washDate=n),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(u,{label:"房间号",prop:"roomNumber"},{default:o(()=>[e(b,{modelValue:a.value.roomNumber,"onUpdate:modelValue":r[2]||(r[2]=n=>a.value.roomNumber=n),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"床号",prop:"bedNumber"},{default:o(()=>[e(b,{modelValue:a.value.bedNumber,"onUpdate:modelValue":r[3]||(r[3]=n=>a.value.bedNumber=n),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),j("div",q,[e(s,{type:"primary",onClick:k,icon:"search"},{default:o(()=>[c("查询")]),_:1}),e(s,{onClick:f,icon:"refresh"},{default:o(()=>[c("重置")]),_:1}),e(s,{icon:"Plus",type:"primary",onClick:S,plain:""},{default:o(()=>[c("新增送洗")]),_:1})])]),_:1},8,["model"]),e(U,{data:_.value,border:"",style:{width:"100%"}},{default:o(()=>[e(l,{prop:"id",label:"序号",width:"60",align:"center"},{default:o(n=>[c(F(n.$index+1),1)]),_:1}),e(l,{prop:"elderName",label:"姓名",align:"center"}),e(l,{prop:"roomNumber",label:"房间号",align:"center"}),e(l,{prop:"bedNumber",label:"床号",align:"center"}),e(l,{prop:"washDate",label:"清洗日期",align:"center","min-width":"120"}),e(l,{label:"送洗衣服",align:"center"},{default:o(()=>[e(l,{prop:"sendCoat",label:"外套",align:"center"}),e(l,{prop:"sendShirt",label:"衬衣",align:"center"}),e(l,{prop:"sendLongSleeve",label:"秋衣",align:"center"}),e(l,{prop:"sendLongPants",label:"秋裤",align:"center"}),e(l,{prop:"sendUnderwear",label:"内裤",align:"center"}),e(l,{prop:"sendTrousers",label:"外裤",align:"center"}),e(l,{prop:"sendSheet",label:"床单",align:"center"}),e(l,{prop:"sendCover",label:"被罩",align:"center"}),e(l,{prop:"sendPillowcase",label:"枕套",align:"center"}),e(l,{prop:"sendSheetMiddle",label:"中单",align:"center"}),e(l,{prop:"sendSocks",label:"袜子",align:"center"}),e(l,{prop:"sendOther",label:"其他",align:"center"}),e(l,{prop:"sendTotal",label:"总计",align:"center"}),e(l,{prop:"sendHandoverPerson",label:"交接人",align:"center"}),e(l,{prop:"sendReceivePerson",label:"接收人",align:"center"})]),_:1}),e(l,{label:"收回衣服",align:"center"},{default:o(()=>[e(l,{prop:"receiveCoat",label:"外套",align:"center"}),e(l,{prop:"receiveShirt",label:"衬衣",align:"center"}),e(l,{prop:"receiveLongSleeve",label:"秋衣",align:"center"}),e(l,{prop:"receiveLongPants",label:"秋裤",align:"center"}),e(l,{prop:"receiveUnderwear",label:"内裤",align:"center"}),e(l,{prop:"receiveTrousers",label:"外裤",align:"center"}),e(l,{prop:"receiveSheet",label:"床单",align:"center"}),e(l,{prop:"receiveCover",label:"被罩",align:"center"}),e(l,{prop:"receivePillowcase",label:"枕套",align:"center"}),e(l,{prop:"receiveSheetMiddle",label:"中单",align:"center"}),e(l,{prop:"receiveSocks",label:"袜子",align:"center"}),e(l,{prop:"receiveOther",label:"其他",align:"center"}),e(l,{prop:"receiveTotal",label:"总计",align:"center"}),e(l,{prop:"receiveHandoverPerson",label:"交接人",align:"center"}),e(l,{prop:"receiveReceivePerson",label:"接收人",align:"center"})]),_:1}),e(l,{label:"操作","min-width":"220",fixed:"right",align:"center"},{default:o(n=>[e(s,{link:"",type:"primary",onClick:w=>x(n.row),icon:"Search"},{default:o(()=>[c("查看")]),_:2},1032,["onClick"]),n.row.receiveHandoverPerson?y("",!0):(v(),I(s,{key:0,link:"",type:"primary",onClick:w=>V(n.row),icon:"Edit"},{default:o(()=>[c("收回衣服")]),_:2},1032,["onClick"])),e(s,{link:"",type:"primary",onClick:w=>R(n.row),icon:"Delete"},{default:o(()=>[c("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),m.value>0?(v(),C("div",J,[e(L,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":r[4]||(r[4]=n=>a.value.pageNum=n),"page-size":a.value.pageSize,"onUpdate:pageSize":r[5]||(r[5]=n=>a.value.pageSize=n),"page-sizes":[10,20,30,40],total:m.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:E,onCurrentChange:z},null,8,["current-page","page-size","total"])])):y("",!0),e(M,{ref:"addElderClothingWashRecordRef",onSuccess:f},null,512)])}}},ne=A(K,[["__scopeId","data-v-605ae219"]]);export{ne as default};
