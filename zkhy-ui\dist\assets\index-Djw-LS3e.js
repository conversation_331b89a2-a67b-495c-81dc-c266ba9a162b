import{X as te,_ as be,r as h,C as fe,N as we,d as ye,e as p,I as ke,c as y,o as _,f as e,i as l,h as t,l as n,K as L,L as Y,j as b,n as I,J as $,k as S,t as u,O as ae,bk as Ne,v as Ve,x as De}from"./index-B0qHf98Y.js";import{g as Ie,a as xe}from"./roommanage-DBG5TiIR.js";function Ce(w){return te({url:"/eldersystem/checkin/aggregate/info/listHisCheckin",method:"post",data:w})}function Te(w,N){return te({url:"/eldersystem/checkin/aggregate/info/listCurrentCheckin?pageNum="+N.pageNum+"&pageSize="+N.pageSize,method:"post",data:w})}const c=w=>(Ve("data-v-829f2e3d"),w=w(),De(),w),Le={class:"app-container"},Ye=c(()=>l("div",{style:{margin:"10px",width:"100%"}},null,-1)),Se=c(()=>l("div",{style:{margin:"10px",width:"100%"}},null,-1)),Ue={class:"tab-container"},Be={class:"detail-dialog",style:{overflow:"hidden","max-height":"600px"}},Oe={class:"section"},ze=c(()=>l("div",{class:"section-title"},"房间信息",-1)),Me={class:"detail-content"},$e={class:"detail-item"},Fe=c(()=>l("span",{class:"detail-label"},"房间号：",-1)),He={class:"detail-value"},Pe={class:"detail-item"},je=c(()=>l("span",{class:"detail-label"},"楼栋信息：",-1)),qe={class:"detail-value"},Qe={class:"detail-item"},Re=c(()=>l("span",{class:"detail-label"},"楼层信息：",-1)),Ae={class:"detail-value"},Ee={class:"detail-item"},Je=c(()=>l("span",{class:"detail-label"},"负责人：",-1)),Ke={class:"detail-value"},Xe={class:"detail-item"},Ge=c(()=>l("span",{class:"detail-label"},"房间类型：",-1)),We={class:"detail-value"},Ze={class:"detail-item"},el=c(()=>l("span",{class:"detail-label"},"房间朝向：",-1)),ll={class:"detail-value"},al={class:"detail-item"},tl=c(()=>l("span",{class:"detail-label"},"床位数：",-1)),ol={class:"detail-value"},nl={class:"section"},sl=c(()=>l("div",{class:"section-title"},"老人信息",-1)),il={class:"detail-content elder-info"},dl={class:"elder-details"},ul={key:0,class:"detail-row"},rl={class:"detail-item"},cl=c(()=>l("span",{class:"detail-label"},"床  位  号：",-1)),pl={class:"detail-value"},_l={class:"detail-row"},ml={class:"detail-item"},vl=c(()=>l("span",{class:"detail-label"},"老人编号：",-1)),hl={class:"detail-value"},gl={class:"detail-item"},bl=c(()=>l("span",{class:"detail-label"},"身份证号：",-1)),fl={class:"detail-value"},wl={class:"detail-row"},yl={class:"detail-item"},kl=c(()=>l("span",{class:"detail-label"},"能力等级：",-1)),Nl={class:"detail-value"},Vl={class:"detail-item"},Dl=c(()=>l("span",{class:"detail-label"},"性       别：",-1)),Il={class:"detail-value"},xl={class:"detail-row"},Cl={class:"detail-item"},Tl=c(()=>l("span",{class:"detail-label"},"护理等级：",-1)),Ll={class:"detail-value"},Yl={class:"detail-item"},Sl=c(()=>l("span",{class:"detail-label"},"年       龄：",-1)),Ul={class:"detail-value"},Bl={class:"detail-row"},Ol={class:"detail-item"},zl=c(()=>l("span",{class:"detail-label"},"照护等级：",-1)),Ml={class:"detail-value"},$l={class:"detail-item"},Fl=c(()=>l("span",{class:"detail-label"},"入住类型：",-1)),Hl={class:"detail-value"},Pl={class:"detail-row"},jl={class:"detail-item"},ql=c(()=>l("span",{class:"detail-label"},"入住时间：",-1)),Ql={class:"detail-value"},Rl={key:0,class:"detail-item"},Al=c(()=>l("span",{class:"detail-label"},"离院时间：",-1)),El={class:"detail-value"},Jl={class:"elder-avatar"},Kl=["src"],Xl={class:"elder-name"},Gl={class:"dialog-footer"},Wl={__name:"index",setup(w){const N=h([]),P=h([]);h([]);const oe=fe({form:{},queryParams:{elderName:null,gender:void 0,areaName:void 0,checkInDate:void 0,checkOutDate:void 0,roomNumber:null,roomType:null,floorId:null,buildingId:null},pageShow:{pageNum:1,pageSize:10},rules:{}}),{queryParams:r,form:j,rules:Zl,pageShow:v}=we(oe),U=h([]),V=h([]),F=h(0),ne=h(0),q=h(!1),g=h("current"),B=h(!1),d=h({}),{proxy:se}=ye(),{room_type:ie,room_area:de,sys_user_sex:O,check_in_type:H}=se.useDict("room_type","room_area","sys_user_sex","check_in_type");function z(){U.value=[],V.value=[],g.value=="current"?Q():g.value=="history"&&R(),Ie().then(i=>{console.log(i,"initBuilding"),N.value=i.rows})}function Q(){Te(r.value,v.value).then(i=>{console.log(i,"ressss"),U.value=i.rows,F.value=i.total})}function ue(i){console.log(i,"111"),xe(i).then(o=>{console.log(o,"getFloorListByBuild"),P.value=o.rows,N.value.map(D=>{console.log(D,"----"),D.id==i&&(j.value.buildingName=D.buildingName,console.log(j.value.buildingName,"form.value.buildingName"))})})}function re(i){U.value=[],V.value=[],v.value.pageNum=1,console.log(g.value,i,"activeTab"),i=="history"?(console.log("history"),R()):i=="current"&&(console.log("current"),Q())}function R(){Ce(r.value,v.value).then(i=>{V.value=i.rows,ne.value=i.total})}function A(){z()}function ce(i,o){if(i==null)return"-/";if(i!=null)return i;if(o==null)return"/-";if(o!=null)return o;if(i!=null&&o!=null)return i+"/"+o}function pe(){r.value={elderName:null,gender:void 0,areaName:void 0,checkInDate:void 0,checkOutDate:void 0,roomNumber:null,roomType:null,floorId:null,buildingId:null},r.pageNum=1,A()}const E=i=>{console.log(i,"row"),d.value={...i},B.value=!0};return z(),(i,o)=>{const D=p("el-input"),f=p("el-form-item"),m=p("el-col"),x=p("el-option"),C=p("el-select"),J=p("el-date-picker"),k=p("el-row"),T=p("el-button"),_e=p("el-form"),s=p("el-table-column"),M=p("dict-tag"),K=p("View"),X=p("el-icon"),G=p("el-table"),W=p("pagination"),Z=p("el-tab-pane"),me=p("el-tabs"),ve=p("el-tag"),ee=p("dict-tag-span"),he=p("el-dialog"),le=ke("loading");return _(),y("div",Le,[e(_e,{inline:!0,model:n(r),class:"search-bar","label-width":"80px"},{default:t(()=>[e(k,{gutter:10},{default:t(()=>[e(m,{span:6},{default:t(()=>[e(f,{label:"老人姓名"},{default:t(()=>[e(D,{modelValue:n(r).elderName,"onUpdate:modelValue":o[0]||(o[0]=a=>n(r).elderName=a),style:{width:"200px"},placeholder:"请输入老人姓名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:6},{default:t(()=>[e(f,{label:"性      别"},{default:t(()=>[e(C,{modelValue:n(r).gender,"onUpdate:modelValue":o[1]||(o[1]=a=>n(r).gender=a),placeholder:"请选择性别",style:{width:"200px"},clearable:""},{default:t(()=>[(_(!0),y(L,null,Y(n(O),a=>(_(),b(x,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:6},{default:t(()=>[e(f,{label:"区      域"},{default:t(()=>[e(C,{modelValue:n(r).areaName,"onUpdate:modelValue":o[2]||(o[2]=a=>n(r).areaName=a),placeholder:"请选择区域",style:{width:"200px"},clearable:""},{default:t(()=>[(_(!0),y(L,null,Y(n(de),a=>(_(),b(x,{key:a.value,label:a.label,value:a.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:6},{default:t(()=>[e(f,{label:"入住时间"},{default:t(()=>[e(J,{modelValue:n(r).checkInDate,"onUpdate:modelValue":o[3]||(o[3]=a=>n(r).checkInDate=a),type:"date",style:{width:"200px"},placeholder:"选择入住时间","value-format":"YYYY-MM-DD",value:"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),Ye,e(k,{gutter:10},{default:t(()=>[e(m,{span:6},{default:t(()=>[e(f,{label:"离院时间"},{default:t(()=>[e(J,{disabled:g.value=="current",modelValue:n(r).checkOutDate,"onUpdate:modelValue":o[4]||(o[4]=a=>n(r).checkOutDate=a),type:"date",style:{width:"200px"},placeholder:"选择离院时间","value-format":"YYYY-MM-DD",value:"YYYY-MM-DD",clearable:""},null,8,["disabled","modelValue"])]),_:1})]),_:1}),e(m,{span:6},{default:t(()=>[e(f,{label:"楼栋信息"},{default:t(()=>[e(C,{modelValue:n(r).buildingId,"onUpdate:modelValue":o[5]||(o[5]=a=>n(r).buildingId=a),style:{width:"200px"},clearable:"",onChange:ue},{default:t(()=>[(_(!0),y(L,null,Y(N.value,a=>(_(),b(x,{key:a.value,label:a.buildingName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:6},{default:t(()=>[e(f,{label:"楼层层数"},{default:t(()=>[e(C,{modelValue:n(r).floorId,"onUpdate:modelValue":o[6]||(o[6]=a=>n(r).floorId=a),style:{width:"200px"},clearable:"",onChange:i.getRoomListByfloor},{default:t(()=>[(_(!0),y(L,null,Y(P.value,a=>(_(),b(x,{key:a.value,label:a.floorName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1})]),_:1}),e(m,{span:6},{default:t(()=>[e(f,{label:"房间类型"},{default:t(()=>[e(C,{modelValue:n(r).roomType,"onUpdate:modelValue":o[7]||(o[7]=a=>n(r).roomType=a),placeholder:"请选择房间类型",style:{width:"200px"},clearable:""},{default:t(()=>[(_(!0),y(L,null,Y(n(ie),a=>(_(),b(x,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),Se,e(k,null,{default:t(()=>[e(m,{span:6},{default:t(()=>[e(f,{label:"房  间  号"},{default:t(()=>[e(D,{modelValue:n(r).roomNumber,"onUpdate:modelValue":o[8]||(o[8]=a=>n(r).roomNumber=a),style:{width:"200px"},placeholder:"请输入房间号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(k,{justify:"end",style:{height:"10px"}},{default:t(()=>[e(T,{type:"primary",onClick:A},{default:t(()=>[I("查 询")]),_:1}),e(T,{onClick:pe},{default:t(()=>[I("重 置")]),_:1})]),_:1})]),_:1},8,["model"]),l("div",Ue,[e(me,{modelValue:g.value,"onUpdate:modelValue":o[15]||(o[15]=a=>g.value=a),onTabChange:re},{default:t(()=>[e(Z,{label:"当前入住",name:"current"},{default:t(()=>[$((_(),b(G,{data:U.value,border:"",style:{width:"100%"}},{default:t(()=>[e(s,{type:"index",label:"序号",width:"60",align:"center"}),e(s,{prop:"elderName",label:"老人姓名",align:"center",width:"100"}),e(s,{prop:"elderCode",label:"老人编号",align:"center",width:"100"}),e(s,{prop:"gender",label:"性别",align:"center",width:"60"},{default:t(a=>[e(M,{options:n(O),value:a.row.gender},null,8,["options","value"])]),_:1}),e(s,{prop:"age",label:"年龄",align:"center",width:"60"}),e(s,{prop:"abilityLevel",label:"能力等级",align:"center",width:"100"}),e(s,{prop:"careLevel",label:"护理等级",align:"center",width:"100"}),e(s,{prop:"bedNumber",label:"床位号",align:"center",width:"100"}),e(s,{prop:"roomNumber",label:"房间号",align:"center",width:"100"}),e(s,{prop:"floorName",label:"楼栋层数",align:"center",width:"100"}),e(s,{prop:"roomType",label:"房间类型",align:"center",width:"100"}),e(s,{prop:"areaName",label:"区域",align:"center",width:"100"}),e(s,{prop:"area",label:"床位数",align:"center",width:"100"},{default:t(a=>[l("span",null,u(a.row.bedUsdCount)+"/"+u(a.row.capacity),1)]),_:1}),e(s,{prop:"buildingName",label:"楼栋信息",align:"center",width:"100"}),e(s,{prop:"checkInDate",label:"入住时间",align:"center",width:"140"},{default:t(a=>[l("span",null,u(i.parseTime(a.row.checkInDate,"{y}-{m}-{d}")),1)]),_:1}),g.value==="history"?(_(),b(s,{key:0,prop:"checkOutDate",label:"离开时间",align:"center",width:"140"},{default:t(a=>[l("span",null,u(i.parseTime(a.row.checkOutDate,"{y}-{m}-{d}")),1)]),_:1})):S("",!0),e(s,{prop:"checkInType",label:"入住类型",align:"center",width:"120"},{default:t(a=>[e(M,{options:n(H),value:a.row.checkInType},null,8,["options","value"])]),_:1}),e(s,{label:"操作",align:"center",width:"100",fixed:"right"},{default:t(a=>[e(T,{type:"primary",link:"",onClick:ge=>E(a.row)},{default:t(()=>[e(X,null,{default:t(()=>[e(K)]),_:1}),I("详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,q.value]]),$(e(W,{total:F.value,page:n(v).pageNum,"onUpdate:page":o[9]||(o[9]=a=>n(v).pageNum=a),limit:n(v).pageSize,"onUpdate:limit":o[10]||(o[10]=a=>n(v).pageSize=a),onPagination:o[11]||(o[11]=a=>z())},null,8,["total","page","limit"]),[[ae,F.value>0]])]),_:1}),e(Z,{label:"历史入住",name:"history"},{default:t(()=>[$((_(),b(G,{data:V.value,border:"",style:{width:"100%"}},{default:t(()=>[e(s,{type:"index",label:"序号",width:"60",align:"center"}),e(s,{prop:"elderName",label:"老人姓名",align:"center",width:"100"}),e(s,{prop:"elderCode",label:"老人编号",align:"center",width:"100"}),e(s,{prop:"gender",label:"性别",align:"center",width:"60"},{default:t(a=>[e(M,{options:n(O),value:a.row.gender},null,8,["options","value"])]),_:1}),e(s,{prop:"age",label:"年龄",align:"center",width:"60"}),e(s,{prop:"abilityLevel",label:"能力等级",align:"center",width:"100"}),e(s,{prop:"careLevel",label:"护理等级",align:"center",width:"100"}),e(s,{prop:"bedNumber",label:"床位号",align:"center",width:"100"}),e(s,{prop:"roomNumber",label:"房间号",align:"center",width:"100"}),e(s,{prop:"floorName",label:"楼栋层数",align:"center",width:"100"}),e(s,{prop:"roomType",label:"房间类型",align:"center",width:"100"}),e(s,{prop:"areaName",label:"区域",align:"center",width:"100"}),e(s,{prop:"buildingName",label:"楼栋信息",align:"center",width:"100"}),e(s,{prop:"checkInDate",label:"入住时间",align:"center",width:"140"},{default:t(a=>[l("span",null,u(i.parseTime(a.row.checkInDate,"{y}-{m}-{d}")),1)]),_:1}),g.value==="history"?(_(),b(s,{key:0,prop:"checkOutDate",label:"离开时间",align:"center",width:"140"},{default:t(a=>[l("span",null,u(i.parseTime(a.row.checkOutDate,"{y}-{m}-{d}")),1)]),_:1})):S("",!0),e(s,{prop:"checkInType",label:"入住类型",align:"center",width:"120"},{default:t(a=>[e(M,{options:n(H),value:a.row.checkInType},null,8,["options","value"])]),_:1}),e(s,{label:"操作",align:"center",width:"100",fixed:"right"},{default:t(a=>[e(T,{type:"primary",link:"",onClick:ge=>E(a.row)},{default:t(()=>[e(X,null,{default:t(()=>[e(K)]),_:1}),I("详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,q.value]]),$(e(W,{total:V.value,page:n(v).pageNum,"onUpdate:page":o[12]||(o[12]=a=>n(v).pageNum=a),limit:n(v).pageSize,"onUpdate:limit":o[13]||(o[13]=a=>n(v).pageSize=a),onPagination:o[14]||(o[14]=a=>z())},null,8,["total","page","limit"]),[[ae,V.value>0]])]),_:1})]),_:1},8,["modelValue"])]),e(he,{modelValue:B.value,"onUpdate:modelValue":o[17]||(o[17]=a=>B.value=a),title:"详情",width:"60%",height:"700px","append-to-body":""},{footer:t(()=>[l("div",Gl,[e(T,{onClick:o[16]||(o[16]=a=>B.value=!1)},{default:t(()=>[I("返回")]),_:1})])]),default:t(()=>[l("div",Be,[l("div",Oe,[ze,l("div",Me,[e(k,null,{default:t(()=>[l("div",$e,[Fe,l("span",He,u(d.value.roomNumber),1),d.value.careLevel?(_(),b(ve,{key:0,size:"small",type:"danger",style:{"margin-left":"10px"}},{default:t(()=>[I(u(d.value.careLevel),1)]),_:1})):S("",!0)])]),_:1}),e(k,null,{default:t(()=>[e(m,{span:8},{default:t(()=>[l("div",Pe,[je,l("span",qe,u(d.value.buildingName||"--"),1)])]),_:1}),e(m,{span:8},{default:t(()=>[l("div",Qe,[Re,l("span",Ae,u(d.value.floorName||"--")+"层",1)])]),_:1}),e(m,{span:8},{default:t(()=>[l("div",Ee,[Je,l("span",Ke,u(d.value.managerName||"--"),1)])]),_:1}),e(m,{span:8},{default:t(()=>[l("div",Xe,[Ge,l("span",We,u(d.value.roomType||"--"),1)])]),_:1}),e(m,{span:8},{default:t(()=>[l("div",Ze,[el,l("span",ll,u(d.value.roomOrientation||"--"),1)])]),_:1}),e(m,{span:8},{default:t(()=>[l("div",al,[tl,l("span",ol,u(ce(d.value.bedUsdCount,d.value.capacity)),1)])]),_:1})]),_:1})])]),l("div",nl,[sl,l("div",il,[l("div",dl,[e(k,null,{default:t(()=>[e(m,{span:16},{default:t(()=>[g.value==="current"?(_(),y("div",ul,[l("div",rl,[cl,l("span",pl,u(d.value.roomNumber+"-"+d.value.bedNumber||"--"),1)])])):S("",!0),l("div",_l,[l("div",ml,[vl,l("span",hl,u(d.value.elderCode||"--"),1)]),l("div",gl,[bl,l("span",fl,u(d.value.idCard||"--"),1)])]),l("div",wl,[l("div",yl,[kl,l("span",Nl,u(d.value.abilityLevel||"--"),1)]),l("div",Vl,[Dl,l("span",Il,[e(ee,{options:n(O),value:d.value.gender},null,8,["options","value"])])])]),l("div",xl,[l("div",Cl,[Tl,l("span",Ll,u(d.value.nursingLevel||"--"),1)]),l("div",Yl,[Sl,l("span",Ul,u(d.value.age||"--"),1)])]),l("div",Bl,[l("div",Ol,[zl,l("span",Ml,u(d.value.careLevel||"--"),1)]),l("div",$l,[Fl,l("span",Hl,[e(ee,{options:n(H),value:d.value.checkInType},null,8,["options","value"])])])]),l("div",Pl,[l("div",jl,[ql,l("span",Ql,u(d.value.checkInDate||"--"),1)]),g.value==="history"?(_(),y("div",Rl,[Al,l("span",El,u(d.value.checkOutDate||"--"),1)])):S("",!0)])]),_:1}),e(m,{span:6},{default:t(()=>[l("div",Jl,[l("img",{src:d.value.avatar||n(Ne),alt:"老人头像"},null,8,Kl),l("div",Xl,u(d.value.elderName),1)])]),_:1})]),_:1})])])])])]),_:1},8,["modelValue"])])}}},aa=be(Wl,[["__scopeId","data-v-829f2e3d"]]);export{aa as default};
