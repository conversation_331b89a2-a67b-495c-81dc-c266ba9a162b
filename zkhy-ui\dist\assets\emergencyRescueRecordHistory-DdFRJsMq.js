import{_ as Q,d as W,r as y,a as G,u as O,w as J,F as K,e as u,c as R,o as S,f as l,i as e,h as a,n as m,l as t,t as o,k as X,D as Z,aR as ee,aS as te,E as le,aT as ne,G as T,v as oe,x as ae}from"./index-B0qHf98Y.js";const _=b=>(oe("data-v-be3272f5"),b=b(),ae(),b),se={class:"log-review-container"},ie={class:"paginationBox"},ce={class:"nurse-log"},de=_(()=>e("h2",{class:"titleLog"},"老人意外情况记录表",-1)),re={class:"table-style"},pe={style:{"text-align":"left"}},ue={style:{"text-align":"left"}},_e={style:{"text-align":"left"}},me={style:{"text-align":"left"}},ge={style:{"text-align":"left"}},he={style:{"text-align":"left"}},fe={style:{"text-align":"left"}},ye={style:{"text-align":"left"}},xe={style:{"text-align":"left"}},be={style:{"text-align":"left"},colspan:"3"},ve={style:{"text-align":"left"},colspan:"3"},we={class:"itemDetail"},De=_(()=>e("span",null,"意外发生地址:",-1)),Ne={style:{"text-align":"left"},colspan:"3"},ke={class:"itemDetail"},Ce=_(()=>e("span",null,"伤情描述:",-1)),Ve={style:{"text-align":"left"},colspan:"3"},Re={class:"itemDetail"},Se=_(()=>e("span",null,"身体处置情况:",-1)),Te={style:{"text-align":"left"},colspan:"3"},ze={class:"itemDetail"},Le=_(()=>e("span",null,"生命体征情况:",-1)),Be={style:{"text-align":"left"},colspan:"3"},Ye={class:"itemDetail"},He=_(()=>e("span",null,"送往医院方式及医院名称:",-1)),Me={style:{"text-align":"left"},colspan:"3"},Ee={class:"itemDetail"},Ie=_(()=>e("span",null,"通知监护人情况:",-1)),Ae={style:{"text-align":"left"},colspan:"3"},$e={class:"itemDetail"},qe=_(()=>e("span",null,"发生意外情况描述:",-1)),Pe={style:{"text-align":"left"},colspan:"3"},Ue={class:"itemDetail"},je=_(()=>e("span",null,"意外处置参与人员:",-1)),Fe={style:{"text-align":"left"},colspan:"3"},Qe={class:"itemDetail"},We=_(()=>e("span",null,"谈话记录:",-1)),Ge={__name:"emergencyRescueRecordHistory",setup(b){const{proxy:z}=W(),{sys_user_sex:D}=z.useDict("sys_user_sex"),c=y({pageNum:1,pageSize:10}),L=G(),B=O(),N=y([]),k=y(0),x=y(!1),n=y(null),C=y(null),g=async()=>{const s=await ee({...c.value});N.value=s.rows,k.value=s.total},Y=()=>{c.value.pageNum=1,g()},H=()=>{c.value={pageNum:1,pageSize:10},g()},M=s=>{c.value.pageSize=s,g()},E=s=>{c.value.pageNum=s,g()},I=s=>{x.value=!0,te(s.id).then(i=>{n.value=i.data})},A=s=>{le.confirm("注：删除老人意外情况记录表将失去原始数据，请慎重删除","确定删除该老人意外情况记录表吗?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{ne(s.id).then(i=>{i.code===200?(T.success("删除成功"),c.value.pageNum=1,g()):T.error(i.msg)})})},$=()=>{const s=C.value.cloneNode(!0);s.querySelectorAll(".el-input, .el-textarea").forEach(v=>{var w;const h=((w=v.querySelector("input, textarea"))==null?void 0:w.value)||"",f=document.createElement("div");f.textContent=h,f.style.padding="8px",v.replaceWith(f)});const p=window.open("","_blank");p.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>老人意外情况记录表</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
              color:#333;
            }
            .table-style td .itemDetail{
              display: flex;
              align-items: center;              
              color:#333;
              pre{
                margin-left: 10px;
              }
            }
            .titleLog {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #D9001B;
                text-align: center;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${s.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `),p.document.close()},q=()=>{L.push("/work/nurseworkstation")};return J(()=>B.path,s=>{s==="/emergencyRescueLog/emergencyRescueRecordHistory/add/0/add"&&g()},{immediate:!0}),K(()=>{g()}),(s,i)=>{const p=u("el-button"),v=u("el-date-picker"),h=u("el-form-item"),f=u("el-input"),w=u("el-form"),r=u("el-table-column"),V=u("dict-tag-span"),P=u("el-table"),U=u("el-pagination"),j=u("el-dialog");return S(),R("div",se,[l(p,{type:"primary",onClick:q,class:"back-button"},{default:a(()=>[m(" 返回工作台 ")]),_:1}),l(w,{model:t(c),ref:"queryForm",inline:!0,class:"search-form","label-width":"120px"},{default:a(()=>[l(h,{label:"意外发生时间",prop:"accidentTime"},{default:a(()=>[l(v,{modelValue:t(c).accidentTime,"onUpdate:modelValue":i[0]||(i[0]=d=>t(c).accidentTime=d),type:"datetime",placeholder:"请选择时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(h,{label:"老人姓名",prop:"elderName"},{default:a(()=>[l(f,{style:{width:"150px"},modelValue:t(c).elderName,"onUpdate:modelValue":i[1]||(i[1]=d=>t(c).elderName=d),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),l(h,{label:"记录人",prop:"recordeName"},{default:a(()=>[l(f,{style:{width:"150px"},modelValue:t(c).recordeName,"onUpdate:modelValue":i[2]||(i[2]=d=>t(c).recordeName=d),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),l(h,null,{default:a(()=>[l(p,{type:"primary",onClick:Y,icon:"Search"},{default:a(()=>[m("查询")]),_:1}),l(p,{onClick:H,icon:"Refresh"},{default:a(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),l(P,{data:t(N),border:"",style:{width:"100%"}},{default:a(()=>[l(r,{label:"序号",width:"80",align:"center"},{default:a(d=>[m(o(d.$index+1),1)]),_:1}),l(r,{prop:"accidentTime",label:"意外发生时间",width:"150",align:"center"}),l(r,{prop:"elderName",label:"老人姓名",width:"120",align:"center"}),l(r,{prop:"gender",label:"老人性别","min-width":"120",align:"center"},{default:a(d=>[l(V,{options:t(D),value:d.row.gender,style:{width:"80%"}},null,8,["options","value"])]),_:1}),l(r,{prop:"age",label:"老人年龄","min-width":"120",align:"center"}),l(r,{prop:"roomNumber",label:"房间号","min-width":"120",align:"center"}),l(r,{prop:"accidentLocation",label:"意外发生地址","min-width":"120",align:"center"}),l(r,{prop:"paramedicName",label:"当天护理员","min-width":"120",align:"center"}),l(r,{prop:"recorderName",label:"记录人",width:"120",align:"center"}),l(r,{prop:"createTime",label:"记录时间","min-width":"180",align:"center"}),l(r,{label:"操作",width:"180",align:"center",fixed:"right"},{default:a(({row:d})=>[l(p,{type:"primary",link:"",onClick:F=>I(d)},{default:a(()=>[m("查看")]),_:2},1032,["onClick"]),l(p,{type:"primary",link:"",onClick:F=>A(d)},{default:a(()=>[m("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e("div",ie,[l(U,{background:"",onSizeChange:M,onCurrentChange:E,"current-page":t(c).pageNum,"page-sizes":[10,20,30,50],"page-size":t(c).pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t(k)},null,8,["current-page","page-size","total"])]),l(j,{title:"查看",modelValue:t(x),"onUpdate:modelValue":i[4]||(i[4]=d=>Z(x)?x.value=d:null),width:"50%"},{footer:a(()=>[l(p,{type:"primary",onClick:i[3]||(i[3]=d=>x.value=!1),plain:""},{default:a(()=>[m("返回")]),_:1}),l(p,{type:"default",onClick:$,plain:""},{default:a(()=>[m("打印")]),_:1})]),default:a(()=>[t(n)?(S(),R("div",{key:0,ref_key:"printContent",ref:C},[e("div",ce,[de,e("table",re,[e("tbody",null,[e("tr",null,[e("td",pe,"老人姓名:"+o(t(n).elderName||"-"),1),e("td",ue,[m("老人性别: "),l(V,{options:t(D),value:t(n).gender,style:{width:"80%"}},null,8,["options","value"])]),e("td",_e,"老人年龄："+o(t(n).age||"-"),1)]),e("tr",null,[e("td",me,"房间信息:"+o(t(n).buildingName+"-"+t(n).roomNumber||"-"),1),e("td",ge,"入住时间"+o(t(n).checkInDate||"-"),1),e("td",he,"能力等级："+o(t(n).abilityLevel||"-"),1)]),e("tr",null,[e("td",fe,"护理等级:"+o(t(n).careLevel||"-"),1),e("td",ye,"照护等级："+o(t(n).nursingLevel||"-"),1),e("td",xe,"当天护理员："+o(t(n).paramedicName||"-"),1)]),e("tr",null,[e("td",be,"意外发生时间:"+o(t(n).accidentTime||"-"),1)]),e("tr",null,[e("td",ve,[e("div",we,[De,e("pre",null,o(t(n).accidentLocation||"-"),1)])])]),e("tr",null,[e("td",Ne,[e("div",ke,[Ce,e("pre",null,o(t(n).injuryCondition||"-"),1)])])]),e("tr",null,[e("td",Ve,[e("div",Re,[Se,e("pre",null,o(t(n).physicalTreatment||"-"),1)])])]),e("tr",null,[e("td",Te,[e("div",ze,[Le,e("pre",null,o(t(n).vitalSigns||"-"),1)])])]),e("tr",null,[e("td",Be,[e("div",Ye,[He,e("pre",null,o(t(n).hospitalTransport||"-"),1)])])]),e("tr",null,[e("td",Me,[e("div",Ee,[Ie,e("pre",null,o(t(n).guardianNotification||"-"),1)])])]),e("tr",null,[e("td",Ae,[e("div",$e,[qe,e("pre",null,o(t(n).accidentDescription||"-"),1)])])]),e("tr",null,[e("td",Pe,[e("div",Ue,[je,e("pre",null,o(t(n).handlingParticipants||"-"),1)])])]),e("tr",null,[e("td",Fe,[e("div",Qe,[We,e("pre",null,o(t(n).conversationRecord||"-"),1)])])])])])])],512)):X("",!0)]),_:1},8,["modelValue"])])}}},Je=Q(Ge,[["__scopeId","data-v-be3272f5"]]);export{Je as default};
