import{_ as re,d as de,a as ie,r as f,F as se,e as i,I as ue,J as ce,l as d,c as y,o as v,f as o,i as s,h as t,n as h,t as V,k as me,p as pe,a1 as fe,K as L,L as T,j as E,D as _e,v as ge,x as ve,G as w,aM as he,M as we}from"./index-B0qHf98Y.js";import{a as be,g as Ve}from"./roommanage-DBG5TiIR.js";import{l as ye}from"./tLiveRoom-DmSXfHxo.js";const q=k=>(ge("data-v-c333f629"),k=k(),ve(),k),ke={class:"replace-consumables"},Ie=q(()=>s("div",{class:"headerTitle"},[s("h2",null,"紫外线消毒记录表")],-1)),Ne={style:{"text-align":"right"}},xe={class:"elder-info"},Ue={class:"info"},De={class:"roomNumber"},Ce={class:"leaderName"},Re={class:"processIndex"},Le={key:0,class:"error-message",style:{color:"red","font-size":"12px"}},Te={style:{"margin-top":"20px","text-align":"center"}},Ee={class:"room_info_top"},Se=q(()=>s("div",{class:"title_room"},[s("h3",null,"房间信息")],-1)),$e={class:"room_form"},Be={class:"roomList"},Me={__name:"uvDisinfectionRecord",setup(k){const{proxy:D}=de(),S=ie(),I=f(!1),b=f(!1),_=f({}),C=f([]),N=f([]),x=f([]),g=f([]),R=f(JSON.parse(localStorage.getItem("userInfo"))),A=f({buildingId:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼层",trigger:"change"}]}),m=f([]),$=()=>{D.$tab.closeOpenPage(),S.push("/work/nurseworkstation")},J=()=>{b.value=!0},P=async()=>{const l=await Ve();C.value=l.rows||[]},B=l=>{if(l.duration){const a=parseInt(l.duration);isNaN(a)?l.durationError="请输入有效数字":a<30?l.durationError="时长必须≥30分钟":l.durationError=""}else l.durationError="请输入消毒时长"},j=()=>{let l=!0;return m.value.forEach(a=>{B(a),a.durationError&&(l=!1)}),l},z=async l=>{N.value=[],g.value=[],x.value=[],_.value.floorId="";const a=await be(l);N.value=a.rows},G=async l=>{x.value=[],g.value=[];const a=await ye({floorId:l});x.value=a.rows},K=l=>{const a=g.value.findIndex(u=>u.roomId===l.id);a>-1?g.value.splice(a,1):g.value.push({roomId:l.id,roomName:l.roomName,roomNumber:l.roomNumber})},Q=l=>{m.value.splice(l,1)},W=async()=>{if(!j()){w.error("请修正消毒时长");return}if(m.value=m.value.map(l=>({...l,startTime:l.disinfectionTime[0],endTime:l.disinfectionTime[1]})),m.value.length==0){w.error("请填写数据！");return}else I.value=!0,(await he(m.value)).code==200?(I.value=!1,w.success("提交成功！"),D.$tab.closeOpenPage(),S.push("/work/nurseworkstation")):(I.value=!1,w.error("提交失败！"))},X=()=>{$()},Z=()=>{D.$refs.formRef.validate(async l=>{if(l){if(g.value.length==0){w.error("请选择房间");return}const a=C.value.find(c=>c.id===_.value.buildingId),u=N.value.find(c=>c.id===_.value.floorId),r=g.value.map(c=>({...c,buildingName:a.buildingName,buildingId:a.id,floorName:u.floorName,floorId:u.id,floorNumber:u.floorNumber,recordDate:we().format("YYYY-MM-DD"),monitoringResult:"正常",recorder:R.value.userName,nurseId:R.value.userId,nurseName:R.value.userName}));if(r.length>0){const c=r.filter(p=>!m.value.some(U=>U.roomId===p.roomId&&U.floorId===p.floorId));c.length===0?w.warning("所选房间已全部存在于当前列表中"):(m.value=[...m.value,...c],w.success(`成功添加${c.length}个房间`)),b.value=!1}console.log(r,"newData")}})},ee=()=>{P()};return se(()=>{ee()}),(l,a)=>{const u=i("el-button"),r=i("el-table-column"),c=i("el-date-picker"),p=i("el-input"),U=i("el-time-picker"),oe=i("el-table"),M=i("el-option"),F=i("el-select"),H=i("el-form-item"),O=i("el-col"),Y=i("el-row"),le=i("el-check-tag"),te=i("el-form"),ae=i("el-dialog"),ne=ue("loading");return ce((v(),y("div",ke,[o(u,{type:"primary",onClick:$},{default:t(()=>[h("返回工作台")]),_:1}),Ie,s("div",Ne,[o(u,{type:"primary",onClick:J},{default:t(()=>[h("+ 新增房间")]),_:1})]),o(oe,{data:d(m),border:"",style:{width:"100%"}},{default:t(()=>[o(r,{label:"房屋信息",width:"200",align:"center",prop:"avatar"},{default:t(e=>[s("div",xe,[s("div",Ue,[s("p",De,V(e.row.roomNumber),1),s("p",Ce,V(e.row.buildingName)+" "+V(e.row.floorNumber),1),s("span",Re,V(e.$index+1),1)])])]),_:1}),o(r,{prop:"recordDate",label:"服务日期","min-width":"230",align:"center"},{default:t(e=>[o(c,{modelValue:e.row.recordDate,"onUpdate:modelValue":n=>e.row.recordDate=n,type:"date",placeholder:"选择日期",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"uvLampCode",label:"紫外灯编号",width:"180",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.uvLampCode,"onUpdate:modelValue":n=>e.row.uvLampCode=n,placeholder:"请输入紫外灯编号",style:{width:"150px"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"disinfectionTime",label:"消毒时间","min-width":"240",align:"center"},{default:t(e=>[o(U,{modelValue:e.row.disinfectionTime,"onUpdate:modelValue":n=>e.row.disinfectionTime=n,"is-range":"","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"200px"},format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"duration",label:"消毒时长",width:"150",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.duration,"onUpdate:modelValue":n=>e.row.duration=n,placeholder:"≥30分钟",style:{width:"120px"},onBlur:n=>B(e.row)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),e.row.durationError?(v(),y("div",Le,V(e.row.durationError),1)):me("",!0)]),_:1}),o(r,{prop:"monitoringResult",label:"辐照强度结果",width:"130",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.monitoringResult,"onUpdate:modelValue":n=>e.row.monitoringResult=n,style:{width:"100px"},placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"disinfectionStaffName",label:"消毒人员",width:"130",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.disinfectionStaffName,"onUpdate:modelValue":n=>e.row.disinfectionStaffName=n,style:{width:"100px"},placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"supervisor",label:"监督人员","min-width":"140",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.supervisor,"onUpdate:modelValue":n=>e.row.supervisor=n,placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"disinfectionTarget",label:"消毒区域","min-width":"200",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.disinfectionTarget,"onUpdate:modelValue":n=>e.row.disinfectionTarget=n,placeholder:"请输入",rows:2,type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{prop:"remark",label:"备注","min-width":"200",align:"center"},{default:t(e=>[o(p,{modelValue:e.row.remark,"onUpdate:modelValue":n=>e.row.remark=n,placeholder:"请输入",rows:2,type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(r,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(e=>[o(u,{type:"danger",icon:d(fe),circle:"",onClick:pe(n=>Q(e.$index),["stop"])},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"]),s("div",Te,[o(u,{type:"primary",onClick:W},{default:t(()=>[h("提交")]),_:1}),o(u,{onClick:X},{default:t(()=>[h("取消")]),_:1})]),o(ae,{modelValue:d(b),"onUpdate:modelValue":a[4]||(a[4]=e=>_e(b)?b.value=e:null),title:"新增房间",width:"70%","close-on-click-modal":!1,"append-to-body":""},{footer:t(()=>[o(u,{type:"primary",onClick:a[2]||(a[2]=e=>Z())},{default:t(()=>[h("提交")]),_:1}),o(u,{onClick:a[3]||(a[3]=e=>b.value=!1)},{default:t(()=>[h("取消")]),_:1})]),default:t(()=>[o(te,{ref:"formRef",model:d(_),rules:d(A),"label-width":"120px","label-position":"left"},{default:t(()=>[s("div",Ee,[Se,s("div",$e,[o(Y,{gutter:24},{default:t(()=>[o(O,{span:8},{default:t(()=>[o(H,{label:"楼栋信息",prop:"buildingId"},{default:t(()=>[o(F,{modelValue:d(_).buildingId,"onUpdate:modelValue":a[0]||(a[0]=e=>d(_).buildingId=e),style:{width:"200px"},onChange:z},{default:t(()=>[(v(!0),y(L,null,T(d(C),e=>(v(),E(M,{key:e.value,label:e.buildingName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(O,{span:8},{default:t(()=>[o(H,{label:"楼栋层数",prop:"floorId"},{default:t(()=>[o(F,{modelValue:d(_).floorId,"onUpdate:modelValue":a[1]||(a[1]=e=>d(_).floorId=e),disabled:!d(_).buildingId,style:{width:"200px"},onChange:G},{default:t(()=>[(v(!0),y(L,null,T(d(N),e=>(v(),E(M,{key:e.value,label:e.floorName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),o(Y,{gutter:24},{default:t(()=>[s("div",Be,[(v(!0),y(L,null,T(d(x),e=>(v(),E(le,{key:e.id,checked:d(g).some(n=>n.roomId===e.id),onChange:n=>K(e)},{default:t(()=>[h(V(e.roomName),1)]),_:2},1032,["checked","onChange"]))),128))])]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])),[[ne,d(I)]])}}},Ye=re(Me,[["__scopeId","data-v-c333f629"]]);export{Ye as default};
