import Ne from"./index-CCXF19OR.js";import{r as Ie,l as G,u as J}from"./telderAttachement-C4ARfNBy.js";import{x as Ue,y as qe,z as ke,A as xe,i as Pe}from"./index-2bfkpdNb.js";import{_ as Se,d as Te,r as p,z as K,C as Ce,w as X,e as v,I as De,J as we,c as _,o as m,f as e,h as a,i as n,k as W,t as h,l as x,K as U,L as q,j as N,n as R,v as Me,x as Le,G as V}from"./index-B0qHf98Y.js";import"./leave-Dd4WELmg.js";const A=P=>(Me("data-v-d3410504"),P=P(),Le(),P),Fe={class:"addMedicationReceive"},Ee={class:"medicine-dialog"},Re={class:"section"},Ae=A(()=>n("h3",null,"老人信息",-1)),Ye={class:"value"},$e={key:0,class:"avatar-container"},ze={class:"value"},Be={class:"value"},He={class:"value"},Oe={class:"value"},Qe={class:"value"},je={class:"value"},Ge={class:"value"},Je={class:"section"},Ke=A(()=>n("h3",null,"药品信息",-1)),Xe={style:{display:"flex","align-items":"center"}},We={style:{display:"flex","align-items":"center"}},Ze={style:{display:"flex","align-items":"center"}},el={class:"section"},ll=A(()=>n("h3",null,"委托人信息",-1)),al={class:"section"},tl={class:"dialog-footer"},dl={__name:"addMedicationReceive",emits:["success"],setup(P,{expose:Z,emit:ee}){const{proxy:Y}=Te(),w=p(!1),{sys_user_sex:le,inventory_results:ae,pharmaceutical_packaging:te,pharmaceutical_properties:de}=Y.useDict("inventory_results","pharmaceutical_packaging","pharmaceutical_properties","sys_user_sex"),I=p(!1),S=p(null),b=p("view"),ue=p(null),T=p([]),C=p([]),s=K(()=>b.value==="view"),oe=K(()=>({view:"查看药品信息",add:"新增药品信息",edit:"编辑药品信息"})[b.value]),l=p({}),ie=Ce({elderName:[{required:!0,message:"请选择老人",trigger:""}],collectionTime:[{required:!0,message:"请选择收药时间",trigger:"blur"}],medicationName:[{required:!0,message:"请输入药品名称",trigger:"blur"}],specification:[{required:!0,message:"请输入药品规格",trigger:"blur"}],dosage:[{required:!0,message:"请输入用量",trigger:"blur"}],dosageUnit:[{required:!0,message:"请选择单位",trigger:"blur"}],manufacturer:[{required:!0,message:"请输入生产厂家",trigger:"blur"}],remark:[{required:!1}],medicationId:[{required:!0,message:"请输入药品编号",trigger:"blur"}],batchNumber:[{required:!0,message:"请输入药品批号",trigger:"blur"}],specificationQuantity:[{required:!0,message:"请输入规格数量",trigger:"blur"}],specificationUnit:[{required:!0,message:"请选择单位",trigger:"blur"}],administrationMethod:[{required:!0,message:"请输入服用方法",trigger:"blur"}],packaging:[{required:!0,message:"请选择药品包装",trigger:"blur"}],medicationStatus:[{required:!0,message:"请选择药品状态",trigger:"blur"}],medicationType:[{required:!0,message:"请选择药品属性",trigger:"blur"}],quantity:[{required:!0,message:"请输入药品数量",trigger:"blur"}],quantityUnit:[{required:!0,message:"请选择单位",trigger:"blur"}],expiryDate:[{required:!0,message:"请选择有效期",trigger:"blur"}],purpose:[{required:!0,message:"请输入药品用途",trigger:"blur"}],collector:[{required:!0,message:"请输入收取人",trigger:"blur"}],delegator:[{required:!0,message:"请输入委托人",trigger:"blur"}],usePeriod:[{required:!0,message:"请选择用药时段",trigger:"blur"}],delegatorPhone:[{required:!0,message:"请输入委托人电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],delegatorIdCard:[{required:!0,message:"请输入委托人身份证",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号码",trigger:"blur"}],noticeFile:[{required:!0,message:"请上传知情通知书",trigger:"change"}],medicinePhotos:[{required:!0,message:"请上传药品实物照片",trigger:"change"}]}),k=p({pageNum:1,pageSize:2e3,elderId:null}),M=p([]),re=p([]),se=p([]);p({});const L=p([{value:"毫升",label:"毫升"},{value:"毫克",label:"毫克"},{value:"克",label:"克"}]),ne=()=>{Y.$refs.elderSelectComponentRef.openElderSelect()},me=o=>{console.log(o),o&&(l.value={elderName:o.elderName,elderId:o.id,elderCode:o.elderCode,gender:o.gender,avatar:o.avatar,bedNumber:o.bedNumber,roomNumber:o.roomNumber,age:o.age,buildingName:o.buildingName,buildingId:o.buildingId,floorNumber:o.floorNumber,floorId:o.floorId,nursingLevel:o.nursingLevel,checkInDate:o.checkInDate,roomId:o.roomId,roomNumber:o.roomNumber,bedId:o.bedId,bedNumber:o.bedNumber},$(o))},$=o=>{Pe({elderId:o.id,pageSize:1e3}).then(t=>{M.value=t.data||[]})},pe=o=>{const t=M.value.find(i=>i.id===o);l.value.specification=Number(t.specification),l.value.specificationUnit=t.specificationUnit,l.value.dosage=Number(t.dosage),l.value.dosageUnit=t.dosageUnit},F=()=>{l.value={},re.value=[],se.value=[],S.value&&S.value.resetFields()},ve=async()=>{var o,t;try{if(await S.value.validate(),b.value==="add"){if(l.value.medicationId&&!(await qe(l.value.medicationId)).data){V.warning("药品编号已存在，请修改");return}const i=await ke({...l.value});i.code==200?((o=f.value)==null?void 0:o.length)>0&&J(f.value,i.data.id).then(u=>{u.code==200?V.success("新增成功"):V.error(u.msg)}):V.error(i.msg)}else if(b.value==="edit"){const i=await xe({...l.value});i.code==200?((t=f.value)==null?void 0:t.length)>0&&k.value.elderId?J(f.value,k.value.elderId).then(u=>{u.code==200?V.success("编辑成功"):V.error(u.msg)}):V.success("编辑成功"):V.error(i.msg)}I.value=!1,ce("success")}catch(i){V.warning("请填写完整信息"),console.error("表单验证失败:",i)}},ce=ee,fe=p([]),f=p([]);function z(o){var t,i;console.log(o,"handleGetFile---------"),b.value=="add"?(o&&(Array.isArray(o)?f.value=f.value.concat(o.map(u=>u.ossId)):f.value.push(o)),fe.value.push(o[0])):(((t=T.value)==null?void 0:t.length)>0&&T.value.forEach(u=>{f.value.push(u.ossId)}),((i=C.value)==null?void 0:i.length)>0&&C.value.forEach(u=>{f.value.push(u.ossId)}),o&&(Array.isArray(o)?f.value=f.value.concat(o.map(u=>u.ossId)):f.value.push(o)))}const B=(o,t)=>{Ie(o).then(i=>{G(k.value).then(u=>{const r=l.value[t].map(E=>E.id).indexOf(o);l.value[t].splice(r,1)})})};function H(o){o.id&&(w.value=!0,Ue(o.id).then(t=>{console.log(t,"res123");const i=["specification","dosage"];l.value=ge(t.data,i),k.value.elderId=t.data.id,be(k.value),b.value=="edit"&&$({id:t.data.elderId}),w.value=!1}))}function ge(o,t){const i={...o};return t.forEach(u=>{u in i&&typeof i[u]=="string"&&(i[u]=isNaN(i[u])?i[u]:Number(i[u]))}),i}const be=function(o){G(o).then(t=>{var i;(!l.value.notice_att||!l.value.medicinePhotos_att)&&(l.value.notice_att=[],l.value.medicinePhotos_att=[]),l.value.notice_att=(i=t.rows.filter(u=>u.attachmentType=="notice_att"))==null?void 0:i.map(u=>u.filePath),T.value=t.rows.filter(u=>u.attachmentType=="notice_att"),l.value.notice_att=T.value,C.value=t.rows.filter(u=>u.attachmentType=="medicinePhotos_att"),l.value.medicinePhotos_att=C.value})};return X(()=>[l.value.specification,l.value.specificationQuantity],([o,t])=>{o!==void 0&&t!==void 0&&(l.value.quantity=o*t)},{immediate:!0}),X(()=>l.value.specificationUnit,o=>{o&&(l.value.quantityUnit=o)}),Z({openView:async o=>{b.value="view",I.value=!0,F(),H(o)},openAdd:o=>{b.value="add",ue.value=null,I.value=!0,F()},openEdit:async o=>{b.value="edit",I.value=!0,F(),H(o)}}),(o,t)=>{const i=v("el-input"),u=v("el-form-item"),r=v("el-col"),E=v("dict-tag-span"),c=v("el-row"),_e=v("el-avatar"),O=v("el-date-picker"),g=v("el-option"),y=v("el-select"),D=v("el-input-number"),Q=v("ImageUpload"),Ve=v("el-form"),j=v("el-button"),ye=v("el-dialog"),he=De("loading");return we((m(),_("div",Fe,[e(ye,{title:oe.value,modelValue:I.value,"onUpdate:modelValue":t[31]||(t[31]=d=>I.value=d),width:"80%","close-on-click-modal":!1},{footer:a(()=>[n("span",tl,[e(j,{onClick:t[30]||(t[30]=d=>I.value=!1)},{default:a(()=>[R("关闭")]),_:1}),s.value?W("",!0):(m(),N(j,{key:0,type:"primary",onClick:ve},{default:a(()=>[R(" 提交 ")]),_:1}))])]),default:a(()=>[e(Ve,{ref_key:"medicineForm",ref:S,model:l.value,"label-width":"120px",rules:ie,"label-position":"left"},{default:a(()=>[n("div",Ee,[n("div",Re,[Ae,e(c,{gutter:24,class:"elder-info"},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"老人姓名",prop:"elderName"},{default:a(()=>[e(i,{modelValue:l.value.elderName,"onUpdate:modelValue":t[0]||(t[0]=d=>l.value.elderName=d),placeholder:"请输入老人姓名",readonly:"",onClick:ne,disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"老人编号",prop:"elderCode"},{default:a(()=>[n("span",Ye,h(l.value.elderCode),1)]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"性别",prop:"gender"},{default:a(()=>[e(E,{options:x(le),value:l.value.gender},null,8,["options","value"])]),_:1})]),_:1})]),_:1}),l.value.avatar?(m(),_("div",$e,[e(_e,{shape:"square",size:140,fit:"fill",src:l.value.avatar},null,8,["src"])])):W("",!0),e(c,{gutter:24,class:"elder-info"},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"床位编号",prop:"bedNumber"},{default:a(()=>[n("span",ze,h(l.value.roomNumber?l.value.roomNumber+"-"+l.value.bedNumber:l.value.bedNumber),1)]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"房间信息",prop:"roomNumber"},{default:a(()=>[n("span",Be,h(l.value.roomNumber),1)]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"年龄",prop:"age"},{default:a(()=>[n("span",He,h(l.value.age),1)]),_:1})]),_:1})]),_:1}),e(c,{gutter:24,class:"elder-info"},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"楼栋信息",prop:"buildingName"},{default:a(()=>[n("span",Oe,h(l.value.buildingName),1)]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"楼层信息",prop:"floorNumber"},{default:a(()=>[n("span",Qe,h(l.value.floorNumber),1)]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"护理等级",prop:"nursingLevel"},{default:a(()=>[n("span",je,h(l.value.nursingLevel),1)]),_:1})]),_:1})]),_:1}),e(c,{gutter:24,class:"elder-info"},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"入住时间",prop:"checkInDate"},{default:a(()=>[n("span",Ge,h(l.value.checkInDate),1)]),_:1})]),_:1})]),_:1})]),n("div",Je,[Ke,e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"收药时间",prop:"collectionTime"},{default:a(()=>[e(O,{modelValue:l.value.collectionTime,"onUpdate:modelValue":t[1]||(t[1]=d=>l.value.collectionTime=d),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"药品名称",prop:"medicationName"},{default:a(()=>[e(i,{modelValue:l.value.medicationName,"onUpdate:modelValue":t[2]||(t[2]=d=>l.value.medicationName=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"关联药品",prop:"boundId"},{default:a(()=>[e(y,{modelValue:l.value.boundId,"onUpdate:modelValue":t[3]||(t[3]=d=>l.value.boundId=d),placeholder:"请选择",disabled:s.value,onChange:pe,clearable:""},{default:a(()=>[(m(!0),_(U,null,q(M.value,d=>(m(),N(g,{key:d.id,label:`${d.medicationName}(${d.medicationId})`,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[n("div",Xe,[e(u,{label:"药品规格",prop:"specification",style:{"margin-right":"10px","margin-bottom":"0",flex:"3"}},{default:a(()=>[e(D,{modelValue:l.value.specification,"onUpdate:modelValue":t[4]||(t[4]=d=>l.value.specification=d),modelModifiers:{number:!0},placeholder:"请输入",min:0,disabled:s.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1}),e(u,{prop:"specificationUnit",style:{"margin-bottom":"0",flex:"1"},"label-width":"0"},{default:a(()=>[e(y,{modelValue:l.value.specificationUnit,"onUpdate:modelValue":t[5]||(t[5]=d=>l.value.specificationUnit=d),placeholder:"单位",disabled:s.value,style:{width:"100%"}},{default:a(()=>[(m(!0),_(U,null,q(L.value,d=>(m(),N(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})])]),_:1}),e(r,{span:8},{default:a(()=>[n("div",We,[e(u,{label:"用量",prop:"dosage",style:{"margin-right":"10px","margin-bottom":"0",flex:"3"}},{default:a(()=>[e(D,{modelValue:l.value.dosage,"onUpdate:modelValue":t[6]||(t[6]=d=>l.value.dosage=d),placeholder:"请输入",min:0,disabled:s.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1}),e(u,{prop:"dosageUnit",style:{"margin-bottom":"0",flex:"1"},"label-width":"0"},{default:a(()=>[e(y,{modelValue:l.value.dosageUnit,"onUpdate:modelValue":t[7]||(t[7]=d=>l.value.dosageUnit=d),placeholder:"单位",disabled:s.value,style:{width:"100%"}},{default:a(()=>[(m(!0),_(U,null,q(L.value,d=>(m(),N(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),R(" / 次 ")])]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"生产厂家",prop:"manufacturer"},{default:a(()=>[e(i,{modelValue:l.value.manufacturer,"onUpdate:modelValue":t[8]||(t[8]=d=>l.value.manufacturer=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"药品编号",prop:"medicationId"},{default:a(()=>[e(i,{modelValue:l.value.medicationId,"onUpdate:modelValue":t[9]||(t[9]=d=>l.value.medicationId=d),placeholder:"请输入",disabled:s.value||b.value=="edit"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"药品批号",prop:"batchNumber"},{default:a(()=>[e(i,{modelValue:l.value.batchNumber,"onUpdate:modelValue":t[10]||(t[10]=d=>l.value.batchNumber=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"规格数量",prop:"specificationQuantity"},{default:a(()=>[e(D,{modelValue:l.value.specificationQuantity,"onUpdate:modelValue":t[11]||(t[11]=d=>l.value.specificationQuantity=d),placeholder:"请输入",min:0,disabled:s.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"服用方法",prop:"administrationMethod"},{default:a(()=>[e(i,{modelValue:l.value.administrationMethod,"onUpdate:modelValue":t[12]||(t[12]=d=>l.value.administrationMethod=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"药品包装",prop:"packaging"},{default:a(()=>[e(y,{modelValue:l.value.packaging,"onUpdate:modelValue":t[13]||(t[13]=d=>l.value.packaging=d),placeholder:"选择",style:{width:"100%"},disabled:s.value},{default:a(()=>[(m(!0),_(U,null,q(x(te),d=>(m(),N(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"药品状态",prop:"medicationStatus"},{default:a(()=>[e(y,{modelValue:l.value.medicationStatus,"onUpdate:modelValue":t[14]||(t[14]=d=>l.value.medicationStatus=d),placeholder:"选择",style:{width:"100%"},disabled:s.value},{default:a(()=>[(m(!0),_(U,null,q(x(ae),d=>(m(),N(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"药品属性",prop:"medicationType"},{default:a(()=>[e(y,{modelValue:l.value.medicationType,"onUpdate:modelValue":t[15]||(t[15]=d=>l.value.medicationType=d),placeholder:"选择",style:{width:"100%"},disabled:s.value},{default:a(()=>[(m(!0),_(U,null,q(x(de),d=>(m(),N(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[n("div",Ze,[e(u,{label:"药品数量",prop:"quantity",style:{"margin-right":"10px","margin-bottom":"0",flex:"3"}},{default:a(()=>[e(D,{modelValue:l.value.quantity,"onUpdate:modelValue":t[16]||(t[16]=d=>l.value.quantity=d),placeholder:"请输入",min:0,disabled:s.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1}),e(u,{prop:"quantityUnit",style:{"margin-bottom":"0",flex:"1"},"label-width":"0"},{default:a(()=>[e(y,{modelValue:l.value.quantityUnit,"onUpdate:modelValue":t[17]||(t[17]=d=>l.value.quantityUnit=d),placeholder:"单位",disabled:s.value,style:{width:"100%"}},{default:a(()=>[(m(!0),_(U,null,q(L.value,d=>(m(),N(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})])]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"有效期",prop:"expiryDate"},{default:a(()=>[e(O,{modelValue:l.value.expiryDate,"onUpdate:modelValue":t[18]||(t[18]=d=>l.value.expiryDate=d),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"药品用途",prop:"purpose"},{default:a(()=>[e(i,{modelValue:l.value.purpose,"onUpdate:modelValue":t[19]||(t[19]=d=>l.value.purpose=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"用药时段",prop:"usePeriod"},{default:a(()=>[e(y,{modelValue:l.value.usePeriod,"onUpdate:modelValue":t[20]||(t[20]=d=>l.value.usePeriod=d),placeholder:"请选择",disabled:s.value},{default:a(()=>[e(g,{label:"餐前",value:"餐前"}),e(g,{label:"餐中",value:"餐中"}),e(g,{label:"餐后",value:"餐后"}),e(g,{label:"睡前",value:"睡前"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(u,{label:"备注事项",prop:"remark"},{default:a(()=>[e(i,{modelValue:l.value.remark,"onUpdate:modelValue":t[21]||(t[21]=d=>l.value.remark=d),placeholder:"请输入",type:"textarea",rows:2,disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),n("div",el,[ll,e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"收 取 人",prop:"collector"},{default:a(()=>[e(i,{modelValue:l.value.collector,"onUpdate:modelValue":t[22]||(t[22]=d=>l.value.collector=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(u,{label:"委 托 人",prop:"delegator"},{default:a(()=>[e(i,{modelValue:l.value.delegator,"onUpdate:modelValue":t[23]||(t[23]=d=>l.value.delegator=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"委托人电话",prop:"delegatorPhone"},{default:a(()=>[e(i,{modelValue:l.value.delegatorPhone,"onUpdate:modelValue":t[24]||(t[24]=d=>l.value.delegatorPhone=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(r,{span:8},{default:a(()=>[e(u,{label:"委托人身份证",prop:"delegatorIdCard"},{default:a(()=>[e(i,{modelValue:l.value.delegatorIdCard,"onUpdate:modelValue":t[25]||(t[25]=d=>l.value.delegatorIdCard=d),placeholder:"请输入",disabled:s.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),n("div",al,[e(u,{prop:"notice_att",label:"自带药品使用知情通知书"},{default:a(()=>[e(Q,{modelValue:l.value.notice_att,"onUpdate:modelValue":t[26]||(t[26]=d=>l.value.notice_att=d),fileData:{category:"notice_type",attachmentType:"notice_att"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:10,disabled:s.value,onSubmitParentValue:z,onRemoveAtt:t[27]||(t[27]=d=>B(d,"notice_att"))},null,8,["modelValue","disabled"])]),_:1}),e(u,{prop:"medicinePhotos_att",label:"药品实物拍照"},{default:a(()=>[e(Q,{modelValue:l.value.medicinePhotos_att,"onUpdate:modelValue":t[28]||(t[28]=d=>l.value.medicinePhotos_att=d),fileData:{category:"medicinePhotos_type",attachmentType:"medicinePhotos_att"},fileType:["jpg","png"],isShowOrEdit:!0,isShowTip:!0,fileSize:10,disabled:s.value,onSubmitParentValue:z,onRemoveAtt:t[29]||(t[29]=d=>B(d,"medicinePhotos_att"))},null,8,["modelValue","disabled"])]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(x(Ne),{ref:"elderSelectComponentRef",onSelectLerder:me},null,512)])),[[he,w.value]])}}},nl=Se(dl,[["__scopeId","data-v-d3410504"]]);export{nl as default};
