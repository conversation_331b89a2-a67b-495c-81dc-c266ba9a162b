import{_ as D,a as $,d as B,F as E,r,G as l,e as _,I as P,c as m,o as c,i as g,J as R,f as h,h as p,n as f,K as j,L as W,j as A}from"./index-B0qHf98Y.js";import{j as U}from"./contract-DgThwd93.js";import{l as L,g as M,b as S,d as V,c as z}from"./tcontractTemplate-gMd2SxUp.js";const O={class:"app-container"},q={class:"contract-header"},G={class:"left-buttons","element-loading-text":"正在上传合同模板...","element-loading-background":"rgba(0, 0, 0, 0.5)"},J={class:"right-buttons"},K={class:"preview-area"},X=["src"],H=["src","alt"],Q=["src"],Y={key:1,style:{"text-align":"center",color:"#bbb","font-size":"18px",padding:"100px 0"}},Z={__name:"index",setup(ee){const x=$(),{proxy:k}=B();E(async()=>{try{const e=await L({pageNum:1,pageSize:1,orderByColumn:"createTime",isAsc:"desc"}),t=e.rows&&e.rows.length>0?e.rows[0]:null;if(!t)return;const a=await M({elderId:t.id,category:"contract_manage",attachmentType:"contract_template"});a.rows&&a.rows.length>0?n.value=a.rows.map(o=>({url:o.filePath,name:o.fileName,id:t.id})):n.value=[]}catch{n.value=[],l.error("获取最新模板或附件失败")}});const d=r(!1);r(!1),r([]),r(!0),r(!0),r(0),r([]),r(!1),r(!1),r(""),r([]),r([]),r(null);const n=r([]);function y(e){return/\.(png|jpe?g)$/i.test(e.name||e.url)}function w(e){return/\.pdf$/i.test(e.name||e.url)}function b(e){return/\.(docx?|DOCX?)$/i.test(e.name||e.url)}function v(e){return`https://api.idocv.com/view/url?url=${encodeURIComponent(e)}`}const u=r({templateName:"",templateCode:"",status:"1",file:null});async function C(e){d.value=!0;try{if(e&&e.file&&e.file.name){const t=e.file.name.replace(/\.[^/.]+$/,"");u.value.templateName=t,u.value.templateCode=t}if(u.value.templateName&&u.value.templateCode){const t={templateName:u.value.templateName,templateCode:u.value.templateCode,status:u.value.status,file:u.value.file},a=await S(t);if(console.log(a,"add"),a.code===200){const o=new FormData;o.append("elderId",a.data.id),o.append("category","contract_manage"),o.append("attachmentType","contract_template"),o.append("file",e.file);try{const i=await U(o,"");i.data&&i.data.filePath?(n.value[0]={url:i.data.filePath,name:i.data.fileName,id:a.data.id},l.success("上传成功")):l.error("上传失败"),e.onSuccess&&e.onSuccess(i.data)}catch(i){l.error("上传失败"),e.onError&&e.onError(i)}}}}catch(t){l.error("上传异常"),e.onError&&e.onError(t)}finally{d.value=!1}}function I(){const e=n.value[0];if(!e||!e.url){l.warning("暂无可打印的合同文件");return}const t=window.open("","_blank");if(!t){l.error("浏览器阻止了弹窗，请允许弹窗后重试");return}if(w(e))t.document.write(`
      <html>
        <head><title>打印</title></head>
        <body style="margin:0">
          <iframe id="printFrame" src="${e.url}" style="width:100vw;height:100vh;border:none;"></iframe>
          <script>
            var iframe = document.getElementById('printFrame');
            iframe.onload = function() {
              setTimeout(function() {
                iframe.contentWindow.focus();
                iframe.contentWindow.print();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `);else if(b(e)){const a=v(e.url);t.location.href=a}else y(e)?t.document.write(`
      <html>
        <head><title>打印</title></head>
        <body style="margin:0">
          <img src="${e.url}" style="max-width:100vw;max-height:100vh;display:block;margin:auto;" onload="setTimeout(function(){window.print();}, 300);" />
        </body>
      </html>
    `):t.location.href=e.url}function N(){x.push("/contractManage/contractList")}function T(){if(!n.value.length){l.warning("暂无可删除的合同模板");return}const e=n.value[0].id;if(!e){l.warning("未找到合同模板ID");return}k.$modal.confirm("是否确认删除该合同模板？").then(async function(){try{await V([e]),await z(e),l.success("删除成功"),n.value=[]}catch(t){throw l.error("删除失败"),t}}).catch(()=>{})}return(e,t)=>{const a=_("el-button"),o=_("el-upload"),i=_("el-link"),F=P("loading");return c(),m("div",O,[g("div",q,[R((c(),m("div",G,[h(o,{class:"contract-uploader","http-request":C,"show-file-list":!1,disabled:d.value.value,accept:".doc,.docx,.pdf,.png,.jpg,.jpeg"},{default:p(()=>[h(a,{type:"primary",loading:d.value.value,disabled:d.value.value},{default:p(()=>[f("上传合同模板")]),_:1},8,["loading","disabled"])]),_:1},8,["disabled"]),h(a,{type:"danger",onClick:T,disabled:d.value.value},{default:p(()=>[f("删除模板")]),_:1},8,["disabled"])])),[[F,d.value.value]]),g("div",J,[h(a,{type:"primary",onClick:I,disabled:d.value.value},{default:p(()=>[f("打印")]),_:1},8,["disabled"]),h(a,{type:"primary",onClick:N},{default:p(()=>[f("返回")]),_:1})])]),g("div",K,[n.value.length?(c(!0),m(j,{key:0},W(n.value,(s,te)=>(c(),m("div",{class:"preview-item",key:s.url||s.name,style:{height:"calc(100vh - 208px)"}},[w(s)?(c(),m("iframe",{key:0,src:v(s.url)+"#toolbar=0",width:"100%",style:{height:"100%"},frameborder:"0"},null,8,X)):y(s)?(c(),m("img",{key:1,src:s.url,alt:s.name,style:{"max-width":"400px","max-height":"300px",border:"1px solid #eee"}},null,8,H)):b(s)?(c(),m("iframe",{key:2,src:v(s.url),width:"100%",style:{height:"100%"},frameborder:"0"},null,8,Q)):(c(),A(i,{key:3,href:s.url,target:"_blank"},{default:p(()=>[f("无法预览，点击下载")]),_:2},1032,["href"]))]))),128)):(c(),m("div",Y,"暂无数据预览"))])])}}},ne=D(Z,[["__scopeId","data-v-e3f52cb3"]]);export{ne as default};
