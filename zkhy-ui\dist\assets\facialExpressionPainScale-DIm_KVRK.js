import{_ as $,d as G,r as d,C as J,N as K,z as Q,e as r,c as p,o as u,f as s,h as c,i as e,k as V,K as M,L as N,Q as W,j as X,t as _,l as n,ax as Z,n as I,v as ee,x as se,G as f}from"./index-B0qHf98Y.js";import{a as le}from"./assessmentRecord-4xWX4TZA.js";import{e as te}from"./eventBus-BDDolVUG.js";const h=m=>(ee("data-v-e8f9b3af"),m=m(),se(),m),oe={class:"fps-container"},ae=h(()=>e("div",{class:"fps-header"},[e("h1",{class:"fps-title"},"面部表情疼痛量表")],-1)),ce={class:"faces-container"},re=["onClick"],ne=["innerHTML"],ie={class:"face-score"},de={class:"nrs-section"},ue=h(()=>e("h3",{class:"nrs-title"},"附录E 数字评分法（numeric rating sale,NRS）",-1)),me={class:"nrs-scale"},ve={class:"nrs-marks"},pe={class:"nrs-number"},_e=h(()=>e("div",{class:"pain-levels"},[e("div",{class:"pain-level"},"没有疼痛"),e("div",{class:"pain-level"},"中等疼痛"),e("div",{class:"pain-level"},"极度疼痛")],-1)),fe={class:"score-display"},he={class:"pain-level-text"},we={class:"form-footer"},ke={class:"assessment-comments"},ge=h(()=>e("div",{class:"comments-header"},"评估意见：",-1)),xe={key:0,class:"action-buttons"},ye={__name:"facialExpressionPainScale",props:{elderId:{type:String,default:null},isShow:{type:String,default:null},data:{type:Object,default:null}},emits:["updateList"],setup(m,{emit:be}){const{proxy:B}=G(),C=d([{score:0,label:"无痛",svg:'<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M35 65q30 15 30 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>'},{score:2,label:"轻微疼痛",svg:'<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M30 65q40 0 40 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>'},{score:4,label:"轻度疼痛",svg:'<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M30 70q40 -10 40 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>'},{score:6,label:"中度疼痛",svg:'<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="#999" stroke="#666" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#666"/><circle cx="65" cy="40" r="5" fill="#666"/><path d="M30 75q40 -20 40 0" stroke="#666" stroke-width="2" fill="none" stroke-linecap="round"/></svg>'},{score:8,label:"重度疼痛",svg:'<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M25 80q50 -30 50 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>'},{score:10,label:"极度疼痛",svg:'<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40" fill="none" stroke="#888" stroke-width="2"/><circle cx="35" cy="40" r="5" fill="#888"/><circle cx="65" cy="40" r="5" fill="#888"/><path d="M20 85q60 -40 60 0" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg>'}]),l=m,T=d([{label:"评分",value:""}]),o=d(null),L=d(""),q=d(""),E=d(""),F=J({form:{}}),{form:t}=K(F);function O(){console.log(l.data,"props"),l.isShow=="add"?console.log("add"):l.isShow=="edit"?(t.value=l.data,w.value=l.data.totalScoreValue,o.value=l.data.totalScoreValue):l.isShow=="show"&&(console.log("show"),t.value=l.data,w.value=l.data.totalScoreValue,o.value=l.data.totalScoreValue)}const w=Q(()=>o.value===null||o.value===0?"(无疼痛)":o.value<=3?"(轻度疼痛)":o.value<=6?"(中等疼痛)":"(极度疼痛)"),D=v=>{o.value=v.score},R=()=>{if(l.elderId===null){f.error("请选择老人信息");return}if(o.value===null){f.error("请选择一个疼痛等级");return}if(!t.value.assessorName||!t.value.assessmentTime){f.error("请填写评估师姓名和日期");return}t.value.itemName=JSON.stringify({type:o.value}),t.value.totalScoreValue=o.value,t.value.assessmentMethod="01",t.value.assessmentFormId="25";let v={elderId:l.elderId,assessmentFormId:25,assessmentMethod:"01",assessmentScores:[],assessmentOrgName:"和孚养老机构"};v.assessmentScores.push(t.value),le(v).then(i=>{f.success("评估表提交成功！"),te.emit("uploadListEvent",{data:"some data"}),B.$tab.closeOpenPage({path:"/assessment/assessmentRecord"})})},Y=()=>{o.value=null,L.value="",q.value="",E.value=""};return O(),(v,i)=>{const z=r("el-icon"),A=r("el-alert"),k=r("el-table-column"),P=r("el-table"),g=r("el-input"),x=r("el-card"),y=r("el-form-item"),b=r("el-col"),U=r("el-date-picker"),j=r("el-row"),H=r("el-form"),S=r("el-button");return u(),p("div",oe,[s(x,{class:"fps-card",shadow:"hover"},{header:c(()=>[ae]),default:c(()=>[e("div",ce,[(u(!0),p(M,null,N(C.value,a=>(u(),p("div",{key:a.score,class:W(["face-item",{"face-selected":o.value===a.score}]),onClick:Se=>D(a)},[e("div",{class:"face-img",innerHTML:a.svg},null,8,ne),e("div",ie,_(a.score),1),o.value===a.score?(u(),X(z,{key:0,class:"checkmark"},{default:c(()=>[s(n(Z))]),_:1})):V("",!0)],10,re))),128))]),s(A,{title:"注: 不向病人展示数字",type:"info",closable:!1,class:"note-alert"}),e("div",de,[ue,e("div",me,[e("div",ve,[(u(),p(M,null,N(11,a=>e("div",{key:a,class:"nrs-mark"},[e("div",pe,_(a-1),1)])),64))])]),_e]),s(P,{data:T.value,class:"score-table",border:"",stripe:""},{default:c(()=>[s(k,{prop:"label",label:"评分",width:"120"}),s(k,{prop:"value"},{default:c(()=>[e("div",fe,_(o.value!==null?o.value:0),1),e("div",he,_(w.value),1)]),_:1})]),_:1},8,["data"]),e("div",we,[s(H,{model:n(t),"label-width":"100px"},{default:c(()=>[e("div",ke,[s(x,{shadow:"never"},{header:c(()=>[ge]),default:c(()=>[s(g,{modelValue:n(t).assessmentOpinion,"onUpdate:modelValue":i[0]||(i[0]=a=>n(t).assessmentOpinion=a),type:"textarea",rows:4,placeholder:"请输入评估意见...",resize:"none",disabled:l.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),s(j,{gutter:20},{default:c(()=>[s(b,{span:12},{default:c(()=>[s(y,{label:"评估师姓名："},{default:c(()=>[s(g,{modelValue:n(t).assessorName,"onUpdate:modelValue":i[1]||(i[1]=a=>n(t).assessorName=a),placeholder:"请输入评估师姓名",disabled:l.isShow=="show"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),s(b,{span:12},{default:c(()=>[s(y,{label:"日期："},{default:c(()=>[s(U,{modelValue:n(t).assessmentTime,"onUpdate:modelValue":i[2]||(i[2]=a=>n(t).assessmentTime=a),type:"date",disabled:l.isShow=="show",placeholder:"选择评估日期","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),l.isShow!="show"?(u(),p("div",xe,[s(S,{type:"primary",onClick:R},{default:c(()=>[I(" 提交 ")]),_:1}),s(S,{onClick:Y},{default:c(()=>[I(" 重置 ")]),_:1})])):V("",!0)]),_:1})])}}},Ie=$(ye,[["__scopeId","data-v-e8f9b3af"]]);export{Ie as default};
