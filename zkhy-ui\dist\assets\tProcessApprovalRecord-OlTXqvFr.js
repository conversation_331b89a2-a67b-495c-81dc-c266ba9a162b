import{X as e}from"./index-B0qHf98Y.js";function t(s){return e({url:"/process/processbed/list",method:"get",params:s})}function o(s){return e({url:"/process/processbed/apply",method:"post",data:s})}function d(s){return e({url:"/process/processbed/audit",method:"post",data:s})}function a(s){return e({url:"/process/processbed/"+s,method:"delete"})}function i(s){return e({url:"/roominfo/building/list",method:"get",params:s})}function l(s){return e({url:"/roominfo/floor/list",method:"get",params:s})}function n(s){return e({url:"/process/record/listDetails",method:"get",params:s})}export{n as a,l as b,d as c,o as d,t as e,a as f,i as l};
