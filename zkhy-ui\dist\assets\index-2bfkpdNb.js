import{X as t}from"./index-B0qHf98Y.js";function o(e){return t({url:"/medication/receiveRecord",method:"post",data:e})}function a(e){return t({url:"/medication/receiveRecord/list",method:"get",params:e})}function i(e){return t({url:`/medication/receiveRecord/${e}`,method:"get"})}function n(e){return t({url:"/medication/receiveRecord",method:"put",data:e})}function d(e){return t({url:`/medication/receiveRecord/${e}`,method:"delete"})}function s(e){return t({url:`/medication/receiveRecord/checkCode/${e}`,method:"get"})}function u(e){return t({url:"/medication/preparationRecord/save",method:"post",data:e})}function c(e){return t({url:"/medication/preparationRecord/list",method:"get",params:e})}function m(e){return t({url:`/medication/preparationRecord/${e}`,method:"delete"})}function p(e){return t({url:"/medication/preparationRecord/getFormData",method:"get",params:e})}function f(e){return t({url:"/medication/useRecord/getUsePlanDayView",method:"get",params:e})}function g(e){return t({url:"/medication/useRecord/getUsePlanWeekView",method:"get",params:e})}function l(e){return t({url:"/medication/useRecord/getDayStatistics",method:"get",params:e})}function h(e){return t({url:"/medication/useRecord/getWeekStatistics",method:"get",params:e})}function v(e){return t({url:"/vhf/medication/receiveRecord/list",method:"get",params:e})}function P(e){return t({url:`/vhf/medication/receiveRecord/${e}`,method:"delete"})}function R(e){return t({url:"/vhf/medication/receiveRecord",method:"post",data:e})}function L(e){return t({url:"/vhf/medication/receiveRecord",method:"put",data:e})}function N(e){return t({url:`/vhf/medication/receiveRecord/${e}`,method:"get"})}function T(e){return t({url:`/vhf/medication/receiveRecord/checkCode/${e}`,method:"get"})}function D(e){return t({url:"/vhf/medication/receiveRecord/listElderBoundMedication",method:"get",params:e})}function H(e){return t({url:"/vhf/medication/receiveRecord/listUsePlan",method:"get",params:e})}function k(e){return t({url:"/vhf/medication/preparationRecord/save",method:"post",data:e})}function C(e){return t({url:`/vhf/medication/preparationRecord/${e}`,method:"delete"})}function y(e){return t({url:"/vhf/medication/preparationRecord/list",method:"get",params:e})}function W(e){return t({url:"/vhf/medication/preparationRecord/getFormData",method:"get",params:e})}function $(e){return t({url:"/vhf/medication/useRecord/getUsePlanDayView",method:"get",params:e})}function F(e){return t({url:"/vhf/medication/useRecord/getUsePlanWeekView",method:"get",params:e})}function U(e){return t({url:"/vhf/medication/useRecord/getDayStatistics",method:"get",params:e})}function w(e){return t({url:"/vhf/medication/useRecord/getWeekStatistics",method:"get",params:e})}export{L as A,v as B,P as C,$ as H,l as a,g as b,h as c,U as d,F as e,w as f,f as g,H as h,D as i,c as j,m as k,p as l,u as m,a as n,y as o,C as p,W as q,i as r,k as s,s as t,o as u,n as v,d as w,N as x,T as y,R as z};
