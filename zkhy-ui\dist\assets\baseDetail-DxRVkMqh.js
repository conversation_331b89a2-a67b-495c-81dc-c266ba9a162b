import{_ as He,B as We,d as Qe,C as Xe,N as Ze,r as I,aw as el,e as g,c as b,o as p,f as a,h as o,i as C,l as t,K as c,L as h,j as m,k as F,n as y,t as R,m as ll,v as al,x as ol,P as dl}from"./index-B0qHf98Y.js";import{u as ae,s as rl,g as tl}from"./telderinfo-BSpoeVyZ.js";import{g as oe}from"./paramUtil-DJB1oWef.js";import"./index-a8qYZQmS.js";import{l as de}from"./tLiveRoom-DmSXfHxo.js";import{a as re,g as te}from"./roommanage-DBG5TiIR.js";import{l as ne}from"./tLiveBed-B9bJPM9s.js";import{g as nl}from"./user-u7DySmj3.js";const $=j=>(al("data-v-4888166f"),j=j(),ol(),j),ul=$(()=>C("div",{class:"baseTitle margintopbottom10"},"基本信息",-1)),il=$(()=>C("div",{style:{height:"10px"}},null,-1)),sl=$(()=>C("div",{class:"baseTitle"},"其他信息",-1)),pl={class:"dialog-footer"},fl=$(()=>C("h4",null,"添加监护人",-1)),ml={style:{flex:"auto"}},vl=We({name:"baseDetail"}),bl=Object.assign(vl,{props:{elderId:{type:String,default:null},isShow:{type:Boolean,default:!1},crudType:{type:String,default:null}},setup(j){const{proxy:V}=Qe(),{sys_normal_disable:gl,sys_user_sex:ue,self_careability:ie,care_level:se,nursing_grade:pe,political_status:fe,residential_type:me,occupation_type:ve,educational_level:be,marital_status:ge,elderly_blood_type:ce,financial_type:he,elderly_label:cl,emergency_contact:J,relationship_elderly:hl,capability_level:_e}=V.useDict("sys_normal_disable","sys_user_sex","self_careability","care_level","nursing_grade","political_status","residential_type","occupation_type","educational_level","marital_status","elderly_blood_type","financial_type","elderly_label","emergency_contact","relationship_elderly","capability_level"),Ie=Xe({form:{elderInfo:{},guardians:[],checkIn:{}},rules:{elderName:[{required:!0,message:"请输入老人姓名",trigger:"blur",validator:(n,l,r)=>{d.value.elderInfo.elderName?r():r(new Error("老人姓名不能为空"))}},{trigger:"blur",validator:(n,l,r)=>{d.value.elderInfo.elderName.length>50?r(new Error("老人姓名长度50个字符以内")):r()}}],elderCode:[{required:!0,message:"请输入老人编号",trigger:"blur",validator:(n,l,r)=>{d.value.elderInfo.elderCode?r():r(new Error("老人编号不能为空"))}},{min:0,max:20,trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.elderCode)==null?void 0:u.length)>20?r(new Error("老老人编号长度20个字符以内")):r()}}],idCard:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.idCard)==null?void 0:u.length)>50?r(new Error("身份证号长度50个字符以内")):r()}}],age:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.age)==null?void 0:u.length)>4?r(new Error("老人年龄不能超过4位")):r()}}],phone:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.phone)==null?void 0:u.length)>50?r(new Error("老人电话长度50个字符以内")):r()}}],nation:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.nation)==null?void 0:u.length)>50?r(new Error("老人民族长度50个字符以内")):r()}}],buildingId:[{required:!0,message:"请输入楼栋信息",trigger:"change",validator:(n,l,r)=>{d.value.checkIn.buildingId?r():r(new Error("楼栋信息不能为空"))}}],floorId:[{required:!0,message:"请输入楼栋层数",trigger:"change",validator:(n,l,r)=>{d.value.checkIn.floorId?r():r(new Error("楼栋层数不能为空"))}}],roomId:[{required:!0,message:"请输入房间号",trigger:"change",validator:(n,l,r)=>{d.value.checkIn.roomId?r():r(new Error("房间号不能为空"))}}],bedId:[{required:!0,message:"请输入房间/床位",trigger:"change",validator:(n,l,r)=>{d.value.checkIn.bedId?r():r(new Error("房间/床位不能为空"))}}],homeAddress:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.homeAddress)==null?void 0:u.length)>200?r(new Error("家庭住址长度200个字符以内")):r()}}],workUnit:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.workUnit)==null?void 0:u.length)>100?r(new Error("工作单位长度100个字符以内")):r()}}],formerOccupation:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.formerOccupation)==null?void 0:u.length)>50?r(new Error("老人职业长度50个字符以内")):r()}}],hometown:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.hometown)==null?void 0:u.length)>50?r(new Error("籍贯长度50个字符以内")):r()}}],socialSecurityCode:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.socialSecurityCode)==null?void 0:u.length)>20?r(new Error("社保号码长度20个字符以内")):r()}}],economicSource:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.economicSource)==null?void 0:u.length)>50?r(new Error("经济来源长度50个字符以内")):r()}}],remark:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.remark)==null?void 0:u.length)>2e3?r(new Error("老人备注长度2000个字符以内")):r()}}],archivist:[{trigger:"blur",validator:(n,l,r)=>{var u;((u=d.value.elderInfo.archivist)==null?void 0:u.length)>50?r(new Error("老人备注长度50个字符以内")):r()}}]},jhrrules:{},queryParams:{pageNum:1,pageSize:100}}),{form:d,rules:ye,jhrrules:Ve,queryParams:H}=Ze(Ie),A=I(!1),W=I([]),U=I([]),O=I([]),T=I([]),q=I([]),B=I([]),k=I([]);I();const z=I(!1),S=I(!1),i=j,we=I([]),ke=I(),f=el("jhrform"),D=I(!1),Y=I(!1),N=I("");function M(){f.value={name:null,phone:null,address:null,relationship:null,isEmergencyContact:"否"},V.resetForm("jhrRef")}function Se(n){k.value=k.value.filter(l=>l.id!=n.id)}function Ce(){const n={path:"/elderInfo/elderFiles"};V.$tab.closeOpenPage(n)}function P(){i.crudType=="show"?(Q(),X()):i.crudType=="add"?(ze(),Ue()):i.crudType=="edit"&&(Q(),X(),z.value=!0)}function Ue(){nl().then(n=>{ke.value=n.data,console.log(n.data,"response.data"),d.value.elderInfo.archivist=n.data.nickName})}function Q(){tl(i.elderId).then(n=>{var l,r,u,s,L,v;d.value.elderInfo=n.data.elderInfo,d.value.checkIn=n.data.checkIn||{},console.log(n.data,"ressss"),k.value=n.data.guardians||{},U.value=(l=n.data.elderInfo)!=null&&l.elderTags?(r=n.data.elderInfo)==null?void 0:r.elderTags.split(","):[],console.log(n.data.checkIn,"4321432"),((u=n.data.checkIn)==null?void 0:u.roomId)!=null&&(d.value.checkIn.roomId=parseInt((s=n.data.checkIn)==null?void 0:s.roomId)),((L=n.data.checkIn)==null?void 0:L.bedId)!=null&&(d.value.checkIn.bedId=parseInt((v=n.data.checkIn)==null?void 0:v.bedId))})}function ze(){te().then(n=>{O.value=n.rows||[]})}function X(){te().then(n=>{O.value=n.rows||[]}),re().then(n=>{T.value=n.rows||[]}),de(H.value).then(n=>{console.log(n,"room"),q.value=n.rows||[]}),ne(H.value).then(n=>{console.log(n,"bed"),B.value=n.rows||[]})}function Ee(n){console.log(n,"handleBuildingChange"),d.value.elderInfo.buildingName=n;const l=O.value.filter(r=>r.id==n);re(l[0].id).then(r=>{console.log(r,"getFloorListByBuild"),T.value=r.rows})}function je(n){console.log(n,"111"),d.value.elderInfo.floorNumber=n;const l=T.value.filter(r=>r.id==n);console.log(T.value,"floorList"),console.log(l,"floorId"),de({floorId:l[0].id}).then(r=>{console.log(r,"getRoomListByBuild"),q.value=r.rows})}function Te(n){d.value.checkIn.roomId=n,ne({roomId:n,checkUsed:!0}).then(l=>{console.log(l,"getUserByRoomId"),B.value=l.rows})}function Be(n){d.value.checkIn.bedId=n,B.value.map(l=>{console.log(l,"roomitem"),l.id==n&&(d.value.checkIn.roomBed=l.bedNumber),console.log(d.value.checkIn.roomBed,"form.value.roomName")})}function De(n){U.value.splice(U.value.indexOf(n),1)}function Ne(){Y.value=!0,dl(()=>{elderInputRef.value.input.focus()})}function Z(){N.value&&U.value.push(N.value),Y.value=!1,N.value=""}function Le(){i.crudType=="show"?ae(d.value).then(n=>{V.$modal.msgSuccess("修改成功"),console.log(n,"submitForm=================");const l={path:"/elderInfo/elderFiles"};V.$tab.closeOpenPage(l),P()}):(console.log(d.value,"submitForm called"),V.$refs.telderinfoRef.validate(n=>{n&&(k.value.map(l=>{console.log(l.id,"item.id..."),l.id=!l.id||String(l.id).startsWith("tmp-")?null:l.id}),d.value.guardians=k.value,d.value.elderInfo.elderTags=U.value.join(","),B.value.map(l=>{l.id==d.value.checkIn.bedId&&d.value.checkIn}),W.value.map(l=>{l.type=="id_card_front_photo"?d.value.elderInfo.idCardFrontPhoto=l.url:l.type=="id_card_back_photo"?d.value.elderInfo.idCardBackPhoto=l.url:l.type=="avatar"&&(d.value.elderInfo.avatar=l.url)}),console.log(d.value.checkIn,"form.checkin"),d.value.checkIn.elderId=i.elderId,i.elderId?ae(d.value).then(l=>{V.$modal.msgSuccess("修改成功"),console.log(l,"submitForm=================");const r={path:"/elderInfo/elderFiles"};V.$tab.closeOpenPage(r),P()}):rl(d.value).then(l=>{V.$modal.msgSuccess("新增成功"),console.log(l,"新增成功================="),P();const r={path:"/elderInfo/elderFiles"};V.$tab.closeOpenPage(r)}))}))}function xe(){S.value=!0,D.value=!0,M()}function Fe(n){S.value=!0,f.value=n,D.value=!1}function Oe(){k.value.map(n=>{n.id==f.value.id&&(n.relationship=f.value.relationship,n.name=f.value.name,n.phone=f.value.phone,n.isEmergencyContact=f.value.isEmergencyContact,n.address=f.value.address)}),S.value=!1}function Pe(){V.$refs.jhrRef.validate(n=>{if(n){f.value.id=oe();let l=Object.assign({},f.value);k.value.push(l),S.value=!1}})}function Re(){V.$refs.jhrRef.validate(n=>{if(n){f.value.id=oe();let l=Object.assign({},f.value);k.value.push(l),M()}})}function $e(){S.value=!1,M()}function K(n){console.log(n,"handleGetFile---------"),n&&(we.value.push(n[0].ossId),W.value.push(n[0]),console.log(n[0].type,"11111"),console.log(n[0].url,"222"))}return P(),(n,l)=>{const r=g("el-input"),u=g("el-form-item"),s=g("el-col"),L=g("el-date-picker"),v=g("el-option"),_=g("el-select"),x=g("el-row"),E=g("el-table-column"),Ae=g("dict-tag"),w=g("el-button"),qe=g("el-table"),Ye=g("el-tag"),G=g("ImageUpload"),ee=g("el-form"),Me=g("el-card"),Ke=g("el-radio-button"),Ge=g("el-radio-group"),Je=g("el-drawer");return p(),b("div",null,[a(Me,{shadow:"hover"},{default:o(()=>[a(ee,{ref:"telderinfoRef",model:t(d),rules:t(ye),"label-width":"120px"},{default:o(()=>[ul,il,a(x,{gutter:15},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(u,{label:"老人姓名",prop:"elderName",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.elderName,"onUpdate:modelValue":l[0]||(l[0]=e=>t(d).elderInfo.elderName=e),placeholder:"请输入老人姓名",disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"老人编号",prop:"elderCode",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.elderCode,"onUpdate:modelValue":l[1]||(l[1]=e=>t(d).elderInfo.elderCode=e),placeholder:"请输入老人编号",disabled:i.isShow||z.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"入驻时间",prop:"checkInDate",size:"large"},{default:o(()=>[a(L,{disabled:i.isShow,modelValue:t(d).checkIn.checkInDate,"onUpdate:modelValue":l[2]||(l[2]=e=>t(d).checkIn.checkInDate=e),clearable:"",placeholder:"请选择入驻时间",style:{width:"100%"},type:"date","value-format":"YYYY-MM-DD"},null,8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"身份证号",prop:"idCard",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.idCard,"onUpdate:modelValue":l[3]||(l[3]=e=>t(d).elderInfo.idCard=e),placeholder:"请输入身份证号",disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"老人性别",prop:"gender",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).elderInfo.gender,"onUpdate:modelValue":l[4]||(l[4]=e=>t(d).elderInfo.gender=e),placeholder:"请选择",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(ue),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"老人年龄",prop:"age",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.age,"onUpdate:modelValue":l[5]||(l[5]=e=>t(d).elderInfo.age=e),modelModifiers:{number:!0},placeholder:"请输入老人年龄",disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"出生日期",prop:"birthDate",size:"large"},{default:o(()=>[a(L,{disabled:i.isShow,modelValue:t(d).elderInfo.birthDate,"onUpdate:modelValue":l[6]||(l[6]=e=>t(d).elderInfo.birthDate=e),clearable:"",placeholder:"请选择出生日期",style:{width:"100%"},type:"date","value-format":"YYYY-MM-DD"},null,8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"老人电话",prop:"phone",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.phone,"onUpdate:modelValue":l[7]||(l[7]=e=>t(d).elderInfo.phone=e),placeholder:"请输入老人电话",disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"民       族",prop:"nation",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.nation,"onUpdate:modelValue":l[8]||(l[8]=e=>t(d).elderInfo.nation=e),placeholder:"请输入民族",disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"能力等级",prop:"abilityLevel",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).checkIn.abilityLevel,"onUpdate:modelValue":l[9]||(l[9]=e=>t(d).checkIn.abilityLevel=e),placeholder:"请选择能力等级",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(_e),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"自理能力",prop:"selfCareAbility",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).checkIn.selfCareAbility,"onUpdate:modelValue":l[10]||(l[10]=e=>t(d).checkIn.selfCareAbility=e),placeholder:"请选择自理能力",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(ie),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"照护等级",prop:"careLevel",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).checkIn.careLevel,"onUpdate:modelValue":l[11]||(l[11]=e=>t(d).checkIn.careLevel=e),placeholder:"请选择照护等级"},{default:o(()=>[(p(!0),b(c,null,h(t(se),e=>(p(),m(v,{disabled:i.isShow,key:e.value,label:e.label,value:e.value},null,8,["disabled","label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"护理等级",prop:"nursingLevel",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).checkIn.nursingLevel,"onUpdate:modelValue":l[12]||(l[12]=e=>t(d).checkIn.nursingLevel=e),placeholder:"请选择护理等级",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(pe),e=>(p(),m(v,{disabled:i.isShow,key:e.value,label:e.label,value:e.value},null,8,["disabled","label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"政治面貌",prop:"politicalStatus",size:"large"},{default:o(()=>[a(_,{disabled:i.isShow,modelValue:t(d).elderInfo.politicalStatus,"onUpdate:modelValue":l[13]||(l[13]=e=>t(d).elderInfo.politicalStatus=e),placeholder:"请选择政治面貌"},{default:o(()=>[(p(!0),b(c,null,h(t(fe),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"楼栋信息",prop:"buildingId"},{default:o(()=>[a(_,{disabled:i.isShow||z.value,modelValue:t(d).checkIn.buildingId,"onUpdate:modelValue":l[14]||(l[14]=e=>t(d).checkIn.buildingId=e),style:{width:"100%"},placeholder:"全部",clearable:"",onChange:Ee},{default:o(()=>[(p(!0),b(c,null,h(O.value,e=>(p(),m(v,{key:e.value,label:e.buildingName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"楼栋层数",prop:"floorId"},{default:o(()=>[a(_,{disabled:i.isShow||z.value,modelValue:t(d).checkIn.floorId,"onUpdate:modelValue":l[15]||(l[15]=e=>t(d).checkIn.floorId=e),style:{width:"100%"},placeholder:"全部",clearable:"",onChange:je},{default:o(()=>[(p(!0),b(c,null,h(T.value,e=>(p(),m(v,{key:e.value,label:e.floorName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"房  间  号",prop:"roomId"},{default:o(()=>[a(_,{disabled:i.isShow||z.value,modelValue:t(d).checkIn.roomId,"onUpdate:modelValue":l[16]||(l[16]=e=>t(d).checkIn.roomId=e),style:{width:"100%"},placeholder:"全部",onChange:Te,clearable:""},{default:o(()=>[(p(!0),b(c,null,h(q.value,e=>(p(),m(v,{key:e.id,label:e.roomNumber,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"床        位",prop:"bedId",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).checkIn.bedId,"onUpdate:modelValue":l[17]||(l[17]=e=>t(d).checkIn.bedId=e),placeholder:"请选择",disabled:i.isShow||z.value,onChange:Be},{default:o(()=>[(p(!0),b(c,null,h(B.value,e=>(p(),m(v,{key:e.id,label:e.bedNumber,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"居住类型",prop:"residenceType",size:"large"},{default:o(()=>[a(_,{disabled:i.isShow,modelValue:t(d).checkIn.residenceType,"onUpdate:modelValue":l[18]||(l[18]=e=>t(d).checkIn.residenceType=e),placeholder:"请选择居住类型"},{default:o(()=>[(p(!0),b(c,null,h(t(me),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:16},{default:o(()=>[a(u,{label:"家庭住址",prop:"homeAddress",size:"large"},{default:o(()=>[a(r,{disabled:i.isShow,modelValue:t(d).elderInfo.homeAddress,"onUpdate:modelValue":l[19]||(l[19]=e=>t(d).elderInfo.homeAddress=e),placeholder:"请输入家庭住址"},null,8,["disabled","modelValue"])]),_:1})]),_:1})]),_:1}),a(x,null,{default:o(()=>[a(s,{span:20},{default:o(()=>[a(u,{label:"监护人信息",prop:"elderName1",size:"large"}),a(qe,{data:k.value,style:{width:"100%","margin-left":"10%"},border:"",stripe:""},{default:o(()=>[F("",!0),a(E,{align:"center",label:"与老人关系",prop:"relationship",width:"180"},{default:o(e=>[y(R(e.row.relationship),1)]),_:1}),a(E,{align:"center",label:"姓名",prop:"name",width:"180"}),a(E,{align:"center",label:"联系电话",prop:"phone"}),a(E,{align:"center",label:"是否是紧急联系人",prop:"isEmergencyContact",width:"200"},{default:o(e=>[a(Ae,{options:t(J),value:e.row.isEmergencyContact},null,8,["options","value"])]),_:1}),a(E,{label:"住址",prop:"address"}),a(E,{align:"center",label:"操作",prop:"careLevel",width:"150px"},{default:o(e=>[a(w,{disabled:A.value||i.isShow,icon:"Edit",link:"",type:"primary",onClick:le=>Fe(e.row)},{default:o(()=>[y("修改")]),_:2},1032,["disabled","onClick"]),a(w,{disabled:A.value||i.isShow,icon:"Delete",link:"",type:"primary",onClick:le=>Se(e.row)},{default:o(()=>[y("删除")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),a(s,{span:4},{default:o(()=>[a(w,{type:"primary",onClick:xe,disabled:i.isShow},{default:o(()=>[y("新增监护人")]),_:1},8,["disabled"])]),_:1})]),_:1}),sl,a(x,{style:{"margin-top":"20px"}},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(u,{label:"工作单位",prop:"workUnit",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.workUnit,"onUpdate:modelValue":l[20]||(l[20]=e=>t(d).elderInfo.workUnit=e),disabled:A.value,placeholder:"请输入工作单位"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"老人职业",prop:"formerOccupation",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).elderInfo.formerOccupation,"onUpdate:modelValue":l[21]||(l[21]=e=>t(d).elderInfo.formerOccupation=e),placeholder:"请选择老人职业",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(ve),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"籍        贯",prop:"hometown",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.hometown,"onUpdate:modelValue":l[22]||(l[22]=e=>t(d).elderInfo.hometown=e),placeholder:"请输入籍贯",disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"教育程度",prop:"education",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).elderInfo.education,"onUpdate:modelValue":l[23]||(l[23]=e=>t(d).elderInfo.education=e),placeholder:"请选择教育程度",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(be),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"婚姻状况",prop:"maritalStatus",size:"large"},{default:o(()=>[a(_,{disabled:i.isShow,modelValue:t(d).elderInfo.maritalStatus,"onUpdate:modelValue":l[24]||(l[24]=e=>t(d).elderInfo.maritalStatus=e),placeholder:"请选择婚姻状况"},{default:o(()=>[(p(!0),b(c,null,h(t(ge),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"老人血型",prop:"bloodType",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).elderInfo.bloodType,"onUpdate:modelValue":l[25]||(l[25]=e=>t(d).elderInfo.bloodType=e),placeholder:"请选择老人血型",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(ce),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"社保号码",prop:"socialSecurityCode",size:"large"},{default:o(()=>[a(r,{disabled:i.isShow,modelValue:t(d).elderInfo.socialSecurityCode,"onUpdate:modelValue":l[26]||(l[26]=e=>t(d).elderInfo.socialSecurityCode=e),placeholder:"请输入社保号码"},null,8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"经济来源",prop:"economicSource",size:"large"},{default:o(()=>[a(_,{modelValue:t(d).elderInfo.economicSource,"onUpdate:modelValue":l[27]||(l[27]=e=>t(d).elderInfo.economicSource=e),placeholder:"请选择经济来源",disabled:i.isShow},{default:o(()=>[(p(!0),b(c,null,h(t(he),e=>(p(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(s,{span:24},{default:o(()=>[a(u,{label:"老人标签",prop:"elderTags"},{default:o(()=>[(p(!0),b(c,null,h(U.value,e=>(p(),m(Ye,{key:e,"disable-transitions":!1,disabled:i.isShow,closable:"",size:"large",style:{"margin-right":"4px"},onClose:le=>De(e)},{default:o(()=>[y(R(e),1)]),_:2},1032,["disabled","onClose"]))),128)),Y.value?(p(),m(r,{key:0,ref:"InputRef",modelValue:N.value,"onUpdate:modelValue":l[28]||(l[28]=e=>N.value=e),class:"w-20",size:"default",style:{width:"120px"},onBlur:Z,onKeyup:ll(Z,["enter"])},null,8,["modelValue"])):(p(),m(w,{key:1,disabled:i.isShow,class:"button-new-tag",size:"default",onClick:Ne},{default:o(()=>[y("+ 新增标签")]),_:1},8,["disabled"]))]),_:1})]),_:1}),a(s,{span:20},{default:o(()=>[a(u,{label:"证件照片",prop:"idCardFrontPhoto",size:"large"},{default:o(()=>[a(G,{disabled:i.isShow,modelValue:t(d).elderInfo.idCardFrontPhoto,"onUpdate:modelValue":l[29]||(l[29]=e=>t(d).elderInfo.idCardFrontPhoto=e),fileData:{category:"elder_profile",attachmentType:"id_card_front_photo"},fileType:["png","jpg","jpeg"],isShowOrEdit:!0,isShowTip:!0,limit:1,onSubmitParentValue:K},null,8,["disabled","modelValue"]),a(G,{disabled:i.isShow,modelValue:t(d).elderInfo.idCardBackPhoto,"onUpdate:modelValue":l[30]||(l[30]=e=>t(d).elderInfo.idCardBackPhoto=e),fileData:{category:"elder_profile",attachmentType:"id_card_back_photo"},fileType:["png","jpg","jpeg"],isShowOrEdit:!0,isShowTip:!0,limit:1,onSubmitParentValue:K},null,8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:20},{default:o(()=>[a(u,{label:"头像照片",prop:"remark",size:"large"},{default:o(()=>[a(G,{disabled:i.isShow,modelValue:t(d).elderInfo.avatar,"onUpdate:modelValue":l[31]||(l[31]=e=>t(d).elderInfo.avatar=e),fileData:{category:"elder_profile",attachmentType:"avatar"},fileType:["png","jpg","jpeg"],isShowOrEdit:!0,isShowTip:!0,limit:1,onSubmitParentValue:K},null,8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:20},{default:o(()=>[a(u,{label:"老人备注",prop:"remark",size:"large"},{default:o(()=>[a(r,{disabled:i.isShow,modelValue:t(d).elderInfo.remark,"onUpdate:modelValue":l[32]||(l[32]=e=>t(d).elderInfo.remark=e),placeholder:"请输入备注内容",rows:"5",type:"textarea"},null,8,["disabled","modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(u,{label:"建档人",prop:"archivist",size:"large"},{default:o(()=>[a(r,{modelValue:t(d).elderInfo.archivist,"onUpdate:modelValue":l[33]||(l[33]=e=>t(d).elderInfo.archivist=e),disabled:i.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),C("div",pl,[a(w,{size:"large",style:{"margin-left":"80%"},type:"primary",disabled:i.isShow,onClick:Le},{default:o(()=>[y("确 定")]),_:1},8,["disabled"]),a(w,{size:"large",onClick:Ce},{default:o(()=>[y(R(i.isShow?"返回":"取消"),1)]),_:1})])]),_:1}),a(Je,{modelValue:S.value,"onUpdate:modelValue":l[40]||(l[40]=e=>S.value=e),direction:"rtl"},{header:o(()=>[fl]),default:o(()=>[C("div",null,[a(ee,{ref:"jhrRef",model:t(f),rules:t(Ve),"label-width":"120px"},{default:o(()=>[a(x,null,{default:o(()=>[a(s,{span:24},{default:o(()=>[a(u,{label:"姓名",prop:"name",size:"large"},{default:o(()=>[a(r,{modelValue:t(f).name,"onUpdate:modelValue":l[34]||(l[34]=e=>t(f).name=e),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:24},{default:o(()=>[a(u,{label:"联系电话",prop:"phone",size:"large"},{default:o(()=>[a(r,{modelValue:t(f).phone,"onUpdate:modelValue":l[35]||(l[35]=e=>t(f).phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:24},{default:o(()=>[a(u,{label:"住址",prop:"address",size:"large"},{default:o(()=>[a(r,{modelValue:t(f).address,"onUpdate:modelValue":l[36]||(l[36]=e=>t(f).address=e),placeholder:"请输入住址",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:24},{default:o(()=>[a(u,{label:"与老人关系",prop:"relationship",size:"large"},{default:o(()=>[a(r,{modelValue:t(f).relationship,"onUpdate:modelValue":l[37]||(l[37]=e=>t(f).relationship=e),placeholder:"请输入与老人关系"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:24},{default:o(()=>[a(u,{label:"是否紧急联系人",prop:"isEmergencyContact",size:"large"},{default:o(()=>[a(Ge,{modelValue:t(f).isEmergencyContact,"onUpdate:modelValue":l[38]||(l[38]=e=>t(f).isEmergencyContact=e),placeholder:"请选择是否紧急联系人",size:"large",style:{width:"100%"}},{default:o(()=>[(p(!0),b(c,null,h(t(J),e=>(p(),m(Ke,{key:e.value,label:e.value},{default:o(()=>[y(R(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(x,null,{default:o(()=>[F("",!0)]),_:1})]),_:1},8,["model","rules"])])]),footer:o(()=>[C("div",ml,[D.value?(p(),m(w,{key:0,type:"primary",onClick:Re},{default:o(()=>[y("连续添加")]),_:1})):F("",!0),D.value?(p(),m(w,{key:1,type:"primary",onClick:Pe},{default:o(()=>[y("添加")]),_:1})):F("",!0),D.value?F("",!0):(p(),m(w,{key:2,type:"primary",onClick:Oe},{default:o(()=>[y("修改")]),_:1})),a(w,{onClick:$e},{default:o(()=>[y("取消")]),_:1})])]),_:1},8,["modelValue"])])}}}),El=He(bl,[["__scopeId","data-v-4888166f"]]);export{El as default};
