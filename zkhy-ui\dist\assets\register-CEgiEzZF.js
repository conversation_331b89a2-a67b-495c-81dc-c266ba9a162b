import{_ as I,a as U,d as z,r as f,e as i,c as v,f as e,h as t,i as d,j as B,k as R,t as S,l as s,m as y,o as h,p as K,n as N,q as T,v as M,x as $,y as j,E as D}from"./index-B0qHf98Y.js";const F=c=>(M("data-v-fc00e288"),c=c(),$(),c),H={class:"register"},L={class:"title"},A={class:"register-code"},G=["src"],J={key:0},O={key:1},Q={style:{float:"right"}},W=F(()=>d("div",{class:"el-register-footer"},[d("span",null,"Copyright © 2024-2025 中科慧颐信息科技有限公司版权所有.")],-1)),X={__name:"register",setup(c){const x="颐佳通智慧养老平台",b=U(),{proxy:k}=z(),o=f({username:"",password:"",confirmPassword:"",code:"",uuid:""}),C={username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:(l,r,a)=>{o.value.password!==r?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},V=f(""),p=f(!1),m=f(!0);function g(){k.$refs.registerRef.validate(l=>{l&&(p.value=!0,j(o.value).then(r=>{const a=o.value.username;D.alert("<font color='red'>恭喜你，您的账号 "+a+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then(()=>{b.push("/login")}).catch(()=>{})}).catch(()=>{p.value=!1,m&&w()}))})}function w(){T().then(l=>{m.value=l.captchaEnabled===void 0?!0:l.captchaEnabled,m.value&&(V.value="data:image/gif;base64,"+l.img,o.value.uuid=l.uuid)})}return w(),(l,r)=>{const a=i("svg-icon"),_=i("el-input"),u=i("el-form-item"),q=i("el-button"),E=i("router-link"),P=i("el-form");return h(),v("div",H,[e(P,{ref:"registerRef",model:s(o),rules:C,class:"register-form"},{default:t(()=>[d("h3",L,S(s(x)),1),e(u,{prop:"username"},{default:t(()=>[e(_,{modelValue:s(o).username,"onUpdate:modelValue":r[0]||(r[0]=n=>s(o).username=n),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:t(()=>[e(a,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"password"},{default:t(()=>[e(_,{modelValue:s(o).password,"onUpdate:modelValue":r[1]||(r[1]=n=>s(o).password=n),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"confirmPassword"},{default:t(()=>[e(_,{modelValue:s(o).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=n=>s(o).confirmPassword=n),type:"password",size:"large","auto-complete":"off",placeholder:"确认密码",onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),s(m)?(h(),B(u,{key:0,prop:"code"},{default:t(()=>[e(_,{size:"large",modelValue:s(o).code,"onUpdate:modelValue":r[3]||(r[3]=n=>s(o).code=n),"auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),d("div",A,[d("img",{src:s(V),onClick:w,class:"register-code-img"},null,8,G)])]),_:1})):R("",!0),e(u,{style:{width:"100%"}},{default:t(()=>[e(q,{loading:s(p),size:"large",type:"primary",style:{width:"100%"},onClick:K(g,["prevent"])},{default:t(()=>[s(p)?(h(),v("span",O,"注 册 中...")):(h(),v("span",J,"注 册"))]),_:1},8,["loading"]),d("div",Q,[e(E,{class:"link-type",to:"/login"},{default:t(()=>[N("使用已有账户登录")]),_:1})])]),_:1})]),_:1},8,["model"]),W])}}},ee=I(X,[["__scopeId","data-v-fc00e288"]]);export{ee as default};
