import{_ as J,r as f,a as q,u as F,w as Q,F as j,e as u,c as N,o as x,f as e,i as t,h as a,n as i,l,t as d,k as K,D as W,aJ as X,aK as Z,E as ee,aL as te,G as E,v as le,x as ae}from"./index-B0qHf98Y.js";const v=y=>(le("data-v-5256fbce"),y=y(),ae(),y),oe={class:"log-review-container"},ne={style:{"text-align":"right"}},se={class:"paginationBox"},re={key:0},ie={class:"nurse-log"},ue=v(()=>t("h2",{class:"titleLog"},"护士日志",-1)),ce={class:"table-style"},de={style:{"text-align":"left","white-space":"nowrap"}},pe={style:{"text-align":"center"}},_e={style:{"text-align":"center"}},me={style:{"text-align":"left"},colspan:"3"},ge={class:"log-content"},he=v(()=>t("span",null,"工作内容:",-1)),fe={class:"preContent"},ye={style:{"text-align":"left"},colspan:"3"},be={class:"log-content"},ve=v(()=>t("span",null,"工作计划:",-1)),we={class:"preContent"},Ce={style:{"text-align":"left"},colspan:"3"},De={class:"log-content"},Ne=v(()=>t("span",null,"工作建议:",-1)),xe={class:"preContent"},Ee={style:{"text-align":"left"},colspan:"3"},ke={__name:"nurseLogHistory",setup(y){const s=f({pageNum:1,pageSize:10}),k=q(),V=F(),w=f([]),C=f(0),h=f(!1),c=f(null),L=r=>{switch(r){case"1":return"正常";case"0":return"作废";case"PENDING":return"待审核";case"APPROVED":return"已通过";case"REJECTED":return"已驳回";case"CANCELED":return"已取消";case"RETURNED":return"已返回";case"COMPLETE":return"已完成";default:return""}},p=async()=>{const r=await X({...s.value});w.value=r.rows||[],C.value=r.total||0},S=()=>{s.value.pageNum=1,p()},R=()=>{s.value={pageNum:1,pageSize:10},p()},z=r=>{s.value.pageSize=r,p()},P=r=>{s.value.pageNum=r,p()},T=r=>{Z(r.id).then(o=>{h.value=!0,c.value=o.data})},B=r=>{ee.confirm("注：删除护士日志将失去原始数据，请慎重删除","确定删除该护士日志吗?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{te(r.id).then(o=>{o.code===200?(E.success("删除成功"),p()):E.error(o.msg)})})},M=()=>{k.push("/work/nurseworkstation")};return Q(()=>V.path,r=>{r==="/nurseLogs/nurseLogHistory/add/0/add"&&p()},{immediate:!0}),j(()=>{p()}),(r,o)=>{const _=u("el-button"),U=u("el-date-picker"),b=u("el-form-item"),D=u("el-input"),m=u("el-option"),I=u("el-select"),A=u("el-form"),g=u("el-table-column"),O=u("el-tag"),Y=u("el-table"),$=u("el-pagination"),G=u("el-dialog");return x(),N("div",oe,[e(_,{type:"primary",onClick:M,class:"back-button"},{default:a(()=>[i(" 返回工作台 ")]),_:1}),e(A,{model:l(s),ref:"queryForm",inline:!0,class:"search-form","label-width":"90px"},{default:a(()=>[e(b,{label:"日志日期：",prop:"logDate"},{default:a(()=>[e(U,{style:{width:"150px"},modelValue:l(s).logDate,"onUpdate:modelValue":o[0]||(o[0]=n=>l(s).logDate=n),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),e(b,{label:"所属部门：",prop:"departmentName"},{default:a(()=>[e(D,{modelValue:l(s).departmentName,"onUpdate:modelValue":o[1]||(o[1]=n=>l(s).departmentName=n),placeholder:"请输入所属部门",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(b,{label:"审阅状态：",prop:"status"},{default:a(()=>[e(I,{modelValue:l(s).status,"onUpdate:modelValue":o[2]||(o[2]=n=>l(s).status=n),placeholder:"全部",clearable:"",style:{width:"150px"}},{default:a(()=>[e(m,{label:"全部",value:""}),e(m,{label:"待审核",value:"PENDING"}),e(m,{label:"已通过",value:"APPROVED"}),e(m,{label:"已驳回",value:"REJECTED"}),e(m,{label:"已取消",value:"CANCELED"}),e(m,{label:"已返回",value:"RETURNED"}),e(m,{label:"已完成",value:"COMPLETE"})]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"护士姓名：",prop:"nurseName"},{default:a(()=>[e(D,{style:{width:"150px"},modelValue:l(s).nurseName,"onUpdate:modelValue":o[3]||(o[3]=n=>l(s).nurseName=n),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),t("div",ne,[e(_,{type:"primary",onClick:S,icon:"Search"},{default:a(()=>[i("查询")]),_:1}),e(_,{onClick:R,icon:"Refresh"},{default:a(()=>[i("重置")]),_:1})])]),_:1},8,["model"]),e(Y,{data:l(w),border:"",style:{width:"100%"}},{default:a(()=>[e(g,{prop:"id",label:"序号",width:"80",align:"center"},{default:a(n=>[i(d(n.$index+1),1)]),_:1}),e(g,{prop:"logDate",label:"日志日期","min-width":"120",align:"center"}),e(g,{prop:"nurseName",label:"护士姓名","min-width":"120",align:"center"}),e(g,{prop:"departmentName",label:"所属部门","min-width":"120",align:"center"}),e(g,{prop:"status",label:"院长审阅",width:"120",align:"center"},{default:a(({row:n})=>[e(O,null,{default:a(()=>[i(d(L(n.status)),1)]),_:2},1024)]),_:1}),e(g,{label:"操作",width:"180",align:"center",fixed:"right"},{default:a(({row:n})=>[e(_,{type:"primary",link:"",onClick:H=>T(n)},{default:a(()=>[i("详情")]),_:2},1032,["onClick"]),e(_,{type:"primary",link:"",onClick:H=>B(n)},{default:a(()=>[i("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),t("div",se,[e($,{background:"",onSizeChange:z,onCurrentChange:P,"current-page":l(s).pageNum,"page-sizes":[10,20,30,50],"page-size":l(s).pageSize,layout:"total, sizes, prev, pager, next, jumper",total:l(C)},null,8,["current-page","page-size","total"])]),e(G,{title:"日志详情",modelValue:l(h),"onUpdate:modelValue":o[5]||(o[5]=n=>W(h)?h.value=n:null),width:"50%"},{footer:a(()=>[e(_,{type:"primary",onClick:o[4]||(o[4]=n=>h.value=!1),plain:""},{default:a(()=>[i("返回")]),_:1})]),default:a(()=>[l(c)?(x(),N("div",re,[t("div",ie,[ue,t("table",ce,[t("tbody",null,[t("tr",null,[t("td",de,"所属部门:"+d(l(c).departmentName||"-"),1),t("td",pe,"护士姓名："+d(l(c).nurseName||"-"),1),t("td",_e,"日志日期："+d(l(c).logDate||"-"),1)]),t("tr",null,[t("td",me,[t("div",ge,[he,i(),t("pre",fe,d(l(c).workContent||"-"),1)])])]),t("tr",null,[t("td",ye,[t("div",be,[ve,i(),t("pre",we,d(l(c).workPlan||"-"),1)])])]),t("tr",null,[t("td",Ce,[t("div",De,[Ne,i(),t("pre",xe,d(l(c).workSuggestion||"-"),1)])])]),t("tr",null,[t("td",Ee,[i("院长审阅: "),e(_,{type:"primary",link:""},{default:a(()=>[i("已审阅")]),_:1})])])])])])])):K("",!0)]),_:1},8,["modelValue"])])}}},Le=J(ke,[["__scopeId","data-v-5256fbce"]]);export{Le as default};
