import{i as ne}from"./index-a8qYZQmS.js";import{X as re,_ as ce,a as de,r as d,F as ue,P as fe,b1 as he,e as j,c as h,o as r,f as o,i as e,h as s,K as T,L as F,j as p,l as a,b2 as _,b3 as pe,Q as _e,k as ve,b4 as C,b5 as ge,b6 as ye,b7 as me,b8 as be,t as u,n as K,au as xe,b9 as A,ba as Ce,bb as we,bc as Se,bd as Q,be as ke,bf as ze,v as Le,x as Ae}from"./index-B0qHf98Y.js";import{g as Re,a as De}from"./notice-DJ0irKZd.js";function Be(w){return re({url:"/home/<USER>/aggregate",method:"get",params:w})}const v=w=>(Le("data-v-64e2adea"),w=w(),Ae(),w),Ne={class:"container"},Ee={style:{"text-align":"center",padding:"15px"}},Te={class:"stat-icon-wrapper"},Fe={style:{color:"#fff","font-size":"16px","margin-bottom":"8px","text-shadow":"0 1px 2px rgba(0,0,0,0.1)"}},Oe={style:{"font-size":"28px","font-weight":"600",margin:"12px 0",color:"#fff","text-shadow":"0 2px 4px rgba(0,0,0,0.15)"}},Ve={class:"chart-container"},We=v(()=>e("h3",null,"用户性别分布",-1)),Ie={class:"chart-container"},$e=v(()=>e("h3",null,"年龄分布",-1)),Ge={class:"chart-container"},Me=v(()=>e("h3",null,"健康状况统计",-1)),Pe={class:"chart-container"},He=v(()=>e("h3",null,"老人能力等级",-1)),Ue={class:"chart-container"},Xe=v(()=>e("h3",null,"护理等级统计",-1)),je={class:"chart-container"},Ke=v(()=>e("h3",null,"本月生日",-1)),Qe={class:"birthday-list"},Ye={style:{"text-align":"center"}},qe={style:{"margin-top":"10px","font-size":"14px"}},Je={style:{color:"#909399","font-size":"12px"}},Ze={class:"notice-container"},et={class:"notice-header"},tt={class:"notice-title"},at=v(()=>e("span",null,"机构公告",-1)),ot=v(()=>e("span",null,"更多",-1)),st={class:"notice-content"},lt={key:0,class:"notice-loading"},it={key:1,class:"notice-empty"},nt={key:2,class:"notice-list"},rt=["onClick"],ct={class:"notice-item-left"},dt={class:"notice-flag"},ut={class:"notice-item-title"},ft={class:"notice-item-right"},ht={class:"notice-item-date"},pt={class:"notice-detail"},_t={class:"notice-detail-title"},vt={class:"notice-detail-info"},gt=["innerHTML"],yt={__name:"index",setup(w){const Y=de(),O=d(!0),R=d(!1),N=d(!1),S=d({noticeTitle:"",noticeContent:"",createTime:"",createBy:""});d([]);const E=d([]),q=()=>{Y.push("/eldersystem/home/<USER>")},J=async n=>{R.value=!0,N.value=!0;try{const l=await De(n.id);l.code===200&&(S.value=l.data)}catch(l){console.error("获取公告详情失败:",l)}finally{N.value=!1}},V=d([{title:"老人数量",value:"0",trend:0,color:"#1890ff"},{title:"入住率",value:"0%",trend:0,color:"#52c41a"},{title:"外出老人",value:"0",trend:0,color:"#f5222d"},{title:"在线设备",value:"0",trend:0,color:"#722ed1"}]),D=d([]),W=d(null),I=d(null),$=d(null),G=d(null),M=d(null);let g=null,y=null,m=null,b=null,x=null;function k(n,l,z){try{if(!n)return console.error("图表容器不存在"),null;l&&l.dispose();const f=ne(n);return f.setOption(z),f}catch(f){return console.error("初始化图表失败:",f),null}}function Z(){g&&g.resize(),y&&y.resize(),m&&m.resize(),b&&b.resize(),x&&x.resize()}const ee=async()=>{try{const n=await Re({pageNum:1,pageSize:5});n.code===200&&(E.value=n.rows.map(l=>({id:l.noticeId,title:l.noticeTitle,date:l.createTime.substring(0,10),content:l.noticeContent})))}catch(n){console.error("获取公告列表失败:",n)}},te=async()=>{try{const n=await Be();if(n.code===200){const{elderCount:l,occupancyRate:z,outElderCount:f,genderStats:i,ageGroupStats:c,abilityStats:H,careLevelStats:U,birthdayElders:X,healthStats:B}=n.data;V.value=[{title:"老人数量",value:l.toString(),trend:0,color:"#1890ff"},{title:"入住率",value:z+"%",trend:0,color:"#52c41a"},{title:"外出老人",value:f.toString(),trend:0,color:"#f5222d"},{title:"在线设备",value:"0",trend:0,color:"#722ed1"}],D.value=X.map(t=>({name:t.elderName,date:P(t.birthDate),avatar:t.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}));const ae={backgroundColor:"rgba(255, 255, 255, 0.1)",tooltip:{trigger:"item",formatter:function(t){return t.seriesName==="性别分布"?`${t.name}：${t.value}%`:""}},legend:{orient:"horizontal",bottom:"5%",textStyle:{color:"#666"}},grid:{left:"5%",right:"5%",bottom:"15%",top:"10%",containLabel:!0},xAxis3D:{type:"category"},yAxis3D:{type:"category"},zAxis3D:{type:"value"},xAxis:{type:"category",data:["男","女"],axisLabel:{interval:0,fontSize:14,color:"#333"},axisLine:{lineStyle:{color:"#ccc"}}},yAxis:{type:"value",name:"百分比",nameTextStyle:{color:"#666",fontSize:12},axisLabel:{formatter:"{value}%",color:"#666"},splitLine:{lineStyle:{type:"dashed",color:"#eee"}}},series:[{name:"性别分布",type:"pictorialBar",symbolSize:[40,10],symbolOffset:[0,-5],z:12,symbolPosition:"end",symbol:"circle",barWidth:"40%",barGap:"10%",barCategoryGap:"30%",data:i.map(t=>({value:t.percentage,name:t.gender==="1"?"男":"女",itemStyle:{color:t.gender==="1"?"#1890ff":"#f759ab"}}))},{name:"性别分布",type:"bar",barWidth:"40%",barGap:"10%",barCategoryGap:"30%",data:i.map(t=>({value:t.percentage,name:t.gender==="1"?"男":"女",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:t.gender==="1"?"#1890ff":"#f759ab"},{offset:1,color:t.gender==="1"?"#36c6ff":"#ff9ed6"}]}}})),label:{show:!0,formatter:"{c}%",position:"top",fontSize:14,fontWeight:"bold",color:"#333"},itemStyle:{borderRadius:[8,8,0,0],shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.3)",shadowOffsetX:2,shadowOffsetY:2},emphasis:{itemStyle:{shadowBlur:20,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]},oe={color:["#40a9ff"],tooltip:{trigger:"axis"},xAxis:{type:"category",data:c.map(t=>t.ageGroup)},yAxis:{type:"value"},series:[{data:c.map(t=>t.count),type:"bar",barWidth:"60%",itemStyle:{color:"#409EFF"},label:{show:!0,position:"top"}}]},se={color:["#2196F3","#4CAF50","#FFC107","#F44336","#9C27B0"],tooltip:{trigger:"item"},legend:{orient:"vertical",right:"5%",top:"middle"},series:[{name:"能力等级",type:"pie",radius:["40%","70%"],center:["40%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!0,formatter:"{b}: {c}%",position:"outside"},emphasis:{label:{show:!0,fontSize:"20",fontWeight:"bold"}},data:H.map(t=>({value:t.percentage,name:t.selfCareAbility}))}]};B.value=B;const le={color:["#52c41a","#1890ff","#faad14","#f5222d","#722ed1","#13c2c2"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:B.value.map(t=>t.disease),axisLabel:{interval:0,rotate:30}},yAxis:{type:"value",name:"人数",nameTextStyle:{color:"#666"}},series:[{name:"患病人数",data:B.value.map(t=>t.count),type:"bar",barWidth:"60%",itemStyle:{color:function(t){const L=["#52c41a","#1890ff","#faad14","#f5222d","#722ed1","#13c2c2"];return L[t.dataIndex%L.length]}},label:{show:!0,position:"top",formatter:"{c}人"}}]},ie={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",max:100},yAxis:{type:"category",data:U.map(t=>t.careLevel),axisLabel:{interval:0}},series:[{name:"人数",type:"bar",data:U.map(t=>t.count),barWidth:"60%",label:{show:!0,position:"right"},itemStyle:{color:function(t){const L=["#FF4500","#9370DB"];return L[t.dataIndex%L.length]}}}]};g=k(W.value,g,ae),y=k(I.value,y,oe),m=k($.value,m,le),b=k(G.value,b,se),x=k(M.value,x,ie),D.value=X.map(t=>({name:t.elderName,date:P(t.birthDate),avatar:t.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}))}}catch(n){console.error("获取聚合数据失败:",n)}},P=n=>{if(!n)return"";const l=new Date(n);return`${l.getMonth()+1}月${l.getDate()}日`};return ue(()=>{fe(async()=>{await Promise.all([te(),ee()]),O.value=!1})}),he(()=>{window.removeEventListener("resize",Z),g&&g.dispose(),y&&y.dispose(),m&&m.dispose(),b&&b.dispose(),x&&x.dispose(),g=null,y=null,m=null,b=null,x=null}),(n,l)=>{const z=j("el-empty"),f=j("el-button");return r(),h("div",Ne,[o(a(A),{gutter:20,class:"stats-cards"},{default:s(()=>[(r(!0),h(T,null,F(V.value,(i,c)=>(r(),p(a(_),{span:6,key:c},{default:s(()=>[o(a(pe),{shadow:"hover",class:_e(`stat-card-${c}`)},{default:s(()=>[e("div",Ee,[e("div",Te,[c===0?(r(),p(a(C),{key:0,size:24,class:"stat-icon"},{default:s(()=>[o(a(ge))]),_:1})):c===1?(r(),p(a(C),{key:1,size:24,class:"stat-icon"},{default:s(()=>[o(a(ye))]),_:1})):c===2?(r(),p(a(C),{key:2,size:24,class:"stat-icon"},{default:s(()=>[o(a(me))]),_:1})):c===3?(r(),p(a(C),{key:3,size:24,class:"stat-icon"},{default:s(()=>[o(a(be))]),_:1})):ve("",!0)]),e("h3",Fe,u(i.title),1),e("p",Oe,[K(u(i.value)+" ",1),e("span",{style:xe([{"font-size":"14px","margin-left":"4px"},{color:i.trend>0?"#ffffff":"#ffdddd"}])},u(i.trend>0?"↑":"↓")+u(Math.abs(i.trend))+"% ",5)])])]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1}),o(a(A),{gutter:20},{default:s(()=>[o(a(_),{span:12},{default:s(()=>[e("div",Ve,[We,e("div",{ref_key:"genderChartRef",ref:W,class:"chart"},null,512)])]),_:1}),o(a(_),{span:12},{default:s(()=>[e("div",Ie,[$e,e("div",{ref_key:"ageChartRef",ref:I,class:"chart"},null,512)])]),_:1})]),_:1}),o(a(A),{gutter:20},{default:s(()=>[o(a(_),{span:12},{default:s(()=>[e("div",Ge,[Me,e("div",{ref_key:"healthChartRef",ref:$,class:"chart"},null,512)])]),_:1}),o(a(_),{span:12},{default:s(()=>[e("div",Pe,[He,e("div",{ref_key:"activityChartRef",ref:G,class:"chart"},null,512)])]),_:1})]),_:1}),o(a(A),{gutter:20},{default:s(()=>[o(a(_),{span:12},{default:s(()=>[e("div",Ue,[Xe,e("div",{ref_key:"careChartRef",ref:M,class:"chart"},null,512)])]),_:1}),o(a(_),{span:12},{default:s(()=>[e("div",je,[Ke,e("div",Qe,[D.value.length===0?(r(),p(z,{key:0,description:"暂无人过生日"})):(r(),p(a(A),{key:1,gutter:20},{default:s(()=>[(r(!0),h(T,null,F(D.value,(i,c)=>(r(),p(a(_),{span:4,key:c},{default:s(()=>[e("div",Ye,[o(a(Ce),{size:60,src:i.avatar},null,8,["src"]),e("p",qe,u(i.name),1),e("p",Je,u(i.date),1)])]),_:2},1024))),128))]),_:1}))])])]),_:1})]),_:1}),e("div",Ze,[e("div",et,[e("div",tt,[o(a(C),null,{default:s(()=>[o(a(we))]),_:1}),at]),e("div",{class:"notice-more",onClick:q},[ot,o(a(C),null,{default:s(()=>[o(a(Se))]),_:1})])]),e("div",st,[O.value?(r(),h("div",lt,[o(a(Q),{rows:3,animated:""})])):E.value.length===0?(r(),h("div",it," 暂无公告 ")):(r(),h("div",nt,[(r(!0),h(T,null,F(E.value,(i,c)=>(r(),h("div",{key:c,class:"notice-item",onClick:H=>J(i)},[e("div",ct,[e("div",dt,[o(a(C),null,{default:s(()=>[o(a(ke))]),_:1})]),e("span",ut,u(i.title),1)]),e("div",ft,[e("span",ht,u(i.date),1)])],8,rt))),128))]))])]),o(a(ze),{modelValue:R.value,"onUpdate:modelValue":l[1]||(l[1]=i=>R.value=i),title:"公告详情",width:"60%","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[o(f,{onClick:l[0]||(l[0]=i=>R.value=!1)},{default:s(()=>[K("关闭")]),_:1})]),default:s(()=>[o(a(Q),{rows:6,loading:N.value,animated:""},{default:s(()=>[e("div",pt,[e("h2",_t,u(S.value.noticeTitle),1),e("div",vt,[e("span",null,"发布时间："+u(S.value.createTime),1),e("span",null,"发布人："+u(S.value.createBy),1)]),e("div",{class:"notice-detail-content",innerHTML:S.value.noticeContent},null,8,gt)])]),_:1},8,["loading"])]),_:1},8,["modelValue"])])}}},wt=ce(yt,[["__scopeId","data-v-64e2adea"]]);export{wt as default};
