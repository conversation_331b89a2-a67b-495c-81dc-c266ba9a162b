<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :inline="true" class="filter-form">
      <el-form-item label="交接班日期">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="区域">
        <el-select
          v-model="selectedAreas"
          multiple
          placeholder="请选择区域"
          clearable
          style="width: 130px"
        >
          <el-option
            v-for="area in areaList"
            :key="area.id"
            :label="area.name"
            :value="area.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="班次">
        <el-select
          v-model="selectedShifts"
          multiple
          placeholder="请选择班次"
          clearable
          style="width: 100px"
        >
          <el-option
            v-for="shift in shiftList"
            :key="shift.id"
            :label="shift.name"
            :value="shift.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员">
        <el-input v-model="staffName" placeholder="请输入姓名" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="primary" @click="handleHandover">交班</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="area" label="区域" />
      <el-table-column prop="date" label="日期" />
      <el-table-column prop="shift" label="班次" />
      <el-table-column prop="nurse" label="护士（交班人）" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">修改</el-button>
          <el-button type="text" @click="handleDetail(row)">详情</el-button>
          <el-button type="text" @click="handleReceive(row)">接班</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 交班弹窗 -->
    <el-dialog title="交班" v-model="handoverDialogVisible" width="60%">
      <el-form :model="handoverForm" label-width="120px">
        <el-form-item label="交接日期">
          <el-date-picker
            v-model="handoverForm.date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="交班人">
          <el-select v-model="handoverForm.nurse" placeholder="请选择交班人">
            <el-option
              v-for="nurse in nurseList"
              :key="nurse.id"
              :label="nurse.name"
              :value="nurse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select v-model="handoverForm.area" placeholder="请选择区域">
            <el-option
              v-for="area in areaList"
              :key="area.id"
              :label="area.name"
              :value="area.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="班次">
          <el-select v-model="handoverForm.shift" placeholder="请选择班次">
            <el-option
              v-for="shift in shiftList"
              :key="shift.id"
              :label="shift.name"
              :value="shift.id"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-form-item label="接班总人数">
            <el-input-number v-model="handoverForm.totalReceivers" :min="0" />
          </el-form-item>
          <el-form-item label="新入住">
            <el-input-number v-model="handoverForm.newAdmissions" :min="0" />
          </el-form-item>
          <el-form-item label="试入住">
            <el-input-number v-model="handoverForm.trialAdmissions" :min="0" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="死亡">
            <el-input-number v-model="handoverForm.deaths" :min="0" />
          </el-form-item>
          <el-form-item label="自行外出">
            <el-input-number v-model="handoverForm.selfOutings" :min="0" />
          </el-form-item>
          <el-form-item label="出院">
            <el-input-number v-model="handoverForm.discharges" :min="0" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="请假外出">
            <el-input-number v-model="handoverForm.leaveOutings" :min="0" />
          </el-form-item>
          <el-form-item label="紧急转诊">
            <el-input-number v-model="handoverForm.emergencyReferrals" :min="0" />
          </el-form-item>
          <el-form-item label="转诊并住院">
            <el-input-number
              v-model="handoverForm.referralAndHospitalizations"
              :min="0"
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="外出就医">
            <el-input-number v-model="handoverForm.medicalOutings" :min="0" />
          </el-form-item>
        </el-row>
        <el-form-item label="快速填写">
          <el-button type="primary" @click="handleQuickFill">导入上一班次数据</el-button>
        </el-form-item>
        <el-form-item label="填写时间">
          <el-input v-model="handoverForm.fillTime" readonly />
        </el-form-item>

        <el-form-item label="接班人" v-if="followStatus == 2">
          <el-input v-model="handoverForm.persion" disabled />
        </el-form-item>
        <el-form-item label="接班时间" v-if="followStatus == 2">
          <el-input v-model="handoverForm.revTime" disabled />
        </el-form-item>
        <el-row style="margin: 5px 0px">
          <el-col :offset="22">
            <el-button type="primary">新增</el-button>
          </el-col>
        </el-row>
        <el-table :data="detailTableData" border style="width: 100%">
          <el-table-column prop="roomNumber" label="房间号">
            <template #default="scope">
              <el-select
                v-model="scope.row.roomNumber"
                placeholder="选择房间号"
                size="large"
                style="width: 120px"
              >
                <el-option
                  v-for="item in roomOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="bedNumber" label="床位号" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="careLevel" label="护理等级" />
          <el-table-column prop="content" label="交接内容">
            <template #default="scope">
              <el-input
                v-model="scope.row.content"
                style="width: 240px"
                :rows="2"
                type="textarea"
                placeholder="交接内容"
              />
            </template>
          </el-table-column>
        </el-table>
        <!-- 新增接班数据字段 -->
      </el-form>
      <template #footer>
        <el-button @click="handoverDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitHandover" v-if="followStatus == 1"
          >提交</el-button
        >
        <el-button type="primary" @click="handleSubmitHandover2" v-if="followStatus == 2"
          >接班</el-button
        >
      </template>
    </el-dialog>

    <!-- 交接详情弹窗 -->
    <el-dialog title="交接详情" v-model="detailDialogVisible" width="80%">
      <el-table :data="detailTableData" border style="width: 100%">
        <el-table-column prop="roomNumber" label="房间号" />
        <el-table-column prop="bedNumber" label="床位号" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="careLevel" label="护理等级" />
        <el-table-column prop="content" label="交接内容">
          <template #default="scope">
            <el-input
              v-model="textarea"
              style="width: 240px"
              :rows="2"
              type="textarea"
              placeholder="交接内容"
            />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
export default {
  data() {
    return {
      dateRange: [],
      selectedAreas: [],
      selectedShifts: [],
      followStatus: 1, //发起1是交班，接班为2
      staffName: "",
      areaList: [
        { id: 1, name: "护理区域" },
        { id: 2, name: "管理区域" },
        { id: 3, name: "高位区域" },
      ], // 区域列表
      shiftList: [
        { id: 1, name: "早班" },
        { id: 2, name: "中班" },
        { id: 3, name: "晚班" },
        { id: 4, name: "夜班" },
      ], // 班次列表
      nurseList: [
        { id: 1, name: "张护士" },
        { id: 2, name: "王护士" },
        { id: 3, name: "孙护士" },
      ], // 护士列表
      tableData: [
        {
          area: "护理区域",
          date: "2023-04-01",
          shift: "早班",
          nurse: "张鸣",
          totalReceivers: 1,
          newAdmissions: 2,
          trialAdmissions: 3,
          deaths: 4,
          selfOutings: 5,
          discharges: 6,
          leaveOutings: 7,
          emergencyReferrals: 8,
          referralAndHospitalizations: 9,
          medicalOutings: 10,
          fillTime: "2025-04-01",
          persion: "admin",
          revTime: "2025-04-01",
        },
        {
          area: "护理区域",
          date: "2023-04-12",
          shift: "早班",
          nurse: "孙俪丽",
          totalReceivers: 1,
          newAdmissions: 2,
          trialAdmissions: 3,
          deaths: 4,
          selfOutings: 5,
          discharges: 6,
          leaveOutings: 7,
          emergencyReferrals: 8,
          referralAndHospitalizations: 9,
          medicalOutings: 10,
          fillTime: "2025-04-01",
          persion: "admin",
          revTime: "2025-04-01",
        },
        {
          area: "护理区域",
          date: "2023-04-13",
          shift: "早班",
          nurse: "程晓",
          totalReceivers: 1,
          newAdmissions: 2,
          trialAdmissions: 3,
          deaths: 4,
          selfOutings: 5,
          discharges: 6,
          leaveOutings: 7,
          emergencyReferrals: 8,
          referralAndHospitalizations: 9,
          medicalOutings: 10,
          fillTime: "2025-04-01",
          persion: "admin",
          revTime: "2025-04-01",
        },
        {
          area: "护理区域",
          date: "2023-04-16",
          shift: "早班",
          nurse: "王晓梅",
          totalReceivers: 1,
          newAdmissions: 2,
          trialAdmissions: 3,
          deaths: 4,
          selfOutings: 5,
          discharges: 6,
          leaveOutings: 7,
          emergencyReferrals: 8,
          referralAndHospitalizations: 9,
          medicalOutings: 10,
          fillTime: "2025-04-01",
          persion: "admin",
          revTime: "2025-04-01",
        },
      ], // 查询结果数据列表
      handoverDialogVisible: false,
      detailDialogVisible: false,
      handoverForm: {
        date: "",
        nurseId: "",
        areaId: "",
        shiftId: "",
        fillTime: "",
      },
      detailTableData: [
        {
          id: 1,
          roomNumber: "102",
          bedNumber: "02",
          name: "张三",
          careLevel: "三级",
          content: "1.今日预约做检查",
        },
        {
          id: 2,
          roomNumber: "102",
          bedNumber: "02",
          name: "张三",
          careLevel: "三级",
          content: "1.今日预约做检查",
        },
        {
          id: 3,
          roomNumber: "102",
          bedNumber: "02",
          name: "张三",
          careLevel: "三级",
          content: "1.今日预约做检查",
        },
      ],
      roomOptions: [
        {
          id: 1,
          label: "101",
          value: "101",
        },
        {
          id: 2,
          label: "202",
          value: "202",
        },
        {
          id: 3,
          label: "303",
          value: "303",
        },
      ],
    };
  },
  methods: {
    handleSearch() {
      // 实现查询逻辑
    },
    handleHandover() {
      this.handoverDialogVisible = true;
    },
    handleEdit(row) {
      this.followStatus = 1;
      console.log(row, "handleEdit");
      this.handoverDialogVisible = true;
      this.handoverForm = row;
      // 实现修改逻辑
    },
    handleDetail(row) {
      this.detailDialogVisible = true;
    },
    handleReceive(row) {
      this.followStatus = 2;
      this.handoverDialogVisible = true;
      this.handoverForm = row;
      // 实现接班逻辑
    },
    handleQuickFill() {
      // 实现快速填写逻辑
    },
    handleSubmitHandover() {
      // 实现提交交班记录逻辑
      ElMessage({
        message: "添加成功",
        type: "success",
      });
      this.handoverDialogVisible = false;
    },
    handleSubmitHandover2() {
      // 实现提交交班记录逻辑
      ElMessage({
        message: "交接班成功",
        type: "success",
      });
      this.handoverDialogVisible = false;
    },
  },
};
</script>

<style scoped>
.filter-form {
  margin-bottom: 20px;
}
</style>
