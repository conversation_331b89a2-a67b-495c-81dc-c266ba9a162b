import{X as t}from"./index-B0qHf98Y.js";function n(e){return t({url:"/tool/gen/list",method:"get",params:e})}function r(e){return t({url:"/tool/gen/db/list",method:"get",params:e})}function l(e){return t({url:"/tool/gen/"+e,method:"get"})}function a(e){return t({url:"/tool/gen",method:"put",data:e})}function u(e){return t({url:"/tool/gen/importTable",method:"post",params:e})}function s(e){return t({url:"/tool/gen/createTable",method:"post",params:e})}function i(e){return t({url:"/tool/gen/preview/"+e,method:"get"})}function g(e){return t({url:"/tool/gen/"+e,method:"delete"})}function d(e){return t({url:"/tool/gen/genCode/"+e,method:"get"})}function m(e){return t({url:"/tool/gen/synchDb/"+e,method:"get"})}export{n as a,d as b,s as c,g as d,l as g,u as i,r as l,i as p,m as s,a as u};
