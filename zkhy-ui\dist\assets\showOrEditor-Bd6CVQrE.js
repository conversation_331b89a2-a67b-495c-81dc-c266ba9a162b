import{g as K,u as j,a as G}from"./tMedicationInventoryRecord-DEKqwOhj.js";import{g as H}from"./telderinfo-BSpoeVyZ.js";import{_ as J,B as W,d as X,r as h,C as Z,N as ee,e as d,c as T,o as f,f as a,h as o,l as t,i as l,t as c,j as g,k,n as w,K as te,L as le,v as ae,x as oe}from"./index-B0qHf98Y.js";const u=b=>(ae("data-v-f7cc10ec"),b=b(),oe(),b),se={class:"app-container"},ne={class:"section"},de=u(()=>l("div",{class:"section-title"},"老人信息",-1)),re={class:"tbcss"},ue=u(()=>l("th",{class:"tbTr"},"老人姓名",-1)),ie={class:"tbTrVal"},ce=u(()=>l("th",{class:"tbTr"},"老人编号",-1)),_e={class:"tbTrVal"},pe=u(()=>l("th",{class:"tbTr"},"性       别",-1)),me={class:"tbTrVal"},fe={key:1},ve=u(()=>l("th",{class:"tbTr"},"床位编号",-1)),he={class:"tbTrVal"},be=u(()=>l("th",{class:"tbTr"},"房间信息",-1)),ye={class:"tbTrVal"},ge=u(()=>l("th",{class:"tbTr"},"年       龄",-1)),Ve={class:"tbTrVal"},xe=u(()=>l("th",{class:"tbTr"},"楼栋信息",-1)),Te={class:"tbTrVal"},ke=u(()=>l("th",{class:"tbTr"},"楼层信息",-1)),we={class:"tbTrVal"},Ie=u(()=>l("th",{class:"tbTr"},"护理等级",-1)),Ne={class:"tbTrVal"},Ue=u(()=>l("th",{class:"tbTr"},"入住时间",-1)),Ce={class:"tbTrVal"},De={class:"section"},Re=u(()=>l("div",{class:"section-title"},"药品清点",-1)),Be={style:{margin:"0px 8px 12px 10px","font-weight":"600",color:"#555"}},Se={style:{"margin-left":"10px"}},Ye={class:"footerLeft"},Le={class:"footerLeftMargin"},Qe={class:"dialog-footer"},Me=W({name:"Notice"}),Pe=Object.assign(Me,{emits:"close",setup(b,{expose:C,emit:D}){const{proxy:V}=X(),{inventory_results:R,sys_user_sex:B}=V.useDict("inventory_results","sys_user_sex"),I=D;h([]);const p=h(!1);h(!0);const i=h(!1),x=h(""),N=h(!1),S=Z({form:{},queryParams:{pageNum:1,pageSize:10},rules:{}}),{queryParams:$e,form:e,rules:Y}=ee(S);function L(v){p.value=!0,v.type=="show"?(i.value=!0,x.value="查看药品清点记录"):v.type=="edit"&&(i.value=!1,N.value=!0,x.value="修改药品清点记录"),K(v.id).then(s=>{console.log(s,"res21212"),e.value=s.data,p.value=!0,H(s.data.elderId).then(r=>{e.value.gender=r.data.elderInfo.gender,e.value.age=r.data.elderInfo.age,e.value.nursingLevel=r.data.elderInfo.nursingLevel,e.value.checkInDate=r.data.elderInfo.checkInDate,e.value.avatar=r.data.elderInfo.avatar})})}function Q(){console.log(e.value,"111111"),e.value.id!=null?(console.log(e.value,"222222"),j(e.value).then(v=>{V.$modal.msgSuccess("修改成功"),p.value=!1,I("close")})):(console.log(e.value,"333333"),G(e.value).then(v=>{V.$modal.msgSuccess("新增成功"),p.value=!1,I("close")}))}function M(){p.value=!1}return C({init:L}),(v,s)=>{const r=d("el-input"),P=d("dict-tag-span"),y=d("el-row"),_=d("el-col"),$=d("el-avatar"),q=d("el-date-picker"),m=d("el-form-item"),E=d("el-option"),z=d("el-select"),F=d("el-card"),O=d("el-form"),U=d("el-button"),A=d("el-dialog");return f(),T("div",se,[a(A,{title:x.value,modelValue:p.value,"onUpdate:modelValue":s[9]||(s[9]=n=>p.value=n),width:"60%","append-to-body":""},{footer:o(()=>[l("div",Ye,[l("div",Le,[a(m,{label:"记录人",prop:"recorder"},{default:o(()=>[a(r,{modelValue:t(e).recorder,"onUpdate:modelValue":s[8]||(s[8]=n=>t(e).recorder=n),placeholder:"请输入记录人",disabled:!0},null,8,["modelValue"])]),_:1})]),l("div",Qe,[i.value?k("",!0):(f(),g(U,{key:0,type:"primary",onClick:Q},{default:o(()=>[w("确 定")]),_:1})),a(U,{onClick:M},{default:o(()=>[w("返 回")]),_:1})])])]),default:o(()=>[a(O,{ref:"inventoryRecordRef",model:t(e),rules:t(Y),"label-width":"80px"},{default:o(()=>[l("div",ne,[de,a(y,null,{default:o(()=>[a(_,{span:20},{default:o(()=>[a(y,{gutter:24},{default:o(()=>[l("table",re,[l("tr",null,[ue,l("th",ie,[a(r,{modelValue:t(e).elderName,"onUpdate:modelValue":s[0]||(s[0]=n=>t(e).elderName=n),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},disabled:i.value},null,8,["modelValue","disabled"])]),ce,l("th",_e,c(t(e).elderCode||"-"),1),pe,l("th",me,[t(e).gender?(f(),g(P,{key:0,options:t(B),value:t(e).gender},null,8,["options","value"])):(f(),T("span",fe,"-"))])]),l("tr",null,[ve,l("th",he,c(t(e).roomNumber||"")+"-"+c(t(e).bedNumber||""),1),be,l("th",ye,c(t(e).roomNumber||"-"),1),ge,l("th",Ve,c(t(e).age||"-"),1)]),l("tr",null,[xe,l("th",Te,c(t(e).buildingName||"-"),1),ke,l("th",we,c(t(e).floorNumber||"-"),1),Ie,l("th",Ne,c(t(e).nursingLevel||"-"),1)]),l("tr",null,[Ue,l("th",Ce,c(t(e).checkInDate||"-"),1)])])]),_:1})]),_:1}),a(_,{span:4},{default:o(()=>[t(e).avatar?(f(),g($,{key:0,shape:"square",size:140,fit:"fill",src:t(e).avatar},null,8,["src"])):k("",!0)]),_:1})]),_:1})]),l("div",De,[Re,a(F,{class:"shadow-md hover:shadow-lg transition-shadow",style:{"margin-bottom":"10px"}},{default:o(()=>[a(y,null,{default:o(()=>[a(_,{span:23},{default:o(()=>[l("div",Be,[w(" 药品名称 "),l("span",Se,c(t(e).medicineName),1),k("",!0)]),a(y,null,{default:o(()=>[a(_,{span:8},{default:o(()=>[a(m,{label:"清点日期",prop:"recordTime"},{default:o(()=>[a(q,{clearable:"",modelValue:t(e).recordTime,"onUpdate:modelValue":s[2]||(s[2]=n=>t(e).recordTime=n),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择清点日期",format:"YYYY-MM-DD",style:{width:"200px"},disabled:i.value||N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(m,{label:"已派发",prop:"distributedQuantity"},{default:o(()=>[a(r,{modelValue:t(e).distributedQuantity,"onUpdate:modelValue":s[3]||(s[3]=n=>t(e).distributedQuantity=n),placeholder:"请输入已派发",style:{width:"200px"},disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(m,{label:"剩余数量",prop:"remainingQuantity"},{default:o(()=>[a(r,{modelValue:t(e).remainingQuantity,"onUpdate:modelValue":s[4]||(s[4]=n=>t(e).remainingQuantity=n),placeholder:"请输入剩余数量",style:{width:"200px"},disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(m,{label:"清点结果",prop:"inventoryResult"},{default:o(()=>[a(z,{modelValue:t(e).inventoryResult,"onUpdate:modelValue":s[5]||(s[5]=n=>t(e).inventoryResult=n),placeholder:"请选择清点结果",clearable:"",style:{width:"200px"},disabled:i.value},{default:o(()=>[(f(!0),T(te,null,le(t(R),n=>(f(),g(E,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(m,{label:"清点人",prop:"inventoryPerson"},{default:o(()=>[a(r,{modelValue:t(e).inventoryPerson,"onUpdate:modelValue":s[6]||(s[6]=n=>t(e).inventoryPerson=n),placeholder:"请输入清点人",style:{width:"200px"},disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:24},{default:o(()=>[a(m,{label:"清点备注",prop:"remark"},{default:o(()=>[a(r,{modelValue:t(e).remark,"onUpdate:modelValue":s[7]||(s[7]=n=>t(e).remark=n),type:"textarea",rows:"3",placeholder:"请输入备注",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Fe=J(Pe,[["__scopeId","data-v-f7cc10ec"]]);export{Fe as default};
