import{g as ee,u as le,a as te}from"./tMedicationDisposalRecord-vFA7mRLG.js";import{g as ae}from"./telderinfo-BSpoeVyZ.js";import{l as oe}from"./telderAttachement-C4ARfNBy.js";import{_ as se,B as de,d as ne,r as v,C as ie,N as re,e as i,c as I,o as f,f as l,h as s,l as t,i as a,t as m,j as y,k as D,n as w,K as R,L as E,v as ue,x as ce}from"./index-B0qHf98Y.js";const r=x=>(ue("data-v-6027bd2e"),x=x(),ce(),x),pe={class:"app-container"},_e={class:"section"},me=r(()=>a("div",{class:"section-title"},"老人信息",-1)),fe={class:"tbcss"},he=r(()=>a("th",{class:"tbTr"},"老人姓名",-1)),ve={class:"tbTrVal"},be=r(()=>a("th",{class:"tbTr"},"老人编号",-1)),ge={class:"tbTrVal"},Ve=r(()=>a("th",{class:"tbTr"},"性       别",-1)),ye={class:"tbTrVal"},xe={key:1},Ie=r(()=>a("th",{class:"tbTr"},"床位编号",-1)),ke={class:"tbTrVal"},Te=r(()=>a("th",{class:"tbTr"},"房间信息",-1)),De={class:"tbTrVal"},we=r(()=>a("th",{class:"tbTr"},"年       龄",-1)),Ne={class:"tbTrVal"},Le=r(()=>a("th",{class:"tbTr"},"楼栋信息",-1)),Ue={class:"tbTrVal"},Ce=r(()=>a("th",{class:"tbTr"},"楼层信息",-1)),Se={class:"tbTrVal"},Re=r(()=>a("th",{class:"tbTr"},"护理等级",-1)),Ee={class:"tbTrVal"},Be=r(()=>a("th",{class:"tbTr"},"入住时间",-1)),Ye={class:"tbTrVal"},Ae={class:"section"},Fe=r(()=>a("div",{class:"section-title"},"药品清点",-1)),Me={style:{margin:"0px 8px 12px 70px","font-weight":"600",color:"#555"}},Oe={style:{"margin-left":"10px"}},Pe={class:"footerLeft"},$e={class:"footerLeftMargin"},qe={class:"dialog-footer"},je=de({name:"Notice"}),ze=Object.assign(je,{emits:"close",setup(x,{expose:B,emit:Y}){const{proxy:k}=ne(),{processing_results:A,sys_user_sex:F,is_expired:M}=k.useDict("processing_results","sys_user_sex","is_expired"),N=Y;v([]);const b=v(!1);v(!0);const c=v(!1),T=v(""),L=v(!1),V=v([]),O=v([]),P=ie({form:{},queryParams:{pageNum:1,pageSize:10},rules:{}}),{queryParams:Ge,form:e,rules:$}=re(P);function q(n){V.value=[],b.value=!0,n.type=="show"?(c.value=!0,T.value="查看药品清点记录"):n.type=="edit"&&(c.value=!1,L.value=!0,T.value="修改药品清点记录"),ee(n.id).then(o=>{console.log(o,"res21212"),e.value=o.data,e.value.isExpired=o.data.isExpired.toString(),b.value=!0,ae(o.data.elderId).then(u=>{e.value.gender=u.data.elderInfo.gender,e.value.age=u.data.elderInfo.age,e.value.nursingLevel=u.data.elderInfo.nursingLevel,e.value.checkInDate=u.data.elderInfo.checkInDate,e.value.avatar=u.data.elderInfo.avatar});let p={elderId:o.data.id,attachmentType:"medicine_processing_form"};oe(p).then(u=>{O.value=u.rows,u.rows.map(g=>{V.value.push(g.filePath)}),console.log(u,"res"),console.log(V.value,"res")})})}function j(){e.value.id!=null?le(e.value).then(n=>{k.$modal.msgSuccess("修改成功"),b.value=!1,N("close")}):te(e.value).then(n=>{k.$modal.msgSuccess("新增成功"),b.value=!1,N("close")})}function z(n){n&&(Array.isArray(n)?(n.map(o=>{medicineCards.value.map(p=>{o.remark==p.medicationId&&p.ossIds.push(o.ossId)})}),fileOssIdList.value=fileOssIdList.value.concat(n.map(o=>o.ossId))):fileOssIdList.value.push(n)),uploadFileList.value.push(n[0])}function G(n){console.log(n,"handleRemoveAtt")}function H(){b.value=!1,reset()}return B({init:q}),(n,o)=>{const p=i("el-input"),u=i("dict-tag-span"),g=i("el-row"),_=i("el-col"),K=i("el-avatar"),J=i("el-date-picker"),h=i("el-form-item"),U=i("el-option"),C=i("el-select"),Q=i("ImageUpload"),W=i("el-card"),X=i("el-form"),S=i("el-button"),Z=i("el-dialog");return f(),I("div",pe,[l(Z,{title:T.value,modelValue:b.value,"onUpdate:modelValue":o[10]||(o[10]=d=>b.value=d),width:"60%","append-to-body":""},{footer:s(()=>[a("div",Pe,[a("div",$e,[l(h,{label:"记录人",prop:"recorder"},{default:s(()=>[l(p,{modelValue:t(e).recorder,"onUpdate:modelValue":o[9]||(o[9]=d=>t(e).recorder=d),placeholder:"请输入记录人",disabled:!0},null,8,["modelValue"])]),_:1})]),a("div",qe,[c.value?D("",!0):(f(),y(S,{key:0,type:"primary",onClick:j},{default:s(()=>[w("确 定")]),_:1})),l(S,{onClick:H},{default:s(()=>[w("返 回")]),_:1})])])]),default:s(()=>[l(X,{ref:"inventoryRecordRef",model:n.card,rules:t($),"label-width":"140px"},{default:s(()=>[a("div",_e,[me,l(g,null,{default:s(()=>[l(_,{span:20},{default:s(()=>[l(g,{gutter:24},{default:s(()=>[a("table",fe,[a("tr",null,[he,a("th",ve,[l(p,{modelValue:t(e).elderName,"onUpdate:modelValue":o[0]||(o[0]=d=>t(e).elderName=d),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},onClick:n.searchElderHandle,disabled:c.value},null,8,["modelValue","onClick","disabled"])]),be,a("th",ge,m(t(e).elderCode||"-"),1),Ve,a("th",ye,[t(e).gender?(f(),y(u,{key:0,options:t(F),value:t(e).gender},null,8,["options","value"])):(f(),I("span",xe,"-"))])]),a("tr",null,[Ie,a("th",ke,m(t(e).roomNumber||"")+"-"+m(t(e).bedNumber||""),1),Te,a("th",De,m(t(e).roomNumber||"-"),1),we,a("th",Ne,m(t(e).age||"-"),1)]),a("tr",null,[Le,a("th",Ue,m(t(e).buildingName||"-"),1),Ce,a("th",Se,m(t(e).floorNumber||"-"),1),Re,a("th",Ee,m(t(e).nursingLevel||"-"),1)]),a("tr",null,[Be,a("th",Ye,m(t(e).checkInDate||"-"),1)])])]),_:1})]),_:1}),l(_,{span:4},{default:s(()=>[t(e).avatar?(f(),y(K,{key:0,shape:"square",size:140,fit:"fill",src:t(e).avatar},null,8,["src"])):D("",!0)]),_:1})]),_:1})]),a("div",Ae,[Fe,l(W,{class:"shadow-md hover:shadow-lg transition-shadow",style:{"margin-bottom":"10px"}},{default:s(()=>[l(g,null,{default:s(()=>[l(_,{span:23},{default:s(()=>[a("div",Me,[w(" 药品名称 "),a("span",Oe,m(t(e).medicineName),1),D("",!0)]),l(g,null,{default:s(()=>[l(_,{span:8},{default:s(()=>[l(h,{label:"处理日期",prop:"disposalDate"},{default:s(()=>[l(J,{clearable:"",modelValue:t(e).disposalDate,"onUpdate:modelValue":o[2]||(o[2]=d=>t(e).disposalDate=d),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择处理日期",value:"YYYY-MM-DD",disabled:c.value||L.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(_,{span:8},{default:s(()=>[l(h,{label:"是否在有效期",prop:"isExpired"},{default:s(()=>[l(C,{modelValue:t(e).isExpired,"onUpdate:modelValue":o[3]||(o[3]=d=>t(e).isExpired=d),placeholder:"请选择清点结果",clearable:"",disabled:c.value},{default:s(()=>[(f(!0),I(R,null,E(t(M),d=>(f(),y(U,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(_,{span:8},{default:s(()=>[l(h,{label:"处理结果",prop:"disposalResult"},{default:s(()=>[l(C,{modelValue:t(e).disposalResult,"onUpdate:modelValue":o[4]||(o[4]=d=>t(e).disposalResult=d),placeholder:"请选择清点结果",clearable:"",disabled:c.value},{default:s(()=>[(f(!0),I(R,null,E(t(A),d=>(f(),y(U,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(_,{span:8},{default:s(()=>[l(h,{label:"老人及监护人确认",prop:"confirmation"},{default:s(()=>[l(p,{modelValue:t(e).confirmation,"onUpdate:modelValue":o[5]||(o[5]=d=>t(e).confirmation=d),placeholder:"请输入老人及监护人确认",disabled:c.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(_,{span:8},{default:s(()=>[l(h,{label:"处理人",prop:"handler"},{default:s(()=>[l(p,{modelValue:t(e).handler,"onUpdate:modelValue":o[6]||(o[6]=d=>t(e).handler=d),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),l(_,{span:24},{default:s(()=>[l(h,{label:"药品问题描述",prop:"problemDescription"},{default:s(()=>[l(p,{modelValue:t(e).problemDescription,"onUpdate:modelValue":o[7]||(o[7]=d=>t(e).problemDescription=d),type:"textarea",disabled:c.value,placeholder:"请输入内容"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(_,{span:24},{default:s(()=>[l(h,{label:"药品处理确认书",prop:"problemDescription"},{default:s(()=>[l(Q,{modelValue:V.value,"onUpdate:modelValue":o[8]||(o[8]=d=>V.value=d),fileData:{category:"medicine_processing_type",attachmentType:"medicine_processing_form",remark:t(e).medicationId},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,disabled:c.value,limit:n.uploadLimit,onDeleteAtt:G,onSubmitParentValue:z},null,8,["modelValue","fileData","disabled","limit"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),We=se(ze,[["__scopeId","data-v-6027bd2e"]]);export{We as default};
