import{g as ae,u as oe,a as se}from"./tMedicationDisposalRecord-vFA7mRLG.js";import{g as de}from"./telderinfo-BSpoeVyZ.js";import{l as ne}from"./telderAttachement-C4ARfNBy.js";import{_ as re,B as ie,d as ue,r as c,C as ce,N as pe,e as r,c as I,o as h,f as t,h as s,l,i as a,t as f,j as y,k as w,n as N,K as B,L as E,v as _e,x as me}from"./index-B0qHf98Y.js";const i=x=>(_e("data-v-e0cef12b"),x=x(),me(),x),fe={class:"app-container"},he={class:"section"},ve=i(()=>a("div",{class:"section-title"},"老人信息",-1)),be={class:"tbcss"},ge=i(()=>a("th",{class:"tbTr"},"老人姓名",-1)),Ve={class:"tbTrVal"},ye=i(()=>a("th",{class:"tbTr"},"老人编号",-1)),xe={class:"tbTrVal"},Ie=i(()=>a("th",{class:"tbTr"},"性       别",-1)),Te={class:"tbTrVal"},ke={key:1},De=i(()=>a("th",{class:"tbTr"},"床位编号",-1)),we={class:"tbTrVal"},Ne=i(()=>a("th",{class:"tbTr"},"房间信息",-1)),Ue={class:"tbTrVal"},Se=i(()=>a("th",{class:"tbTr"},"年       龄",-1)),Ce={class:"tbTrVal"},Le=i(()=>a("th",{class:"tbTr"},"楼栋信息",-1)),Re={class:"tbTrVal"},Be=i(()=>a("th",{class:"tbTr"},"楼层信息",-1)),Ee={class:"tbTrVal"},Ye=i(()=>a("th",{class:"tbTr"},"护理等级",-1)),Ae={class:"tbTrVal"},Fe=i(()=>a("th",{class:"tbTr"},"入住时间",-1)),Me={class:"tbTrVal"},Pe={class:"section"},$e=i(()=>a("div",{class:"section-title"},"药品清点",-1)),qe={style:{margin:"0px 8px 12px 70px","font-weight":"600",color:"#555"}},Oe={style:{"margin-left":"10px"}},je={class:"footerLeft"},ze={class:"footerLeftMargin"},Ge={class:"dialog-footer"},Ke=ie({name:"Notice"}),He=Object.assign(Ke,{emits:"close",setup(x,{expose:Y,emit:A}){const{proxy:T}=ue(),{processing_results:F,sys_user_sex:M,is_expired:P}=T.useDict("processing_results","sys_user_sex","is_expired"),U=A;c([]);const b=c(!1);c(!0);const p=c(!1),k=c(""),S=c(!1),V=c([]),$=c([]),q=c([]),O=c([]),D=c([]),j=ce({form:{},queryParams:{pageNum:1,pageSize:10},rules:{}}),{queryParams:Je,form:e,rules:z}=pe(j);function G(n){V.value=[],b.value=!0,n.type=="show"?(p.value=!0,k.value="查看药品清点记录"):n.type=="edit"&&(p.value=!1,S.value=!0,k.value="修改药品清点记录"),ae(n.id).then(o=>{console.log(o,"res21212"),e.value=o.data,e.value.isExpired=o.data.isExpired.toString(),b.value=!0,de(o.data.elderId).then(u=>{e.value.gender=u.data.elderInfo.gender,e.value.age=u.data.elderInfo.age,e.value.nursingLevel=u.data.elderInfo.nursingLevel,e.value.checkInDate=u.data.elderInfo.checkInDate,e.value.avatar=u.data.elderInfo.avatar});let _={elderId:o.data.id,attachmentType:"medicine_processing_form"};ne(_).then(u=>{$.value=u.rows,u.rows.map(g=>{V.value.push(g.filePath)}),console.log(u,"res"),console.log(V.value,"res")})})}function K(){e.value.id!=null?oe(e.value).then(n=>{T.$modal.msgSuccess("修改成功"),b.value=!1,U("close")}):se(e.value).then(n=>{T.$modal.msgSuccess("新增成功"),b.value=!1,U("close")})}function H(n){n&&(Array.isArray(n)?(n.map(o=>{q.value.map(_=>{o.remark==_.medicationId&&_.ossIds.push(o.ossId)})}),D.value=D.value.concat(n.map(o=>o.ossId))):D.value.push(n)),O.value.push(n[0])}function J(n){console.log(n,"handleRemoveAtt")}function Q(){b.value=!1}return Y({init:G}),(n,o)=>{const _=r("el-input"),u=r("dict-tag-span"),g=r("el-row"),m=r("el-col"),W=r("el-avatar"),X=r("el-date-picker"),v=r("el-form-item"),C=r("el-option"),L=r("el-select"),Z=r("ImageUpload"),ee=r("el-card"),le=r("el-form"),R=r("el-button"),te=r("el-dialog");return h(),I("div",fe,[t(te,{title:k.value,modelValue:b.value,"onUpdate:modelValue":o[10]||(o[10]=d=>b.value=d),width:"60%","append-to-body":""},{footer:s(()=>[a("div",je,[a("div",ze,[t(v,{label:"记录人",prop:"recorder"},{default:s(()=>[t(_,{modelValue:l(e).recorder,"onUpdate:modelValue":o[9]||(o[9]=d=>l(e).recorder=d),placeholder:"请输入记录人",disabled:!0},null,8,["modelValue"])]),_:1})]),a("div",Ge,[p.value?w("",!0):(h(),y(R,{key:0,type:"primary",onClick:K},{default:s(()=>[N("确 定")]),_:1})),t(R,{onClick:Q},{default:s(()=>[N("返 回")]),_:1})])])]),default:s(()=>[t(le,{ref:"inventoryRecordRef",model:l(e),rules:l(z),"label-width":"140px"},{default:s(()=>[a("div",he,[ve,t(g,null,{default:s(()=>[t(m,{span:20},{default:s(()=>[t(g,{gutter:24},{default:s(()=>[a("table",be,[a("tr",null,[ge,a("th",Ve,[t(_,{modelValue:l(e).elderName,"onUpdate:modelValue":o[0]||(o[0]=d=>l(e).elderName=d),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},disabled:p.value},null,8,["modelValue","disabled"])]),ye,a("th",xe,f(l(e).elderCode||"-"),1),Ie,a("th",Te,[l(e).gender?(h(),y(u,{key:0,options:l(M),value:l(e).gender},null,8,["options","value"])):(h(),I("span",ke,"-"))])]),a("tr",null,[De,a("th",we,f(l(e).roomNumber||"")+"-"+f(l(e).bedNumber||""),1),Ne,a("th",Ue,f(l(e).roomNumber||"-"),1),Se,a("th",Ce,f(l(e).age||"-"),1)]),a("tr",null,[Le,a("th",Re,f(l(e).buildingName||"-"),1),Be,a("th",Ee,f(l(e).floorNumber||"-"),1),Ye,a("th",Ae,f(l(e).nursingLevel||"-"),1)]),a("tr",null,[Fe,a("th",Me,f(l(e).checkInDate||"-"),1)])])]),_:1})]),_:1}),t(m,{span:4},{default:s(()=>[l(e).avatar?(h(),y(W,{key:0,shape:"square",size:140,fit:"fill",src:l(e).avatar},null,8,["src"])):w("",!0)]),_:1})]),_:1})]),a("div",Pe,[$e,t(ee,{class:"shadow-md hover:shadow-lg transition-shadow",style:{"margin-bottom":"10px"}},{default:s(()=>[t(g,null,{default:s(()=>[t(m,{span:23},{default:s(()=>[a("div",qe,[N(" 药品名称 "),a("span",Oe,f(l(e).medicineName),1),w("",!0)]),t(g,null,{default:s(()=>[t(m,{span:8},{default:s(()=>[t(v,{label:"处理日期",prop:"disposalDate"},{default:s(()=>[t(X,{clearable:"",modelValue:l(e).disposalDate,"onUpdate:modelValue":o[2]||(o[2]=d=>l(e).disposalDate=d),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择处理日期",value:"YYYY-MM-DD",disabled:p.value||S.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(m,{span:8},{default:s(()=>[t(v,{label:"是否在有效期",prop:"isExpired"},{default:s(()=>[t(L,{modelValue:l(e).isExpired,"onUpdate:modelValue":o[3]||(o[3]=d=>l(e).isExpired=d),placeholder:"请选择清点结果",clearable:"",disabled:p.value},{default:s(()=>[(h(!0),I(B,null,E(l(P),d=>(h(),y(C,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),t(m,{span:8},{default:s(()=>[t(v,{label:"处理结果",prop:"disposalResult"},{default:s(()=>[t(L,{modelValue:l(e).disposalResult,"onUpdate:modelValue":o[4]||(o[4]=d=>l(e).disposalResult=d),placeholder:"请选择清点结果",clearable:"",disabled:p.value},{default:s(()=>[(h(!0),I(B,null,E(l(F),d=>(h(),y(C,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),t(m,{span:8},{default:s(()=>[t(v,{label:"老人及监护人确认",prop:"confirmation"},{default:s(()=>[t(_,{modelValue:l(e).confirmation,"onUpdate:modelValue":o[5]||(o[5]=d=>l(e).confirmation=d),placeholder:"请输入老人及监护人确认",disabled:p.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(m,{span:8},{default:s(()=>[t(v,{label:"处理人",prop:"handler"},{default:s(()=>[t(_,{modelValue:l(e).handler,"onUpdate:modelValue":o[6]||(o[6]=d=>l(e).handler=d),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(m,{span:24},{default:s(()=>[t(v,{label:"药品问题描述",prop:"problemDescription"},{default:s(()=>[t(_,{modelValue:l(e).problemDescription,"onUpdate:modelValue":o[7]||(o[7]=d=>l(e).problemDescription=d),type:"textarea",disabled:p.value,placeholder:"请输入内容"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(m,{span:24},{default:s(()=>[t(v,{label:"药品处理确认书",prop:"problemDescription"},{default:s(()=>[t(Z,{modelValue:V.value,"onUpdate:modelValue":o[8]||(o[8]=d=>V.value=d),fileData:{category:"medicine_processing_type",attachmentType:"medicine_processing_form",remark:l(e).medicationId},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,disabled:p.value,onDeleteAtt:J,onSubmitParentValue:H},null,8,["modelValue","fileData","disabled"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),el=re(He,[["__scopeId","data-v-e0cef12b"]]);export{el as default};
