import{_ as Te,r as _,d as De,C as Se,N as Ce,e as f,c as C,o as w,f as e,bg as <PERSON>,h as l,n as v,l as r,i as d,k as q,j as W,t as T,K as X,L as ke,Q as Ee,J as Ie,O as Pe,G as k,E as Z,v as xe,x as Re}from"./index-B0qHf98Y.js";import{b as qe,c as Ue,e as Ae,f as ee,g as Oe}from"./leave-Dd4WELmg.js";import{g as Me}from"./user-u7DySmj3.js";import{i as Ye,a as Be,b as $e}from"./yjj-DmX1NTQH.js";import{u as ze}from"./telderAttachement-C4ARfNBy.js";const He="/assets/sxdxj-BgBYMjpN.png",y=U=>(xe("data-v-3d3dc2df"),U=U(),<PERSON>(),U),je={class:"section"},Ge=y(()=>d("div",{class:"section-title"},"老人信息",-1)),Fe={class:"info-item"},Je=y(()=>d("b",null,"经  办  人",-1)),Qe={key:0,class:"info-item"},Ke=y(()=>d("b",null,"老人姓名",-1)),We={key:1,class:"info-item"},Xe=y(()=>d("b",null,"老人姓名",-1)),Ze={class:"info-item"},ea=y(()=>d("b",null,"老人编号",-1)),aa={class:"info-item"},la=y(()=>d("b",null,"联系电话",-1)),ta={class:"info-item"},oa=y(()=>d("b",null,"申请时间",-1)),na={class:"info-item"},sa=y(()=>d("b",null,"床位编号",-1)),ua={class:"info-item"},da=y(()=>d("b",null,"年        龄",-1)),ra={class:"info-item"},ia=y(()=>d("b",null,"性        别",-1)),pa=["src"],ma={class:"section"},ca=y(()=>d("div",{class:"section-title"},"陪同人信息",-1)),va={key:0,class:"section"},fa=y(()=>d("div",{class:"section-title"},"销假信息",-1)),ga={key:1,class:"section"},_a=y(()=>d("div",{class:"section-title"},"审批信息",-1)),ba={class:"audit-flow"},ha={class:"audit-step-content"},ya={class:"audit-step-info"},Va={class:"audit-step-time"},Na={class:"audit-step-name"},wa={key:0,class:"audit-step-line"},Ta={class:"paginationBox"},Da={class:"dialog-footer"},Sa={__name:"leaveForm",props:{activeTabValue:{type:String,default:"all"},assessmentCode:{type:String,default:null}},emits:["refresh"],setup(U,{expose:ae,emit:le}){const D=_(!1),H=_(null),E=_(""),A=_(!1),I=_(!1),{proxy:P}=De(),V=_([]),O=le,{sys_user_sex:j}=P.useDict("sys_user_sex"),p=_(""),te=Se({formLeave:{companionName:"",companionIdCard:"",companionPhone:"",relationship:"",plannedLeaveTime:[],plannedDays:1,otherMatters:""},rules:{companionName:[{required:!0,message:"请输入陪同人姓名",trigger:"blur"}],companionIdCard:[{required:!0,message:"请输入陪同人身份证号",trigger:"blur"}],companionPhone:[{required:!0,message:"请输入陪同人电话",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],relationship:[{required:!0,message:"请输入与老人关系",trigger:"blur"}],plannedLeaveTime:[{required:!0,message:"请选择请假时间",trigger:"change"}],plannedDays:[{required:!0,message:"请输入请假天数",trigger:"blur"}],actualDays:[{required:!0,message:"请输入请假天数",trigger:"blur"}],actualLeaveTime:[{required:!0,message:"请选择请假时间",trigger:"change"}],waysBack:[{required:!0,message:"请输入返回方式",trigger:"blur"}]}}),{formLeave:s,rules:G}=Ce(te),g=_({pageNum:1,pageSize:20,elderName:"",elderCode:""}),Y=_({}),F=_(),n=_({}),B=_(0),oe=_(),x=_([]),ne=t=>{if(t.stepOrder==1)return n.value.elderName+" 提交了外出申请"||"-";if(t.stepOrder==2)return t.approvalStatus=="APPROVED"?t.currentApproverName?t.currentApproverName+" 已同意申请":"-":t.approvalStatus=="REJECTED"?t.currentApproverName?t.currentApproverName+" 已拒绝申请":"-":t.approvalStatus=="PENDING"&&t.currentApproverName?t.currentApproverName+" 待审批":"-";if(t.stepOrder==3)return t.approvalStatus=="APPROVED"?n.value.elderName+" 办理销假"||"-":t.approvalStatus=="PENDING"&&n.value.elderName+" 待销假"||"-";if(t.stepOrder==4)return t.approvalStatus=="APPROVED"?"系统自动归档":"-"};function se(){A.value=!0,g.value.pageNum=1,R()}const ue=()=>{g.value.pageNum=1,R()},R=()=>{Oe({...g.value}).then(t=>{F.value=t.rows,B.value=t.total})};function de(){Me().then(t=>{Y.value=t.data})}const re=(t,a)=>{de(),p.value=t,p.value==="wcsq"?(n.value={},s.value={},V.value=[],x.value=[]):p.value==="review"&&(E.value=""),a&&a.id&&qe(a.id).then(u=>{var i,b,h,c,m,L;n.value=u.data,s.value={...u.data,plannedDays:Number(u.data.plannedDays),plannedLeaveTime:(i=u.data)!=null&&i.plannedLeaveTime&&((b=u.data)!=null&&b.plannedReturnTime)?[u.data.plannedLeaveTime,u.data.plannedReturnTime]:[],actualLeaveTime:(h=u.data)!=null&&h.actualLeaveTime&&((c=u.data)!=null&&c.actualReturnTime)?[u.data.actualLeaveTime,u.data.actualReturnTime]:[],pgimage:((m=u.data.attachments)==null?void 0:m.map(M=>M.filePath))||[]},x.value=((L=u.data)==null?void 0:L.tProcessApprovalRecords)||[]}),D.value=!0,setTimeout(()=>{H.value.clearValidate()},0)};function J(t){console.log(t,"row"),n.value=t,n.value.avatar=t.avatar,oe.value=t.id,A.value=!1}const ie=t=>{g.value.pageSize=t,R()},pe=t=>{g.value.pageNum=t,R()};function me(){g.value={elderName:null,elderCode:null,pageNum:1,pageSize:20},R()}async function ce(t){if(t=="wcsq"){if(!n.value.elderName){k.warning("请选择老人");return}P.$refs.formRef.validate(async(a,u)=>{var i,b;if(a){let h={handlerName:Y.value.nickName,elderId:n.value.id,elderName:n.value.elderName,bedNumber:n.value.roomNumber+"-"+n.value.bedNumber,avatar:n.value.avatar,createTime:Q(),elderCode:n.value.elderCode,elderAge:n.value.age,elderGender:n.value.gender,elderPhone:n.value.phone,...s.value,plannedLeaveTime:s.value.plannedLeaveTime[0],plannedReturnTime:s.value.plannedLeaveTime[1],attachments:((i=V.value)==null?void 0:i.length)>0?[{fileName:V.value[0].name,filePath:V.value[0].url,id:V.value[0].id,ossId:V.value[0].ossId}]:[]};const c=await Ue(h);if(c.code==200){if(P.$message.success("提交成功！"),D.value=!1,((b=h.attachments)==null?void 0:b.length)>0){let m=[];h.attachments.forEach(L=>{m.push(L.ossId)}),await ze(m,c.data.id)}V.value=[],O("refresh",!0)}}else console.log("error submit!",u)})}else P.$refs.formRef.validate(async(a,u)=>{var i,b,h,c;a?(console.log("formLeave.value",s.value),(await Ae({actualDays:s.value.actualDays,actualLeaveTime:((b=(i=s.value)==null?void 0:i.actualLeaveTime)==null?void 0:b.length)>0?s.value.actualLeaveTime[0]:void 0,actualReturnTime:((c=(h=s.value)==null?void 0:h.actualLeaveTime)==null?void 0:c.length)>0?s.value.actualLeaveTime[1]:void 0,businessId:n.value.id,approvalStatus:"APPROVED"})).code==200&&(D.value=!1,O("refresh",!0))):P.$refs.formRef.scrollToField(u[0])})}async function ve(){Z.confirm("确认同意外出申请？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await ee({approvalOpinion:"",approvalStatus:"APPROVED",businessId:n.value.id})).code===200?(k.success("审批成功"),D.value=!1,O("refresh",!0)):k.error("审批失败")})}function Q(){const t=new Date,a=t.getFullYear(),u=(t.getMonth()+1).toString().padStart(2,"0"),i=t.getDate().toString().padStart(2,"0"),b=t.getHours().toString().padStart(2,"0"),h=t.getMinutes().toString().padStart(2,"0"),c=t.getSeconds().toString().padStart(2,"0");return`${a}-${u}-${i} ${b}:${h}:${c}`}function fe(t){if(t){if(t==="PENDING")return Ye;if(t=="APPROVED")return He;if(t=="COMPLETE")return Be;if(t=="REJECTED")return $e}}function ge(t){console.log(t,"handleGetFile11---------"),s.value.pgimage=t.url,t&&(Array.isArray(t)?V.value=V.value.concat(t.map(a=>a)):V.value.push(t))}const _e=(t,a)=>{var u;V.value=(u=V.value)==null?void 0:u.filter(i=>i.id!==t)},be=()=>{if(!E.value){k.warning("请输入拒绝理由");return}Z.confirm("确认拒绝外出申请？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await ee({approvalOpinion:E.value,approvalStatus:"REJECTED",businessId:n.value.id})).code===200?(k.success("拒绝外出申请成功！"),D.value=!1,I.value=!1,O("refresh",!0)):k.error("拒绝申请失败！")})},he=()=>{I.value=!0};return ae({openDialog:re}),(t,a)=>{const u=f("el-input"),i=f("el-col"),b=f("dict-tag-span"),h=f("el-avatar"),c=f("el-row"),m=f("el-form-item"),L=f("el-date-picker"),M=f("el-input-number"),ye=f("ImageUpload"),K=f("el-form"),N=f("el-button"),$=f("el-dialog"),S=f("el-table-column"),Ve=f("el-table"),Ne=f("el-scrollbar"),we=f("el-pagination");return w(),C(X,null,[e($,{modelValue:D.value,"onUpdate:modelValue":a[16]||(a[16]=o=>D.value=o),title:p.value=="wcsq"?"外出申请":p.value=="detail"?"查看":p.value=="review"?"审核":p.value=="fromLeave"?"销假":"",width:"70%","close-on-click-modal":!1,"append-to-body":""},Le({default:l(()=>[e(K,{ref_key:"formRef",ref:H,model:r(s),rules:r(G),"label-width":"120px","label-position":"left"},{default:l(()=>[d("div",je,[Ge,e(c,{gutter:24},{default:l(()=>[e(i,{span:8},{default:l(()=>[d("div",Fe,[Je,v(T(n.value.handlerName||Y.value.nickName),1)]),p.value=="wcsq"?(w(),C("div",Qe,[Ke,e(u,{modelValue:n.value.elderName,"onUpdate:modelValue":a[0]||(a[0]=o=>n.value.elderName=o),style:{width:"200px"},readonly:"",onClick:se,placeholder:"请选择"},null,8,["modelValue"])])):(w(),C("div",We,[Xe,v(T(n.value.elderName),1)])),d("div",Ze,[ea,v(T(n.value.elderCode),1)]),d("div",aa,[la,v(T(n.value.phone||n.value.elderPhone),1)])]),_:1}),e(i,{span:7},{default:l(()=>[d("div",ta,[oa,v(T(p.value=="wcsq"?Q():n.value.createTime),1)]),d("div",na,[sa,v(T(n.value.roomNumber?n.value.roomNumber+"-"+n.value.bedNumber:n.value.bedNumber),1)]),d("div",ua,[da,v(T(n.value.age||n.value.elderAge),1)]),d("div",ra,[ia,e(b,{options:r(j),value:n.value.gender||n.value.elderGender},null,8,["options","value"])])]),_:1}),n.value.avatar?(w(),W(i,{key:0,span:4},{default:l(()=>[e(h,{shape:"square",size:140,fit:"fill",src:n.value.avatar},null,8,["src"])]),_:1})):q("",!0),n.value.status&&p.value!=="wcsq"?(w(),W(i,{key:1,span:4},{default:l(()=>[d("img",{src:fe(n.value.status),alt:"",style:{width:"100%",height:"auto"}},null,8,pa)]),_:1})):q("",!0)]),_:1})]),d("div",ma,[ca,e(c,{gutter:24},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(m,{label:"陪同人",prop:"companionName"},{default:l(()=>[e(u,{modelValue:r(s).companionName,"onUpdate:modelValue":a[1]||(a[1]=o=>r(s).companionName=o),placeholder:"请输入",disabled:p.value!="wcsq",clearable:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(m,{label:"陪同人身份证号",prop:"companionIdCard"},{default:l(()=>[e(u,{modelValue:r(s).companionIdCard,"onUpdate:modelValue":a[2]||(a[2]=o=>r(s).companionIdCard=o),placeholder:"请输入",disabled:p.value!="wcsq",clearable:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(m,{label:"陪同人电话",prop:"companionPhone"},{default:l(()=>[e(u,{modelValue:r(s).companionPhone,"onUpdate:modelValue":a[3]||(a[3]=o=>r(s).companionPhone=o),placeholder:"请输入",disabled:p.value!="wcsq",clearable:""},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(m,{label:"与老人关系",prop:"relationship"},{default:l(()=>[e(u,{modelValue:r(s).relationship,"onUpdate:modelValue":a[4]||(a[4]=o=>r(s).relationship=o),placeholder:"请输入",disabled:p.value!="wcsq",clearable:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(m,{label:"计划请假时间",prop:"plannedLeaveTime"},{default:l(()=>[e(L,{modelValue:r(s).plannedLeaveTime,"onUpdate:modelValue":a[5]||(a[5]=o=>r(s).plannedLeaveTime=o),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},placeholder:"请选择",disabled:p.value!="wcsq",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm",clearable:""},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(m,{label:"计划请假天数",prop:"plannedDays",style:{width:"100%"}},{default:l(()=>[e(M,{modelValue:r(s).plannedDays,"onUpdate:modelValue":a[6]||(a[6]=o=>r(s).plannedDays=o),min:0,placeholder:"请输入",disabled:p.value!="wcsq",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(m,{label:"外出方式",prop:"waysOut"},{default:l(()=>[e(u,{modelValue:r(s).waysOut,"onUpdate:modelValue":a[7]||(a[7]=o=>r(s).waysOut=o),placeholder:"请输入",disabled:p.value!="wcsq"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:24},{default:l(()=>[e(i,{span:24},{default:l(()=>[e(m,{label:"其他事项",prop:"otherMatters"},{default:l(()=>[e(u,{modelValue:r(s).otherMatters,"onUpdate:modelValue":a[8]||(a[8]=o=>r(s).otherMatters=o),type:"textarea",placeholder:"请输入",disabled:p.value!="wcsq"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(m,{label:"纸质文档"},{default:l(()=>[e(ye,{modelValue:r(s).pgimage,"onUpdate:modelValue":a[9]||(a[9]=o=>r(s).pgimage=o),fileData:{category:"leave_type",attachmentType:"leave_att"},fileType:["doc","docx","pdf","jpg","jpeg","png"],disabled:p.value!="wcsq",isShowOrEdit:!0,isShowTip:!0,onSubmitParentValue:ge,onRemoveAtt:_e},null,8,["modelValue","disabled"])]),_:1})]),p.value=="fromLeave"?(w(),C("div",va,[fa,e(c,{gutter:16},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(m,{label:"实际请假时间",prop:"actualLeaveTime"},{default:l(()=>[e(L,{modelValue:r(s).actualLeaveTime,"onUpdate:modelValue":a[10]||(a[10]=o=>r(s).actualLeaveTime=o),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},placeholder:"请选择",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(m,{label:"实际请假天数",prop:"actualDays"},{default:l(()=>[e(M,{modelValue:r(s).actualDays,"onUpdate:modelValue":a[11]||(a[11]=o=>r(s).actualDays=o),min:1,placeholder:"请输入"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(m,{label:"返回方式",prop:"waysBack"},{default:l(()=>[e(u,{modelValue:r(s).waysBack,"onUpdate:modelValue":a[12]||(a[12]=o=>r(s).waysBack=o),placeholder:"请输入",disabled:p.value!="fromLeave"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])):q("",!0),x.value.length>0?(w(),C("div",ga,[_a,d("div",ba,[(w(!0),C(X,null,ke(x.value,(o,z)=>(w(),C("div",{class:"audit-step",key:z},[d("div",ha,[d("div",{class:Ee(["audit-step-title",{active:o.approvalStatus==="PENDING"||n.value.status==="COMPLETE"||o.approvalTime}])},T(o.stepName?o.stepName:"-"),3),d("div",ya,[d("div",Va,T(o.approvalTime||"-"),1),d("div",Na,T(ne(o)||"-"),1)])]),z<x.value.length-1?(w(),C("div",wa)):q("",!0)]))),128))])])):q("",!0)]),_:1},8,["model","rules"])]),_:2},[p.value=="wcsq"||p.value=="fromLeave"?{name:"footer",fn:l(()=>[e(N,{type:"primary",onClick:a[13]||(a[13]=o=>ce(p.value))},{default:l(()=>[v("提交")]),_:1}),e(N,{onClick:a[14]||(a[14]=o=>D.value=!1)},{default:l(()=>[v("取消")]),_:1})]),key:"0"}:p.value=="review"?{name:"footer",fn:l(()=>[e(N,{onClick:he},{default:l(()=>[v("审批拒绝")]),_:1}),e(N,{type:"primary",onClick:ve},{default:l(()=>[v("审批通过")]),_:1})]),key:"1"}:{name:"footer",fn:l(()=>[e(N,{onClick:a[15]||(a[15]=o=>D.value=!1),type:"primary"},{default:l(()=>[v("返回")]),_:1})]),key:"2"}]),1032,["modelValue","title"]),e($,{modelValue:A.value,"onUpdate:modelValue":a[21]||(a[21]=o=>A.value=o),class:"elder-dialog-custom",title:"选择老人",width:"65%"},{default:l(()=>[e(K,{model:g.value,rules:r(G),ref:"userRef","label-width":"80px"},{default:l(()=>[e(c,null,{default:l(()=>[e(m,{label:"姓名",prop:"elderName"},{default:l(()=>[e(u,{modelValue:g.value.elderName,"onUpdate:modelValue":a[17]||(a[17]=o=>g.value.elderName=o),placeholder:"请输入姓名",maxlength:"30"},null,8,["modelValue"])]),_:1}),e(m,{label:"老人编号",prop:"elderCode"},{default:l(()=>[e(u,{modelValue:g.value.elderCode,"onUpdate:modelValue":a[18]||(a[18]=o=>g.value.elderCode=o),placeholder:"请输入老人编号",maxlength:"30"},null,8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(N,{type:"primary",icon:"Search",onClick:ue},{default:l(()=>[v("搜索")]),_:1}),e(N,{icon:"Refresh",onClick:me},{default:l(()=>[v("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),e(Ne,{"max-height":"500px"},{default:l(()=>[e(Ve,{data:F.value,onRowDblclick:J},{default:l(()=>[e(S,{type:"index",label:"序号",width:"120"}),e(S,{label:"老人编号",prop:"elderCode"}),e(S,{label:"姓名",prop:"elderName",width:"120"}),e(S,{label:"身份证号",prop:"idCard",width:"200"}),e(S,{label:"年龄",prop:"age",width:"80"}),e(S,{label:"性别",prop:"gender",width:"80"},{default:l(o=>[e(b,{options:r(j),value:o.row.gender},null,8,["options","value"])]),_:1}),e(S,{label:"联系电话",prop:"phone",width:"150"}),e(S,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[e(N,{type:"primary",onClick:z=>J(o.row)},{default:l(()=>[v("选择")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),Ie(d("div",Ta,[e(we,{background:"","current-page":g.value.pageNum,"onUpdate:currentPage":a[19]||(a[19]=o=>g.value.pageNum=o),"page-size":g.value.pageSize,"onUpdate:pageSize":a[20]||(a[20]=o=>g.value.pageSize=o),"page-sizes":[10,20,30,40],total:B.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ie,onCurrentChange:pe},null,8,["current-page","page-size","total"])],512),[[Pe,B.value>0]])]),_:1},8,["modelValue"]),e($,{modelValue:I.value,"onUpdate:modelValue":a[24]||(a[24]=o=>I.value=o),class:"elder-dialog-custom",title:"审核拒绝",width:"40%"},{footer:l(()=>[d("span",Da,[e(N,{type:"primary",onClick:be},{default:l(()=>[v("提交")]),_:1}),e(N,{onClick:a[23]||(a[23]=o=>I.value=!1)},{default:l(()=>[v("取 消")]),_:1})])]),default:l(()=>[e(u,{modelValue:E.value,"onUpdate:modelValue":a[22]||(a[22]=o=>E.value=o),type:"textarea",rows:6,placeholder:"请输入审核拒绝理由"},null,8,["modelValue"])]),_:1},8,["modelValue"])],64)}}},Pa=Te(Sa,[["__scopeId","data-v-3d3dc2df"]]);export{Pa as default};
