import{B as G,d as H,r as d,a as X,C as Z,N as ee,e as r,I as le,c as k,o as _,J as C,f as e,O as S,l as a,h as n,m as V,K as Y,L as $,j as x,k as N,n as c,i as B,t as te}from"./index-B0qHf98Y.js";import{l as ae,d as ne}from"./tWarehouseMedication-Ycu1QDaW.js";const oe={class:"app-container"},ie=G({name:"Medication"}),we=Object.assign(ie,{setup(re){const{proxy:g}=H(),{medication_type:ue,medication_dosage:de,is_otc:se,invoice_items:ce,packing_unit:pe,dosage_unit:me,usage_type:_e,goods_status:F,goods_type:D}=g.useDict("medication_type","medication_dosage","is_otc","invoice_items","packing_unit","dosage_unit","usage_type","goods_status","goods_type"),U=d([]);d(!1);const b=d(!0),I=d(!0),K=d([]);d(!0),d(!0);const w=d(0);d("");const M=X(),R=Z({form:{},queryParams:{pageNum:1,pageSize:10,medicineCode:null,barcode:null,medicineName:null,pinyinCode:null,category:null,specification:null,dosageForm:null,isOtc:null,invoiceItem:null,approvalNumber:null,manufacturer:null,status:null,packageUnit:null,baseFactor:null,baseUnit:null,dosageFactor:null,dosageUnit:null,purchasePrice:null,retailPrice:null,usageMethod:null,singleDose:null,maxInventory:null,minInventory:null,warehouse:null,locationCode:null,expiryWarningDays:null,currentQuantity:null},rules:{}}),{queryParams:o,form:ye,rules:ge}=ee(R);function f(){b.value=!0,ae(o.value).then(u=>{U.value=u.rows,w.value=u.total,b.value=!1})}function y(){o.value.pageNum=1,f()}function q(){g.resetForm("queryRef"),y()}function L(u,t){M.push("/wmsmedication/AddMedication/add/0/"+t)}function v(u,t){const p=u?u.id:0;M.push("/wmsmedication/editMedication/edit/"+p+"/"+t)}function j(u){const t=u.id||K.value;g.$modal.confirm('是否确认删除药品编号为"'+t+'"的数据项？').then(function(){return ne(t)}).then(()=>{f(),g.$modal.msgSuccess("删除成功")}).catch(()=>{})}return f(),(u,t)=>{const p=r("el-input"),m=r("el-form-item"),P=r("el-option"),T=r("el-select"),z=r("el-date-picker"),O=r("el-form"),s=r("el-button"),Q=r("el-row"),i=r("el-table-column"),A=r("dict-tag"),E=r("el-table"),J=r("pagination"),W=le("loading");return _(),k("div",oe,[C(e(O,{model:a(o),ref:"queryRef",inline:!0,"label-width":"88px"},{default:n(()=>[e(m,{label:"名       称",prop:"medicineName"},{default:n(()=>[e(p,{modelValue:a(o).medicineName,"onUpdate:modelValue":t[0]||(t[0]=l=>a(o).medicineName=l),placeholder:"请输入名称",clearable:"",style:{width:"200px"},onKeyup:V(y,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"类    别",prop:"goodsCategory"},{default:n(()=>[e(T,{modelValue:a(o).goodsCategory,"onUpdate:modelValue":t[1]||(t[1]=l=>a(o).goodsCategory=l),placeholder:"请选择",clearable:"",style:{width:"200px"}},{default:n(()=>[(_(!0),k(Y,null,$(a(D),l=>(_(),x(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"状    态",prop:"status"},{default:n(()=>[e(T,{modelValue:a(o).status,"onUpdate:modelValue":t[2]||(t[2]=l=>a(o).status=l),placeholder:"请选择",clearable:"",style:{width:"200px"}},{default:n(()=>[(_(!0),k(Y,null,$(a(F),l=>(_(),x(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"编    码",prop:"medicineCode"},{default:n(()=>[e(p,{modelValue:a(o).medicineCode,"onUpdate:modelValue":t[3]||(t[3]=l=>a(o).medicineCode=l),placeholder:"请输入编码",clearable:"",style:{width:"200px"},onKeyup:V(y,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"创建时间",prop:"createTime"},{default:n(()=>[e(z,{clearable:"",modelValue:a(o).createTime,"onUpdate:modelValue":t[4]||(t[4]=l=>a(o).createTime=l),type:"date","value-format":"YYYY-MM-DD",style:{width:"200px"},placeholder:"请选择创建时间",value:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(m,{label:"创建人",prop:"createBy"},{default:n(()=>[e(p,{modelValue:a(o).createBy,"onUpdate:modelValue":t[5]||(t[5]=l=>a(o).createBy=l),placeholder:"请输入创建人",clearable:"",style:{width:"200px"},onKeyup:V(y,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),[[S,a(I)]]),e(Q,{gutter:10,class:"mb8",justify:"end"},{default:n(()=>[e(s,{type:"primary",icon:"Search",onClick:y},{default:n(()=>[c("搜索")]),_:1}),e(s,{icon:"Refresh",onClick:q},{default:n(()=>[c("重置")]),_:1}),e(s,{type:"primary",plain:"",icon:"Plus",onClick:t[6]||(t[6]=l=>L(null,"add"))},{default:n(()=>[c("新增")]),_:1}),N("",!0),N("",!0),N("",!0)]),_:1}),C((_(),x(E,{data:a(U),border:"",stripe:""},{default:n(()=>[e(i,{type:"index",label:"序号",width:"55",align:"center"}),e(i,{label:"编码",align:"center",prop:"medicineCode",width:"160"}),e(i,{label:"药品名称",align:"center",prop:"medicineName"}),e(i,{label:"规格",align:"center",prop:"specification",width:"120"}),e(i,{label:"物品类别",align:"center",prop:"goodsCategory",width:"120"},{default:n(l=>[e(A,{options:a(D),value:l.row.goodsCategory},null,8,["options","value"])]),_:1}),e(i,{label:"采购价",align:"center",prop:"purchasePrice",width:"120"}),e(i,{label:"生产厂家",align:"center",prop:"manufacturer",width:"120"}),e(i,{label:"仓库",align:"center",prop:"warehouse",width:"120"}),e(i,{label:"货位号",align:"center",prop:"locationCode",width:"120"}),e(i,{label:"创建人",align:"center",prop:"createBy",width:"120"}),e(i,{label:"创建时间",align:"center",prop:"createTime",width:"120"},{default:n(l=>[B("span",null,te(u.parseTime(l.row.createTime,"{y}-{m}-{d}")),1)]),_:1}),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width",style:{width:"80px"}},{default:n(l=>[B("div",null,[e(s,{link:"",type:"primary",icon:"Search",onClick:h=>v(l.row,"show")},{default:n(()=>[c("查看")]),_:2},1032,["onClick"]),e(s,{link:"",type:"primary",icon:"Edit",onClick:h=>v(l.row,"edit")},{default:n(()=>[c("修改")]),_:2},1032,["onClick"])]),B("div",null,[e(s,{link:"",type:"primary",icon:"DocumentCopy",onClick:h=>v(l.row,"copy")},{default:n(()=>[c("复制")]),_:2},1032,["onClick"]),e(s,{link:"",type:"primary",icon:"Delete",onClick:h=>j(l.row)},{default:n(()=>[c("删除")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[W,a(b)]]),C(e(J,{total:a(w),page:a(o).pageNum,"onUpdate:page":t[7]||(t[7]=l=>a(o).pageNum=l),limit:a(o).pageSize,"onUpdate:limit":t[8]||(t[8]=l=>a(o).pageSize=l),onPagination:f},null,8,["total","page","limit"]),[[S,a(w)>0]])])}}});export{we as default};
