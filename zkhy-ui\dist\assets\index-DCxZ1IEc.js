import{X as r}from"./index-B0qHf98Y.js";function n(e){return r({url:"/handover/nurse/list",method:"get",params:e})}function o(e){return r({url:"/handover/nurse",method:"post",data:e})}function a(e){return r({url:"/handover/nurse/"+e,method:"get"})}function s(e){return r({url:"/system/role/authUser/allocatedList",method:"get",params:e})}function u(e){return r({url:"/elderinfo/basicInfo/listinfo",method:"get",params:e})}function d(e){return r({url:"/handover/nurse/"+e,method:"delete"})}export{o as a,s as b,n as c,d,a as e,u as g};
