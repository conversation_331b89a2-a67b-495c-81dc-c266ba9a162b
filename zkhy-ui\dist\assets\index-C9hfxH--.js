import{_ as $e,r as b,F as qe,G as p,e as N,c as S,o as A,i as k,f as l,h as o,n as v,k as ee,j as W,l as le,bh as De,bi as Se,bj as ze,t as h,p as ae,K as je,L as Pe,v as Me,x as Le,E as oe,P as Ge}from"./index-B0qHf98Y.js";import{a as Ke,l as Ze,g as He,d as Je,b as Qe,c as We,u as Xe,e as Ye,f as el,h as ll,i as al,j as ol,k as rl,m as ul,n as tl}from"./buildmanage-CIqJJJF0.js";const re=J=>(Me("data-v-36783e8d"),J=J(),<PERSON>(),J),dl={class:"buildmanage-page"},il={class:"page-container"},nl={class:"left-panel"},ml={class:"panel-header"},sl=re(()=>k("span",{class:"title"},"楼栋信息",-1)),pl={class:"custom-tree-node"},vl={class:"node-content"},fl={key:0,class:"operation-btns"},cl={key:0,class:"right-panel"},bl={class:"panel-header"},yl=re(()=>k("span",{class:"title"},"房间详情",-1)),gl={class:"room-info"},Nl={key:0},Vl={class:"bed-info",style:{"margin-top":"20px"}},kl={class:"section-header",style:{"margin-bottom":"16px"}},_l=re(()=>k("span",{class:"title"},"床位信息",-1)),hl={key:1,class:"right-panel empty-panel"},wl={__name:"index",setup(J){const F=b([]),fe=async()=>{try{const d=await He();d.code===200&&(F.value=d.data)}catch(d){console.error("获取房间树形数据失败:",d),p.error("获取楼栋信息失败")}},ce={children:"children",label:"label"},t=b(null);qe(async()=>{var d,e;if(await fe(),F.value.length>0&&((d=F.value[0].children)==null?void 0:d.length)>0&&((e=F.value[0].children[0].children)==null?void 0:e.length)>0){const r=F.value[0],i=r.children[0],C=i.children[0];x.value=r,c.value=i,ie(C,{parent:{data:i,parent:{data:r}}})}});const D=b(null),z=b(null),X=b(null),j=b(!1),P=b({buildingName:""}),ue={buildingName:[{required:!0,message:"请输入楼栋名称",trigger:"blur"}]},M=b(!1),w=b({buildingId:"",floorName:"",floorNumber:"",remark:""}),te={floorName:[{required:!0,message:"请输入楼层名称",trigger:"blur"}],floorNumber:[{required:!0,message:"请输入楼层号",trigger:"blur"}]},L=b(!1),n=b({roomNumber:"",roomName:"",roomType:"单人间",roomOrientation:"东",roomArea:25,roomFacilities:["电视","空调","床头柜"],areaName:"自理区",capacity:1,managerName:"",managerCode:"",remark:""}),de={roomNumber:[{required:!0,message:"请输入房间号",trigger:"blur"}],roomName:[{required:!0,message:"请输入房间名称",trigger:"blur"}],roomType:[{required:!0,message:"请选择房间类型",trigger:"change"}],roomOrientation:[{required:!0,message:"请选择房间朝向",trigger:"change"}],roomArea:[{required:!0,message:"请输入房间面积",trigger:"blur"}],roomFacilities:[{required:!0,message:"请选择房间设施",trigger:"change"}],areaName:[{required:!0,message:"请选择区域类型",trigger:"change"}],capacity:[{required:!0,message:"请输入床位容量",trigger:"blur"}]};function be(d){return"#409EFF"}async function ie(d,e){if(d.type==="room"){t.value=d,e&&e.parent&&(c.value=e.parent.data,e.parent.parent&&(x.value=e.parent.parent.data));try{const r=await Ke(d.id);if(r.code===200&&r.data){const i=r.data,C=t.value.beds||[];t.value={...d,roomNo:i.roomNumber,roomType:i.roomType,direction:i.roomOrientation,area:i.roomArea,zone:i.areaName,facilities:i.roomFacilities?i.roomFacilities.split(","):[],remark:i.remark,status:i.status,beds:C}}await G(d.id)}catch(r){console.error("获取房间详情失败:",r),p.error("获取房间详情失败"),await G(d.id)}}else d.type==="floor"?(c.value=d,e&&e.parent&&(x.value=e.parent.data),t.value=null):(d.type==="building"&&(x.value=d,c.value=null),t.value=null)}async function G(d){try{const e=await Ze({roomId:d});if(e.code===200){const r=e.rows.map(i=>({id:i.id,bedNo:i.bedNumber,bedCode:i.bedCode,type:i.bedType,price:i.bedPrice||0,remark:i.remark,elderId:i.elderId}));t.value.beds=r}else p.error(e.msg||"获取床位信息失败")}catch(e){console.error("获取床位信息失败:",e),p.error("获取床位信息失败")}}async function ye(){D.value.validate(async d=>{var e;if(d)try{const r=await We({buildingName:P.value.buildingName});r.code===200?(F.value.push({id:r.data.id,label:r.data.buildingName||r.data.name,type:"building",children:[]}),p.success("添加成功"),j.value=!1,P.value={buildingName:""},D.value.resetFields()):p.error(r.message||"添加失败")}catch(r){console.error("添加楼栋失败:",r),((e=r.response)==null?void 0:e.status)===200?p.success("添加成功"):p.error("添加楼栋失败")}})}function ge(d,e){e.type==="building"?(M.value=!0,w.value.name="",x.value=e):e.type==="floor"&&(L.value=!0,c.value=e,x.value=d.parent.data)}async function Ne(){z.value.validate(async d=>{if(d)try{const e=await el({buildingId:x.value.id,floorName:w.value.floorName,floorNumber:w.value.floorNumber,remark:w.value.remark});e.code===200?(x.value.children.push({id:e.data.id,label:e.data.floorName,type:"floor",children:[]}),p.success("添加成功"),M.value=!1,w.value={buildingId:"",floorName:"",floorNumber:"",remark:""},z.value.resetFields()):p.error(e.message||"添加失败")}catch(e){console.error("添加楼层失败:",e),p.error("添加楼层失败")}})}async function Ve(){X.value.validate(async d=>{if(d)try{n.value.managerCode=n.value.managerName;const e=Array.isArray(n.value.roomFacilities)?n.value.roomFacilities.join(","):"",r=await ll({buildingId:x.value.id,floorId:c.value.id,roomName:n.value.roomName,roomNumber:n.value.roomNumber,roomType:n.value.roomType,roomOrientation:n.value.roomOrientation,roomArea:n.value.roomArea,roomFacilities:e,areaName:n.value.areaName,capacity:n.value.capacity,managerName:n.value.managerName,managerCode:n.value.managerCode,remark:n.value.remark});r.code===200?(c.value.children.push({id:r.data.id,label:n.value.roomName,type:"room",roomNo:n.value.roomNumber,roomType:n.value.roomType,direction:n.value.roomOrientation,area:n.value.roomArea,facilities:e,remark:n.value.remark,beds:[],zone:n.value.areaName,capacity:n.value.capacity,managerName:n.value.managerName,status:"空闲"}),p.success("添加成功"),L.value=!1,X.value.resetFields()):p.error(r.message||"添加失败")}catch(e){console.error("添加房间失败:",e),p.error("添加房间失败")}})}const x=b(null),c=b(null),E=b(!1),O=b({id:"",buildingName:""}),$=b(!1),V=b({id:"",buildingId:"",floorName:"",floorNumber:"",remark:""});function ke(d,e){console.log(d,e,$.value,E.value),e.type==="building"?($.value=!1,E.value=!0,O.value={id:e.id,buildingName:e.label}):e.type==="floor"&&($.value=!0,E.value=!1,console.log($.value,E.value),V.value={id:e.id,buildingId:d.parent.data.id,floorName:e.label,floorNumber:e.floorNumber,remark:e.remark})}async function _e(){D.value.validate(async d=>{if(d)try{const e=await Xe({id:O.value.id,buildingName:O.value.buildingName});if((e==null?void 0:e.code)===200){const r=F.value.find(i=>i.id===O.value.id);r&&(r.label=O.value.buildingName),p.success("更新成功"),E.value=!1,O.value={id:"",buildingName:""},D.value.resetFields()}else p.error((e==null?void 0:e.message)||"更新失败")}catch(e){console.error("更新失败:",e),p.error(e.message||"更新失败")}})}async function he(){z.value.validate(async d=>{if(d)try{const e=await Ye(V.value);if(e.code===200){const r=F.value.find(i=>i.id===V.value.buildingId);if(r){const i=r.children.find(C=>C.id===V.value.id);i&&(i.label=V.value.floorName,i.floorNumber=V.value.floorNumber,i.remark=V.value.remark)}p.success("编辑成功"),$.value=!1}else p.error(e.message||"编辑失败")}catch(e){console.error("编辑楼层失败:",e),p.error("编辑楼层失败")}})}async function we(d,e){var r,i,C;try{await oe.confirm("确认删除该节点吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});let f;if(e.type==="building"?f=await Je(e.id):e.type==="floor"&&(f=await Qe(e.id)),(f==null?void 0:f.code)===200){if(e.type==="building"){const m=F.value.findIndex(_=>_.id===e.id);m>-1&&F.value.splice(m,1)}else{const _=d.parent.data.children||[],I=_.findIndex(U=>U.id===e.id);I>-1&&_.splice(I,1)}((r=t.value)==null?void 0:r.id)===e.id&&(t.value=null),p.success("删除成功")}else p.error((f==null?void 0:f.message)||"删除失败")}catch(f){f!=="cancel"&&(console.error("删除失败:",f),p.error(((C=(i=f.response)==null?void 0:i.data)==null?void 0:C.message)||"删除失败"))}}const K=b(!1),ne=b(null),u=b({id:"",buildingId:"",floorId:"",roomNumber:"",roomName:"",roomType:"",roomOrientation:"",roomArea:0,areaName:"",roomFacilities:[],capacity:1,status:"",managerName:"",managerCode:"",remark:""});function Ce(){t.value&&(u.value={id:t.value.id,buildingId:x.value?x.value.id:"",floorId:c.value?c.value.id:"",roomNumber:t.value.roomNo,roomName:t.value.label,roomType:t.value.roomType||"",roomOrientation:t.value.direction||"",roomArea:t.value.area||0,areaName:t.value.zone||"",roomFacilities:t.value.facilities||[],capacity:t.value.capacity||1,status:t.value.status||"空闲",managerName:t.value.managerName||"",managerCode:t.value.managerName||"",remark:t.value.remark||""},K.value=!0)}async function Fe(){ne.value.validate(async d=>{if(d)try{u.value.managerCode=u.value.managerName;const e={id:u.value.id,buildingId:u.value.buildingId,floorId:u.value.floorId,roomNumber:u.value.roomNumber,roomName:u.value.roomName,roomType:u.value.roomType,roomOrientation:u.value.roomOrientation,roomArea:u.value.roomArea,areaName:u.value.areaName,roomFacilities:u.value.roomFacilities.join(","),capacity:u.value.capacity,status:u.value.status,managerName:u.value.managerName,managerCode:u.value.managerCode,remark:u.value.remark},r=await tl(e);if(r.code===200){if(c.value&&c.value.children){const i=c.value.children.findIndex(C=>C.id===t.value.id);i!==-1&&(c.value.children[i].label=u.value.roomName,c.value.children[i].roomNo=u.value.roomNumber,c.value.children[i].roomType=u.value.roomType,c.value.children[i].direction=u.value.roomOrientation,c.value.children[i].area=u.value.roomArea,c.value.children[i].zone=u.value.areaName,c.value.children[i].facilities=u.value.roomFacilities,c.value.children[i].capacity=u.value.capacity,c.value.children[i].status=u.value.status,c.value.children[i].managerName=u.value.managerName,c.value.children[i].managerCode=u.value.managerCode,c.value.children[i].remark=u.value.remark)}t.value.label=u.value.roomName,t.value.roomNo=u.value.roomNumber,t.value.roomType=u.value.roomType,t.value.direction=u.value.roomOrientation,t.value.area=u.value.roomArea,t.value.zone=u.value.areaName,t.value.facilities=u.value.roomFacilities,t.value.capacity=u.value.capacity,t.value.status=u.value.status,t.value.managerName=u.value.managerName,t.value.managerCode=u.value.managerCode,t.value.remark=u.value.remark,p.success("修改成功"),K.value=!1}else p.error(r.msg||"修改失败")}catch(e){console.error("修改房间失败:",e),p.error("修改房间失败")}})}function xe(){if(!t.value)return;if(t.value.beds&&t.value.beds.some(e=>e.elderId)){p.error("该房间中有床位已有老人入住，不允许删除");return}oe.confirm("确认删除该房间吗？删除后将无法恢复，且会同时删除房间内的所有床位。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await al(t.value.id);if(e.code===200){if(c.value&&c.value.children){const r=c.value.children.findIndex(i=>i.id===t.value.id);r!==-1&&c.value.children.splice(r,1)}t.value=null,p.success("删除成功")}else p.error(e.msg||"删除失败")}catch(e){console.error("删除房间失败:",e),p.error("删除房间失败")}}).catch(()=>{})}const Q=b(null),Y=b(null),me={bedNo:[{required:!0,message:"请输入床位号",trigger:"blur"},{pattern:/^[0-9A-Za-z-]+$/,message:"床位号只能包含数字、字母和横杠",trigger:"blur"}],bedCode:[{required:!0,message:"请输入床位编码",trigger:"blur"}],price:[{required:!0,message:"请输入床位价格",trigger:"blur"},{pattern:/^\d+$/,message:"请输入有效的价格数字",trigger:"blur"}],type:[{required:!0,message:"请选择床位类型",trigger:"change"}]},Z=b(!1),y=b({bedNo:"",bedCode:"",price:0,type:"",remark:""});function Ie(){Z.value=!0,y.value={bedNo:"",bedCode:"",price:0,type:"",remark:""},Ge(()=>{var d;(d=Q.value)==null||d.resetFields()})}async function Ue(){Q.value.validate(async d=>{if(d)try{const e={roomId:t.value.id,bedNumber:y.value.bedNo,bedCode:y.value.bedCode,bedType:y.value.type,bedPrice:y.value.price,remark:y.value.remark},r=await rl(e);r.code===200?(await G(t.value.id),p.success("添加成功"),Z.value=!1,y.value={bedNo:"",bedCode:"",price:0,type:"",remark:""},Q.value.resetFields()):p.error(r.msg||"添加失败")}catch(e){console.error("添加床位失败:",e),p.error("添加床位失败")}})}const H=b(!1),g=b({bedNo:"",bedCode:"",price:0,type:"",remark:""}),se=b(null);function Be(d){se.value=d,Object.assign(g.value,{bedNo:d.bedNo,bedCode:d.bedCode,price:d.price||0,type:d.type,remark:d.remark||""}),H.value=!0}async function Re(){Y.value.validate(async d=>{if(d)try{const e={id:se.value.id,roomId:t.value.id,bedNumber:g.value.bedNo,bedCode:g.value.bedCode,bedType:g.value.type,bedPrice:g.value.price,remark:g.value.remark},r=await ul(e);r.code===200?(await G(t.value.id),p.success("修改成功"),H.value=!1,g.value={bedNo:"",bedCode:"",price:0,type:"",remark:""},Y.value.resetFields()):p.error(r.msg||"修改失败")}catch(e){console.error("修改床位失败:",e),p.error("修改床位失败")}})}function Te(d){if(d.elderId){p.error("该床位已有老人入住，不允许删除");return}oe.confirm("确认删除该床位吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await ol(d.id);e.code===200?(await G(t.value.id),p.success("删除成功")):p.error(e.msg||"删除失败")}catch(e){console.error("删除床位失败:",e),p.error("删除床位失败")}}).catch(()=>{})}return(d,e)=>{const r=N("el-button"),i=N("el-icon"),C=N("el-tree"),f=N("el-input"),m=N("el-form-item"),_=N("el-form"),I=N("el-dialog"),U=N("el-input-number"),s=N("el-option"),B=N("el-select"),R=N("el-descriptions-item"),pe=N("el-tag"),Ae=N("el-descriptions"),q=N("el-table-column"),Oe=N("el-table"),Ee=N("el-empty");return A(),S("div",dl,[k("div",il,[k("div",nl,[k("div",ml,[sl,l(r,{type:"primary",onClick:e[0]||(e[0]=a=>j.value=!0)},{default:o(()=>[v("新增楼栋")]),_:1})]),l(C,{data:F.value,props:ce,"node-key":"id","default-expand-all":"","highlight-current":"","default-expanded-keys":["1","1-1"],"current-key":"1-1-1",onNodeClick:ie},{default:o(({node:a,data:T})=>[k("div",pl,[k("div",vl,[l(i,{size:16,color:be(T),style:{"margin-right":"4px"}},{default:o(()=>[T.type==="building"?(A(),W(le(De),{key:0})):T.type==="floor"?(A(),W(le(Se),{key:1})):T.type==="room"?(A(),W(le(ze),{key:2})):ee("",!0)]),_:2},1032,["color"]),k("span",null,h(a.label),1)]),T.type!=="room"?(A(),S("span",fl,[l(r,{link:"",type:"primary",onClick:ae(ve=>ge(a,T),["stop"])},{default:o(()=>[v("添加")]),_:2},1032,["onClick"]),l(r,{link:"",type:"primary",onClick:ae(ve=>ke(a,T),["stop"])},{default:o(()=>[v("编辑")]),_:2},1032,["onClick"]),l(r,{link:"",type:"danger",onClick:ae(ve=>we(a,T),["stop"])},{default:o(()=>[v("删除")]),_:2},1032,["onClick"])])):ee("",!0)])]),_:1},8,["data"]),l(I,{modelValue:j.value,"onUpdate:modelValue":e[3]||(e[3]=a=>j.value=a),title:"新增楼栋",width:"400px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[2]||(e[2]=a=>j.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:ye},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:P.value,rules:ue,ref_key:"buildingFormRef",ref:D,"label-width":"80px"},{default:o(()=>[l(m,{label:"楼栋名称",prop:"buildingName"},{default:o(()=>[l(f,{modelValue:P.value.buildingName,"onUpdate:modelValue":e[1]||(e[1]=a=>P.value.buildingName=a),placeholder:"请输入楼栋名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(I,{modelValue:E.value,"onUpdate:modelValue":e[6]||(e[6]=a=>E.value=a),title:"编辑楼栋",width:"400px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[5]||(e[5]=a=>E.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:_e},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:O.value,rules:ue,ref_key:"buildingFormRef",ref:D,"label-width":"80px"},{default:o(()=>[l(m,{label:"楼栋名称",prop:"buildingName"},{default:o(()=>[l(f,{modelValue:O.value.buildingName,"onUpdate:modelValue":e[4]||(e[4]=a=>O.value.buildingName=a),placeholder:"请输入楼栋名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(I,{modelValue:$.value,"onUpdate:modelValue":e[11]||(e[11]=a=>$.value=a),title:"编辑楼层",width:"400px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[10]||(e[10]=a=>$.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:he},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:V.value,rules:te,ref_key:"floorFormRef",ref:z,"label-width":"100px"},{default:o(()=>[l(m,{label:"楼层名称",prop:"floorName"},{default:o(()=>[l(f,{modelValue:V.value.floorName,"onUpdate:modelValue":e[7]||(e[7]=a=>V.value.floorName=a),placeholder:"请输入楼层名称"},null,8,["modelValue"])]),_:1}),l(m,{label:"楼层号",prop:"floorNumber"},{default:o(()=>[l(U,{modelValue:V.value.floorNumber,"onUpdate:modelValue":e[8]||(e[8]=a=>V.value.floorNumber=a),min:1,style:{width:"100%"},placeholder:"请输入楼层号"},null,8,["modelValue"])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(f,{modelValue:V.value.remark,"onUpdate:modelValue":e[9]||(e[9]=a=>V.value.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(I,{modelValue:M.value,"onUpdate:modelValue":e[16]||(e[16]=a=>M.value=a),title:"新增楼层",width:"400px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[15]||(e[15]=a=>M.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:Ne},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:w.value,rules:te,ref_key:"floorFormRef",ref:z,"label-width":"100px"},{default:o(()=>[l(m,{label:"楼层名称",prop:"floorName"},{default:o(()=>[l(f,{modelValue:w.value.floorName,"onUpdate:modelValue":e[12]||(e[12]=a=>w.value.floorName=a),placeholder:"请输入楼层名称"},null,8,["modelValue"])]),_:1}),l(m,{label:"楼层号",prop:"floorNumber"},{default:o(()=>[l(U,{modelValue:w.value.floorNumber,"onUpdate:modelValue":e[13]||(e[13]=a=>w.value.floorNumber=a),min:1,style:{width:"100%"},placeholder:"请输入楼层号"},null,8,["modelValue"])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(f,{modelValue:w.value.remark,"onUpdate:modelValue":e[14]||(e[14]=a=>w.value.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(I,{modelValue:L.value,"onUpdate:modelValue":e[28]||(e[28]=a=>L.value=a),title:"新增房间",width:"500px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[27]||(e[27]=a=>L.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:Ve},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:n.value,rules:de,ref_key:"roomFormRef",ref:X,"label-width":"100px"},{default:o(()=>[l(m,{label:"房间号",prop:"roomNumber"},{default:o(()=>[l(f,{modelValue:n.value.roomNumber,"onUpdate:modelValue":e[17]||(e[17]=a=>n.value.roomNumber=a),placeholder:"请输入房间号"},null,8,["modelValue"])]),_:1}),l(m,{label:"房间名称",prop:"roomName"},{default:o(()=>[l(f,{modelValue:n.value.roomName,"onUpdate:modelValue":e[18]||(e[18]=a=>n.value.roomName=a),placeholder:"请输入房间名称"},null,8,["modelValue"])]),_:1}),l(m,{label:"房间类型",prop:"roomType"},{default:o(()=>[l(B,{modelValue:n.value.roomType,"onUpdate:modelValue":e[19]||(e[19]=a=>n.value.roomType=a),placeholder:"请选择房间类型",style:{width:"100%"}},{default:o(()=>[l(s,{label:"单人间",value:"单人间"}),l(s,{label:"双人间",value:"双人间"}),l(s,{label:"三人间",value:"三人间"}),l(s,{label:"多人间",value:"多人间"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"朝向",prop:"roomOrientation"},{default:o(()=>[l(B,{modelValue:n.value.roomOrientation,"onUpdate:modelValue":e[20]||(e[20]=a=>n.value.roomOrientation=a),placeholder:"请选择房间朝向",style:{width:"100%"}},{default:o(()=>[l(s,{label:"东",value:"东"}),l(s,{label:"南",value:"南"}),l(s,{label:"西",value:"西"}),l(s,{label:"北",value:"北"}),l(s,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"面积",prop:"roomArea"},{default:o(()=>[l(U,{modelValue:n.value.roomArea,"onUpdate:modelValue":e[21]||(e[21]=a=>n.value.roomArea=a),min:1,precision:0,style:{width:"100%"},placeholder:"请输入房间面积"},null,8,["modelValue"])]),_:1}),l(m,{label:"区域",prop:"areaName"},{default:o(()=>[l(B,{modelValue:n.value.areaName,"onUpdate:modelValue":e[22]||(e[22]=a=>n.value.areaName=a),placeholder:"请选择区域类型",style:{width:"100%"}},{default:o(()=>[l(s,{label:"自理区",value:"自理区"}),l(s,{label:"介助区",value:"介助区"}),l(s,{label:"介护区",value:"介护区"}),l(s,{label:"其他区",value:"其他区"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"设施",prop:"roomFacilities"},{default:o(()=>[l(B,{modelValue:n.value.roomFacilities,"onUpdate:modelValue":e[23]||(e[23]=a=>n.value.roomFacilities=a),multiple:"",placeholder:"请选择房间设施",style:{width:"100%"}},{default:o(()=>[l(s,{label:"电视",value:"电视"}),l(s,{label:"空调",value:"空调"}),l(s,{label:"床头柜",value:"床头柜"}),l(s,{label:"卫生间",value:"卫生间"}),l(s,{label:"护理床",value:"护理床"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"床位",prop:"capacity"},{default:o(()=>[l(U,{modelValue:n.value.capacity,"onUpdate:modelValue":e[24]||(e[24]=a=>n.value.capacity=a),min:1,precision:0,style:{width:"100%"},placeholder:"请输入床位容量"},null,8,["modelValue"])]),_:1}),l(m,{label:"负责人",prop:"managerName"},{default:o(()=>[l(f,{modelValue:n.value.managerName,"onUpdate:modelValue":e[25]||(e[25]=a=>n.value.managerName=a),placeholder:"请输入负责人姓名"},null,8,["modelValue"])]),_:1}),l(m,{label:"备注",prop:"remark"},{default:o(()=>[l(f,{modelValue:n.value.remark,"onUpdate:modelValue":e[26]||(e[26]=a=>n.value.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),t.value?(A(),S("div",cl,[k("div",bl,[yl,k("div",null,[l(r,{type:"primary",onClick:Ce},{default:o(()=>[v("编辑")]),_:1}),l(r,{type:"danger",onClick:xe},{default:o(()=>[v("删除")]),_:1})])]),k("div",gl,[l(Ae,{column:2,border:""},{default:o(()=>[l(R,{label:"房间号"},{default:o(()=>[v(h(t.value.roomNo),1)]),_:1}),l(R,{label:"房间类型"},{default:o(()=>[v(h(t.value.roomType||"未设置"),1)]),_:1}),l(R,{label:"区域"},{default:o(()=>[v(h(t.value.zone),1)]),_:1}),l(R,{label:"朝向"},{default:o(()=>[v(h(t.value.direction),1)]),_:1}),l(R,{label:"面积"},{default:o(()=>[v(h(t.value.area)+"㎡",1)]),_:1}),l(R,{label:"状态"},{default:o(()=>[v(h(t.value.status||"未知"),1)]),_:1}),l(R,{label:"设施",span:2},{default:o(()=>[(A(!0),S(je,null,Pe(t.value.facilities,a=>(A(),W(pe,{key:a,size:"small",style:{"margin-right":"4px"}},{default:o(()=>[v(h(a),1)]),_:2},1024))),128)),!t.value.facilities||t.value.facilities.length===0?(A(),S("span",Nl,"无")):ee("",!0)]),_:1}),l(R,{label:"床位容量",span:2},{default:o(()=>[v(h(t.value.capacity||t.value.bedCapacity||0),1)]),_:1}),l(R,{label:"负责人",span:2},{default:o(()=>[v(h(t.value.managerName||"未设置"),1)]),_:1}),l(R,{label:"备注",span:2},{default:o(()=>[v(h(t.value.remark||"无"),1)]),_:1})]),_:1}),k("div",Vl,[k("div",kl,[_l,l(r,{type:"primary",onClick:Ie},{default:o(()=>[v("添加床位")]),_:1})]),l(Oe,{data:t.value.beds,border:"",style:{width:"100%"}},{default:o(()=>[l(q,{prop:"bedNo",label:"床位号",width:"100"}),l(q,{prop:"bedCode",label:"床位编码",width:"120"}),l(q,{prop:"type",label:"床位类型",width:"100"}),l(q,{prop:"price",label:"床位价格",width:"100"},{default:o(({row:a})=>[v(h(a.price||0)+"元/月 ",1)]),_:1}),l(q,{prop:"elderId",label:"入住状态",width:"100"},{default:o(({row:a})=>[l(pe,{type:a.elderId?"success":"info"},{default:o(()=>[v(h(a.elderId?"已入住":"空闲"),1)]),_:2},1032,["type"])]),_:1}),l(q,{prop:"remark",label:"备注"}),l(q,{label:"操作",width:"150"},{default:o(({row:a})=>[l(r,{link:"",type:"primary",onClick:T=>Be(a)},{default:o(()=>[v("编辑")]),_:2},1032,["onClick"]),l(r,{link:"",type:"danger",onClick:T=>Te(a)},{default:o(()=>[v("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])])])):(A(),S("div",hl,[l(Ee,{description:"请选择房间查看详情"})]))]),l(I,{modelValue:Z.value,"onUpdate:modelValue":e[35]||(e[35]=a=>Z.value=a),title:"新增床位",width:"500px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[34]||(e[34]=a=>Z.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:Ue},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:y.value,rules:me,ref_key:"bedFormRef",ref:Q,"label-width":"100px"},{default:o(()=>[l(m,{label:"床位号",prop:"bedNo"},{default:o(()=>[l(f,{modelValue:y.value.bedNo,"onUpdate:modelValue":e[29]||(e[29]=a=>y.value.bedNo=a),placeholder:"请输入床位号"},null,8,["modelValue"])]),_:1}),l(m,{label:"床位编码",prop:"bedCode"},{default:o(()=>[l(f,{modelValue:y.value.bedCode,"onUpdate:modelValue":e[30]||(e[30]=a=>y.value.bedCode=a),placeholder:"请输入床位编码"},null,8,["modelValue"])]),_:1}),l(m,{label:"床位价格",prop:"price"},{default:o(()=>[l(U,{modelValue:y.value.price,"onUpdate:modelValue":e[31]||(e[31]=a=>y.value.price=a),min:0,precision:0,style:{width:"100%"},placeholder:"请输入床位价格"},null,8,["modelValue"])]),_:1}),l(m,{label:"床位类型",prop:"type"},{default:o(()=>[l(B,{modelValue:y.value.type,"onUpdate:modelValue":e[32]||(e[32]=a=>y.value.type=a),placeholder:"请选择床位类型",style:{width:"100%"}},{default:o(()=>[l(s,{label:"普通床",value:"普通床"}),l(s,{label:"医疗床",value:"医疗床"}),l(s,{label:"护理床",value:"护理床"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(f,{modelValue:y.value.remark,"onUpdate:modelValue":e[33]||(e[33]=a=>y.value.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(I,{modelValue:H.value,"onUpdate:modelValue":e[42]||(e[42]=a=>H.value=a),title:"编辑床位",width:"500px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[41]||(e[41]=a=>H.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:Re},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:g.value,rules:me,ref_key:"editBedFormRef",ref:Y,"label-width":"100px"},{default:o(()=>[l(m,{label:"床位号",prop:"bedNo"},{default:o(()=>[l(f,{modelValue:g.value.bedNo,"onUpdate:modelValue":e[36]||(e[36]=a=>g.value.bedNo=a),placeholder:"请输入床位号"},null,8,["modelValue"])]),_:1}),l(m,{label:"床位编码",prop:"bedCode"},{default:o(()=>[l(f,{modelValue:g.value.bedCode,"onUpdate:modelValue":e[37]||(e[37]=a=>g.value.bedCode=a),placeholder:"请输入床位编码"},null,8,["modelValue"])]),_:1}),l(m,{label:"床位价格",prop:"price"},{default:o(()=>[l(U,{modelValue:g.value.price,"onUpdate:modelValue":e[38]||(e[38]=a=>g.value.price=a),min:0,precision:0,style:{width:"100%"},placeholder:"请输入床位价格"},null,8,["modelValue"])]),_:1}),l(m,{label:"床位类型",prop:"type"},{default:o(()=>[l(B,{modelValue:g.value.type,"onUpdate:modelValue":e[39]||(e[39]=a=>g.value.type=a),placeholder:"请选择床位类型",style:{width:"100%"}},{default:o(()=>[l(s,{label:"普通床",value:"普通床"}),l(s,{label:"医疗床",value:"医疗床"}),l(s,{label:"护理床",value:"护理床"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(f,{modelValue:g.value.remark,"onUpdate:modelValue":e[40]||(e[40]=a=>g.value.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(I,{modelValue:K.value,"onUpdate:modelValue":e[54]||(e[54]=a=>K.value=a),title:"编辑房间",width:"600px","close-on-click-modal":!1},{footer:o(()=>[l(r,{onClick:e[53]||(e[53]=a=>K.value=!1)},{default:o(()=>[v("取消")]),_:1}),l(r,{type:"primary",onClick:Fe},{default:o(()=>[v("确定")]),_:1})]),default:o(()=>[l(_,{model:u.value,rules:de,ref_key:"editRoomFormRef",ref:ne,"label-width":"100px"},{default:o(()=>[l(m,{label:"房间号",prop:"roomNumber"},{default:o(()=>[l(f,{modelValue:u.value.roomNumber,"onUpdate:modelValue":e[43]||(e[43]=a=>u.value.roomNumber=a),placeholder:"请输入房间号"},null,8,["modelValue"])]),_:1}),l(m,{label:"房间名称",prop:"roomName"},{default:o(()=>[l(f,{modelValue:u.value.roomName,"onUpdate:modelValue":e[44]||(e[44]=a=>u.value.roomName=a),placeholder:"请输入房间名称"},null,8,["modelValue"])]),_:1}),l(m,{label:"房间类型",prop:"roomType"},{default:o(()=>[l(B,{modelValue:u.value.roomType,"onUpdate:modelValue":e[45]||(e[45]=a=>u.value.roomType=a),placeholder:"请选择房间类型",style:{width:"100%"}},{default:o(()=>[l(s,{label:"单人间",value:"单人间"}),l(s,{label:"双人间",value:"双人间"}),l(s,{label:"三人间",value:"三人间"}),l(s,{label:"多人间",value:"多人间"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"朝向",prop:"roomOrientation"},{default:o(()=>[l(B,{modelValue:u.value.roomOrientation,"onUpdate:modelValue":e[46]||(e[46]=a=>u.value.roomOrientation=a),placeholder:"请选择房间朝向",style:{width:"100%"}},{default:o(()=>[l(s,{label:"东",value:"东"}),l(s,{label:"南",value:"南"}),l(s,{label:"西",value:"西"}),l(s,{label:"北",value:"北"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"面积",prop:"roomArea"},{default:o(()=>[l(U,{modelValue:u.value.roomArea,"onUpdate:modelValue":e[47]||(e[47]=a=>u.value.roomArea=a),min:1,precision:0,style:{width:"100%"},placeholder:"请输入房间面积"},null,8,["modelValue"])]),_:1}),l(m,{label:"区域",prop:"areaName"},{default:o(()=>[l(B,{modelValue:u.value.areaName,"onUpdate:modelValue":e[48]||(e[48]=a=>u.value.areaName=a),placeholder:"请选择区域类型",style:{width:"100%"}},{default:o(()=>[l(s,{label:"自理区",value:"自理区"}),l(s,{label:"介助区",value:"介助区"}),l(s,{label:"照护区",value:"照护区"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"设施",prop:"roomFacilities"},{default:o(()=>[l(B,{modelValue:u.value.roomFacilities,"onUpdate:modelValue":e[49]||(e[49]=a=>u.value.roomFacilities=a),multiple:"",placeholder:"请选择房间设施",style:{width:"100%"}},{default:o(()=>[l(s,{label:"电视",value:"电视"}),l(s,{label:"空调",value:"空调"}),l(s,{label:"床头柜",value:"床头柜"}),l(s,{label:"卫生间",value:"卫生间"}),l(s,{label:"护理床",value:"护理床"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"床位容量",prop:"capacity"},{default:o(()=>[l(U,{modelValue:u.value.capacity,"onUpdate:modelValue":e[50]||(e[50]=a=>u.value.capacity=a),min:1,precision:0,style:{width:"100%"},placeholder:"请输入床位容量"},null,8,["modelValue"])]),_:1}),l(m,{label:"负责人",prop:"managerName"},{default:o(()=>[l(f,{modelValue:u.value.managerName,"onUpdate:modelValue":e[51]||(e[51]=a=>u.value.managerName=a),placeholder:"请输入负责人姓名"},null,8,["modelValue"])]),_:1}),l(m,{label:"备注",prop:"remark"},{default:o(()=>[l(f,{modelValue:u.value.remark,"onUpdate:modelValue":e[52]||(e[52]=a=>u.value.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},xl=$e(wl,[["__scopeId","data-v-36783e8d"]]);export{xl as default};
