import{_ as D,r as i,e as _,j as k,o as r,h as c,i as e,t as o,c as h,K as P,L as B,f as m,n as b,v as T,x as V}from"./index-B0qHf98Y.js";const f=s=>(T("data-v-f0ca487d"),s=s(),V(),s),N={class:"header"},I=f(()=>e("h1",{class:"title_border"},"和孚护理查房记录",-1)),S={class:"info-row"},L={class:"info-item"},j={class:"info-item"},E={class:"info-item"},A={class:"table-style"},F=f(()=>e("thead",null,[e("tr",null,[e("th",{style:{width:"100px"}},"查房次数"),e("th",{style:{width:"120px"}},"查房时间"),e("th",{style:{width:"120px"}},"查房人"),e("th",{style:{"min-width":"150px"}},"查房内容")])],-1)),H={class:"action-buttons"},K={__name:"PrintPreview",setup(s,{expose:v}){const l=i(!1),p=i(null),n=i({}),x=()=>{const a=window.open("","_blank"),d=`
    <!DOCTYPE html>
    <html>
      <head>
        <title>和孚护理查房记录</title>
        <style>
          body { padding: 20px; font-family: Arial; }
          h1 { 
            border-bottom: 4px solid #D9001B;
            padding-bottom: 10px;
            margin-bottom: 10px;
            color: #D9001B;
            text-align: center;
          }
          .info-row { display: flex; justify-content: space-between; margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse;}
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        </style>
      </head>
      <body>
        ${p.value.innerHTML.replace(/<el-table[^>]*>/g,"<table>").replace(/<\/el-table>/g,"</table>").replace(/<el-table-column[^>]*>/g,"").replace(/<\/el-table-column>/g,"").replace(/<el-button[^>]*>.*?<\/el-button>/g,"")}
        <script>
          setTimeout(() => {
            window.print()
            window.close()
          }, 200)
        <\/script>
      </body>
    </html>
  `;a.document.write(d),a.document.close()},g=()=>{l.value=!1},w=()=>{l.value=!1};return v({openPrintDialog:a=>{n.value=a,l.value=!0}}),(a,d)=>{const u=_("el-button"),y=_("el-dialog");return r(),k(y,{modelValue:l.value,"onUpdate:modelValue":d[0]||(d[0]=t=>l.value=t),title:"打印预览",width:"900px","append-to-body":"",onClose:w},{default:c(()=>[e("div",{class:"nursing-rounds-record",ref_key:"printContent",ref:p},[e("div",N,[I,e("div",S,[e("div",L,"查房时间："+o(n.value.roundDate||"-"),1),e("div",j,"房间号/床位号："+o(n.value.roomNumber||"-"),1),e("div",E,"老人姓名："+o(n.value.elderName||"-"),1)])]),e("table",A,[F,e("tbody",null,[(r(!0),h(P,null,B(n.value.visits,(t,C)=>(r(),h("tr",{key:C},[e("td",null,o(t.roundCount||"-"),1),e("td",null,o(t.roundTime?t.roundTime[0]+"~"+t.roundTime[1]:"-"),1),e("td",null,o(t.roundName||"-"),1),e("td",null,o(t.roundContent||"-"),1)]))),128))])])],512),e("div",H,[m(u,{type:"primary",onClick:x},{default:c(()=>[b("打印")]),_:1}),m(u,{onClick:g},{default:c(()=>[b("返回")]),_:1})])]),_:1},8,["modelValue"])}}},U=D(K,[["__scopeId","data-v-f0ca487d"]]);export{U as default};
