import{X as t}from"./index-B0qHf98Y.js";function o(e){return t({url:"/medication/inventoryRecord/list",method:"get",params:e})}function r(e){return t({url:"/medication/inventoryRecord/"+e,method:"get"})}function d(e){return t({url:"/medication/inventoryRecord",method:"post",data:e})}function i(e){return t({url:"/medication/inventoryRecord",method:"put",data:e})}function a(e){return t({url:"/medication/inventoryRecord/"+e,method:"delete"})}function c(e){return t({url:"/medication/inventoryRecord/save",method:"post",data:e})}export{d as a,a as d,r as g,o as l,c as s,i as u};
