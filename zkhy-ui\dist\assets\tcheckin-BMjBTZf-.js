import{X as t}from"./index-B0qHf98Y.js";function c(e){return t({url:"/checkin/checkIn/list",method:"get",params:e})}function a(e){return t({url:"/checkin/checkIn",method:"post",data:e})}function r(e){return t({url:"/checkin/checkIn",method:"put",data:e})}function u(e){return t({url:"/checkin/checkIn/"+e,method:"delete"})}function d(e){return t({url:"/eldersystem/checkin/aggregate/save",method:"post",data:e})}function h(e){return t({url:"/eldersystem/checkin/aggregate/info/"+e,method:"get"})}function s(e){return t({url:"/eldersystem/checkin/aggregate/update",method:"put",data:e})}export{s as C,d as a,a as b,u as d,h as g,c as l,r as u};
