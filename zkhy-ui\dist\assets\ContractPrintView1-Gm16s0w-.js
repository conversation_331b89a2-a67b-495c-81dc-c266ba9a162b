import{_ as P,u as B,r as p,F as E,e as b,c as v,o as r,i as s,t,K as y,L as g,f as k,h as w,j as J,n as L,v as M,x as R}from"./index-B0qHf98Y.js";import{g as q,a as W}from"./contract-DgThwd93.js";const o=u=>(M("data-v-341f2040"),u=u(),R(),u),j={class:"contract-print-view"},F={class:"print-content section"},H=o(()=>s("h1",{class:"print-title contract-title-red"},"和孚养老机构入住合同清单",-1)),O=o(()=>s("div",{class:"section-title"},"老人基本信息",-1)),$={class:"info-grid"},K={class:"info-item"},U=o(()=>s("span",{class:"info-label"},"老人姓名：",-1)),z={class:"info-value"},G={class:"info-item"},Q=o(()=>s("span",{class:"info-label"},"身份证号：",-1)),X={class:"info-value"},Y={class:"info-item"},Z=o(()=>s("span",{class:"info-label"},"年龄：",-1)),ss={class:"info-value"},es={class:"info-item"},ts=o(()=>s("span",{class:"info-label"},"性别：",-1)),os={class:"info-value"},as={class:"info-item"},ns=o(()=>s("span",{class:"info-label"},"联系电话：",-1)),ls={class:"info-value"},is={class:"info-item"},cs=o(()=>s("span",{class:"info-label"},"老人编号：",-1)),ds={class:"info-value"},_s=o(()=>s("div",{class:"section-title"},"监护人信息",-1)),rs={class:"info-grid"},us={class:"info-item"},vs=o(()=>s("span",{class:"info-label"},"监护人姓名：",-1)),hs={class:"info-value"},fs={class:"info-item"},ps=o(()=>s("span",{class:"info-label"},"与老人关系：",-1)),ms={class:"info-value"},bs={class:"info-item"},ys=o(()=>s("span",{class:"info-label"},"监护人电话：",-1)),gs={class:"info-value"},ks={class:"info-item"},ws=o(()=>s("span",{class:"info-label"},"监护人身份证：",-1)),Cs={class:"info-value"},Is={class:"info-item"},Ls=o(()=>s("span",{class:"info-label"},"监护人地址：",-1)),Ns={class:"info-value"},Ss=o(()=>s("div",{class:"section-title"},"居住信息",-1)),xs={class:"info-grid"},As={class:"info-item"},Ds=o(()=>s("span",{class:"info-label"},"养老机构：",-1)),Ts={class:"info-value"},Vs={class:"info-item"},Ps=o(()=>s("span",{class:"info-label"},"床位号：",-1)),Bs={class:"info-value"},Es={class:"info-item"},Js=o(()=>s("span",{class:"info-label"},"房间类型：",-1)),Ms={class:"info-value"},Rs=o(()=>s("div",{class:"section-title"},"费用信息",-1)),qs={class:"info-grid"},Ws={class:"info-item"},js=o(()=>s("span",{class:"info-label"},"合同编号：",-1)),Fs={class:"info-value"},Hs={class:"info-item"},Os=o(()=>s("span",{class:"info-label"},"签约时间：",-1)),$s={class:"info-value"},Ks={class:"info-item"},Us=o(()=>s("span",{class:"info-label"},"合同开始时间：",-1)),zs={class:"info-value"},Gs={class:"info-item"},Qs=o(()=>s("span",{class:"info-label"},"合同结束时间：",-1)),Xs={class:"info-value"},Ys={class:"info-item"},Zs=o(()=>s("span",{class:"info-label"},"支付类型：",-1)),se={class:"info-value"},ee={class:"info-item"},te=o(()=>s("span",{class:"info-label"},"支付时间：",-1)),oe={class:"info-value"},ae=o(()=>s("div",{class:"section-title"},"费用明细",-1)),ne={class:"fee-details-table"},le=o(()=>s("thead",null,[s("tr",null,[s("th",null,"费用项目"),s("th",null,"收费标准"),s("th",null,"说明"),s("th",null,"开始时间"),s("th",null,"结束时间"),s("th",null,"优惠"),s("th",null,"月缴纳标准"),s("th",null,"备注")])],-1)),ie=o(()=>s("div",{class:"section-title"},"服务信息",-1)),ce={class:"info-grid"},de={class:"info-item"},_e=o(()=>s("span",{class:"info-label"},"照护等级：",-1)),re={class:"info-value"},ue={class:"info-item"},ve=o(()=>s("span",{class:"info-label"},"护理等级：",-1)),he={class:"info-value"},fe={class:"info-item"},pe=o(()=>s("span",{class:"info-label"},"能力评估：",-1)),me={class:"info-value"},be={class:"info-item full-width"},ye={class:"info-label"},ge={class:"care-items-container"},ke={class:"care-items-grid"},we={class:"info-item full-width"},Ce={class:"info-label"},Ie={class:"info-value"},Le={class:"info-item full-width"},Ne=o(()=>s("span",{class:"info-label"},"其他事项：",-1)),Se={class:"info-value"},xe={class:"print-button-container"},Ae={__name:"ContractPrintView1",props:{contractId:{type:[String,Number],required:!1}},emits:["close"],setup(u,{emit:N}){const S=u,x=N,A=B(),e=p({}),C=p([]),h=p([]),f=p([]),D=["整理床单元","床头柜整理","床单，被套更换洗","老人衣服换洗","物品整齐摆放","出轨内衣物整理","晨间协助老人洗漱","房间内垃圾桶倾倒","老人足部洗脚","加压清洗","定时洗澡","胃管老人口腔护理","气垫床使用","协助排便","提醒，协助老人服药","每月理发","水瓶内接水","尿不湿会阴区护理","尿袋倒尿","按时喂水，提醒喝水","定时更换导尿管","生活用品清洗","护理垫，纸尿裤更换","失能老人每2小时翻身"];E(async()=>{const _=S.contractId||A.params.id;if(_)try{const n=await q(_),{contract:c,contractService:l,feeDetails:d}=n.data||{};if(e.value=c||{},c&&c.paymentDate)try{f.value=c.paymentDate?JSON.parse(c.paymentDate):[],e.value.paymentDates=f.value.join(", ")}catch{f.value=[],e.value.paymentDates=""}else f.value=[],e.value.paymentDates="";if(l&&(e.value={...e.value,careLevel:l.careLevel,nursingLevel:l.nursingLevel,abilityAssessment:l.abilityAssessmentResult,carePlan:l.carePlan,remarks:l.remark,recorderName:l.recorderName,careLevel2:l.careLevel2,care_level_2:l.care_level_2||l.careLevel2||""},l.serviceItemsJson))try{h.value=JSON.parse(l.serviceItemsJson)}catch{h.value=[]}if(C.value=Array.isArray(d)?d:[],c&&c.elderId)try{const a=await W(c.elderId),i=a.data||a.rows&&a.rows[0];i&&(e.value={...e.value,elderName:i.elderName,idCard:i.idCard,age:i.age,gender:i.gender==="1"?"男":i.gender==="0"?"女":"-",phone:i.phone,elderCode:i.elderCode})}catch(a){console.error("获取老人信息失败:",a)}}catch(n){console.error("获取合同详情失败:",n)}});const T=()=>{const _=document.querySelector(".print-content");if(!_)return;let n=document.createElement("iframe");n.style.position="fixed",n.style.right="0",n.style.bottom="0",n.style.width="0",n.style.height="0",n.style.border="0",document.body.appendChild(n);const c=Array.from(document.querySelectorAll('style, link[rel="stylesheet"]'));let l="";c.forEach(a=>{l+=a.outerHTML});const d=n.contentWindow.document;d.open(),d.write(`
    <html>
      <head>
        <title>打印</title>
        ${l}
        <style>
          body { background: #fff; margin: 0; }
        </style>
      </head>
      <body>
        <div class="print-content">${_.innerHTML}</div>
      </body>
    </html>
  `),d.close(),n.onload=()=>{n.contentWindow.focus(),n.contentWindow.print(),setTimeout(()=>{document.body.removeChild(n)},100)}},V=()=>{x("close")};return(_,n)=>{const c=b("el-checkbox"),l=b("el-checkbox-group"),d=b("el-button");return r(),v("div",j,[s("div",F,[H,O,s("div",$,[s("div",K,[U,s("span",z,t(e.value.elderName),1)]),s("div",G,[Q,s("span",X,t(e.value.idCard),1)]),s("div",Y,[Z,s("span",ss,t(e.value.age),1)]),s("div",es,[ts,s("span",os,t(e.value.gender),1)]),s("div",as,[ns,s("span",ls,t(e.value.phone),1)]),s("div",is,[cs,s("span",ds,t(e.value.elderCode),1)])]),_s,s("div",rs,[s("div",us,[vs,s("span",hs,t(e.value.guardianName),1)]),s("div",fs,[ps,s("span",ms,t(e.value.guardianRelation),1)]),s("div",bs,[ys,s("span",gs,t(e.value.guardianPhone),1)]),s("div",ks,[ws,s("span",Cs,t(e.value.guardianIdcard),1)]),s("div",Is,[Ls,s("span",Ns,t(e.value.guardianAddress),1)])]),Ss,s("div",xs,[s("div",As,[Ds,s("span",Ts,t(e.value.orgName),1)]),s("div",Vs,[Ps,s("span",Bs,t(e.value.bedNo),1)]),s("div",Es,[Js,s("span",Ms,t(e.value.roomType),1)])]),Rs,s("div",qs,[s("div",Ws,[js,s("span",Fs,t(e.value.contractNo),1)]),s("div",Hs,[Os,s("span",$s,t(e.value.signTime),1)]),s("div",Ks,[Us,s("span",zs,t(e.value.contractStarttime),1)]),s("div",Gs,[Qs,s("span",Xs,t(e.value.contractEndtime),1)]),s("div",Ys,[Zs,s("span",se,t(e.value.paymentType),1)]),s("div",ee,[te,s("span",oe,t(e.value.paymentDates),1)])]),ae,s("table",ne,[le,s("tbody",null,[(r(!0),v(y,null,g(C.value,(a,i)=>(r(),v("tr",{key:i},[s("td",null,t(a.feeItem),1),s("td",null,t(a.feeStandard),1),s("td",null,t(a.description),1),s("td",null,t(a.startTime),1),s("td",null,t(a.endTime),1),s("td",null,t(a.discount),1),s("td",null,t(a.actualAmount),1),s("td",null,t(a.remark),1)]))),128))])]),ie,s("div",ce,[s("div",de,[_e,s("span",re,t(e.value.careLevel),1)]),s("div",ue,[ve,s("span",he,t(e.value.nursingLevel),1)]),s("div",fe,[pe,s("span",me,t(e.value.abilityAssessment),1)]),s("div",be,[s("span",ye,t(e.value.nursingLevel)+"：",1),s("div",ge,[s("div",ke,[k(l,{modelValue:h.value,"onUpdate:modelValue":n[0]||(n[0]=a=>h.value=a),disabled:_.isViewMode},{default:w(()=>[(r(),v(y,null,g(3,(a,i)=>s("div",{key:i,class:"care-column"},[(r(!0),v(y,null,g(D.filter((m,I)=>I%3===i),(m,I)=>(r(),J(c,{disabled:"",key:m,label:m,class:"care-checkbox"},null,8,["label"]))),128))])),64))]),_:1},8,["modelValue","disabled"])])])]),s("div",we,[s("span",Ce,t(e.value.careLevel)+"：",1),s("span",Ie,t(e.value.care_level_2||e.value.careLevel2),1)])]),s("div",Le,[Ne,s("span",Se,t(e.value.remark),1)]),s("div",xe,[k(d,{type:"primary",onClick:T},{default:w(()=>[L("打印")]),_:1}),k(d,{onClick:V},{default:w(()=>[L("关闭")]),_:1})])])])}}},Ve=P(Ae,[["__scopeId","data-v-341f2040"]]);export{Ve as default};
