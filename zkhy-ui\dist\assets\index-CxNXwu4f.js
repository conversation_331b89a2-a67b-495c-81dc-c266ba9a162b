import K from"./medicationPreparePublic-BvK_NlUE.js";import{g as Q,a as J}from"./roommanage-DBG5TiIR.js";import{o as O,p as W}from"./index-2bfkpdNb.js";import{_ as X,d as Z,r as c,F as ee,e as d,c as s,o as u,f as l,k as y,h as t,i as S,K as k,L as x,j as D,l as le,n as m,t as i,E as ae,G as U}from"./index-B0qHf98Y.js";import"./index-CCXF19OR.js";import"./leave-Dd4WELmg.js";import"./index-e0lvOvDC.js";const oe={class:"drug-receive-record-container"},te={class:"button-group",style:{"text-align":"right"}},ne={key:0,class:"medication-plan"},re={key:1,class:"medication-plan"},ie={key:2,class:"medication-plan"},ue={key:0,class:"pagination-container"},de={__name:"index",setup(pe){const{proxy:b}=Z(),{medication_plan:z}=b.useDict("medication_plan"),C=c([]),h=c([]),a=c({pageSize:10,pageNum:1});c([{value:"片",label:"片"},{value:"粒",label:"粒"},{value:"袋",label:"袋"},{value:"毫升",label:"毫升"},{value:"毫克",label:"毫克"},{value:"克",label:"克"}]);const M=c([]),V=c(0),_=c([]),I=()=>{a.value.pageNum=1,g()},B=()=>{_.value=[],a.value={pageSize:10,pageNum:1},g()},L=()=>{b.$refs.MedicationPreparePublicRef.openAdd()},E=n=>{b.$refs.MedicationPreparePublicRef.openView(n)},Y=n=>{b.$refs.MedicationPreparePublicRef.openEdit(n)},$=n=>{console.log("删除",n),ae.confirm("注：无服药计划药品支持删除，删除药品将失去原始数据，请慎重删除","确定删除该摆药计划数据吗？",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await W(n.id)).code==200?(U.success("删除成功"),a.value.pageNum=1,g()):U.success("删除失败")})},R=async n=>{h.value=[],a.value.floorId="";const o=await J(n);h.value=o.rows},F=n=>{a.value.pageSize=n,g()},j=n=>{a.value.pageNum=n,g()},g=async()=>{const n=await O(b.addDateRange(a.value,_.value,"PreparationStartTime"));M.value=n.rows||[],V.value=n.total||0},A=async()=>{const n=await Q();C.value=n.rows||[]};return ee(()=>{A(),g()}),(n,o)=>{const P=d("el-date-picker"),p=d("el-form-item"),w=d("el-input"),f=d("el-option"),N=d("el-select"),v=d("el-button"),H=d("el-form"),r=d("el-table-column"),q=d("el-table"),G=d("el-pagination");return u(),s("div",oe,[l(H,{inline:!0,model:a.value,class:"search-form","label-width":"100px"},{default:t(()=>[l(p,{label:"摆药日期",prop:"updateTime"},{default:t(()=>[l(P,{modelValue:a.value.updateTime,"onUpdate:modelValue":o[0]||(o[0]=e=>a.value.updateTime=e),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(p,{label:"老人姓名",prop:"elderName"},{default:t(()=>[l(w,{modelValue:a.value.elderName,"onUpdate:modelValue":o[1]||(o[1]=e=>a.value.elderName=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(p,{label:"楼栋信息",prop:"buildingId"},{default:t(()=>[l(N,{modelValue:a.value.buildingId,"onUpdate:modelValue":o[2]||(o[2]=e=>a.value.buildingId=e),placeholder:"全部",style:{width:"200px"},clearable:"",onChange:R},{default:t(()=>[l(f,{label:"全部",value:""}),(u(!0),s(k,null,x(C.value,e=>(u(),D(f,{key:e.value,label:e.buildingName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"楼栋层数",prop:"floorId"},{default:t(()=>[l(N,{modelValue:a.value.floorId,"onUpdate:modelValue":o[3]||(o[3]=e=>a.value.floorId=e),placeholder:"全部",style:{width:"200px"},clearable:"",disabled:!a.value.buildingId},{default:t(()=>[l(f,{label:"全部",value:""}),(u(!0),s(k,null,x(h.value,e=>(u(),D(f,{key:e.value,label:e.floorName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),l(p,{label:"房间号",prop:"roomNumber"},{default:t(()=>[l(w,{modelValue:a.value.roomNumber,"onUpdate:modelValue":o[4]||(o[4]=e=>a.value.roomNumber=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(p,{label:"药品名称",prop:"medicationName"},{default:t(()=>[l(w,{modelValue:a.value.medicationName,"onUpdate:modelValue":o[5]||(o[5]=e=>a.value.medicationName=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(p,{label:"摆药周期"},{default:t(()=>[l(P,{modelValue:_.value,"onUpdate:modelValue":o[6]||(o[6]=e=>_.value=e),type:"daterange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(p,{label:"摆药计划",prop:"timeType"},{default:t(()=>[l(N,{modelValue:a.value.timeType,"onUpdate:modelValue":o[7]||(o[7]=e=>a.value.timeType=e),placeholder:"全部",style:{width:"200px"},clearable:""},{default:t(()=>[(u(!0),s(k,null,x(le(z),e=>(u(),D(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"摆药人",prop:"recorder"},{default:t(()=>[l(w,{modelValue:a.value.recorder,"onUpdate:modelValue":o[8]||(o[8]=e=>a.value.recorder=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),S("div",te,[l(v,{type:"primary",onClick:I,icon:"search"},{default:t(()=>[m("查询")]),_:1}),l(v,{onClick:B,icon:"refresh"},{default:t(()=>[m("重置")]),_:1}),l(v,{icon:"Plus",type:"primary",onClick:L,plain:""},{default:t(()=>[m("新增预备")]),_:1})])]),_:1},8,["model"]),l(q,{data:M.value,border:"",style:{width:"100%"}},{default:t(()=>[l(r,{prop:"id",label:"序号",width:"60",align:"center"},{default:t(e=>[m(i(e.$index+1),1)]),_:1}),l(r,{prop:"updateTime",label:"摆药日期",align:"center","min-width":"120"},{default:t(e=>[S("span",null,i(n.parseTime(e.row.updateTime,"{y}-{m}-{d}")),1)]),_:1}),l(r,{prop:"elderName",label:"老人姓名",align:"center"}),l(r,{prop:"floorNumber",label:"楼层信息",align:"center"}),l(r,{prop:"roomNumber",label:"房间号",align:"center"}),l(r,{prop:"buildingName",label:"楼栋信息",align:"center"}),l(r,{prop:"medicationId",label:"药品编号",align:"center","min-width":"200"}),l(r,{prop:"medicationName",label:"药品名称",align:"center","min-width":"150"}),l(r,{prop:"dosage",label:"用量",align:"center"}),l(r,{prop:"administrationMethod",label:"服用方法",align:"center"}),l(r,{prop:"quantity",label:"药品数量",align:"center"}),l(r,{prop:"specification",label:"摆药周期",align:"center","min-width":"200"},{default:t(e=>[m(i(e.row.preparationStartTime)+" ~ "+i(e.row.preparationEndTime),1)]),_:1}),l(r,{prop:"specificationQuantity",label:"服药计划",align:"left","min-width":"200","header-align":"center"},{default:t(e=>[e.row.morningDosage?(u(),s("div",ne,"早晨:"+i(e.row.morningBeforeMeal=="0"?"餐前":e.row.morningBeforeMeal=="1"?"餐中":"餐后")+" "+i(e.row.morningDosage||"-")+i(e.row.morningDosageUnit||"-"),1)):y("",!0),e.row.noonDosage?(u(),s("div",re,"中午:"+i(e.row.noonBeforeMeal=="0"?"餐前":e.row.noonBeforeMeal=="1"?"餐中":"餐后")+" "+i(e.row.noonDosage||"-")+i(e.row.noonDosageUnit||"-"),1)):y("",!0),e.row.eveningDosage?(u(),s("div",ie,"晚上:"+i(e.row.eveningBeforeMeal=="0"?"餐前":e.row.eveningBeforeMeal=="1"?"餐中":e.row.eveningBeforeMeal=="2"?"餐后":"睡前")+" "+i(e.row.eveningDosage||"-")+i(e.row.eveningDosageUnit||"-"),1)):y("",!0)]),_:1}),l(r,{prop:"recorder",label:"摆药人",align:"center","min-width":"180"}),l(r,{prop:"preparer",label:"核对人",align:"center"}),l(r,{label:"操作","min-width":"220",fixed:"right",align:"center"},{default:t(e=>[l(v,{link:"",type:"primary",onClick:T=>E(e.row),icon:"Search"},{default:t(()=>[m("查看")]),_:2},1032,["onClick"]),l(v,{link:"",type:"primary",onClick:T=>Y(e.row),icon:"Edit"},{default:t(()=>[m("修改")]),_:2},1032,["onClick"]),l(v,{link:"",type:"primary",onClick:T=>$(e.row),icon:"Delete"},{default:t(()=>[m("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),V.value>0?(u(),s("div",ue,[l(G,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":o[9]||(o[9]=e=>a.value.pageNum=e),"page-size":a.value.pageSize,"onUpdate:pageSize":o[10]||(o[10]=e=>a.value.pageSize=e),"page-sizes":[10,20,30,40],total:V.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:j},null,8,["current-page","page-size","total"])])):y("",!0),l(K,{ref:"MedicationPreparePublicRef",onSuccess:B},null,512)])}}},_e=X(de,[["__scopeId","data-v-2eb46724"]]);export{_e as default};
