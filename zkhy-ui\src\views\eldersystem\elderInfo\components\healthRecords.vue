<template>
  <div>
    <el-card shadow="never">
      <div class="cardDetailTop">
        <div
          class="cardDetailTopDiv"
          :class="isSelectTopTag == '01' ? 'cardDetailTopDivSelect' : ''"
          @click="showTagDetail('01')"
        >
          健康信息
        </div>
        <div
          class="cardDetailTopDiv"
          :class="isSelectTopTag == '02' ? 'cardDetailTopDivSelect' : ''"
          @click="showTagDetail('02')"
        >
          健康档案
        </div>
      </div>
      <el-row>
        <el-col :span="24">
          <div v-if="isSelectTopTag == '01'">
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form
                  ref="telderinfoRef"
                  :model="queryform"
                  :rules="queryrules"
                  label-width="120px"
                >
                  <el-date-picker
                    v-model="queryform.dateRange"
                    type="datetimerange"
                    start-placeholder="开始时间"
                    range-separator="至"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm"
                    date-format="YYYY-MM-DD HH:mm"
                    time-format="hh:mm"
                  />
                </el-form>
              </el-col>
            </el-row>
            <div style="width: 100%; height: 20px"></div>
            <div class="dashboard">
              <!-- 血压卡片 -->
              <div class="card">
                <div class="card-header">
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiM4QTJCRTIiLz48L3N2Zz4="
                    alt="血压"
                  />
                  <span>血压监测</span>
                </div>
                <div class="value-display">
                  128<span class="unit">mmHg</span> / 89<span class="unit">mmHg</span>
                </div>
                <div id="chart1" class="chart-container"></div>
              </div>

              <!-- 心率卡片 -->
              <div class="card">
                <div class="card-header">
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDIxLjM1TDEwLjU1IDIwLjAzQzUuNCAxNS4zNiAyIDEyLjI3IDIgOC41QzIgNS40MSA0LjQyIDMgNy41IDNDOS4yNCAzIDEwLjkxIDMuODEgMTIgNS4wO0MxMy4wOSAzLjgxIDE0Ljc2IDMgMTYuNSAzQzE5LjU4IDMgMjIgNS40MSAyMiA4LjVDMjIgMTIuMjcgMTguNiAxNS4zNiAxMy40NSAyMC4wM0wxMiAyMS4zNVoiIGZpbGw9IiNGRjY5QjQiLz48L3N2Zz4="
                    alt="心率"
                  />
                  <span>心率监测</span>
                </div>
                <div class="value-display">72<span class="unit">次/分</span></div>
                <div id="chart2" class="chart-container"></div>
              </div>

              <!-- 睡眠卡片 -->
              <div class="card">
                <div class="card-header">
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIzIDEySDE5VjVDMTkgNC40NCAxOC41NiA0IDE4IDRIMTZDMTUuNDQgNCAxNSA0LjQ0IDE1IDVWMTJIMTNWNUMxMyA0LjQ0IDEyLjU2IDQgMTIgNEgxMEM5LjQ0IDQgOSA0LjQ0IDkgNVYxMkg1QzQuNDQgMTIgNCAxMi40NCA0IDEzVjE1QzQgMTUuNTYgNC40NCAxNiA1IDE2SDlWMTlDOSAxOS41NiA5LjQ0IDIwIDEwIDIwSDEyQzEyLjU2IDIwIDEzIDE5LjU2IDEzIDE5VjE2SDE1VjE5QzE1IDE5LjU2IDE1LjQ0IDIwIDE2IDIwSDE4QzE4LjU2IDIwIDE5IDE5LjU2IDE5IDE5VjE2SDIzQzIzLjU2IDE2IDI0IDE1LjU2IDI0IDE1VjEzQzI0IDEyLjQ0IDIzLjU2IDEyIDIzIDEyWiIgZmlsbD0iIzAwQjNENyIvPjwvc3ZnPg=="
                    alt="睡眠"
                  />
                  <span>睡眠监测</span>
                </div>
                <div class="value-display">7.5<span class="unit">小时</span></div>
                <div id="chart3" class="chart-container"></div>
              </div>

              <!-- 血氧卡片 -->
              <div class="card">
                <div class="card-header">
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyLDMgQzE2Ljk3LDMgMjEsNi41OCAyMSwxMSBDMjEsMTUuNDIgMTYuOTcsMTkgMTIsMTkgQzcuMDMsMTkgMywxNS40MiAzLDExIEMzLDYuNTggNy4wMywzIDEyLDMgTTEyLDUgQzguMTQsNSA1LDcuNzEgNSwxMSBDNSwxNC4yOSA4LjE0LDE3IDEyLDE3IEMxNS44NiwxNyAxOSwxNC4yOSAxOSwxMSBDMTksNy43MSAxNS44Niw1IDEyLDUgTTEyLDcgQzE0LjI5LDcgMTYsOC4zNCAxNiwxMCBDMTYsMTEuNjYgMTQuMjksMTMgMTIsMTMgQzkuNzEsMTMgOCwxMS42NiA4LDEwIEM4LDguMzQgOS43MSw3IDEyLDcgTTEyLDkgQzExLjQ1LDkgMTEsOS40NSAxMSwxMCBDMTEsMTAuNTUgMTEuNDUsMTEgMTIsMTEgQzEyLjU1LDExIDEzLDEwLjU1IDEzLDEwIEMxMyw5LjQ1IDEyLjU1LDkgMTIsOSBaIiBmaWxsPSIjRkY0NDQ0Ii8+PC9zdmc+"
                    alt="血氧"
                  />
                  <span>血氧监测</span>
                </div>
                <div class="value-display">98<span class="unit">%</span></div>
                <div id="chart4" class="chart-container"></div>
              </div>

              <!-- 体温卡片 -->
              <div class="card">
                <div class="card-header">
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiNGRjhCMDAiLz48L3N2Zz4="
                    alt="体温"
                  />
                  <span>体温监测</span>
                </div>
                <div class="value-display">36.7<span class="unit">°C</span></div>
                <div id="chart5" class="chart-container"></div>
              </div>

              <!-- 呼吸频率卡片 -->
              <div class="card">
                <div class="card-header">
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xIDE4aDJ2LTJoLTJ2MnptMi4wNy0xMy4yNGMtLjI2LS4xOS0uNTktLjE5LS44MSAwbC0xLjA3Ljc1Yy0uMzMuMjMtLjQzLjY2LS4yNSAxLjAxLjE5LjM0LjU2LjUxLjkxLjQzLjM1LS4wOC42My0uMzEuNzItLjY2LjA5LS4zNSAwLS43Mi0uMjUtLjk3bC0uNzUtLjUzVjEyaDJWOC43NnoiIGZpbGw9IiMwMEI1RTMiLz48L3N2Zz4="
                    alt="呼吸"
                  />
                  <span>呼吸频率</span>
                </div>
                <div class="value-display">16<span class="unit">次/分</span></div>
                <div id="chart6" class="chart-container"></div>
              </div>
            </div>
          </div>
          <div v-if="isSelectTopTag == '02'">
            <el-form
              ref="telderinfoRef"
              :model="formbase"
              :rules="rules"
              label-width="120px"
            >
              <el-divider content-position="left"
                ><span class="subtitleCss">基本信息</span></el-divider
              >

              <div style="height: 10px"></div>
              <el-row :gutter="15">
                <el-col :span="8">
                  <el-form-item label="老人身高" prop="heightCm" size="large">
                    <el-input
                      v-model="formbase.heightCm"
                      placeholder="请输入身高(cm)"
                      :disabled="props.isShow"
                    >
                      <template #append>cm</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="老人体重" prop="weightKg" size="large">
                    <el-input v-model="formbase.weightKg" placeholder="请输入体重(kg)">
                      <template #append>kg</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="RH因子" prop="rhFactor" size="large">
                    <el-select v-model="formbase.rhFactor" placeholder="请选择RH因子">
                      <el-option
                        v-for="dict in rh_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="血型" prop="bloodType" size="large">
                    <el-select v-model="formbase.bloodType" placeholder="请选择血型">
                      <el-option
                        v-for="dict in elderly_blood_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="睡眠质量" prop="sleepQuality" size="large">
                    <el-select
                      v-model="formbase.sleepQuality"
                      placeholder="请选择睡眠质量"
                    >
                      <el-option
                        v-for="dict in sleep_quality"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="吸烟频率" prop="smokingFrequency" size="large">
                    <el-select
                      v-model="formbase.smokingFrequency"
                      placeholder="请选择吸烟频率"
                    >
                      <el-option
                        v-for="dict in smoking_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="饮酒频率" prop="drinkingFrequency" size="large">
                    <el-select
                      v-model="formbase.drinkingFrequency"
                      placeholder="请选择饮酒频率"
                    >
                      <el-option
                        v-for="dict in drinking_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="运动频率" prop="exerciseFrequency" size="large">
                    <el-select
                      v-model="formbase.exerciseFrequency"
                      placeholder="请选择运动频率"
                    >
                      <el-option
                        v-for="dict in sports_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="饮食偏好" prop="dietaryPreference" size="large">
                    <el-select
                      v-model="formbase.dietaryPreference"
                      placeholder="请选择饮食偏好"
                    >
                      <el-option
                        v-for="dict in dietary_preferences"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="慢性病史" prop="chronicDiseases" size="large">
                    <el-tag
                      style="margin-right: 4px"
                      v-for="tag in dynamicTags"
                      :key="tag"
                      closable
                      :disable-transitions="false"
                      @close="handleClose(tag)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="inputVisible"
                      ref="InputRef"
                      v-model="inputValue"
                      class="w-20"
                      size="larger"
                      @keyup.enter="handleInputConfirm"
                      @blur="handleInputConfirm"
                    />
                    <el-button
                      v-else
                      class="button-new-tag"
                      size="large"
                      @click="showInput"
                    >
                      + 添加
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-divider content-position="left">
                  <span class="subtitleCss">健康史</span>
                </el-divider>
                <div class="historyDiv">
                  <el-col :span="24" style="height: 130px">
                    <div class="cardDetailcenter">
                      <div class="historyCss">既往病史</div>
                      <div>
                        <el-button type="primary" @click="openList1('1')" size="small"
                          >既往病史管理</el-button
                        >
                      </div>
                    </div>

                    <div class="timelineProcessBox">
                      <el-timeline class="timeline">
                        <el-timeline-item
                          class="lineitem"
                          :class="activity.done ? 'active' : 'inactive'"
                          v-for="(activity, index) in jwsDataList"
                          :key="index"
                        >
                          <span style="display: flex; flex-direction: column">
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              确证时间：{{ activity.eventDate }}
                            </span>
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              名称：{{ activity.name }}
                            </span>
                          </span>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </el-col>

                  <el-col :span="24">
                    <div class="cardDetailcenter">
                      <div class="historyCss">手术史</div>
                      <div>
                        <el-button type="primary" @click="openList1('2')" size="small"
                          >手术史管理</el-button
                        >
                      </div>
                    </div>

                    <div class="timelineProcessBox">
                      <el-timeline class="timeline">
                        <el-timeline-item
                          class="lineitem"
                          :class="activity.done ? 'active' : 'inactive'"
                          v-for="(activity, index) in sssDataList"
                          :key="index"
                        >
                          <span style="display: flex; flex-direction: column">
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              确证时间：{{ activity.eventDate }}
                            </span>
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              名称：{{ activity.name }}
                            </span>
                          </span>
                        </el-timeline-item>
                      </el-timeline>
                    </div></el-col
                  >
                  <el-col :span="24"
                    ><div class="cardDetailcenter">
                      <div class="historyCss">长期用药史</div>
                      <div>
                        <el-button type="primary" @click="openList1('3')" size="small"
                          >长期用药史管理</el-button
                        >
                      </div>
                    </div>

                    <div class="timelineProcessBox">
                      <el-timeline class="timeline">
                        <el-timeline-item
                          class="lineitem"
                          :class="activity.done ? 'active' : 'inactive'"
                          v-for="(activity, index) in yysDataList"
                          :key="index"
                        >
                          <span style="display: flex; flex-direction: column">
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              确证时间：{{ activity.eventDate }}
                            </span>
                            <span style="margin: 10px 0; font-size: 12px; color: #999">
                              名称：{{ activity.name }}
                            </span>
                          </span>
                        </el-timeline-item>
                      </el-timeline>
                    </div></el-col
                  >
                </div>
                <el-divider content-position="left"
                  ><span class="subtitleCss">其他信息</span></el-divider
                >
                <el-col :span="24" style="margin-top: 12px">
                  <el-form-item label="过敏史" prop="allergyHistory" size="large">
                    <el-input
                      v-model="formbase.allergyHistory"
                      type="textarea"
                      rows="4"
                      placeholder="请输入内容"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="家住病史" prop="familyHistory" size="large">
                    <el-input
                      v-model="formbase.familyHistory"
                      type="textarea"
                      rows="4"
                      placeholder="请输入内容"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="能力评估"
                    prop="allergyHistory"
                    size="small"
                    stripe
                  >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <table class="el-table tables">
              <thead class="el-table__header">
                <tr>
                  <th class="border border-gray-300 p-2 bg-gray-100">序号</th>
                  <th class="border border-gray-300 p-2 bg-gray-100">能力</th>
                  <th class="border border-gray-300 p-2 bg-gray-100">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="border border-gray-300 p-2">1</td>
                  <td class="border border-gray-300 p-2">进食</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue1">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">2</td>
                  <td class="border border-gray-300 p-2">洗澡</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue2">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">3</td>
                  <td class="border border-gray-300 p-2">修饰</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue3">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">4</td>
                  <td class="border border-gray-300 p-2">穿衣</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue4">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">5</td>
                  <td class="border border-gray-300 p-2">如厕，排泄</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue5">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">6</td>
                  <td class="border border-gray-300 p-2">移动</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue6">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">7</td>
                  <td class="border border-gray-300 p-2">认知能力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue7">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">8</td>
                  <td class="border border-gray-300 p-2">情绪能力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue8">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">9</td>
                  <td class="border border-gray-300 p-2">视觉能力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue9">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
                <tr>
                  <td class="border border-gray-300 p-2">10</td>
                  <td class="border border-gray-300 p-2">听力</td>
                  <td class="border border-gray-300 p-2">
                    <el-radio-group v-model="radioValue10">
                      <el-radio :value="0">正常</el-radio>
                      <el-radio :value="1">轻度依赖</el-radio>
                      <el-radio :value="2">重度依赖</el-radio>
                      <el-radio :value="3">完全依赖</el-radio>
                    </el-radio-group>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-col>
      </el-row>

      <div class="dialog-footer" style="margin-left: 90%; margin-top: 10px">
        <el-button size="large" type="primary" @click="submitFormHealthyProfile"
          >确 定</el-button
        >
      </div>
    </el-card>
    <el-drawer
      v-model="open"
      :title="title"
      direction="rtl"
      :before-close="handleCloseDraws"
      size="90%"
    >
      <el-row :gutter="15">
        <el-col :span="showOrAddEdit == '01' ? 21 : 18">
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="事件日期" prop="eventDate">
              <el-date-picker
                clearable
                v-model="queryParams.eventDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择事件日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable />
            </el-form-item>
            <el-form-item label="地点" prop="location">
              <el-input
                v-model="queryParams.location"
                placeholder="请输入地点"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" plain icon="Plus" @click="handleAdd"
                >新增</el-button
              >
            </el-form-item>
          </el-form>
          <el-table v-loading="loading" :data="elderHealthEventList" border stripe>
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="记录类型" align="center" prop="recordType" />
            <el-table-column label="事件日期" align="center" prop="eventDate" width="180">
              <template #default="scope">
                <span>{{ parseTime(scope.row.eventDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="名称" align="center" prop="name" />
            <el-table-column label="详情" align="center" prop="details" />
            <el-table-column label="地点" align="center" prop="location" />
            <el-table-column label="用药状态" align="center" prop="medicationStatus" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column
              label="操作"
              align="center"
              width="200"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  >修改</el-button
                >
                <el-button
                  link
                  type="primary"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
        <el-col :span="showOrAddEdit == '01' ? 0 : 6" v-if="showOrAddEdit == '02'">
          <el-card shadow="hover">
            <div class="Addtitle">{{ title }}</div>
            <el-form
              ref="elderHealthEventRef"
              :model="formevent"
              :rules="formeventrules"
              label-width="80px"
            >
              <el-input v-model="formevent.id" type="hidden" />
              <el-form-item label="事件日期" prop="eventDate">
                <el-date-picker
                  clearable
                  v-model="formevent.eventDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择事件日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="名称" prop="name">
                <el-input
                  v-model="formevent.name"
                  placeholder="请输入名称(疾病/手术/药品)"
                />
              </el-form-item>
              <el-form-item label="详情" prop="details">
                <el-input
                  v-model="formevent.details"
                  type="textarea"
                  rows="4"
                  placeholder="请输入内容(治疗结果/手术描述/剂量及用法/用药原因)"
                />
              </el-form-item>
              <el-form-item label="地点" prop="location">
                <el-input
                  v-model="formevent.location"
                  placeholder="请输入地点(就诊/手术医院)"
                />
              </el-form-item>
              <el-form-item label="用药状态" prop="medicationStatus">
                <el-input
                  v-model="formevent.medicationStatus"
                  placeholder="请输入用药状态(仅用于用药记录: 服用中/已停用)"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formevent.remark"
                  type="textarea"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-form>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitFormEvent">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="handleCloseDraws">关闭</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup name="healthRecords">
const { proxy } = getCurrentInstance();
import {
  addElderHealthEvent,
  delElderHealthEvent,
  getElderHealthEvent,
  listElderHealthEvent,
  updateElderHealthEvent,
} from "@/api/ReceptionManagement/telderHealthEvent";
import {
  addElderHealthProfile,
  listElderHealthProfile,
  updateElderHealthProfile,
} from "@/api/ReceptionManagement/telderHealthProfile";
import * as echarts from "echarts";
const {
  rh_type,
  elderly_blood_type,
  sleep_quality,
  smoking_frequency,
  drinking_frequency,
  sports_frequency,
  dietary_preferences,
  medical_history_status,
} = proxy.useDict(
  "rh_type",
  "elderly_blood_type",
  "sleep_quality",
  "smoking_frequency",
  "drinking_frequency",
  "sports_frequency",
  "dietary_preferences",
  "medical_history_status"
);
const data = reactive({
  formbase: {},
  queryform: {},
  formevent: {},
  queryParams: {},
  queryParamslist: {},
  queryParamsElderHealth: {},
  formeventrules: {
    details: [
      {
        required: true,
        message: "详情不能为空",
        trigger: "blur",
      },
    ],
    name: [
      {
        required: true,
        message: "名称不能为空",
        trigger: "blur",
      },
    ],
  },
  queryrules: {},
  rules: {},
});
const props = defineProps({
  // 老人的id
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: Boolean,
    default: false,
  },
});
const {
  formbase,
  queryform,
  formevent,
  queryParams,
  queryParamslist,
  queryParamsElderHealth,
  formeventrules,
  queryrules,
  rules,
} = toRefs(data);
const isSelectTopTag = ref("01");
const elderHealthEventList = ref([]);
const open = ref(false);
const title = ref("");

const jwsDataList = ref([]); //既往病史
const sssDataList = ref([]); //手术史
const yysDataList = ref([]); //长期用药史

const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showOrAddEdit = ref("01");
const addType = ref("");
//tag
const inputValue = ref("");
const dynamicTags = ref([]);
const inputVisible = ref(false);
const InputRef = ref();
const radioValue1 = ref(0);
const radioValue2 = ref(0);
const radioValue3 = ref(0);
const radioValue4 = ref(0);
const radioValue5 = ref(0);
const radioValue6 = ref(0);
const radioValue7 = ref(0);
const radioValue8 = ref(0);
const radioValue9 = ref(0);
const radioValue10 = ref(0);
const submitFormType = ref("");

function initCharts() {
  // 血压图表

  chart1();
  chart2();
  chart3();
  chart4();
  chart5();
  chart6();
}

function chart1() {
  var chartDom = document.getElementById("chart1");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: ["00:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
    },
    yAxis: {
      type: "value",
    },
    legend: {
      data: ["收缩压", "舒张压"],
    },
    series: [
      {
        data: [55, 68, 95, 87, 78, 65],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(140, 47, 226)", // 设置折线颜色为红色
        },
      },
      {
        data: [65, 78, 65, 77, 98, 67],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 169, 11)", // 设置折线颜色为红色
        },
      },
    ],
  };

  option && myChart.setOption(option);
}

function chart2() {
  var chartDom = document.getElementById("chart2");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["00:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
    },
    yAxis: {
      type: "value",
    },
    legend: {
      data: ["收缩压", "舒张压"],
    },
    series: [
      {
        data: [55, 68, 95, 87, 78, 65],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 102, 108)", // 设置折线颜色为红色
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(250,188,228,0.8)",
            },
          ]),
        }, //曲线覆盖区域设置
      },
    ],
  };

  option && myChart.setOption(option);
}

function chart3() {
  var chartDom = document.getElementById("chart3");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    tooltip: {
      trigger: "item",
    },
    legend: {
      top: "5%",
      left: "center",
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: "深睡眠" },
          { value: 735, name: "浅睡眠" },
          { value: 580, name: "清醒" },
        ],
      },
    ],
  };

  option && myChart.setOption(option);
}
function chart4() {
  var chartDom = document.getElementById("chart4");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: ["00:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
    },
    yAxis: {
      type: "value",
    },

    series: [
      {
        data: [55, 68, 95, 87, 78, 65],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 74, 74)", // 设置折线颜色为红色
        },
      },
    ],
  };

  option && myChart.setOption(option);
}
function chart5() {
  var chartDom = document.getElementById("chart5");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: ["00:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
    },
    yAxis: {
      type: "value",
    },

    series: [
      {
        data: [36.5, 36.7, 37.2, 37.5, 36.9, 36.5],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(225, 152, 0)", // 设置折线颜色为红色
        },
      },
    ],
  };

  option && myChart.setOption(option);
}
function chart6() {
  var chartDom = document.getElementById("chart6");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    xAxis: {
      type: "category",
      data: ["00:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
    },
    yAxis: {
      type: "value",
    },

    series: [
      {
        data: [14, 16, 19, 23, 17, 21],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "rgb(57, 196, 234)", // 设置折线颜色为红色
        },
      },
    ],
  };

  option && myChart.setOption(option);
}

function showTagDetail(type) {
  if (type == "01") {
    isSelectTopTag.value = "01";
    initTargetChart();
  } else if (type == "02") {
    isSelectTopTag.value = "02";
    //获取页面数据
    getPage2List();
    getHealthValue();
  }
}

function getPage2List() {
  queryParamslist.value.elderId = props.elderId;
  queryParamslist.value.recordType = "既往病史";
  listElderHealthEvent(queryParamslist.value).then((response) => {
    jwsDataList.value = response.rows?.slice(0, 3);
  });
  queryParamslist.value.recordType = "手术史";
  listElderHealthEvent(queryParamslist.value).then((response) => {
    sssDataList.value = response.rows?.slice(0, 3);
  });
  queryParamslist.value.recordType = "长期用药史";
  listElderHealthEvent(queryParamslist.value).then((response) => {
    yysDataList.value = response.rows?.slice(0, 3);
  });
}

function getHealthValue() {
  queryParamsElderHealth.value.elderId = props.elderId;
  console.log(queryParamsElderHealth.value, "queryParamsElderHealth");
  listElderHealthProfile(queryParamsElderHealth.value).then((res) => {
    if (!res.rows || res.rows.length == 0) {
      formbase.value = {};
      dynamicTags.value = [];
      return;
    }
    formbase.value = res.rows[0];
    var d = res.rows[0]?.chronicDiseases.split(",");
    dynamicTags.value = d;
    if (res.rows[0]?.elderAbilities.length > 0) {
      JSON.parse(res.rows[0]?.elderAbilities).forEach((item) => {
        if (item.type == "进食") {
          radioValue1.value = item.value;
        } else if (item.type == "洗澡") {
          radioValue2.value = item.value;
        } else if (item.type == "修饰") {
          radioValue3.value = item.value;
        } else if (item.type == "穿衣") {
          radioValue4.value = item.value;
        } else if (item.type == "如厕，排泄") {
          radioValue5.value = item.value;
        } else if (item.type == "移动") {
          radioValue6.value = item.value;
        } else if (item.type == "认知能力") {
          radioValue7.value = item.value;
        } else if (item.type == "情绪能力") {
          radioValue8.value = item.value;
        } else if (item.type == "视觉能力") {
          radioValue9.value = item.value;
        } else if (item.type == "听力") {
          radioValue10.value = item.value;
        }
        console.log(item, "item");
      });
    }
  });
  getPage2List();
}

const handleClose = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value.inputs.focus();
  });
};

const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value);
  }
  inputVisible.value = false;
  inputValue.value = "";
};

// 取消按钮
function cancel() {
  //open.value = false;
  showOrAddEdit.value = "01";
  reset();
}

// 表单重置
function reset() {
  formevent.value = {
    id: null,
    elderId: null,
    eventDate: null,
    name: null,
    details: null,
    location: null,
    medicationStatus: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("elderHealthEventRef");
}

function openList1(type) {
  open.value = true;
  loading.value = true;
  showOrAddEdit.value = "01";
  if (type == 1) {
    title.value = "既往病史";
    //record_type
    formevent.value.recordType = "既往病史";
  } else if (type == 2) {
    title.value = "手术史";
    formevent.value.recordType = "手术史";
  } else if (type == 3) {
    title.value = "长期用药史";
    formevent.value.recordType = "长期用药史";
  }
  openDrawGetDataList();
}

function openDrawGetDataList() {
  queryParams.value.recordType = title.value;
  queryParams.value.elderId = props.elderId;
  listElderHealthEvent(queryParams.value).then((response) => {
    elderHealthEventList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function handleAdd() {
  showOrAddEdit.value = "02";
}
/** 既往病史、手术史、用药史提交按钮 */
function submitFormEvent() {
  proxy.$refs["elderHealthEventRef"].validate((valid) => {
    if (valid) {
      formevent.value.elderId = props.elderId;
      formevent.value.recordType = title.value;
      if (formevent.value.id != null) {
        updateElderHealthEvent(formevent.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          //open.value = false;
          openDrawGetDataList();
          reset();
        });
      } else {
        addElderHealthEvent(formevent.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          //open.value = false;
          openDrawGetDataList();
          reset();
        });
      }
    }
  });
}

onMounted(() => {
  nextTick(() => {
    initCharts();
  });
});

//老人附属信息提交
function submitFormHealthyProfile() {
  proxy.$refs["telderinfoRef"].validate((valid) => {
    if (valid) {
      formbase.value.elderId = props.elderId;
      formbase.value.chronicDiseases = dynamicTags.value.join(",");
      formbase.value.elderAbilities = JSON.stringify([
        {
          type: "进食",
          value: radioValue1.value,
        },
        {
          type: "洗澡",
          value: radioValue2.value,
        },
        {
          type: "修饰",
          value: radioValue3.value,
        },
        {
          type: "穿衣",
          value: radioValue4.value,
        },
        {
          type: "如厕，排泄",
          value: radioValue5.value,
        },
        {
          type: "移动",
          value: radioValue6.value,
        },
        {
          type: "认知能力",
          value: radioValue7.value,
        },
        {
          type: "情绪能力",
          value: radioValue8.value,
        },
        {
          type: "视觉能力",
          value: radioValue9.value,
        },
        {
          type: "听力",
          value: radioValue10.value,
        },
      ]);
      if (formbase.value.id != null) {
        updateElderHealthProfile(formbase.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
        });
      } else {
        addElderHealthProfile(formbase.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
        });
      }
    }
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  open.value = true;
  showOrAddEdit.value = "02";
  const _id = row.id || ids.value;
  getElderHealthEvent(_id).then((response) => {
    formevent.value = response.data;
    open.value = true;
    title.value = "修改";
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除老人健康事件记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delElderHealthEvent(_ids);
    })
    .then(() => {
      openDrawGetDataList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
function handleCloseDraws() {
  open.value = false;
  reset();
  elderHealthEventList.value = [];
  queryParams.value = {};
  getHealthValue();
}
</script>

<style scoped lang="scss">
.cardDetailTop {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.cardDetailcenter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.cardDetailTopDiv {
  width: 120px;
  height: 40px;

  text-align: center;
  margin-right: 5px;
  font-size: 16px;
  color: #999;
  font-weight: 600;
}
.cardDetailTopDivSelect {
  border-bottom: 2px solid rgb(64, 158, 255);
}
.healthyTitle {
  margin: 10px 0px;
  font-size: 16px;
  font-weight: 600;
  color: #999;
}
.healthyDivCss {
  width: 100%;
  height: 300px;
}
.timelineProcessBox {
  .timeline {
    display: flex;
    width: 95%;
    height: 80px;
    margin: 10px 0px;
    .lineitem {
      transform: translateX(50%);
      width: 25%;
    }
  }
}
:deep(.el-timeline-item__tail) {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}
:deep(.el-timeline-item__wrapper) {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}
:deep(.el-timeline-item__timestamp) {
  font-size: 14px;
}
.active {
  :deep(.el-timeline-item__node) {
    background-color: #dad8d8;
  }
  :deep(.el-timeline-item__tail) {
    border-color: #dad8d8;
  }
}
// 有active样式的下一个li
.active + li {
  :deep(.el-timeline-item__node) {
    background-color: #dad8d8;
  }
}
.historyCss {
  color: #606266;
  font-weight: 600;
  font-size: 14px;
}
.historyDiv {
  width: 100%;
  height: 420px;
  margin-left: 50px;
  padding-top: 10px;
}
.subtitleCss {
  font-size: 18px;
  color: rgb(64, 158, 225);
  font-weight: 600;
}

body {
  background-color: #f0f2f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #2c3e50;
}

.container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 32px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.header:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-cards {
  margin-bottom: 32px;
}

.stats-cards .el-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
}

.stats-cards .el-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

body {
  font-family: "Arial", sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f7fa;
  color: #333;
}
.dashboard {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 16px;
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 18px;
}
.card-header img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.chart-container {
  width: 100%;
  height: 300px;
}
.value-display {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}
.unit {
  font-size: 16px;
  color: #666;
  margin-left: 4px;
}
@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
  }
}

/* 基本表格样式 */
table {
  border-collapse: collapse; /* 合并表格边框 */
  width: 100%; /* 表格宽度占满父容器 */
  font-family: Arial, sans-serif; /* 字体 */
}

/* 表格表头样式 */
th {
  background-color: #f2f2f2; /* 表头背景颜色 */
  text-align: left; /* 表头文字左对齐 */
  padding: 8px 20px; /* 内边距 */
  //border: 1px solid #ddd; /* 边框 */
}

/* 表格行样式 */
tr {
  border-bottom: 1px solid #ddd; /* 每行底部边框 */
}

/* 表格单元格样式 */
td {
  padding: 8px 20px; /* 内边距 */
  //sborder: 1px solid #ddd; /* 边框 */
}

/* 鼠标悬停在表格行上的样式 */
tr:hover {
  background-color: #f5f5f5; /* 鼠标悬停时的背景颜色 */
}

/* 隔行变色样式 */
tr:nth-child(even) {
  background-color: #f9f9f9; /* 偶数行背景颜色 */
}
</style>
