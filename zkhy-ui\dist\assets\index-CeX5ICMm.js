import{_ as ie,B as ue,d as pe,r as p,C as ce,N as _e,F as fe,aZ as he,e as m,I as O,c as L,o as h,J as I,i as g,f as s,O as P,l,h as a,m as $,K as ge,L as we,j as y,n as b,t as ve,k as C,D as ye,v as be,x as ke}from"./index-B0qHf98Y.js";import{l as Ve,d as Ne,u as Ie,a as xe}from"./assessmentRecord-4xWX4TZA.js";import{l as De}from"./tassessmentForm-B23DRaU7.js";import{d as Se}from"./paramUtil-DJB1oWef.js";import{e as q}from"./eventBus-BDDolVUG.js";const R=k=>(be("data-v-ad4a0fec"),k=k(),ke(),k),Ce={class:"app-container"},Re={class:"flexRight"},Fe=R(()=>g("span",null,"新建评估",-1)),Me=["alt","src"],Be=R(()=>g("span",null,"查看",-1)),Ue=R(()=>g("span",null,"查看",-1));const Ae={class:"dialog-footer"},Oe=ue({name:"AssessmentRecord"}),Le=Object.assign(Oe,{setup(k){const{proxy:c}=pe(),F=p([]),x=p([]),w=p(!1),V=p(!0),z=p(!0),M=p([]),E=p(!0),K=p(!0),D=p(0),T=p(""),{sys_user_sex:Y,assessment_manager:j}=c.useDict("sys_user_sex","assessment_manager"),Q=ce({form:{},queryParams:{pageNum:1,pageSize:10,elderId:null,assessmentFormId:null,assessmentOrgName:null,assessmentMethod:null,assessmentDate:[]},rules:{elderId:[{required:!0,message:"关联的老人ID不能为空",trigger:"blur"}],assessmentFormId:[{required:!0,message:"评估表单id不能为空",trigger:"blur"}]}}),{queryParams:o,form:r,rules:J}=_e(Q);function _(){V.value=!0;const n={...o.value};Se(n,o,["assessmentDate"]),delete n.assessmentDate,Ve(n).then(t=>{console.log(t,"res"),t.rows&&t.rows.length>0&&t.rows.forEach(d=>{d.assessmentScores&&d.assessmentScores.length>0&&(d.assessorName=d.assessmentScores[0].assessorName,d.assessorCode=d.assessmentScores[0].assessorCode)}),F.value=t.rows,D.value=t.total,V.value=!1})}function Z(){V.value=!0,De({pageNum:1,pageSize:1e4,status:"1"}).then(n=>{x.value=[],n.rows&&n.rows.length>0&&(x.value=n.rows)})}function G(){w.value=!1,H()}function H(){r.value={id:null,elderId:null,assessmentFormId:null,assessmentOrgName:null,assessmentMethod:null,remark:null,createTime:null,updateTime:null,createBy:null,updateBy:null},c.resetForm("assessmentRecordRef")}function N(){o.value.pageNum=1,_()}function W(){c.resetForm("queryRef"),N()}function X(n){M.value=n.map(t=>t.id),E.value=n.length!=1,K.value=!n.length}function ee(){c.$refs.assessmentRecordRef.validate(n=>{n&&(r.value.id!=null?Ie(r.value).then(t=>{c.$modal.msgSuccess("修改成功"),w.value=!1,_()}):xe(r.value).then(t=>{c.$modal.msgSuccess("新增成功"),w.value=!1,_()}))})}function se(n){const t=n.id||M.value;c.$modal.confirm('是否确认删除评估信息记录编号为"'+t+'"的数据项？').then(function(){return Ne(t)}).then(()=>{_(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}return fe(()=>{q.on("uploadListEvent",n=>{console.log(n.data),_()})}),he(()=>{q.off("customEvent")}),_(),Z(),(n,t)=>{const d=m("el-input"),u=m("el-form-item"),te=m("el-option"),le=m("el-select"),ae=m("el-date-picker"),B=m("el-form"),f=m("el-button"),S=m("router-link"),i=m("el-table-column"),U=m("dict-tag"),oe=m("el-table"),ne=m("pagination"),re=m("el-dialog"),de=O("hasPermi"),me=O("loading");return h(),L("div",Ce,[I(s(B,{ref:"queryRef",inline:!0,model:l(o),"label-width":"68px"},{default:a(()=>[s(u,{label:"老人姓名",prop:"elderName"},{default:a(()=>[s(d,{modelValue:l(o).elderName,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).elderName=e),clearable:"",placeholder:"请输入老人姓名",style:{width:"240px"},onKeyup:$(N,["enter"])},null,8,["modelValue"])]),_:1}),s(u,{label:"评估表单",prop:"assessmentFormId"},{default:a(()=>[s(le,{modelValue:l(o).assessmentFormId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).assessmentFormId=e),clearable:"",placeholder:"请选择评估表单",style:{width:"240px"}},{default:a(()=>[(h(!0),L(ge,null,we(l(x),e=>(h(),y(te,{key:e.id,label:e.formName+" - [ "+e.version+" ]",value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(u,{label:"评估师",prop:"assessorName"},{default:a(()=>[s(d,{modelValue:l(o).assessorName,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).assessorName=e),clearable:"",placeholder:"请输入评估师",style:{width:"240px"},onKeyup:$(N,["enter"])},null,8,["modelValue"])]),_:1}),s(u,{label:"评估时间",prop:"assessmentDate",style:{width:"308px"}},{default:a(()=>[s(ae,{modelValue:l(o).assessmentDate,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).assessmentDate=e),clearable:"","end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",type:"daterange",style:{width:"240px"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),s(u)]),_:1},8,["model"]),[[P,l(z)]]),g("div",Re,[s(f,{icon:"Search",type:"primary",onClick:N},{default:a(()=>[b("搜索")]),_:1}),s(f,{icon:"Refresh",onClick:W},{default:a(()=>[b("重置")]),_:1}),s(f,{type:"primary",plain:"",icon:"Plus"},{default:a(()=>[s(S,{to:"/eldercheckin/showAssessmentDetails/add/0/add"},{default:a(()=>[Fe]),_:1})]),_:1})]),I((h(),y(oe,{data:l(F),border:"",stripe:"",onSelectionChange:X},{default:a(()=>[s(i,{align:"center",label:"序号",type:"index",width:"60"},{default:a(e=>[g("span",null,ve((l(o).pageNum-1)*l(o).pageSize+e.$index+1),1)]),_:1}),s(i,{align:"center",label:"头像","min-width":"80px",prop:"elderInfo.avatar"},{default:a(e=>{var v,A;return[g("img",{alt:((v=e.row.elderInfo)==null?void 0:v.elderName)+"头像",src:(A=e.row.elderInfo)==null?void 0:A.avatar,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,Me)]}),_:1}),s(i,{align:"center",label:"姓名","min-width":"90",prop:"elderInfo.elderName"}),s(i,{align:"center",label:"性别","min-width":"80",prop:"elderInfo.gender"},{default:a(e=>{var v;return[s(U,{options:l(Y),value:(v=e.row.elderInfo)==null?void 0:v.gender},null,8,["options","value"])]}),_:1}),s(i,{align:"center",label:"年龄","min-width":"80",prop:"elderInfo.age"}),s(i,{align:"center",label:"评估时间",width:"120",prop:"assessmentScores[0].assessmentTime"}),s(i,{align:"center",label:"评估表单","min-width":"200",prop:"assessmentForm.formName"}),s(i,{align:"center",label:"评估机构","min-width":"240",prop:"assessmentOrgName"}),s(i,{align:"center",label:"评估师","min-width":"90",prop:"assessorName"}),s(i,{align:"center",label:"评估方式",prop:"assessmentMethod"},{default:a(e=>[s(U,{options:l(j),value:e.row.assessmentMethod},null,8,["options","value"])]),_:1}),s(i,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作","min-width":"160"},{default:a(e=>[s(f,{icon:"Search",link:"",type:"primary"},{default:a(()=>[e.row.assessmentMethod=="02"?(h(),y(S,{key:0,to:"/eldercheckin/showAssessmentDetails/detail/"+e.row.id+"/show"},{default:a(()=>[Be]),_:2},1032,["to"])):C("",!0),e.row.assessmentMethod=="01"?(h(),y(S,{key:1,to:"/eldercheckin/showAssessmentDetails/detailOnLine/"+e.row.id+"/show"},{default:a(()=>[Ue]),_:2},1032,["to"])):C("",!0)]),_:2},1024),C("",!0),I((h(),y(f,{icon:"Delete",link:"",type:"primary",onClick:v=>se(e.row)},{default:a(()=>[b("删除")]),_:2},1032,["onClick"])),[[de,["assessment:assessmentRecord:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,l(V)]]),I(s(ne,{limit:l(o).pageSize,"onUpdate:limit":t[4]||(t[4]=e=>l(o).pageSize=e),page:l(o).pageNum,"onUpdate:page":t[5]||(t[5]=e=>l(o).pageNum=e),total:l(D),onPagination:_},null,8,["limit","page","total"]),[[P,l(D)>0]]),s(re,{modelValue:l(w),"onUpdate:modelValue":t[11]||(t[11]=e=>ye(w)?w.value=e:null),title:l(T),"append-to-body":"",width:"500px"},{footer:a(()=>[g("div",Ae,[s(f,{type:"primary",onClick:ee},{default:a(()=>[b("确 定")]),_:1}),s(f,{onClick:G},{default:a(()=>[b("取 消")]),_:1})])]),default:a(()=>[s(B,{ref:"assessmentRecordRef",model:l(r),rules:l(J),"label-width":"80px"},{default:a(()=>[s(u,{label:"关联的老人ID",prop:"elderId"},{default:a(()=>[s(d,{modelValue:l(r).elderId,"onUpdate:modelValue":t[6]||(t[6]=e=>l(r).elderId=e),placeholder:"请输入关联的老人ID"},null,8,["modelValue"])]),_:1}),s(u,{label:"评估表单id",prop:"assessmentFormId"},{default:a(()=>[s(d,{modelValue:l(r).assessmentFormId,"onUpdate:modelValue":t[7]||(t[7]=e=>l(r).assessmentFormId=e),placeholder:"请输入评估表单id"},null,8,["modelValue"])]),_:1}),s(u,{label:"评估机构名称",prop:"assessmentOrgName"},{default:a(()=>[s(d,{modelValue:l(r).assessmentOrgName,"onUpdate:modelValue":t[8]||(t[8]=e=>l(r).assessmentOrgName=e),placeholder:"请输入评估机构名称"},null,8,["modelValue"])]),_:1}),s(u,{label:"评估方式(如: 在线评估, 纸质评估)",prop:"assessmentMethod"},{default:a(()=>[s(d,{modelValue:l(r).assessmentMethod,"onUpdate:modelValue":t[9]||(t[9]=e=>l(r).assessmentMethod=e),placeholder:"请输入评估方式(如: 在线评估, 纸质评估)"},null,8,["modelValue"])]),_:1}),s(u,{label:"备注",prop:"remark"},{default:a(()=>[s(d,{modelValue:l(r).remark,"onUpdate:modelValue":t[10]||(t[10]=e=>l(r).remark=e),placeholder:"请输入内容",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}}),Ke=ie(Le,[["__scopeId","data-v-ad4a0fec"]]);export{Ke as default};
