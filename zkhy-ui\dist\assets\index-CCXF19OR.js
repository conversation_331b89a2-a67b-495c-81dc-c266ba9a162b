import{g as L}from"./leave-Dd4WELmg.js";import{_ as P,d as Q,r as i,e as n,c as T,o as j,f as e,h as o,i as F,l as a,n as _,D as O}from"./index-B0qHf98Y.js";const $={class:"pc-container"},q={class:"paginationBox"},A={__name:"index",setup(G,{expose:C,emit:x}){const{proxy:v}=Q(),{sys_user_sex:w}=v.useDict("sys_user_sex"),N=x,p=i(!1),l=i({pageNum:1,pageSize:20,elderName:"",elderCode:""}),g=i(0),f=i([]),u=()=>{L({...l.value}).then(d=>{f.value=d.rows,g.value=d.total})},S=()=>{l.value.pageNum=1,u()},V=d=>{l.value.pageSize=d,u()},z=d=>{l.value.pageNum=d,u()};function y(){l.value={elderName:null,elderCode:null,pageNum:1,pageSize:20},u()}const b=d=>{p.value=!1,N("selectLerder",d)};return C({openElderSelect:()=>{p.value=!0,u()}}),(d,r)=>{const h=n("el-input"),c=n("el-form-item"),m=n("el-button"),k=n("el-row"),E=n("el-form"),s=n("el-table-column"),D=n("dict-tag-span"),U=n("el-table"),B=n("el-scrollbar"),R=n("el-pagination"),I=n("el-dialog");return j(),T("div",$,[e(I,{modelValue:a(p),"onUpdate:modelValue":r[4]||(r[4]=t=>O(p)?p.value=t:null),class:"elder-dialog-custom",title:"选择老人",width:"65%"},{default:o(()=>[e(E,{model:a(l),ref:"userRef","label-width":"80px"},{default:o(()=>[e(k,null,{default:o(()=>[e(c,{label:"姓名",prop:"elderName"},{default:o(()=>[e(h,{modelValue:a(l).elderName,"onUpdate:modelValue":r[0]||(r[0]=t=>a(l).elderName=t),placeholder:"请输入姓名",maxlength:"30"},null,8,["modelValue"])]),_:1}),e(c,{label:"老人编号",prop:"elderCode"},{default:o(()=>[e(h,{modelValue:a(l).elderCode,"onUpdate:modelValue":r[1]||(r[1]=t=>a(l).elderCode=t),placeholder:"请输入老人编号",maxlength:"30"},null,8,["modelValue"])]),_:1}),e(c,null,{default:o(()=>[e(m,{type:"primary",icon:"Search",onClick:S},{default:o(()=>[_("搜索")]),_:1}),e(m,{icon:"Refresh",onClick:y},{default:o(()=>[_("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e(B,{"max-height":"500px"},{default:o(()=>[e(U,{data:a(f),onRowDblclick:b},{default:o(()=>[e(s,{type:"index",label:"序号",width:"120"}),e(s,{label:"老人编号",prop:"elderCode"}),e(s,{label:"姓名",prop:"elderName",width:"120"}),e(s,{label:"身份证号",prop:"idCard",width:"200"}),e(s,{label:"年龄",prop:"age",width:"80"}),e(s,{label:"性别",prop:"gender",width:"80"},{default:o(t=>[e(D,{options:a(w),value:t.row.gender},null,8,["options","value"])]),_:1}),e(s,{label:"联系电话",prop:"phone",width:"150"}),e(s,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:o(t=>[e(m,{type:"primary",onClick:J=>b(t.row)},{default:o(()=>[_("选择")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),F("div",q,[e(R,{background:"","current-page":a(l).pageNum,"onUpdate:currentPage":r[2]||(r[2]=t=>a(l).pageNum=t),"page-size":a(l).pageSize,"onUpdate:pageSize":r[3]||(r[3]=t=>a(l).pageSize=t),"page-sizes":[10,20,30,40],total:a(g),layout:"total, sizes, prev, pager, next, jumper",onSizeChange:V,onCurrentChange:z},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"])])}}},W=P(A,[["__scopeId","data-v-97218b5e"]]);export{W as default};
