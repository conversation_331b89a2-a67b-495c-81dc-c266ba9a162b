import{B as O,d as Q,r as c,a as E,C as J,N as G,e as s,I as H,c as W,o as N,J as b,f as e,O as P,l,h as o,m as S,n as f,j as X,i as U,t as Z,D as ee}from"./index-B0qHf98Y.js";import{b as le,u as te,a as ae}from"./tWarehouseInventoryCheck-Dhh0m5wG.js";const oe={class:"app-container"},ne={class:"dialog-footer"},re=O({name:"InventoryCheck"}),ie=Object.assign(re,{setup(ue){const{proxy:k}=Q(),V=c([]),p=c(!1),v=c(!0),x=c(!0);c([]),c(!0),c(!0);const y=c(0),R=c(""),w=E(),Y=J({form:{},queryParams:{pageNum:1,pageSize:10,checkNo:null,checkPerson:null,checkDate:null,status:null},rules:{}}),{queryParams:n,form:r,rules:B}=G(Y);function h(){v.value=!0,le(n.value).then(u=>{V.value=u.rows,y.value=u.total,v.value=!1})}function I(){p.value=!1,$()}function $(){r.value={id:null,checkNo:null,checkPerson:null,checkDate:null,status:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null},k.resetForm("inventoryCheckRef")}function g(){n.value.pageNum=1,h()}function q(){k.resetForm("queryRef"),g()}function M(u){w.push("/wmsstocktaking/AddStocktaking/show/0/"+u)}function T(u,t){const d=u.id;w.push("/wmsstocktaking/AddStocktaking/show/"+d+"/"+t)}function j(){k.$refs.inventoryCheckRef.validate(u=>{u&&(r.value.id!=null?te(r.value).then(t=>{k.$modal.msgSuccess("修改成功"),p.value=!1,h()}):ae(r.value).then(t=>{k.$modal.msgSuccess("新增成功"),p.value=!1,h()}))})}return h(),(u,t)=>{const d=s("el-input"),i=s("el-form-item"),C=s("el-date-picker"),D=s("el-form"),m=s("el-button"),z=s("el-row"),_=s("el-table-column"),A=s("el-table"),F=s("pagination"),K=s("el-dialog"),L=H("loading");return N(),W("div",oe,[b(e(D,{model:l(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[e(i,{label:"盘点单号",prop:"checkNo"},{default:o(()=>[e(d,{modelValue:l(n).checkNo,"onUpdate:modelValue":t[0]||(t[0]=a=>l(n).checkNo=a),placeholder:"请输入盘点单号",clearable:"",onKeyup:S(g,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"盘点日期",prop:"checkDate"},{default:o(()=>[e(C,{clearable:"",modelValue:l(n).checkDate,"onUpdate:modelValue":t[1]||(t[1]=a=>l(n).checkDate=a),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择盘点日期"},null,8,["modelValue"])]),_:1}),e(i,{label:"盘点人",prop:"checkPerson"},{default:o(()=>[e(d,{modelValue:l(n).checkPerson,"onUpdate:modelValue":t[2]||(t[2]=a=>l(n).checkPerson=a),placeholder:"请输入盘点人",clearable:"",onKeyup:S(g,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),[[P,l(x)]]),e(z,{gutter:10,class:"mb8",justify:"end"},{default:o(()=>[e(m,{type:"primary",icon:"Search",onClick:g},{default:o(()=>[f("搜索")]),_:1}),e(m,{icon:"Refresh",onClick:q},{default:o(()=>[f("重置")]),_:1}),e(m,{type:"primary",plain:"",icon:"Plus",onClick:t[3]||(t[3]=a=>M("add"))},{default:o(()=>[f("新增盘点")]),_:1})]),_:1}),b((N(),X(A,{data:l(V),border:"",stripe:""},{default:o(()=>[e(_,{type:"index",label:"序号",width:"85",align:"center"}),e(_,{label:"盘点单号",align:"center",prop:"checkNo"}),e(_,{label:"盘点日期",align:"center",prop:"checkDate",width:"280"},{default:o(a=>[U("span",null,Z(u.parseTime(a.row.checkDate,"{y}-{m}-{d}")),1)]),_:1}),e(_,{label:"盘点人",align:"center",prop:"checkPerson"}),e(_,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"220"},{default:o(a=>[e(m,{link:"",type:"primary",icon:"Search",onClick:se=>T(a.row,"show")},{default:o(()=>[f("详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[L,l(v)]]),b(e(F,{total:l(y),page:l(n).pageNum,"onUpdate:page":t[4]||(t[4]=a=>l(n).pageNum=a),limit:l(n).pageSize,"onUpdate:limit":t[5]||(t[5]=a=>l(n).pageSize=a),onPagination:h},null,8,["total","page","limit"]),[[P,l(y)>0]]),e(K,{title:l(R),modelValue:l(p),"onUpdate:modelValue":t[10]||(t[10]=a=>ee(p)?p.value=a:null),width:"500px","append-to-body":""},{footer:o(()=>[U("div",ne,[e(m,{type:"primary",onClick:j},{default:o(()=>[f("确 定")]),_:1}),e(m,{onClick:I},{default:o(()=>[f("取 消")]),_:1})])]),default:o(()=>[e(D,{ref:"inventoryCheckRef",model:l(r),rules:l(B),"label-width":"80px"},{default:o(()=>[e(i,{label:"盘点单号",prop:"checkNo"},{default:o(()=>[e(d,{modelValue:l(r).checkNo,"onUpdate:modelValue":t[6]||(t[6]=a=>l(r).checkNo=a),placeholder:"请输入盘点单号"},null,8,["modelValue"])]),_:1}),e(i,{label:"盘点人",prop:"checkPerson"},{default:o(()=>[e(d,{modelValue:l(r).checkPerson,"onUpdate:modelValue":t[7]||(t[7]=a=>l(r).checkPerson=a),placeholder:"请输入盘点人"},null,8,["modelValue"])]),_:1}),e(i,{label:"盘点日期",prop:"checkDate"},{default:o(()=>[e(C,{clearable:"",modelValue:l(r).checkDate,"onUpdate:modelValue":t[8]||(t[8]=a=>l(r).checkDate=a),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择盘点日期"},null,8,["modelValue"])]),_:1}),e(i,{label:"备注",prop:"remark"},{default:o(()=>[e(d,{modelValue:l(r).remark,"onUpdate:modelValue":t[9]||(t[9]=a=>l(r).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ie as default};
