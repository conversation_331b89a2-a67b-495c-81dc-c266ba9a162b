import{_ as Te,d as ke,r as i,w as Z,C as ee,N as Be,e as p,j as g,o as u,h as n,f as o,c as v,k as T,l as s,i as d,t as b,K as I,L as C,Q as Re,J as Pe,n as S,O as Ee,v as Se,x as je,M as xe,G as le}from"./index-B0qHf98Y.js";import{g as Ue}from"./user-u7DySmj3.js";import{a as Ae,l as Fe,b as Oe,d as Le}from"./tProcessApprovalRecord-OlTXqvFr.js";import{l as Ge}from"./tLiveBed-B9bJPM9s.js";import{l as qe}from"./tLiveRoom-DmSXfHxo.js";import{l as Me}from"./telderinfo-BSpoeVyZ.js";import{a as ze,b as Je,i as Qe}from"./yjj-DmX1NTQH.js";import{l as ae,r as Ye,u as He}from"./telderAttachement-C4ARfNBy.js";const _=k=>(Se("data-v-5fa1404f"),k=k(),je(),k),$e={class:"section"},Ke=_(()=>d("div",{class:"section-title"},"老人信息",-1)),We={class:"tbcss"},Xe=_(()=>d("th",{class:"tbTr"},"经  办 人",-1)),Ze={class:"tbTrVal"},el=_(()=>d("th",{class:"tbTr"},"申请时间",-1)),ll={class:"tbTrVal"},al=_(()=>d("th",{class:"tbTr"},"老人姓名",-1)),tl={class:"tbTrVal"},ol=_(()=>d("th",{class:"tbTr"},"床位编号",-1)),sl={class:"tbTrVal"},nl=_(()=>d("th",{class:"tbTr"},"老人编号",-1)),dl={class:"tbTrVal"},rl=_(()=>d("th",{class:"tbTr"},"申请时间",-1)),ul={class:"tbTrVal"},il=_(()=>d("th",{class:"tbTr"},"联系电话",-1)),pl={class:"tbTrVal"},cl=_(()=>d("th",{class:"tbTr"},"年       龄",-1)),ml={class:"tbTrVal"},gl=["src"],fl={class:"section"},vl=_(()=>d("div",{class:"section-title"},"床位调整信息",-1)),_l={key:0,class:"section"},hl=_(()=>d("div",{class:"section-title"},"审批信息",-1)),bl={class:"audit-flow"},Vl={class:"audit-step-content"},Nl={class:"audit-step-info"},yl={class:"audit-step-time"},Il={class:"audit-step-name"},Cl={key:0,class:"audit-step-line"},wl={__name:"bedadjustForm",props:{isView:{type:Boolean,default:!1},formData:{type:Object,default:()=>null},businessId:{type:String,default:null}},emits:["close"],setup(k,{emit:te}){const{proxy:oe}=ke(),q=i([]),M=i([]),z=i([]),J=i([]),B=i([]),j=i([]),R=i([]),se=i(),A=i([]),{sys_yes_no:Q,sys_user_sex:Y,bed_adjust_type:ne}=oe.useDict("sys_yes_no","sys_user_sex","bed_adjust_type"),r=k,H=te,P=i(!0),F=i(null),x=i(!1),$=i(),O=i(0);i();const N=i({});Z(()=>r.formData,e=>{console.log(r.formData,e,"-----------"),e&&(N.value.name=e.elderName,N.value.no=e.elderNo,N.value.age=e.age||"",N.value.gender=e.gender||"",N.value.phone=e.phone||"",N.value.bedNo=e.oldBed,N.value.agent=e.agent,N.value.applyTime=e.applyTime)}),console.log(r.formData,"formData");const de=ee({type:"",newBed:[],change:"",date:"",feeDiff:"",reason:"",contract:""});function re(e){return e.approvalStatus=="APPROVED"||e.approvalStatus=="PENDING"||e.approvalStatus=="REJECTED"}const ue=ee({adjustForm:{},adjustRules:{targetBuildingName:[{required:!0,message:"请选择",trigger:"change"}],adjustmentType:[{required:!0,message:"请选择调整类型",trigger:"change"}],newBed:[{required:!0,message:"请选择调整后床位号",trigger:"change"}],isPriceChanged:[{required:!0,message:"请选择床位费是否变化",trigger:"change"}],adjustmentDate:[{required:!0,message:"请选择调整日期",trigger:"change"}],priceDifference:[{required:!0,message:"请输入床位费差额",trigger:"blur"}],changeReason:[{validator:(e,t,m)=>{console.log(a.value.isContractChanged,"222"),a.value.isContractChanged=="N"?m():a.value.changeReason==null||a.value.changeReason==""?m(new Error("请输入更换原因")):m(),console.log(t,e,"-----------")},trigger:"blur"}],isContractChanged:[{required:!0,message:"请选择是否更换合同",trigger:"change"}]},auditInfo:{},elderQueryParams:{pageNum:1,pageSize:10,elderName:"",idCard:""}});i("");const{adjustForm:a,adjustRules:ie,auditInfo:Dl,elderQueryParams:h}=Be(ue),pe=["申请","审核","归档"];i(0);function ce(){if(console.log(r.isView,"isView"),!r.isView)a.value.status="PENDING";else if(r.formData){a.value=r.formData,A.value=[],Ae({businessId:r.businessId,processName:"床位调整"}).then(t=>{A.value=t.rows});let e={elderId:r.businessId,category:"room_bed_change",attachment_type:"roombed_contract_change"};ae(e).then(t=>{console.log(t,"resfile"),R.value=t.rows.map(m=>m.filePath)})}Fe().then(e=>{q.value=e.rows})}async function me(){if(F.value)try{await F.value.validate(),a.value.attachments=B.value,a.value.status="PENDING",console.log(a.value,"adjustForm.value"),Le(a.value).then(e=>{console.log(e,"addProcessd"),le.success("提交成功"),j.value.length>0&&He(j.value,e.data.id).then(t=>{}),P.value=!1,H("close")})}catch(e){e!=="cancel"&&le.error("表单验证失败，请检查必填项")}}Z(P,e=>{e||H("close")});function L(){x.value=!0,Me(h.value).then(e=>{console.log(e,"res"),$.value=e.rows,O.value=e.total})}function ge(e){a.value.targetBuildingId=e.id,a.value.targetBuildingName=e.buildingName,Oe({buildingId:e.id}).then(t=>{console.log(t,"build-----"),M.value=t.rows})}function fe(e){console.log(e,"floor111"),a.value.targetFloorId=e.id,a.value.targetFloorName=e.floorName,qe({floorId:e.id}).then(t=>{console.log(t,"floor-----"),z.value=t.rows})}function ve(e){console.log(e,"room111"),a.value.targetRoomId=e.id,a.value.targetRoomName=e.roomName,Ge({roomId:e.id}).then(t=>{console.log(t,"room-----"),J.value=t.rows})}function _e(e){console.log(e,"bed111"),a.value.targetBedId=e.id,a.value.targetBedNumber=e.bedNumber}function K(e){console.log(e,"handlerow...."),a.value=e,a.value.originalBedNumber=e.bedNumber,a.value.elderCode=e.elderCode,a.value.elderGender=e.gender,a.value.elderPhone=e.phone,a.value.elderAge=e.age,a.value.handlerName=e.phone,a.value.elderId=e.id,a.value.originalBuildingId=e.buildingId,a.value.originalBuildingName=e.buildingName,a.value.originalFloorId=e.floorId,a.value.originalFloorName=e.floorNumber,a.value.originalRoomId=e.roomId,a.value.originalRoomName=e.roomNumber,a.value.originalBedId=e.bedId,a.value.originalBedNumber=e.bedNumber,x.value=!1,a.value.adjustmentTime=xe().format("YYYY-MM-DD HH:mm:ss"),Ue().then(t=>{console.log(t,"userinfo"),a.value.handlerName=t.data.nickName,se.value=t.data})}function he(e){R.value=e.url,console.log(e,"handleGetFile---------"),e&&(Array.isArray(e)?(B.value=B.value.concat(e.map(t=>t)),j.value=j.value.concat(e.map(t=>t.ossId))):B.value.push(e)),console.log(B.value,"handleGetFile---------")}function be(e){if(e){if(e=="APPROVED"||e=="COMPLETE")return ze;if(e=="REJECTED")return Je;if(e=="PENDING")return Qe}}function Ve(e){if(console.log(e,"item----------"),e.stepOrder=="1")return e.approvalStatus=="APPROVED"?e.currentApproverName+" 发起申请":"-";if(e.stepOrder=="2")return e.approvalStatus=="REJECTED"?e.currentApproverName+" 驳回了申请":e.approvalStatus=="PENDING"?e.currentApproverName+" 待审核":e.approvalStatus=="COMPLETE"||e.approvalStatus=="APPROVED"?e.currentApproverName+" 通过了申请":"-";if(e.stepOrder=="3")return e.approvalStatus?"系统已归档":"-"}function Ne(e,t){console.log(e,"remove"),Ye(e).then(m=>{let U={elderId:r.businessId,attachment_type:"roombed_contract_change"};ae(U).then(c=>{R.value=c.rows.map(V=>V.filePath)})})}return ce(),(e,t)=>{const m=p("el-input"),U=p("dict-tag-span"),c=p("el-col"),V=p("el-row"),ye=p("el-avatar"),w=p("el-option"),D=p("el-select"),f=p("el-form-item"),Ie=p("el-date-picker"),Ce=p("ImageUpload"),W=p("el-form"),E=p("el-button"),y=p("el-table-column"),we=p("el-table"),De=p("pagination"),X=p("el-dialog");return u(),g(X,{modelValue:P.value,"onUpdate:modelValue":t[18]||(t[18]=l=>P.value=l),title:"申请床位调整",width:"70%","append-to-body":""},{footer:n(()=>[o(E,{onClick:t[12]||(t[12]=l=>P.value=!1)},{default:n(()=>[S(b(r.isView?"关闭":"取消"),1)]),_:1}),r.isView?T("",!0):(u(),g(E,{key:0,type:"primary",onClick:me},{default:n(()=>[S("提交")]),_:1}))]),default:n(()=>[o(W,{ref_key:"formRef",ref:F,model:s(a),rules:s(ie),"label-width":"110px"},{default:n(()=>[d("div",$e,[Ke,o(V,null,{default:n(()=>[o(c,{span:16},{default:n(()=>[o(V,{gutter:20},{default:n(()=>[d("table",We,[d("tr",null,[Xe,d("th",Ze,b(s(a).handlerName),1),el,d("th",ll,b(s(a).adjustmentTime),1)]),d("tr",null,[al,d("th",tl,[o(m,{modelValue:s(a).elderName,"onUpdate:modelValue":t[0]||(t[0]=l=>s(a).elderName=l),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},onClick:L,disabled:r.isView},null,8,["modelValue","disabled"])]),ol,d("th",sl,b(s(a).originalBedNumber),1)]),d("tr",null,[nl,d("th",dl,b(s(a).elderCode),1),rl,d("th",ul,[o(U,{options:s(Y),value:s(a).elderGender},null,8,["options","value"])])]),d("tr",null,[il,d("th",pl,b(s(a).elderPhone),1),cl,d("th",ml,b(s(a).elderAge),1)])]),s(a).avatar?(u(),g(c,{key:0,span:6})):T("",!0)]),_:1})]),_:1}),o(c,{span:4},{default:n(()=>[s(a).avatar?(u(),g(ye,{key:0,shape:"square",size:140,fit:"fill",src:s(a).avatar},null,8,["src"])):T("",!0)]),_:1}),r.isView?(u(),g(c,{key:0,span:4},{default:n(()=>[d("img",{src:be(s(a).status),alt:"",style:{width:"100%",height:"auto"}},null,8,gl)]),_:1})):T("",!0)]),_:1})]),d("div",fl,[vl,o(V,{gutter:16},{default:n(()=>[o(c,{span:12},{default:n(()=>[o(f,{label:"调整类型",prop:"adjustmentType"},{default:n(()=>[o(D,{modelValue:s(a).adjustmentType,"onUpdate:modelValue":t[1]||(t[1]=l=>s(a).adjustmentType=l),placeholder:"请选择",style:{width:"100%"},disabled:r.isView},{default:n(()=>[(u(!0),v(I,null,C(s(ne),l=>(u(),g(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),o(c,{span:12},{default:n(()=>[o(f,{label:"调整后床位号",prop:"targetBuildingName"},{default:n(()=>[o(D,{modelValue:s(a).targetBuildingName,"onUpdate:modelValue":t[2]||(t[2]=l=>s(a).targetBuildingName=l),placeholder:"请选择楼栋",style:{width:"120px"},disabled:r.isView,onChange:ge},{default:n(()=>[(u(!0),v(I,null,C(q.value,l=>(u(),g(w,{key:l.value,label:l.buildingName,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),o(D,{modelValue:s(a).targetFloorName,"onUpdate:modelValue":t[3]||(t[3]=l=>s(a).targetFloorName=l),placeholder:"请选择楼层",style:{width:"120px"},disabled:r.isView,onChange:fe},{default:n(()=>[(u(!0),v(I,null,C(M.value,l=>(u(),g(w,{key:l.value,label:l.floorName,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),o(D,{modelValue:s(a).targetRoomName,"onUpdate:modelValue":t[4]||(t[4]=l=>s(a).targetRoomName=l),placeholder:"请选择房间",style:{width:"120px"},disabled:r.isView,onChange:ve},{default:n(()=>[(u(!0),v(I,null,C(z.value,l=>(u(),g(w,{key:l.value,label:l.roomName,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),o(D,{modelValue:s(a).targetBedNumber,"onUpdate:modelValue":t[5]||(t[5]=l=>s(a).targetBedNumber=l),placeholder:"请选择床位",style:{width:"120px"},disabled:r.isView,onChange:_e},{default:n(()=>[(u(!0),v(I,null,C(J.value,l=>(u(),g(w,{key:l.value,label:l.bedNumber,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),o(V,{gutter:16},{default:n(()=>[o(c,{span:12},{default:n(()=>[o(f,{label:"调整日期",prop:"adjustmentDate"},{default:n(()=>[o(Ie,{modelValue:s(a).adjustmentDate,"onUpdate:modelValue":t[6]||(t[6]=l=>s(a).adjustmentDate=l),type:"date",placeholder:"请选择",style:{width:"100%"},disabled:r.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1}),o(c,{span:12},{default:n(()=>[o(f,{label:"床位费变化",prop:"isPriceChanged"},{default:n(()=>[o(D,{modelValue:s(a).isPriceChanged,"onUpdate:modelValue":t[7]||(t[7]=l=>s(a).isPriceChanged=l),placeholder:"请选择",style:{width:"100%"},disabled:r.isView},{default:n(()=>[(u(!0),v(I,null,C(s(Q),l=>(u(),g(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),o(V,{gutter:16},{default:n(()=>[o(c,{span:12},{default:n(()=>[o(f,{label:"床位费差额",prop:"priceDifference"},{default:n(()=>[o(m,{modelValue:s(a).priceDifference,"onUpdate:modelValue":t[8]||(t[8]=l=>s(a).priceDifference=l),placeholder:"请输入",disabled:r.isView||s(de).change==="否"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),o(c,{span:12},{default:n(()=>[o(f,{label:"更换合同",prop:"isContractChanged"},{default:n(()=>[o(D,{modelValue:s(a).isContractChanged,"onUpdate:modelValue":t[9]||(t[9]=l=>s(a).isContractChanged=l),placeholder:"请选择",style:{width:"100%"},disabled:r.isView},{default:n(()=>[(u(!0),v(I,null,C(s(Q),l=>(u(),g(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),o(c,{span:24},{default:n(()=>[o(f,{label:"更换原因",prop:"changeReason"},{default:n(()=>[o(m,{modelValue:s(a).changeReason,"onUpdate:modelValue":t[10]||(t[10]=l=>s(a).changeReason=l),type:"textarea",rows:4,placeholder:"请输入更换原因",disabled:r.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),o(V,{gutter:16},{default:n(()=>[o(c,{span:12},{default:n(()=>[o(f,{label:"上传附件"},{default:n(()=>[o(Ce,{modelValue:R.value,"onUpdate:modelValue":t[11]||(t[11]=l=>R.value=l),fileData:{category:"room_bed_change",attachmentType:"roombed_contract_change"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!0,limit:10,onRemoveAtt:Ne,disabled:r.isView,onSubmitParentValue:he},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"]),r.isView?(u(),v("div",_l,[hl,d("div",bl,[(u(!0),v(I,null,C(A.value,(l,G)=>(u(),v("div",{class:"audit-step",key:G},[d("div",Vl,[d("div",{class:Re(["audit-step-title",{active:re(l)}])},b(l.stepName),3),d("div",Nl,[d("div",yl,b(l.approvalTime||"-"),1),d("div",Il,b(Ve(l)),1)])]),G<pe.length-1?(u(),v("div",Cl)):T("",!0)]))),128))])])):T("",!0),o(X,{modelValue:x.value,"onUpdate:modelValue":t[17]||(t[17]=l=>x.value=l),class:"elder-dialog-custom",title:"选择老人",width:"60%"},{default:n(()=>[o(W,{model:s(h),rules:e.rules,ref:"userRef","label-width":"80px"},{default:n(()=>[o(V,null,{default:n(()=>[o(f,{label:"姓名",prop:"elderName"},{default:n(()=>[o(m,{modelValue:s(h).elderName,"onUpdate:modelValue":t[13]||(t[13]=l=>s(h).elderName=l),placeholder:"请输入姓名",maxlength:"30",clearable:""},null,8,["modelValue"])]),_:1}),o(f,{label:"老人编号",prop:"elderCode"},{default:n(()=>[o(m,{modelValue:s(h).elderCode,"onUpdate:modelValue":t[14]||(t[14]=l=>s(h).elderCode=l),placeholder:"请输入老人编号",maxlength:"30",clearable:""},null,8,["modelValue"])]),_:1}),o(f,null,{default:n(()=>[o(E,{type:"primary",icon:"Search",onClick:L},{default:n(()=>[S("搜索")]),_:1}),o(E,{icon:"Refresh",onClick:e.resetElderQuery},{default:n(()=>[S("重置")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model","rules"]),o(we,{data:$.value,onRowDblclick:K},{default:n(()=>[o(y,{type:"index",label:"序号",width:"120"}),o(y,{label:"老人编号",prop:"elderCode"}),o(y,{label:"姓名",prop:"elderName",width:"120"}),o(y,{label:"老人身份证",prop:"idCard",width:"200"}),o(y,{label:"年龄",prop:"age",width:"80"}),o(y,{label:"性别",prop:"gender",width:"80"},{default:n(l=>[o(U,{options:s(Y),value:l.row.gender},null,8,["options","value"])]),_:1}),o(y,{label:"联系电话",prop:"phone",width:"150"}),o(y,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(l=>[o(E,{type:"primary",onClick:G=>K(l.row)},{default:n(()=>[S("选择")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),Pe(o(De,{total:O.value,page:s(h).pageNum,"onUpdate:page":t[15]||(t[15]=l=>s(h).pageNum=l),limit:s(h).pageSize,"onUpdate:limit":t[16]||(t[16]=l=>s(h).pageSize=l),onPagination:L},null,8,["total","page","limit"]),[[Ee,O.value>0]])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}},xl=Te(wl,[["__scopeId","data-v-5fa1404f"]]);export{xl as default};
