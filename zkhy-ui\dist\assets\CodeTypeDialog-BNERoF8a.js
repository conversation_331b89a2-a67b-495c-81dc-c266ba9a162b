import{cC as V,dc as O,r as s,e as t,j as p,o as u,h as e,f as n,l as r,k as U,c as q,K as L,L as j,n as f,t as E}from"./index-B0qHf98Y.js";const S={__name:"CodeTypeDialog",props:V({showFileName:Boolean},{modelValue:{},modelModifiers:{}}),emits:V(["confirm"],["update:modelValue"]),setup(m,{emit:b}){const d=O(m,"modelValue"),v=m,N=b,o=s({fileName:void 0,type:"file"}),c=s(),C={fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],type:[{required:!0,message:"生成类型不能为空",trigger:"change"}]},k=s([{label:"页面",value:"file"},{label:"弹窗",value:"dialog"}]);function w(){v.showFileName&&(o.value.fileName=`${+new Date}.vue`)}function i(){d.value=!1}function x(){c.value.validate(_=>{_&&(N("confirm",{...o.value}),i())})}return(_,a)=>{const F=t("el-radio-button"),h=t("el-radio-group"),g=t("el-form-item"),B=t("el-input"),D=t("el-form"),y=t("el-button"),T=t("el-dialog");return u(),p(T,{modelValue:d.value,"onUpdate:modelValue":a[2]||(a[2]=l=>d.value=l),width:"500px",title:"选择生成类型",onOpen:w,onClose:i},{footer:e(()=>[n(y,{onClick:i},{default:e(()=>[f("取消")]),_:1}),n(y,{type:"primary",onClick:x},{default:e(()=>[f("确定")]),_:1})]),default:e(()=>[n(D,{ref_key:"codeTypeForm",ref:c,model:r(o),rules:C,"label-width":"100px"},{default:e(()=>[n(g,{label:"生成类型",prop:"type"},{default:e(()=>[n(h,{modelValue:r(o).type,"onUpdate:modelValue":a[0]||(a[0]=l=>r(o).type=l)},{default:e(()=>[(u(!0),q(L,null,j(r(k),(l,M)=>(u(),p(F,{key:M,label:l.value},{default:e(()=>[f(E(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),m.showFileName?(u(),p(g,{key:0,label:"文件名",prop:"fileName"},{default:e(()=>[n(B,{modelValue:r(o).fileName,"onUpdate:modelValue":a[1]||(a[1]=l=>r(o).fileName=l),placeholder:"请输入文件名",clearable:""},null,8,["modelValue"])]),_:1})):U("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])}}};export{S as default};
