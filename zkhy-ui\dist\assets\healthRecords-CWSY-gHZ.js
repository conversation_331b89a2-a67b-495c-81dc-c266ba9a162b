import{_ as nl,B as ul,d as il,C as pl,N as cl,r as p,F as vl,P as Ne,e as _,I as ml,c as I,o as v,f as e,h as l,i as a,Q as ae,l as s,k as ye,n as d,K as V,L as T,j as D,t as L,m as yl,D as E,J as ge,O as Ce,v as gl,x as fl}from"./index-B0qHf98Y.js";import{u as _l,a as bl,g as Dl,d as Il,b as Ml,c as hl,l as oe,e as El}from"./telderHealthProfile-BPOGoL2M.js";import{i as P,L as Vl}from"./index-a8qYZQmS.js";const c=Y=>(gl("data-v-94afbd5c"),Y=Y(),fl(),Y),Tl={class:"cardDetailTop"},xl={key:0},Nl=c(()=>a("div",{style:{width:"100%",height:"20px"}},null,-1)),Cl=c(()=>a("div",{class:"dashboard"},[a("div",{class:"card"},[a("div",{class:"card-header"},[a("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiM4QTJCRTIiLz48L3N2Zz4=",alt:"血压"}),a("span",null,"血压监测")]),a("div",{class:"value-display"},[d(" 128"),a("span",{class:"unit"},"mmHg"),d(" / 89"),a("span",{class:"unit"},"mmHg")]),a("div",{id:"chart1",class:"chart-container"})]),a("div",{class:"card"},[a("div",{class:"card-header"},[a("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDIxLjM1TDEwLjU1IDIwLjAzQzUuNCAxNS4zNiAyIDEyLjI3IDIgOC41QzIgNS40MSA0LjQyIDMgNy41IDNDOS4yNCAzIDEwLjkxIDMuODEgMTIgNS4wO0MxMy4wOSAzLjgxIDE0Ljc2IDMgMTYuNSAzQzE5LjU4IDMgMjIgNS40MSAyMiA4LjVDMjIgMTIuMjcgMTguNiAxNS4zNiAxMy40NSAyMC4wM0wxMiAyMS4zNVoiIGZpbGw9IiNGRjY5QjQiLz48L3N2Zz4=",alt:"心率"}),a("span",null,"心率监测")]),a("div",{class:"value-display"},[d("72"),a("span",{class:"unit"},"次/分")]),a("div",{id:"chart2",class:"chart-container"})]),a("div",{class:"card"},[a("div",{class:"card-header"},[a("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIzIDEySDE5VjVDMTkgNC40NCAxOC41NiA0IDE4IDRIMTZDMTUuNDQgNCAxNSA0LjQ0IDE1IDVWMTJIMTNWNUMxMyA0LjQ0IDEyLjU2IDQgMTIgNEgxMEM5LjQ0IDQgOSA0LjQ0IDkgNVYxMkg1QzQuNDQgMTIgNCAxMi40NCA0IDEzVjE1QzQgMTUuNTYgNC40NCAxNiA1IDE2SDlWMTlDOSAxOS41NiA5LjQ0IDIwIDEwIDIwSDEyQzEyLjU2IDIwIDEzIDE5LjU2IDEzIDE5VjE2SDE1VjE5QzE1IDE5LjU2IDE1LjQ0IDIwIDE2IDIwSDE4QzE4LjU2IDIwIDE5IDE5LjU2IDE5IDE5VjE2SDIzQzIzLjU2IDE2IDI0IDE1LjU2IDI0IDE1VjEzQzI0IDEyLjQ0IDIzLjU2IDEyIDIzIDEyWiIgZmlsbD0iIzAwQjNENyIvPjwvc3ZnPg==",alt:"睡眠"}),a("span",null,"睡眠监测")]),a("div",{class:"value-display"},[d("7.5"),a("span",{class:"unit"},"小时")]),a("div",{id:"chart3",class:"chart-container"})]),a("div",{class:"card"},[a("div",{class:"card-header"},[a("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyLDMgQzE2Ljk3LDMgMjEsNi41OCAyMSwxMSBDMjEsMTUuNDIgMTYuOTcsMTkgMTIsMTkgQzcuMDMsMTkgMywxNS40MiAzLDExIEMzLDYuNTggNy4wMywzIDEyLDMgTTEyLDUgQzguMTQsNSA1LDcuNzEgNSwxMSBDNSwxNC4yOSA4LjE0LDE3IDEyLDE3IEMxNS44NiwxNyAxOSwxNC4yOSAxOSwxMSBDMTksNy43MSAxNS44Niw1IDEyLDUgTTEyLDcgQzE0LjI5LDcgMTYsOC4zNCAxNiwxMCBDMTYsMTEuNjYgMTQuMjksMTMgMTIsMTMgQzkuNzEsMTMgOCwxMS42NiA4LDEwIEM4LDguMzQgOS43MSw3IDEyLDcgTTEyLDkgQzExLjQ1LDkgMTEsOS40NSAxMSwxMCBDMTEsMTAuNTUgMTEuNDUsMTEgMTIsMTEgQzEyLjU1LDExIDEzLDEwLjU1IDEzLDEwIEMxMyw5LjQ1IDEyLjU1LDkgMTIsOSBaIiBmaWxsPSIjRkY0NDQ0Ii8+PC9zdmc+",alt:"血氧"}),a("span",null,"血氧监测")]),a("div",{class:"value-display"},[d("98"),a("span",{class:"unit"},"%")]),a("div",{id:"chart4",class:"chart-container"})]),a("div",{class:"card"},[a("div",{class:"card-header"},[a("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiNGRjhCMDAiLz48L3N2Zz4=",alt:"体温"}),a("span",null,"体温监测")]),a("div",{class:"value-display"},[d("36.7"),a("span",{class:"unit"},"°C")]),a("div",{id:"chart5",class:"chart-container"})]),a("div",{class:"card"},[a("div",{class:"card-header"},[a("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xIDE4aDJ2LTJoLTJ2MnptMi4wNy0xMy4yNGMtLjI2LS4xOS0uNTktLjE5LS44MSAwbC0xLjA3Ljc1Yy0uMzMuMjMtLjQzLjY2LS4yNSAxLjAxLjE5LjM0LjU2LjUxLjkxLjQzLjM1LS4wOC42My0uMzEuNzItLjY2LjA5LS4zNSAwLS43Mi0uMjUtLjk3bC0uNzUtLjUzVjEyaDJWOC43NnoiIGZpbGw9IiMwMEI1RTMiLz48L3N2Zz4=",alt:"呼吸"}),a("span",null,"呼吸频率")]),a("div",{class:"value-display"},[d("16"),a("span",{class:"unit"},"次/分")]),a("div",{id:"chart6",class:"chart-container"})])],-1)),Ll={key:1},wl=c(()=>a("span",{class:"subtitleCss"},"基本信息",-1)),zl=c(()=>a("div",{style:{height:"10px"}},null,-1)),jl=c(()=>a("span",{class:"subtitleCss"},"健康史",-1)),Sl={class:"historyDiv"},Al={class:"cardDetailcenter"},kl=c(()=>a("div",{class:"historyCss"},"既往病史",-1)),Ul={class:"timelineProcessBox"},Hl={style:{display:"flex","flex-direction":"column"}},Ql={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Bl={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Zl={class:"cardDetailcenter"},Ol=c(()=>a("div",{class:"historyCss"},"手术史",-1)),Pl={class:"timelineProcessBox"},Yl={style:{display:"flex","flex-direction":"column"}},Rl={style:{margin:"10px 0","font-size":"12px",color:"#999"}},ql={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Wl={class:"cardDetailcenter"},Gl=c(()=>a("div",{class:"historyCss"},"长期用药史",-1)),Fl={class:"timelineProcessBox"},$l={style:{display:"flex","flex-direction":"column"}},Jl={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Kl={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Xl=c(()=>a("span",{class:"subtitleCss"},"其他信息",-1)),ea={class:"el-table tables"},la=c(()=>a("thead",{class:"el-table__header"},[a("tr",null,[a("th",{class:"border border-gray-300 p-2 bg-gray-100"},"序号"),a("th",{class:"border border-gray-300 p-2 bg-gray-100"},"能力"),a("th",{class:"border border-gray-300 p-2 bg-gray-100"},"操作")])],-1)),aa=c(()=>a("td",{class:"border border-gray-300 p-2"},"1",-1)),ta=c(()=>a("td",{class:"border border-gray-300 p-2"},"进食",-1)),oa={class:"border border-gray-300 p-2"},sa=c(()=>a("td",{class:"border border-gray-300 p-2"},"2",-1)),da=c(()=>a("td",{class:"border border-gray-300 p-2"},"洗澡",-1)),ra={class:"border border-gray-300 p-2"},na=c(()=>a("td",{class:"border border-gray-300 p-2"},"3",-1)),ua=c(()=>a("td",{class:"border border-gray-300 p-2"},"修饰",-1)),ia={class:"border border-gray-300 p-2"},pa=c(()=>a("td",{class:"border border-gray-300 p-2"},"4",-1)),ca=c(()=>a("td",{class:"border border-gray-300 p-2"},"穿衣",-1)),va={class:"border border-gray-300 p-2"},ma=c(()=>a("td",{class:"border border-gray-300 p-2"},"5",-1)),ya=c(()=>a("td",{class:"border border-gray-300 p-2"},"如厕，排泄",-1)),ga={class:"border border-gray-300 p-2"},fa=c(()=>a("td",{class:"border border-gray-300 p-2"},"6",-1)),_a=c(()=>a("td",{class:"border border-gray-300 p-2"},"移动",-1)),ba={class:"border border-gray-300 p-2"},Da=c(()=>a("td",{class:"border border-gray-300 p-2"},"7",-1)),Ia=c(()=>a("td",{class:"border border-gray-300 p-2"},"认知能力",-1)),Ma={class:"border border-gray-300 p-2"},ha=c(()=>a("td",{class:"border border-gray-300 p-2"},"8",-1)),Ea=c(()=>a("td",{class:"border border-gray-300 p-2"},"情绪能力",-1)),Va={class:"border border-gray-300 p-2"},Ta=c(()=>a("td",{class:"border border-gray-300 p-2"},"9",-1)),xa=c(()=>a("td",{class:"border border-gray-300 p-2"},"视觉能力",-1)),Na={class:"border border-gray-300 p-2"},Ca=c(()=>a("td",{class:"border border-gray-300 p-2"},"10",-1)),La=c(()=>a("td",{class:"border border-gray-300 p-2"},"听力",-1)),wa={class:"border border-gray-300 p-2"},za={class:"dialog-footer",style:{"margin-left":"90%","margin-top":"10px"}},ja={class:"Addtitle"},Sa={class:"dialog-footer"},Aa={style:{flex:"auto"}},ka=ul({name:"healthRecords"}),Ua=Object.assign(ka,{props:{elderId:{type:String,default:null},isShow:{type:Boolean,default:!1}},setup(Y){const{proxy:x}=il(),{rh_type:Le,elderly_blood_type:we,sleep_quality:ze,smoking_frequency:je,drinking_frequency:Se,sports_frequency:Ae,dietary_preferences:ke,medical_history_status:Ha}=x.useDict("rh_type","elderly_blood_type","sleep_quality","smoking_frequency","drinking_frequency","sports_frequency","dietary_preferences","medical_history_status"),Ue=pl({formbase:{},queryform:{},formevent:{},queryParams:{},queryParamslist:{},queryParamsElderHealth:{},formeventrules:{details:[{required:!0,message:"详情不能为空",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"}]},queryrules:{},rules:{}}),B=Y,{formbase:i,queryform:se,formevent:y,queryParams:b,queryParamslist:j,queryParamsElderHealth:de,formeventrules:He,queryrules:Qe,rules:Be}=cl(Ue),Z=p("01"),re=p([]),S=p(!1),w=p(""),fe=p([]),_e=p([]),be=p([]),ne=p(!0),Ze=p(!0),De=p([]);p(!0),p(!0);const ue=p(0),A=p("01");p("");const O=p(""),k=p([]),ie=p(!1),Ie=p(),R=p(0),q=p(0),W=p(0),G=p(0),F=p(0),$=p(0),J=p(0),K=p(0),X=p(0),ee=p(0);p("");function Oe(){Pe(),Ye(),Re(),qe(),We(),Ge()}function Pe(){var r=document.getElementById("chart1"),o=P(r),u;u={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},legend:{data:["收缩压","舒张压"]},series:[{data:[55,68,95,87,78,65],type:"line",smooth:!0,lineStyle:{color:"rgb(140, 47, 226)"}},{data:[65,78,65,77,98,67],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 169, 11)"}}]},u&&o.setOption(u)}function Ye(){var r=document.getElementById("chart2"),o=P(r),u;u={xAxis:{type:"category",boundaryGap:!1,data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},legend:{data:["收缩压","舒张压"]},series:[{data:[55,68,95,87,78,65],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 102, 108)"},areaStyle:{color:new Vl(0,0,0,1,[{offset:0,color:"rgba(250,188,228,0.8)"}])}}]},u&&o.setOption(u)}function Re(){var r=document.getElementById("chart3"),o=P(r),u;u={tooltip:{trigger:"item"},legend:{top:"5%",left:"center"},series:[{name:"Access From",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[{value:1048,name:"深睡眠"},{value:735,name:"浅睡眠"},{value:580,name:"清醒"}]}]},u&&o.setOption(u)}function qe(){var r=document.getElementById("chart4"),o=P(r),u;u={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[55,68,95,87,78,65],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 74, 74)"}}]},u&&o.setOption(u)}function We(){var r=document.getElementById("chart5"),o=P(r),u;u={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[36.5,36.7,37.2,37.5,36.9,36.5],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 152, 0)"}}]},u&&o.setOption(u)}function Ge(){var r=document.getElementById("chart6"),o=P(r),u;u={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[14,16,19,23,17,21],type:"line",smooth:!0,lineStyle:{color:"rgb(57, 196, 234)"}}]},u&&o.setOption(u)}function Me(r){r=="01"?(Z.value="01",initTargetChart()):r=="02"&&(Z.value="02",he(),Ee())}function he(){j.value.elderId=B.elderId,j.value.recordType="既往病史",oe(j.value).then(r=>{var o;fe.value=(o=r.rows)==null?void 0:o.slice(0,3)}),j.value.recordType="手术史",oe(j.value).then(r=>{var o;_e.value=(o=r.rows)==null?void 0:o.slice(0,3)}),j.value.recordType="长期用药史",oe(j.value).then(r=>{var o;be.value=(o=r.rows)==null?void 0:o.slice(0,3)})}function Ee(){de.value.elderId=B.elderId,console.log(de.value,"queryParamsElderHealth"),El(de.value).then(r=>{var u,U,g;if(!r.rows||r.rows.length==0){i.value={},k.value=[];return}i.value=r.rows[0];var o=(u=r.rows[0])==null?void 0:u.chronicDiseases.split(",");k.value=o,((U=r.rows[0])==null?void 0:U.elderAbilities.length)>0&&JSON.parse((g=r.rows[0])==null?void 0:g.elderAbilities).forEach(m=>{m.type=="进食"?R.value=m.value:m.type=="洗澡"?q.value=m.value:m.type=="修饰"?W.value=m.value:m.type=="穿衣"?G.value=m.value:m.type=="如厕，排泄"?F.value=m.value:m.type=="移动"?$.value=m.value:m.type=="认知能力"?J.value=m.value:m.type=="情绪能力"?K.value=m.value:m.type=="视觉能力"?X.value=m.value:m.type=="听力"&&(ee.value=m.value),console.log(m,"item")})}),he()}const Fe=r=>{k.value.splice(k.value.indexOf(r),1)},$e=()=>{ie.value=!0,Ne(()=>{Ie.value.inputs.focus()})},Ve=()=>{O.value&&k.value.push(O.value),ie.value=!1,O.value=""};function Je(){A.value="01",le()}function le(){y.value={id:null,elderId:null,eventDate:null,name:null,details:null,location:null,medicationStatus:null,remark:null,createTime:null,updateTime:null,createBy:null,updateBy:null},x.resetForm("elderHealthEventRef")}function pe(r){S.value=!0,ne.value=!0,A.value="01",r==1?(w.value="既往病史",y.value.recordType="既往病史"):r==2?(w.value="手术史",y.value.recordType="手术史"):r==3&&(w.value="长期用药史",y.value.recordType="长期用药史"),te()}function te(){b.value.recordType=w.value,b.value.elderId=B.elderId,oe(b.value).then(r=>{re.value=r.rows,ue.value=r.total,ne.value=!1})}function Ke(){A.value="02"}function Xe(){x.$refs.elderHealthEventRef.validate(r=>{r&&(y.value.elderId=B.elderId,y.value.recordType=w.value,y.value.id!=null?Ml(y.value).then(o=>{x.$modal.msgSuccess("修改成功"),te(),le()}):hl(y.value).then(o=>{x.$modal.msgSuccess("新增成功"),te(),le()}))})}vl(()=>{Ne(()=>{Oe()})});function el(){x.$refs.telderinfoRef.validate(r=>{r&&(i.value.elderId=B.elderId,i.value.chronicDiseases=k.value.join(","),i.value.elderAbilities=JSON.stringify([{type:"进食",value:R.value},{type:"洗澡",value:q.value},{type:"修饰",value:W.value},{type:"穿衣",value:G.value},{type:"如厕，排泄",value:F.value},{type:"移动",value:$.value},{type:"认知能力",value:J.value},{type:"情绪能力",value:K.value},{type:"视觉能力",value:X.value},{type:"听力",value:ee.value}]),i.value.id!=null?_l(i.value).then(o=>{x.$modal.msgSuccess("修改成功")}):bl(i.value).then(o=>{x.$modal.msgSuccess("新增成功")}))})}function ll(r){le(),S.value=!0,A.value="02";const o=r.id||De.value;Dl(o).then(u=>{y.value=u.data,S.value=!0,w.value="修改"})}function al(r){const o=r.id||De.value;x.$modal.confirm('是否确认删除老人健康事件记录编号为"'+o+'"的数据项？').then(function(){return Il(o)}).then(()=>{te(),x.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Te(){S.value=!1,le(),re.value=[],b.value={},Ee()}return(r,o)=>{const u=_("el-date-picker"),U=_("el-form"),g=_("el-col"),m=_("el-row"),ce=_("el-divider"),M=_("el-input"),f=_("el-form-item"),H=_("el-option"),Q=_("el-select"),tl=_("el-tag"),h=_("el-button"),ve=_("el-timeline-item"),me=_("el-timeline"),n=_("el-radio"),N=_("el-radio-group"),xe=_("el-card"),C=_("el-table-column"),ol=_("el-table"),sl=_("pagination"),dl=_("el-drawer"),rl=ml("loading");return v(),I("div",null,[e(xe,{shadow:"never"},{default:l(()=>[a("div",Tl,[a("div",{class:ae(["cardDetailTopDiv",s(Z)=="01"?"cardDetailTopDivSelect":""]),onClick:o[0]||(o[0]=t=>Me("01"))}," 健康信息 ",2),a("div",{class:ae(["cardDetailTopDiv",s(Z)=="02"?"cardDetailTopDivSelect":""]),onClick:o[1]||(o[1]=t=>Me("02"))}," 健康档案 ",2)]),e(m,null,{default:l(()=>[e(g,{span:24},{default:l(()=>[s(Z)=="01"?(v(),I("div",xl,[e(m,{gutter:15},{default:l(()=>[e(g,{span:24},{default:l(()=>[e(U,{ref:"telderinfoRef",model:s(se),rules:s(Qe),"label-width":"120px"},{default:l(()=>[e(u,{modelValue:s(se).dateRange,"onUpdate:modelValue":o[2]||(o[2]=t=>s(se).dateRange=t),type:"datetimerange","start-placeholder":"开始时间","range-separator":"至","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm","date-format":"YYYY-MM-DD HH:mm","time-format":"hh:mm"},null,8,["modelValue"])]),_:1},8,["model","rules"])]),_:1})]),_:1}),Nl,Cl])):ye("",!0),s(Z)=="02"?(v(),I("div",Ll,[e(U,{ref:"telderinfoRef",model:s(i),rules:s(Be),"label-width":"120px"},{default:l(()=>[e(ce,{"content-position":"left"},{default:l(()=>[wl]),_:1}),zl,e(m,{gutter:15},{default:l(()=>[e(g,{span:8},{default:l(()=>[e(f,{label:"老人身高",prop:"heightCm",size:"large"},{default:l(()=>[e(M,{modelValue:s(i).heightCm,"onUpdate:modelValue":o[3]||(o[3]=t=>s(i).heightCm=t),placeholder:"请输入身高(cm)",disabled:B.isShow},{append:l(()=>[d("cm")]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"老人体重",prop:"weightKg",size:"large"},{default:l(()=>[e(M,{modelValue:s(i).weightKg,"onUpdate:modelValue":o[4]||(o[4]=t=>s(i).weightKg=t),placeholder:"请输入体重(kg)"},{append:l(()=>[d("kg")]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"RH因子",prop:"rhFactor",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).rhFactor,"onUpdate:modelValue":o[5]||(o[5]=t=>s(i).rhFactor=t),placeholder:"请选择RH因子"},{default:l(()=>[(v(!0),I(V,null,T(s(Le),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"血型",prop:"bloodType",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).bloodType,"onUpdate:modelValue":o[6]||(o[6]=t=>s(i).bloodType=t),placeholder:"请选择血型"},{default:l(()=>[(v(!0),I(V,null,T(s(we),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"睡眠质量",prop:"sleepQuality",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).sleepQuality,"onUpdate:modelValue":o[7]||(o[7]=t=>s(i).sleepQuality=t),placeholder:"请选择睡眠质量"},{default:l(()=>[(v(!0),I(V,null,T(s(ze),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"吸烟频率",prop:"smokingFrequency",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).smokingFrequency,"onUpdate:modelValue":o[8]||(o[8]=t=>s(i).smokingFrequency=t),placeholder:"请选择吸烟频率"},{default:l(()=>[(v(!0),I(V,null,T(s(je),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"饮酒频率",prop:"drinkingFrequency",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).drinkingFrequency,"onUpdate:modelValue":o[9]||(o[9]=t=>s(i).drinkingFrequency=t),placeholder:"请选择饮酒频率"},{default:l(()=>[(v(!0),I(V,null,T(s(Se),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"运动频率",prop:"exerciseFrequency",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).exerciseFrequency,"onUpdate:modelValue":o[10]||(o[10]=t=>s(i).exerciseFrequency=t),placeholder:"请选择运动频率"},{default:l(()=>[(v(!0),I(V,null,T(s(Ae),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"饮食偏好",prop:"dietaryPreference",size:"large"},{default:l(()=>[e(Q,{modelValue:s(i).dietaryPreference,"onUpdate:modelValue":o[11]||(o[11]=t=>s(i).dietaryPreference=t),placeholder:"请选择饮食偏好"},{default:l(()=>[(v(!0),I(V,null,T(s(ke),t=>(v(),D(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:8},{default:l(()=>[e(f,{label:"慢性病史",prop:"chronicDiseases",size:"large"},{default:l(()=>[(v(!0),I(V,null,T(s(k),t=>(v(),D(tl,{style:{"margin-right":"4px"},key:t,closable:"","disable-transitions":!1,onClose:z=>Fe(t)},{default:l(()=>[d(L(t),1)]),_:2},1032,["onClose"]))),128)),s(ie)?(v(),D(M,{key:0,ref_key:"InputRef",ref:Ie,modelValue:s(O),"onUpdate:modelValue":o[12]||(o[12]=t=>E(O)?O.value=t:null),class:"w-20",size:"larger",onKeyup:yl(Ve,["enter"]),onBlur:Ve},null,8,["modelValue"])):(v(),D(h,{key:1,class:"button-new-tag",size:"large",onClick:$e},{default:l(()=>[d(" + 添加 ")]),_:1}))]),_:1})]),_:1}),e(ce,{"content-position":"left"},{default:l(()=>[jl]),_:1}),a("div",Sl,[e(g,{span:24,style:{height:"130px"}},{default:l(()=>[a("div",Al,[kl,a("div",null,[e(h,{type:"primary",onClick:o[13]||(o[13]=t=>pe("1")),size:"small"},{default:l(()=>[d("既往病史管理")]),_:1})])]),a("div",Ul,[e(me,{class:"timeline"},{default:l(()=>[(v(!0),I(V,null,T(s(fe),(t,z)=>(v(),D(ve,{class:ae(["lineitem",t.done?"active":"inactive"]),key:z},{default:l(()=>[a("span",Hl,[a("span",Ql," 确证时间："+L(t.eventDate),1),a("span",Bl," 名称："+L(t.name),1)])]),_:2},1032,["class"]))),128))]),_:1})])]),_:1}),e(g,{span:24},{default:l(()=>[a("div",Zl,[Ol,a("div",null,[e(h,{type:"primary",onClick:o[14]||(o[14]=t=>pe("2")),size:"small"},{default:l(()=>[d("手术史管理")]),_:1})])]),a("div",Pl,[e(me,{class:"timeline"},{default:l(()=>[(v(!0),I(V,null,T(s(_e),(t,z)=>(v(),D(ve,{class:ae(["lineitem",t.done?"active":"inactive"]),key:z},{default:l(()=>[a("span",Yl,[a("span",Rl," 确证时间："+L(t.eventDate),1),a("span",ql," 名称："+L(t.name),1)])]),_:2},1032,["class"]))),128))]),_:1})])]),_:1}),e(g,{span:24},{default:l(()=>[a("div",Wl,[Gl,a("div",null,[e(h,{type:"primary",onClick:o[15]||(o[15]=t=>pe("3")),size:"small"},{default:l(()=>[d("长期用药史管理")]),_:1})])]),a("div",Fl,[e(me,{class:"timeline"},{default:l(()=>[(v(!0),I(V,null,T(s(be),(t,z)=>(v(),D(ve,{class:ae(["lineitem",t.done?"active":"inactive"]),key:z},{default:l(()=>[a("span",$l,[a("span",Jl," 确证时间："+L(t.eventDate),1),a("span",Kl," 名称："+L(t.name),1)])]),_:2},1032,["class"]))),128))]),_:1})])]),_:1})]),e(ce,{"content-position":"left"},{default:l(()=>[Xl]),_:1}),e(g,{span:24,style:{"margin-top":"12px"}},{default:l(()=>[e(f,{label:"过敏史",prop:"allergyHistory",size:"large"},{default:l(()=>[e(M,{modelValue:s(i).allergyHistory,"onUpdate:modelValue":o[16]||(o[16]=t=>s(i).allergyHistory=t),type:"textarea",rows:"4",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:24},{default:l(()=>[e(f,{label:"家住病史",prop:"familyHistory",size:"large"},{default:l(()=>[e(M,{modelValue:s(i).familyHistory,"onUpdate:modelValue":o[17]||(o[17]=t=>s(i).familyHistory=t),type:"textarea",rows:"4",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:24},{default:l(()=>[e(f,{label:"能力评估",prop:"allergyHistory",size:"small",stripe:""})]),_:1})]),_:1})]),_:1},8,["model","rules"]),a("table",ea,[la,a("tbody",null,[a("tr",null,[aa,ta,a("td",oa,[e(N,{modelValue:s(R),"onUpdate:modelValue":o[18]||(o[18]=t=>E(R)?R.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[sa,da,a("td",ra,[e(N,{modelValue:s(q),"onUpdate:modelValue":o[19]||(o[19]=t=>E(q)?q.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[na,ua,a("td",ia,[e(N,{modelValue:s(W),"onUpdate:modelValue":o[20]||(o[20]=t=>E(W)?W.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[pa,ca,a("td",va,[e(N,{modelValue:s(G),"onUpdate:modelValue":o[21]||(o[21]=t=>E(G)?G.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[ma,ya,a("td",ga,[e(N,{modelValue:s(F),"onUpdate:modelValue":o[22]||(o[22]=t=>E(F)?F.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[fa,_a,a("td",ba,[e(N,{modelValue:s($),"onUpdate:modelValue":o[23]||(o[23]=t=>E($)?$.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[Da,Ia,a("td",Ma,[e(N,{modelValue:s(J),"onUpdate:modelValue":o[24]||(o[24]=t=>E(J)?J.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[ha,Ea,a("td",Va,[e(N,{modelValue:s(K),"onUpdate:modelValue":o[25]||(o[25]=t=>E(K)?K.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[Ta,xa,a("td",Na,[e(N,{modelValue:s(X),"onUpdate:modelValue":o[26]||(o[26]=t=>E(X)?X.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),a("tr",null,[Ca,La,a("td",wa,[e(N,{modelValue:s(ee),"onUpdate:modelValue":o[27]||(o[27]=t=>E(ee)?ee.value=t:null)},{default:l(()=>[e(n,{value:0},{default:l(()=>[d("正常")]),_:1}),e(n,{value:1},{default:l(()=>[d("轻度依赖")]),_:1}),e(n,{value:2},{default:l(()=>[d("重度依赖")]),_:1}),e(n,{value:3},{default:l(()=>[d("完全依赖")]),_:1})]),_:1},8,["modelValue"])])])])])])):ye("",!0)]),_:1})]),_:1}),a("div",za,[e(h,{size:"large",type:"primary",onClick:el},{default:l(()=>[d("确 定")]),_:1})])]),_:1}),e(dl,{modelValue:s(S),"onUpdate:modelValue":o[40]||(o[40]=t=>E(S)?S.value=t:null),title:s(w),direction:"rtl","before-close":Te,size:"90%"},{footer:l(()=>[a("div",Aa,[e(h,{onClick:Te},{default:l(()=>[d("关闭")]),_:1})])]),default:l(()=>[e(m,{gutter:15},{default:l(()=>[e(g,{span:s(A)=="01"?21:18},{default:l(()=>[ge(e(U,{model:s(b),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(f,{label:"事件日期",prop:"eventDate"},{default:l(()=>[e(u,{clearable:"",modelValue:s(b).eventDate,"onUpdate:modelValue":o[28]||(o[28]=t=>s(b).eventDate=t),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择事件日期"},null,8,["modelValue"])]),_:1}),e(f,{label:"名称",prop:"name"},{default:l(()=>[e(M,{modelValue:s(b).name,"onUpdate:modelValue":o[29]||(o[29]=t=>s(b).name=t),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"地点",prop:"location"},{default:l(()=>[e(M,{modelValue:s(b).location,"onUpdate:modelValue":o[30]||(o[30]=t=>s(b).location=t),placeholder:"请输入地点",clearable:""},null,8,["modelValue"])]),_:1}),e(f,null,{default:l(()=>[e(h,{type:"primary",icon:"Search",onClick:r.handleQuery},{default:l(()=>[d("搜索")]),_:1},8,["onClick"]),e(h,{icon:"Refresh",onClick:r.resetQuery},{default:l(()=>[d("重置")]),_:1},8,["onClick"]),e(h,{type:"primary",plain:"",icon:"Plus",onClick:Ke},{default:l(()=>[d("新增")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ce,s(Ze)]]),ge((v(),D(ol,{data:s(re),border:"",stripe:""},{default:l(()=>[e(C,{type:"selection",width:"55",align:"center"}),e(C,{label:"记录类型",align:"center",prop:"recordType"}),e(C,{label:"事件日期",align:"center",prop:"eventDate",width:"180"},{default:l(t=>[a("span",null,L(r.parseTime(t.row.eventDate,"{y}-{m}-{d}")),1)]),_:1}),e(C,{label:"名称",align:"center",prop:"name"}),e(C,{label:"详情",align:"center",prop:"details"}),e(C,{label:"地点",align:"center",prop:"location"}),e(C,{label:"用药状态",align:"center",prop:"medicationStatus"}),e(C,{label:"备注",align:"center",prop:"remark"}),e(C,{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},{default:l(t=>[e(h,{link:"",type:"primary",icon:"Edit",onClick:z=>ll(t.row)},{default:l(()=>[d("修改")]),_:2},1032,["onClick"]),e(h,{link:"",type:"primary",icon:"Delete",onClick:z=>al(t.row)},{default:l(()=>[d("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[rl,s(ne)]]),ge(e(sl,{total:s(ue),page:s(b).pageNum,"onUpdate:page":o[31]||(o[31]=t=>s(b).pageNum=t),limit:s(b).pageSize,"onUpdate:limit":o[32]||(o[32]=t=>s(b).pageSize=t),onPagination:r.getList},null,8,["total","page","limit","onPagination"]),[[Ce,s(ue)>0]])]),_:1},8,["span"]),s(A)=="02"?(v(),D(g,{key:0,span:s(A)=="01"?0:6},{default:l(()=>[e(xe,{shadow:"hover"},{default:l(()=>[a("div",ja,L(s(w)),1),e(U,{ref:"elderHealthEventRef",model:s(y),rules:s(He),"label-width":"80px"},{default:l(()=>[e(M,{modelValue:s(y).id,"onUpdate:modelValue":o[33]||(o[33]=t=>s(y).id=t),type:"hidden"},null,8,["modelValue"]),e(f,{label:"事件日期",prop:"eventDate"},{default:l(()=>[e(u,{clearable:"",modelValue:s(y).eventDate,"onUpdate:modelValue":o[34]||(o[34]=t=>s(y).eventDate=t),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择事件日期"},null,8,["modelValue"])]),_:1}),e(f,{label:"名称",prop:"name"},{default:l(()=>[e(M,{modelValue:s(y).name,"onUpdate:modelValue":o[35]||(o[35]=t=>s(y).name=t),placeholder:"请输入名称(疾病/手术/药品)"},null,8,["modelValue"])]),_:1}),e(f,{label:"详情",prop:"details"},{default:l(()=>[e(M,{modelValue:s(y).details,"onUpdate:modelValue":o[36]||(o[36]=t=>s(y).details=t),type:"textarea",rows:"4",placeholder:"请输入内容(治疗结果/手术描述/剂量及用法/用药原因)"},null,8,["modelValue"])]),_:1}),e(f,{label:"地点",prop:"location"},{default:l(()=>[e(M,{modelValue:s(y).location,"onUpdate:modelValue":o[37]||(o[37]=t=>s(y).location=t),placeholder:"请输入地点(就诊/手术医院)"},null,8,["modelValue"])]),_:1}),e(f,{label:"用药状态",prop:"medicationStatus"},{default:l(()=>[e(M,{modelValue:s(y).medicationStatus,"onUpdate:modelValue":o[38]||(o[38]=t=>s(y).medicationStatus=t),placeholder:"请输入用药状态(仅用于用药记录: 服用中/已停用)"},null,8,["modelValue"])]),_:1}),e(f,{label:"备注",prop:"remark"},{default:l(()=>[e(M,{modelValue:s(y).remark,"onUpdate:modelValue":o[39]||(o[39]=t=>s(y).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),a("div",Sa,[e(h,{type:"primary",onClick:Xe},{default:l(()=>[d("确 定")]),_:1}),e(h,{onClick:Je},{default:l(()=>[d("取 消")]),_:1})])]),_:1})]),_:1},8,["span"])):ye("",!0)]),_:1})]),_:1},8,["modelValue","title"])])}}}),Oa=nl(Ua,[["__scopeId","data-v-94afbd5c"]]);export{Oa as default};
