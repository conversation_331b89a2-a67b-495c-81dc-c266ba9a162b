import{_ as E,B as F,u as J,a as $,d as G,r as p,C as W,N as X,e as _,I as Z,c as x,i as e,J as D,f as o,h as c,n as N,t as i,l as t,O as I,m as b,j as ee,k as S,o as v,v as te,x as oe}from"./index-B0qHf98Y.js";import{b as ae}from"./tWarehouseMedication-Ycu1QDaW.js";import{l as le}from"./tWarehouseInventoryCheck-Dhh0m5wG.js";const r=f=>(te("data-v-7d068269"),f=f(),oe(),f),se={class:"app-container"},ne={class:"bottom_room_table"},re={class:"cardBox"},ce=r(()=>e("div",{class:"title_room"},[e("h3",null,"基本信息")],-1)),ie={class:"tbcss"},de=r(()=>e("th",{class:"tbTr"},"物品编码",-1)),ue={class:"tbTrVal"},pe=r(()=>e("th",{class:"tbTr"},null,-1)),_e=r(()=>e("th",{class:"tbTr"},"物品名称",-1)),me={class:"tbTrVal"},he=r(()=>e("th",{class:"tbTr"},"物品类型",-1)),be={class:"tbTrVal"},fe=r(()=>e("th",{class:"tbTr"},"物品规格",-1)),ye={class:"tbTrVal"},ge=r(()=>e("th",{class:"tbTr"},"生产厂家",-1)),ve={class:"tbTrVal"},ke=r(()=>e("th",{class:"tbTr"},"库存数量",-1)),Te={class:"tbTrVal"},Ve=r(()=>e("th",{class:"tbTr"},"库存上限",-1)),we={class:"tbTrVal"},xe=r(()=>e("th",{class:"tbTr"},"库存下线",-1)),De={class:"tbTrVal"},Ne=r(()=>e("th",{class:"tbTr"},"库存状态",-1)),Ce={class:"tbTrVal"},Ie=["innerHTML"],Se=r(()=>e("th",{class:"tbTr"},"仓       库",-1)),Be={class:"tbTrVal"},Pe=r(()=>e("th",{class:"tbTr"},"货       号",-1)),Me={class:"tbTrVal"},qe={key:0},Ue={key:1},Ye=F({name:"AddMedication"}),Ke=Object.assign(Ye,{setup(f){const y=J(),B=$(),{proxy:k}=G(),{inventory_status:Le,goods_type:Re}=k.useDict("inventory_status","goods_type"),C=p([]);p(!1);const P=p(!1);p(!0),p([]),p("");const M=p(!0);p(!0),p(!0);const T=p(0),q=W({form:{status:0},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0}}),{queryParams:a,form:n,rules:Oe}=X(q);function V(){console.log(y.params.id,"id"),U(),Y()}function U(){y.params.id&&ae(y.params.id).then(u=>{console.log(u.data,"res"),n.value=u.data})}function Y(){a.value.id=y.params.id,console.log(a.value,"queryParams.value"),le(a.value).then(u=>{console.log(u,"res"),C.value=u.rows,T.value=u.total})}function m(){a.value.pageNum=1,V()}function K(){k.resetForm("queryRef"),a.value={stockDate:null,stockNo:null,type:null,stockPerson:null,creator:null},m()}function L(u,l){if(u>l)return"<span style='color: #bfbf00'>预警</span>";if(l>u)return"<span style='color: #09d971fe'>正常</span>";if(l===0)return"<span style='color: #D9001B'>空盘</span>"}function R(){k.$tab.closeOpenPage(),B.push("/warehouse/warehouse/wmsmanagement")}return V(),(u,l)=>{const w=_("el-button"),O=_("el-date-picker"),h=_("el-form-item"),g=_("el-input"),Q=_("el-form"),j=_("el-row"),d=_("el-table-column"),z=_("el-table"),A=_("pagination"),H=Z("loading");return v(),x("div",se,[e("div",ne,[e("div",re,[ce,e("div",null,[o(w,{plain:"",onClick:R},{default:c(()=>[N("返 回")]),_:1})])]),e("table",ie,[e("tr",null,[de,e("th",ue,i(t(n).medicineCode),1),pe]),e("tr",null,[_e,e("th",me,i(t(n).medicineName),1),he,e("th",be,i(t(n).category),1),fe,e("th",ye,i(t(n).specification),1),ge,e("th",ve,i(t(n).manufacturer),1)]),e("tr",null,[ke,e("th",Te,i(t(n).currentQuantity),1),Ve,e("th",we,i(t(n).maxInventory),1),xe,e("th",De,i(t(n).minInventory),1),Ne,e("th",Ce,[e("span",{innerHTML:L(t(n).minInventory,t(n).currentQuantity)},null,8,Ie)])]),e("tr",null,[Se,e("th",Be,i(t(n).warehouse),1),Pe,e("th",Me,i(t(n).locationCode),1)])])]),D(o(Q,{model:t(a),ref:"queryRef",inline:!0,"label-width":"88px"},{default:c(()=>[o(h,{label:"操作时间",prop:"stockDate"},{default:c(()=>[o(O,{clearable:"",modelValue:t(a).stockDate,"onUpdate:modelValue":l[0]||(l[0]=s=>t(a).stockDate=s),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择操作时间",value:"YYYY-MM-DD",onKeyup:b(m,["enter"]),style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(h,{label:"单号",prop:"stockNo"},{default:c(()=>[o(g,{modelValue:t(a).stockNo,"onUpdate:modelValue":l[1]||(l[1]=s=>t(a).stockNo=s),placeholder:"请输入单号",clearable:"",style:{width:"200px"},onKeyup:b(m,["enter"])},null,8,["modelValue"])]),_:1}),o(h,{label:"类型",prop:"type"},{default:c(()=>[o(g,{modelValue:t(a).type,"onUpdate:modelValue":l[2]||(l[2]=s=>t(a).type=s),placeholder:"请输入类型",clearable:"",style:{width:"200px"},onKeyup:b(m,["enter"])},null,8,["modelValue"])]),_:1}),o(h,{label:"出入库人",prop:"stockPerson"},{default:c(()=>[o(g,{modelValue:t(a).stockPerson,"onUpdate:modelValue":l[3]||(l[3]=s=>t(a).stockPerson=s),placeholder:"请选择出入库人",clearable:"",style:{width:"200px"},onKeyup:b(m,["enter"])},null,8,["modelValue"])]),_:1}),o(h,{label:"制单人",prop:"creator"},{default:c(()=>[o(g,{modelValue:t(a).creator,"onUpdate:modelValue":l[4]||(l[4]=s=>t(a).creator=s),placeholder:"请输入制单人",clearable:"",style:{width:"200px"},onKeyup:b(m,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),[[I,t(M)]]),o(j,{gutter:10,class:"mb8",justify:"end"},{default:c(()=>[o(w,{type:"primary",icon:"Search",onClick:m},{default:c(()=>[N("搜索")]),_:1}),o(w,{icon:"Refresh",onClick:K},{default:c(()=>[N("重置")]),_:1})]),_:1}),D((v(),ee(z,{data:t(C),border:"",stripe:""},{default:c(()=>[o(d,{type:"index",label:"序号",width:"55",align:"center"}),o(d,{label:"操作时间",align:"center",prop:"stockDate"}),o(d,{label:"单号",align:"center",prop:"stockNo"}),o(d,{label:"类型",align:"center",prop:"type"}),o(d,{label:"有效期",align:"center",prop:"expiryDate"}),o(d,{label:"出/入库人",align:"center",prop:"stockPerson"}),o(d,{label:"制单人",align:"center",prop:"creator"}),o(d,{label:"制单日期",align:"center",prop:"createDate"}),o(d,{label:"采购金额",align:"center",prop:"purchaseAmount"}),o(d,{label:"出入数量",align:"center",prop:"quantity"},{default:c(s=>[s.row.mainType=="OUT"?(v(),x("span",qe,i(s.row.quantity),1)):S("",!0),s.row.mainType=="IN"?(v(),x("span",Ue,"+"+i(s.row.quantity),1)):S("",!0)]),_:1}),o(d,{label:"库存数量",align:"center",prop:"stockAmount"})]),_:1},8,["data"])),[[H,t(P)]]),D(o(A,{total:t(T),page:t(a).pageNum,"onUpdate:page":l[5]||(l[5]=s=>t(a).pageNum=s),limit:t(a).pageSize,"onUpdate:limit":l[6]||(l[6]=s=>t(a).pageSize=s),onPagination:V},null,8,["total","page","limit"]),[[I,t(T)>0]])])}}}),Ae=E(Ke,[["__scopeId","data-v-7d068269"]]);export{Ae as default};
