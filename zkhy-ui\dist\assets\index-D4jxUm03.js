import{_ as oe,u as ne,r,d as de,C as ue,N as ie,e as i,c as N,o as p,f as e,k as y,h as t,i as s,t as w,l as n,j as k,$ as re,n as C,a0 as ce,K as pe,L as me,J as _e,O as ve,v as fe,x as he}from"./index-B0qHf98Y.js";import{l as ge}from"./tassessmentForm-B23DRaU7.js";import{l as be}from"./telderinfo-BSpoeVyZ.js";import ye from"./bradenPressureUlcerAssessmentScale-CeREIv4l.js";import we from"./facialExpressionPainScale-DIm_KVRK.js";import Ce from"./paperQuality-Sa2uQBH8.js";import Se from"./selfRatingDepressionScale-BCKfVoA4.js";import{g as Ie}from"./assessmentRecord-4xWX4TZA.js";import"./eventBus-BDDolVUG.js";import"./telderAttachement-C4ARfNBy.js";const Ne="/assets/nodata-ys34dHY2.png",m=V=>(fe("data-v-9974f135"),V=V(),he(),V),ke={class:"app-container"},Ve=m(()=>s("td",{class:"tbTitle"},"老人姓名:",-1)),xe={class:"tbvalue"},Te=m(()=>s("td",{class:"tbTitle"},"老人编号:",-1)),Le={class:"tbvalue"},Ue=m(()=>s("td",{class:"tbTitle"},"联系电话:",-1)),Re={class:"tbvalue"},Fe=m(()=>s("td",{class:"tbTitle"},"身份证号:",-1)),De={class:"tbvalue"},Ae=m(()=>s("td",{class:"tbTitle"},"性       别:",-1)),Be={class:"tbvalue"},Ee=m(()=>s("td",{class:"tbTitle"},"年       龄:",-1)),ze={class:"tbvalue"},Pe=m(()=>s("td",{class:"tbTitle"},"床  位  号:",-1)),Qe={class:"tbvalue"},$e=m(()=>s("td",{class:"tbTitle"},"照护等级:",-1)),He={class:"tbvalue"},Oe=m(()=>s("td",{class:"tbTitle"},"护理等级:",-1)),je={class:"tbvalue"},qe={key:0},Je={key:1},Ke={key:0},Me={__name:"index",setup(V){const z=ne(),d=r({}),F=r([]);r();const u=r(""),S=r(),h=r(),x=r(!1),P=r(),D=r(0),g=r(),b=r(),T=r(),{proxy:j}=de(),_=r(0),{sys_yes_no:Ye,sys_user_sex:q,self_careability:Ge,care_level:J,nursing_grade:K,capability_level:We,residential_type:Xe}=j.useDict("sys_yes_no","sys_user_sex","self_careability","care_level","nursing_grade","capability_level","residential_type"),M=ue({elderQueryParams:{pageNum:1,pageSize:10,elderName:"",idCard:"",status:1}}),{elderQueryParams:c}=ie(M);function Q(){const a=z.params.type;g.value=z.params.id,console.log(a,"path--------"),a=="show"?u.value="show":a=="edit"?u.value="edit":a=="add"&&(u.value="add"),Ie(g.value).then(l=>{l.code==200?(console.log(l,"getlist????"),d.value=l.data.elderInfo,console.log(l,"getInfo===="),_.value=l.data.assessmentMethod=="01"?0:1,b.value=l.data.assessmentForm.formName,h.value=l.data.assessmentForm.id,S.value=l.data.assessmentScores[0],T.value=l.data.assessmentForm.formCode):(d.value={},_.value=0)}),ge(c.value).then(l=>{F.value=[],l.rows&&l.rows.length>0&&(F.value=l.rows)})}function Y(){c.value={elderName:null,idCard:null},L()}function G(a){console.log(a,"change"),h.value=a.id,b.value=a.formName,T.value=a.formCode}function W(){h.value=null,b.value=null,T.value=null}function L(){x.value=!0,be(c.value).then(a=>{console.log(a,"res"),P.value=a.rows,D.value=a.total})}function $(a){console.log(a,"handle...."),d.value=a,d.value.avatar=a.avatar,g.value=a.id,x.value=!1}function H(a){a==0?_.value=0:a==1&&(_.value=1)}function U(){console.log("updateList1111111111111111111"),Q()}return Q(),(a,l)=>{const A=i("el-input"),B=i("dict-tag-span"),v=i("el-col"),O=i("el-image"),R=i("el-row"),X=i("el-card"),I=i("el-button"),Z=i("el-option"),ee=i("el-select"),E=i("el-form-item"),le=i("el-form"),f=i("el-table-column"),ae=i("el-table"),te=i("pagination"),se=i("el-dialog");return p(),N("div",ke,[e(X,{class:"assessment-card",shadow:"never"},{default:t(()=>[e(R,null,{default:t(()=>[e(v,{span:20},{default:t(()=>[s("table",null,[s("tr",null,[Ve,s("td",xe,[e(A,{disabled:u.value==="show"||u.value==="edit",modelValue:d.value.elderName,"onUpdate:modelValue":l[0]||(l[0]=o=>d.value.elderName=o),style:{"max-width":"100%"},placeholder:"请选择老人",readonly:"",onClick:L},null,8,["disabled","modelValue"])]),Te,s("td",Le,w(d.value.elderCode),1),Ue,s("td",Re,w(d.value.phone),1)]),s("tr",null,[Fe,s("td",De,w(d.value.idCard),1),Ae,s("td",Be,[e(B,{options:n(q),value:d.value.gender},null,8,["options","value"])]),Ee,s("td",ze,w(d.value.age),1)]),s("tr",null,[Pe,s("td",Qe,w(d.value.roomId)+"-"+w(d.value.bedId),1),$e,s("td",He,[e(B,{options:n(J),value:d.value.careLevel},null,8,["options","value"])]),Oe,s("td",je,[e(B,{options:n(K),value:d.value.nursingLevel},null,8,["options","value"])])])])]),_:1}),e(v,{span:4},{default:t(()=>[s("div",null,[d.value.avatar?(p(),k(O,{key:0,src:d.value.avatar,class:"avatarcss"},null,8,["src"])):y("",!0)])]),_:1})]),_:1})]),_:1}),e(R,{gutter:15,style:{"margin-top":"10px"}},{default:t(()=>[e(v,{span:1.5},{default:t(()=>[e(I,{type:_.value==0?"primary":"",disabled:u.value=="edit"||u.value=="show",icon:n(re),onClick:l[1]||(l[1]=o=>H(0))},{default:t(()=>[C("在线评估")]),_:1},8,["type","disabled","icon"])]),_:1}),e(v,{span:1.5},{default:t(()=>[e(I,{type:_.value==1?"primary":"",disabled:u.value=="edit"||u.value=="show",icon:n(ce),onClick:l[2]||(l[2]=o=>H(1))},{default:t(()=>[C("纸质评估")]),_:1},8,["type","disabled","icon"])]),_:1}),e(v,{span:20},{default:t(()=>[C(" 评估量表："),e(ee,{onChange:G,modelValue:b.value,"onUpdate:modelValue":l[3]||(l[3]=o=>b.value=o),disabled:u.value=="edit"||u.value=="show",clearable:"",placeholder:"请选择评估表单",style:{width:"300px"},onClear:W},{default:t(()=>[(p(!0),N(pe,null,me(F.value,o=>(p(),k(Z,{key:o.id,label:o.formName+" - [ "+o.version+" ]",value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(R,null,{default:t(()=>[e(v,{span:2}),e(v,{span:20},{default:t(()=>[_.value==0?(p(),N("div",qe,[h.value=="30"?(p(),k(n(ye),{key:0,elderId:g.value,isShow:u.value,data:S.value,onUpdateList:U},null,8,["elderId","isShow","data"])):y("",!0),h.value=="25"?(p(),k(n(we),{key:1,elderId:g.value,isShow:u.value,data:S.value,onUpdateList:U},null,8,["elderId","isShow","data"])):y("",!0),h.value=="32"?(p(),k(n(Se),{key:2,elderId:g.value,isShow:u.value,data:S.value,onUpdateList:U},null,8,["elderId","isShow","data"])):y("",!0)])):y("",!0),_.value==1?(p(),N("div",Je,[e(n(Ce),{elderId:g.value,assessmentTitle:b.value,assessmentCode:T.value,assessmentRecordId:h.value,isShow:u.value,data:S.value,onUpdateList:U},null,8,["elderId","assessmentTitle","assessmentCode","assessmentRecordId","isShow","data"])])):y("",!0)]),_:1}),e(v,{span:2})]),_:1}),b.value==null?(p(),N("div",Ke,[e(O,{src:n(Ne),class:"showNodata"},null,8,["src"])])):y("",!0),e(se,{modelValue:x.value,"onUpdate:modelValue":l[8]||(l[8]=o=>x.value=o),class:"elder-dialog-custom",title:"选择老人",width:"60%"},{default:t(()=>[e(le,{model:n(c),rules:a.rules,ref:"userRef","label-width":"80px"},{default:t(()=>[e(R,null,{default:t(()=>[e(E,{label:"用户昵称",prop:"elderName"},{default:t(()=>[e(A,{modelValue:n(c).elderName,"onUpdate:modelValue":l[4]||(l[4]=o=>n(c).elderName=o),placeholder:"请输入用户昵称",maxlength:"30"},null,8,["modelValue"])]),_:1}),e(E,{label:"老人编号",prop:"elderCode"},{default:t(()=>[e(A,{modelValue:n(c).elderCode,"onUpdate:modelValue":l[5]||(l[5]=o=>n(c).elderCode=o),placeholder:"请输入老人编号",maxlength:"30"},null,8,["modelValue"])]),_:1}),e(E,null,{default:t(()=>[e(I,{type:"primary",icon:"Search",onClick:L},{default:t(()=>[C("搜索")]),_:1}),e(I,{icon:"Refresh",onClick:Y},{default:t(()=>[C("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),e(ae,{data:P.value,onRowDblclick:$,border:"",stripe:""},{default:t(()=>[e(f,{type:"index",label:"序号",width:"120"}),e(f,{label:"姓名",prop:"elderName",width:"120"}),e(f,{label:"身份证号",prop:"idCard",width:"200"}),e(f,{label:"年龄",prop:"age",width:"80"}),e(f,{label:"性别",prop:"gender",width:"80"}),e(f,{label:"联系电话",prop:"phone",width:"150"}),e(f,{label:"老人编号",prop:"elderCode"}),e(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(o=>[e(I,{type:"primary",onClick:Ze=>$(o.row),size:a.small},{default:t(()=>[C("选择")]),_:2},1032,["onClick","size"])]),_:1})]),_:1},8,["data"]),_e(e(te,{total:D.value,page:n(c).pageNum,"onUpdate:page":l[6]||(l[6]=o=>n(c).pageNum=o),limit:n(c).pageSize,"onUpdate:limit":l[7]||(l[7]=o=>n(c).pageSize=o),onPagination:L},null,8,["total","page","limit"]),[[ve,D.value>0]])]),_:1},8,["modelValue"])])}}},rl=oe(Me,[["__scopeId","data-v-9974f135"]]);export{rl as default};
