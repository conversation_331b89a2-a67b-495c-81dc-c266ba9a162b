import{_ as me,d as _e,a as ge,r as p,F as ve,e as u,c as P,o as U,f as t,i as _,h as r,n as f,t as b,p as L,l as i,a1 as fe,K as he,L as we,j as be,a2 as ye,ap as Ie,D as xe,aq as Re,M as q,v as Ce,x as Ne,G as M,ar as ke,as as Ve,at as De}from"./index-B0qHf98Y.js";import{g as Se}from"./roommanage-DBG5TiIR.js";import{g as qe}from"./leave-Dd4WELmg.js";const Me=V=>(Ce("data-v-f9b9dcae"),V=V(),Ne(),V),Ye={class:"replace-consumables"},Ee=Me(()=>_("div",{class:"headerTitle"},[_("h2",null,"更换易耗品记录表")],-1)),ze={style:{"text-align":"right"}},Ue={class:"elder-info"},Le=["src"],$e={class:"info"},Be={class:"leaderName"},Te={class:"processIndex"},Oe={class:"seqNo"},Pe={style:{"margin-top":"20px","text-align":"center"}},Ae={class:"paginationBox"},Fe={__name:"completeTable",setup(V){const{proxy:Y}=_e(),{sys_user_sex:A}=Y.useDict("sys_user_sex"),E=ge(),y=p(!1),c=p({pageNum:1,pageSize:20,elderName:"",elderCode:""}),$=p(0),B=p([]),z=p([]),D=p(JSON.parse(localStorage.getItem("userInfo"))),s=p([]);p({});const F=p([]);p([]),p([]),p([]);const Q=p({handoverDate:[{required:!0,message:"请选择日期",trigger:"change"}],buildingId:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼层",trigger:"change"}]}),m=p([]),j=()=>{E.push("/work/nurseworkstation")},G=a=>{const e=s.value.findIndex(n=>n.id===a.elderId);if(e!==-1){const n={serviceDate:q().format("YYYY-MM-DD"),supplyItem:"",quantity:1,total:"",price:0,remark:""};s.value[e].serviceRecords.push(n),m.value=x(s.value)}},J=(a,e)=>{const n=z.value.find(o=>o.itemName===a);n&&(e.total=n.price*(e.quantity||1),e.price=n.price,e.supplyItemId=n.id)},K=(a,e)=>{console.log(a,"row"),console.log(e,"rowItem"),e.price!==void 0?e.total=e.price*a:e.total=0};function T(a){const e=s.value.some(o=>o.elderId==a.id&&o.bedId==a.bedId);if(console.log(e,"isElderExist"),e){M.warning("该老人已存在于表格中,请勿重复添加"),y.value=!1;return}const n={elderId:a.id,nurseId:D.value.userId,nurseName:D.value.userName,...a,serviceRecords:[{serviceDate:q().format("YYYY-MM-DD"),supplyItem:"",quantity:1,price:0,remark:"",seqNo:1}]};s.value.push(n),m.value=x(s.value),console.log(s.value,"originalData.value"),y.value=!1}const H=a=>{const e=s.value.findIndex(n=>n.id==a.row.elderId);console.log(e,"elderIndex"),e!==-1&&(s.value.splice(e,1),m.value=x(s.value))},W=a=>{const e=m.value[a],n=s.value.findIndex(o=>o.id===e.elderId);if(n!==-1){const o=s.value[n].serviceRecords.findIndex(g=>g.serviceDate===e._serviceRecords.serviceDate);o!==-1&&(s.value[n].serviceRecords.length===1?s.value.splice(n,1):s.value[n].serviceRecords.splice(o,1),m.value=x(s.value))}},O=async a=>{var o;if(!s.value.length){M.warning("请选择老人");return}s.value=((o=s.value)==null?void 0:o.map(g=>({...g,status:a})))||[],(await(a=="1"?ke:Ve)(s.value)).code===200?(M.success(a==="1"?"提交成功":"保存成功"),a==="1"&&(Y.$tab.closeOpenPage(),E.push("/work/nurseworkstation"))):M.error(a==="1"?"提交失败":"保存失败")},X=()=>{console.log("取消操作"),Y.$tab.closeOpenPage(),E.push("/work/nurseworkstation")},Z=({row:a,column:e,rowIndex:n,columnIndex:o})=>{const g=["avatar"],v=a[e.property];if(g.includes(e.property)||g.includes(e.label)||g.includes(e.type)){const h=m.value[n-1],C=m.value[n+1],w=v==null,S=h&&h.elderId===a.elderId&&(h[e.property]===v||w),N=C&&C.elderId===a.elderId&&(C[e.property]===v||w);if(S)return{rowspan:0,colspan:0};if(N){let k=1,I=n+1;for(;I<m.value.length&&m.value[I].elderId===a.elderId&&(m.value[I][e.property]===v||w);)k++,I++;if(k>1)return{rowspan:k,colspan:1}}}return{rowspan:1,colspan:1}};function x(a){let e={},n=1;return a.flatMap(o=>{(!o.serviceRecords||o.serviceRecords.length===0)&&(o.serviceRecords=[ee(0)]),e[o.id]||(e[o.id]=n++);const g=e[o.id];return o.serviceRecords.forEach((v,h)=>{v.seqNo=h+1}),o.serviceRecords.map((v,h)=>({...o,processIndex:g,...v,elderId:o.id,_serviceRecords:v}))})}function ee(a){return{seqNo:a+1,serviceDate:q().format("YYYY-MM-DD"),supplyItem:"",quantity:1,price:0,remark:""}}const le=()=>{y.value=!0,c.value.pageNum=1,R()},R=()=>{qe({...c.value}).then(a=>{B.value=a.rows,$.value=a.total})},ae=()=>{c.value.pageNum=1,R()},te=a=>{c.value.pageSize=a,R()},oe=a=>{c.value.pageNum=a,R()};function ne(){c.value={elderName:null,elderCode:null,pageNum:1,pageSize:20},R()}const re=async()=>{const a=await Se();F.value=a.rows||[]},se=()=>{re(),Re({nurseId:D.value.userId,nurseName:D.value.userName,serviceDate:q().format("YYYY-MM-DD")}).then(a=>{a.code==200&&(a.data.length>0?(s.value=a.data,m.value=x(s.value)):m.value=[])}),de()},de=async()=>{const a=await De({pageSize:1e3});z.value=a.rows};return ve(()=>{se()}),(a,e)=>{const n=u("el-button"),o=u("el-table-column"),g=u("el-date-picker"),v=u("el-option"),h=u("el-select"),C=u("el-input-number"),w=u("el-input"),S=u("el-table"),N=u("el-form-item"),k=u("el-row"),I=u("el-form"),ie=u("dict-tag-span"),ce=u("el-scrollbar"),ue=u("el-pagination"),pe=u("el-dialog");return U(),P("div",Ye,[t(n,{type:"primary",onClick:j},{default:r(()=>[f("返回工作台")]),_:1}),Ee,_("div",ze,[t(n,{type:"primary",onClick:le},{default:r(()=>[f("+ 新增老人")]),_:1})]),t(S,{data:i(m),border:"",style:{width:"100%"},"span-method":Z},{default:r(()=>[t(o,{label:"老人信息",width:"200",align:"center",prop:"avatar"},{default:r(l=>[_("div",Ue,[_("img",{src:l.row.avatar,alt:"老人头像",class:"avatar"},null,8,Le),_("div",$e,[_("p",Be,b(l.row.elderName),1),_("p",null,b(l.row.roomNumber?l.row.roomNumber:"")+" "+b(l.row.roomNumber&&l.row.bedNumber?l.row.roomNumber+"-"+l.row.bedNumber:""),1),_("span",Te,b(l.row.processIndex),1)]),t(n,{type:"danger",icon:i(fe),circle:"",onClick:L(d=>H(l),["stop"]),class:"deleteRow"},null,8,["icon","onClick"])])]),_:1}),t(o,{prop:"seqNo",width:"80",align:"center"},{default:r(l=>[_("div",Oe,b(l.row._serviceRecords.seqNo),1)]),_:1}),t(o,{prop:"serviceDate",label:"服务日期",width:"230",align:"center"},{default:r(l=>[t(g,{modelValue:l.row._serviceRecords.serviceDate,"onUpdate:modelValue":d=>l.row._serviceRecords.serviceDate=d,type:"date",placeholder:"选择日期",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(o,{prop:"supplyItem",label:"服务项目",width:"180",align:"center"},{default:r(l=>[t(h,{modelValue:l.row._serviceRecords.supplyItem,"onUpdate:modelValue":d=>l.row._serviceRecords.supplyItem=d,placeholder:"请选择服务项目",onChange:d=>J(d,l.row._serviceRecords)},{default:r(()=>[(U(!0),P(he,null,we(i(z),d=>(U(),be(v,{key:d.id,label:d.itemName,value:d.itemName},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(o,{prop:"quantity",label:"数量",width:"130",align:"center"},{default:r(l=>[t(C,{modelValue:l.row._serviceRecords.quantity,"onUpdate:modelValue":d=>l.row._serviceRecords.quantity=d,min:0,style:{width:"100px"},onChange:d=>K(d,l.row._serviceRecords)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(o,{prop:"price",label:"价格",width:"130",align:"center"},{default:r(l=>[f(" ￥"+b(l.row._serviceRecords.price),1)]),_:1}),t(o,{prop:"total",label:"总价",width:"130",align:"center"},{default:r(l=>[f(" ￥"+b(l.row._serviceRecords.total),1)]),_:1}),t(o,{prop:"remark",label:"备注","min-width":"140",align:"center"},{default:r(l=>[t(w,{modelValue:l.row._serviceRecords.remark,"onUpdate:modelValue":d=>l.row._serviceRecords.remark=d,placeholder:"请输入备注",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(o,{label:"操作",width:"120",align:"center",fixed:"right"},{default:r(l=>[t(n,{type:"primary",icon:i(ye),circle:"",onClick:L(d=>G(l.row),["stop"])},null,8,["icon","onClick"]),t(n,{type:"danger",icon:i(Ie),circle:"",onClick:L(d=>W(l.$index),["stop"])},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"]),_("div",Pe,[t(n,{type:"danger",onClick:e[0]||(e[0]=l=>O("2"))},{default:r(()=>[f("保存")]),_:1}),t(n,{type:"primary",onClick:e[1]||(e[1]=l=>O("1"))},{default:r(()=>[f("提交")]),_:1}),t(n,{onClick:X},{default:r(()=>[f("取消")]),_:1})]),t(pe,{modelValue:i(y),"onUpdate:modelValue":e[6]||(e[6]=l=>xe(y)?y.value=l:null),class:"elder-dialog-custom",title:"选择老人",width:"65%"},{default:r(()=>[t(I,{model:i(c),rules:i(Q),ref:"userRef","label-width":"80px"},{default:r(()=>[t(k,null,{default:r(()=>[t(N,{label:"姓名",prop:"elderName"},{default:r(()=>[t(w,{modelValue:i(c).elderName,"onUpdate:modelValue":e[2]||(e[2]=l=>i(c).elderName=l),placeholder:"请输入姓名",maxlength:"30"},null,8,["modelValue"])]),_:1}),t(N,{label:"老人编号",prop:"elderCode"},{default:r(()=>[t(w,{modelValue:i(c).elderCode,"onUpdate:modelValue":e[3]||(e[3]=l=>i(c).elderCode=l),placeholder:"请输入老人编号",maxlength:"30"},null,8,["modelValue"])]),_:1}),t(N,null,{default:r(()=>[t(n,{type:"primary",icon:"Search",onClick:ae},{default:r(()=>[f("搜索")]),_:1}),t(n,{icon:"Refresh",onClick:ne},{default:r(()=>[f("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),t(ce,{"max-height":"500px"},{default:r(()=>[t(S,{data:i(B),onRowDblclick:T},{default:r(()=>[t(o,{type:"index",label:"序号",width:"120"}),t(o,{label:"老人编号",prop:"elderCode"}),t(o,{label:"姓名",prop:"elderName",width:"120"}),t(o,{label:"身份证号",prop:"idCard",width:"200"}),t(o,{label:"年龄",prop:"age",width:"80"}),t(o,{label:"性别",prop:"gender",width:"80"},{default:r(l=>[t(ie,{options:i(A),value:l.row.gender},null,8,["options","value"])]),_:1}),t(o,{label:"联系电话",prop:"phone",width:"150"}),t(o,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:r(l=>[t(n,{type:"primary",onClick:d=>T(l.row)},{default:r(()=>[f("选择")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),_("div",Ae,[t(ue,{background:"","current-page":i(c).pageNum,"onUpdate:currentPage":e[4]||(e[4]=l=>i(c).pageNum=l),"page-size":i(c).pageSize,"onUpdate:pageSize":e[5]||(e[5]=l=>i(c).pageSize=l),"page-sizes":[10,20,30,40],total:i($),layout:"total, sizes, prev, pager, next, jumper",onSizeChange:te,onCurrentChange:oe},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"])])}}},Je=me(Fe,[["__scopeId","data-v-f9b9dcae"]]);export{Je as default};
