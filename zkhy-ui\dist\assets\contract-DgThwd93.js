import{X as e}from"./index-B0qHf98Y.js";function n(t){return e({url:"/contract/contract/list",method:"get",params:t})}function o(t){return e({url:"/contract/contract",method:"put",data:t})}function d(t){return e({url:"/contract/contract/"+t,method:"delete"})}function u(t){return e({url:"/elderinfo/basicInfo/list",method:"get",params:t})}function s(t){return e({url:"/elderinfo/basicInfo/"+t,method:"get"})}function l(t,r=""){return e({url:"/eldersystem/fileinfo/upload",method:"post",data:t,params:{},headers:{"Content-Type":"multipart/form-data"}})}function c(t,r=""){return e({url:"/eldersystem/fileinfo/uploadByElderId",method:"post",data:t,params:{},headers:{"Content-Type":"multipart/form-data"}})}function i(t){return e({url:"/eldersystem/contract/aggregate/save",method:"post",data:t})}function m(t){return e({url:"/eldersystem/contract/aggregate/update",method:"put",data:t})}function f(t){return e({url:"/eldersystem/contract/aggregate/info/"+t,method:"get"})}function g(t,r){return e({url:"/eldersystem/fileinfo/updateElderId/"+r,method:"put",data:t})}function p(t){return e({url:"/eldersystem/fileinfo/list",method:"get",params:t})}function h(t){return e({url:`/eldersystem/fileinfo/${t}`,method:"delete"})}export{s as a,h as b,l as c,d,m as e,g as f,f as g,u as h,p as i,c as j,n as l,i as s,o as u};
