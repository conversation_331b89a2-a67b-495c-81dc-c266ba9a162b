<template>
    <div class='app-container'><!-- 搜索区域 -->
        <div class='search-container'>
            <el-form ref='queryForm' :inline='true' :model='queryParams' label-width='80px'>
                <el-form-item label='项目名称' prop='name'>
                    <el-input v-model='queryParams.name' clearable placeholder='请输入项目名称' @keyup.enter='handleQuery'/>
                </el-form-item>
                <el-form-item label='费用类型' prop='feeTypes'>
                    <el-select v-model='queryParams.feeTypes' clearable multiple placeholder='请选择费用类型' style='width: 240px'>
                        <el-option v-for='item in feeTypeOptions' :key='item.value' :label='item.label' :value='item.value'/>
                    </el-select>
                </el-form-item>
                <el-form-item label='项目状态' prop='status'>
                    <el-select v-model='queryParams.status' clearable multiple placeholder='请选择项目状态' style='width: 240px'>
                        <el-option v-for='item in statusOptions' :key='item.value' :label='item.label' :value='item.value'/>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type='primary' @click='handleQuery'>搜索</el-button>
                    <el-button @click='resetQuery'>重置</el-button>
                </el-form-item>
            </el-form>
        </div><!-- 操作按钮 -->
        <div class='operation-container'>
            <el-button plain type='primary' @click='handleAdd'>
                <el-icon>
                    <plus/>
                </el-icon>
                新增
            </el-button>
            <el-button :disabled='multiple' plain type='danger' @click="handleStatusChange('disabled')">
                <el-icon>
                    <close/>
                </el-icon>
                停用
            </el-button>
            <el-button :disabled='multiple' plain type='success' @click="handleStatusChange('enabled')">
                <el-icon>
                    <check/>
                </el-icon>
                启用
            </el-button>
        </div><!-- 数据表格 -->
        <el-table v-loading='loading' :data='feeItemList' @selection-change='handleSelectionChange'>
            <el-table-column align='center' min-width='55' type='selection'/>
            <el-table-column label='收费名称' min-width='180' prop='name'/>
            <el-table-column label='费用类型' min-width='120' prop='feeType'>
                <template #default='scope'>
                    <dict-tag :options='feeTypeOptions' :value='scope.row.feeType'/>
                </template>
            </el-table-column>
            <el-table-column label='费用等级' min-width='120' prop='feeLevel'>
                <template #default='scope'>
                    <dict-tag :options='feeLevelOptions' :value='scope.row.feeLevel'/>
                </template>
            </el-table-column>
            <el-table-column align='right' label='单价' min-width='120' prop='price'>
                <template #default='scope'> {{ scope.row.price.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column label='允许优惠' min-width='100' prop='allowDiscount'>
                <template #default='scope'>
                    <dict-tag :options='yesNoOptions' :value='scope.row.allowDiscount'/>
                </template>
            </el-table-column>
            <el-table-column label='项目状态' min-width='100' prop='status'>
                <template #default='scope'>
                    <el-tag :type="scope.row.status === 'enabled' ? 'success' : 'danger'"> {{ scope.row.status === "enabled" ? "启用" : "停用" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label='生效日期' min-width='180' prop='effectiveDate'>
                <template #default='scope'> {{ parseTime(scope.row.effectiveDate, "{y}-{m}-{d}") }}
                </template>
            </el-table-column>
            <el-table-column label='失效日期' prop='expiryDate' width='180'>
                <template #default='scope'> {{ scope.row.expiryDate ? parseTime(scope.row.expiryDate, "{y}-{m}-{d}") : "持续有效" }}
                </template>
            </el-table-column>
            <el-table-column align='center' fixed='right' label='操作' width='180'>
                <template #default='scope'>
                    <el-button size='default' type='text' @click='handleDetail(scope.row)'>
                        <el-icon>
                            <view/>
                        </el-icon>
                        详情
                    </el-button>
                    <el-button size='default' type='text' @click='handleEdit(scope.row)'>
                        <el-icon>
                            <edit/>
                        </el-icon>
                        修改
                    </el-button>
                </template>
            </el-table-column>
        </el-table><!-- 分页 -->
        <pagination v-show='total > 0' v-model:limit='queryParams.pageSize' v-model:page='queryParams.pageNum' :total='total' @pagination='getList'/><!-- 新增/修改对话框 -->
        <el-dialog v-model='open' :close-on-click-modal='false' :title='title' append-to-body width='800px'>
            <el-form ref='form' :model='form1' :rules='rules' label-width='120px'>
                <el-row>
                    <el-col :span='12'>
                        <el-form-item label='项目名称' prop='name'>
                            <el-input v-model='form1.name' placeholder='请输入项目名称'/>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='费用类型' prop='feeType'>
                            <el-select v-model='form1.feeType' placeholder='请选择费用类型' style='width: 100%' @change='handleFeeTypeChange'>
                                <el-option v-for='item in feeTypeOptions' :key='item.value' :label='item.label' :value='item.value'/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='费用等级' prop='feeLevel'>
                            <el-select v-model='form1.feeLevel' placeholder='请选择费用等级' style='width: 100%'>
                                <el-option v-for='item in filteredFeeLevelOptions' :key='item.value' :label='item.label' :value='item.value'/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='单价' prop='price'>
                            <el-input-number v-model='form1.price' :controls='false' :min='0' :precision='2' placeholder='请输入单价' style='width: 100%'/>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='是否允许优惠' prop='allowDiscount'>
                            <el-radio-group v-model='form1.allowDiscount'>
                                <el-radio v-for='item in yesNoOptions' :key='item.value' :label='item.value'>{{ item.label }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='计费周期' prop='billingCycle'>
                            <el-select v-model='form1.billingCycle' placeholder='请选择计费周期' style='width: 100%'>
                                <el-option v-for='item in billingCycleOptions' :key='item.value' :label='item.label' :value='item.value'/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='生效日期' prop='effectiveDate'>
                            <el-date-picker v-model='form1.effectiveDate' placeholder='选择生效日期' style='width: 100%' type='date' value-format='YYYY-MM-DD'/>
                        </el-form-item>
                    </el-col>
                    <el-col :span='12'>
                        <el-form-item label='失效日期' prop='expiryDate'>
                            <el-date-picker v-model='form1.expiryDate' placeholder='选择失效日期' style='width: 100%' type='date' value-format='YYYY-MM-DD'/>
                        </el-form-item>
                    </el-col>
                    <el-col :span='24'>
                        <el-form-item label='项目状态' prop='status'>
                            <el-radio-group v-model='form1.status'>
                                <el-radio label='enabled'>启用</el-radio>
                                <el-radio label='disabled'>停用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span='24'>
                        <el-form-item label='项目描述' prop='description'>
                            <el-input v-model='form1.description' :rows='3' placeholder='请输入项目描述' type='textarea'/>
                        </el-form-item>
                    </el-col>
                    <el-col :span='24'>
                        <el-form-item label='备注' prop='remark'>
                            <el-input v-model='form1.remark' :rows='2' placeholder='请输入备注' type='textarea'/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class='dialog-footer'>
                    <el-button type='primary' @click='submitForm'>确 定</el-button>
                    <el-button @click='cancel'>取 消</el-button>
                </div>
            </template>
        </el-dialog><!-- 详情对话框 -->
        <el-dialog v-model='detailOpen' append-to-body title='收费项目详情' width='700px'>
            <el-descriptions :column='2' border>
                <el-descriptions-item label='项目名称'>{{ currentItem.name }}</el-descriptions-item>
                <el-descriptions-item label='费用类型'>
                    <dict-tag :options='feeTypeOptions' :value='currentItem.feeType'/>
                </el-descriptions-item>
                <el-descriptions-item label='费用等级'>
                    <dict-tag :options='feeLevelOptions' :value='currentItem.feeLevel'/>
                </el-descriptions-item>
                <el-descriptions-item label='单价'>{{ currentItem.price.toFixed(2) }}</el-descriptions-item>
                <el-descriptions-item label='是否允许优惠'>
                    <dict-tag :options='yesNoOptions' :value='currentItem.allowDiscount'/>
                </el-descriptions-item>
                <el-descriptions-item label='计费周期'>
                    <dict-tag :options='billingCycleOptions' :value='currentItem.billingCycle'/>
                </el-descriptions-item>
                <el-descriptions-item label='项目状态'>
                    <el-tag :type="currentItem.status === 'enabled' ? 'success' : 'danger'"> {{ currentItem.status === "enabled" ? "启用" : "停用" }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label='生效日期'>{{ formatDate(currentItem.effectiveDate) }}</el-descriptions-item>
                <el-descriptions-item label='失效日期'>{{ currentItem.expiryDate ? formatDate(currentItem.expiryDate) : "持续有效" }}</el-descriptions-item>
                <el-descriptions-item :span='2' label='项目描述'>{{ currentItem.description }}</el-descriptions-item>
                <el-descriptions-item :span='2' label='备注'>{{ currentItem.remark }}</el-descriptions-item>
            </el-descriptions>
            <template #footer>
                <div class='dialog-footer'>
                    <el-button @click='detailOpen = false'>关 闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script name='FeeItem' setup>
import DictTag from "@/components/DictTag";
import {formatDate} from "@/utils/index.js";
import {parseTime} from "@/utils/ruoyi.js";
import {Check, Close, Edit, Plus, View} from "@element-plus/icons-vue";
import {computed, onMounted, reactive, ref} from "vue";

// 引入ex_data.json
import exData from "./ex_data.json";
// console.log(exData.feeItemListAll);

// 查询参数
const queryParams = reactive({
                                 pageNum : 1,
                                 pageSize: 10,
                                 name    : undefined,
                                 feeTypes: [],
                                 status  : [],
                             });

// 表单数据

const form1 = ref({
                      id           : undefined,
                      name         : "",
                      feeType      : "",
                      feeLevel     : "",
                      price        : 0,
                      allowDiscount: "Y",
                      billingCycle : "monthly",
                      effectiveDate: new Date().toISOString()
                                               .split("T")[0],
                      expiryDate   : undefined,
                      status       : "enabled",
                      description  : "",
                      remark       : "",
                  });

// 数据列表
const feeItemList = ref([]);
const feeItemListAll = exData.feeItemListAll;
const loading = ref(true);
const total = ref(0);
const title = ref("");
const open = ref(false);
const detailOpen = ref(false);
const currentItem = ref({});
const ids = ref([]);
const multiple = ref(true);

// 表单校验
const rules = reactive({
                           name         : [{
                               required: true,
                               message : "项目名称不能为空",
                               trigger : "blur",
                           }],
                           feeType      : [{
                               required: true,
                               message : "请选择费用类型",
                               trigger : "change",
                           }],
                           feeLevel     : [{
                               required: true,
                               message : "请选择费用等级",
                               trigger : "change",
                           }],
                           price        : [
                               {
                                   required: true,
                                   message : "单价不能为空",
                                   trigger : "blur",
                               },
                               {
                                   type   : "number",
                                   min    : 0,
                                   message: "单价必须大于0",
                                   trigger: "blur",
                               },
                           ],
                           effectiveDate: [
                               {
                                   required: true,
                                   message : "请选择生效日期",
                                   trigger : "change",
                               },
                           ],
                           status       : [{
                               required: true,
                               message : "请选择项目状态",
                               trigger : "change",
                           }],
                       });

// 字典选项
const feeTypeOptions = ref(exData.feeTypeOptions);

const feeLevelOptions = ref(exData.feeLevelOptions);

const filteredFeeLevelOptions = computed(() => {
    return feeLevelOptions.value.filter(
        (item) => item.type === form1.value.feeType,
    );
});

const statusOptions = ref(exData.statusOptions);

const yesNoOptions = ref(exData.yesNoOptions);

const billingCycleOptions = ref(exData.billingCycleOptions);

let arrayIncludeItem = function (arr, item) {
    // console.log(arr, item);
    // 判断arr是不是array, 不是返回true
    if (!Array.isArray(arr) || arr.length === 0) return true;
    // 判断item类型是不是字符串,是的情况下不区分大小写比较是不是在数组中，是返回true,不是返回false;不是字符串是直接比较相等
    if (typeof item === "string") {
        return arr.some((v) => v.toLowerCase() === item.toLowerCase());
    } else {
        return arr.some((v) => v === item);
    }

};

// 实现方法:判断一段字符串, 是不是在另一个字符串中出现,不区分大小写比较, 如果是返回true,不是返回false
function includeStr(str, target) {
    // console.log(str, target);
    if (!str || typeof str !== "string") return false;
    if (!target || typeof target !== "string") return true;
    return str.toLowerCase()
              .includes(target.toLowerCase());
}

// 获取列表数据
function getList() {
    // loading.value = false;
    // return ;
    loading.value = true;
    // 模拟API调用
    setTimeout(() => {
        // console.log("查询条件:",JSON.stringify(queryParams));
        let filterd = feeItemListAll.filter(item => {
            if (includeStr(item.name, queryParams.name) &&
                arrayIncludeItem(queryParams.feeTypes, item.feeType) &&
                arrayIncludeItem(queryParams.status, item.status)) {
                return item;
            }
            return false;
        })
                                    .sort((a, b) => {
                                        // effectiveDate字段转为date类型 再进行比较
                                        if (parseTime(a.effectiveDate) < parseTime(b.effectiveDate)) return -1;
                                        else if (parseTime(a.effectiveDate) === parseTime(b.effectiveDate)) return 0;
                                        else return 1;
                                    })
                                    .reverse();

        let start = (queryParams.pageNum - 1) * queryParams.pageSize;
        let value = filterd.slice(start, start + ((filterd.length - start) < queryParams.pageSize ? filterd.length - start : queryParams.pageSize));
        feeItemList.value = value;
        total.value = filterd.length;
        loading.value = false;
    }, 500);
}

// 搜索
function handleQuery() {
    queryParams.pageNum = 1;
    getList();
}

// 重置搜索
function resetQuery() {
    queryParams.name = undefined;
    queryParams.feeTypes = [];
    queryParams.status = [];
    handleQuery();
}

// 多选
function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    multiple.value = !selection.length;
}

// 新增
function handleAdd() {
    resetForm();
    title.value = "新增收费项目";
    open.value = true;
}

// 修改
function handleEdit(row) {
    resetForm();
    title.value = "修改收费项目";
    form1.value = row;
    open.value = true;
}

// 详情
function handleDetail(row) {
    currentItem.value = row;
    detailOpen.value = true;
}

// 提交表单
function submitForm() {
    // 这里应该是调用API保存数据
    if (form1.value.id) {
        console.log("修改记录", JSON.stringify(form1.value));
    } else {
        console.log("新增记录", JSON.stringify(form1.value));
    }
    open.value = false;
    getList();
}

// 取消
function cancel() {
    open.value = false;
}

// 重置表单
function resetForm() {
    form1.value = {
        id           : undefined,
        name         : "",
        feeType      : "",
        feeLevel     : "",
        price        : 0,
        allowDiscount: "Y",
        billingCycle : "monthly",
        effectiveDate: new Date().toISOString()
                                 .split("T")[0],
        expiryDate   : undefined,
        status       : "enabled",
        description  : "",
        remark       : "",
    };
}

// 费用类型变化处理
function handleFeeTypeChange() {
    form1.value.feeLevel = "";
}

// 状态变更
function handleStatusChange(status) {
    // 这里应该是调用API批量修改状态
    getList();
}

// 初始化
onMounted(() => {
    getList();
});
</script>
<style scoped>
.search-container {
    margin-bottom: 20px;
}

.operation-container {
    margin-bottom: 16px;
}
</style>
