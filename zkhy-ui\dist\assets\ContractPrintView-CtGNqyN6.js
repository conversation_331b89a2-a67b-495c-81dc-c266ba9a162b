import{_ as P,u as B,r as m,F as E,e as y,c as v,o as _,i as e,t,K as f,L as g,f as k,h as C,j as J,n as w,v as M,x as R}from"./index-B0qHf98Y.js";import{g as q,a as W}from"./contract-DgThwd93.js";const s=u=>(M("data-v-0a57fc66"),u=u(),R(),u),j={class:"contract-print-view"},F={class:"print-content"},H=s(()=>e("div",{class:"contract-header"},[e("h1",{class:"print-title"},"和孚养老机构入住合同清单")],-1)),O={class:"contract-table"},$=s(()=>e("tr",null,[e("td",{class:"table-header",colspan:"6"},"老人基本信息")],-1)),K=s(()=>e("td",{class:"label-cell"},"老人姓名",-1)),U={class:"value-cell"},z=s(()=>e("td",{class:"label-cell"},"身份证号",-1)),G={class:"value-cell",colspan:"3"},Q=s(()=>e("td",{class:"label-cell"},"年龄",-1)),X={class:"value-cell"},Y=s(()=>e("td",{class:"label-cell"},"性别",-1)),Z={class:"value-cell"},ee=s(()=>e("td",{class:"label-cell"},"联系电话",-1)),le={class:"value-cell"},te=s(()=>e("td",{class:"label-cell"},"监护人姓名",-1)),se={class:"value-cell"},ae=s(()=>e("td",{class:"label-cell"},"与老人关系",-1)),ce={class:"value-cell"},oe=s(()=>e("td",{class:"label-cell"},"监护人电话",-1)),ne={class:"value-cell"},de=s(()=>e("td",{class:"label-cell"},"监护人身份证",-1)),re={class:"value-cell",colspan:"5"},ie=s(()=>e("td",{class:"label-cell"},"监护人地址",-1)),_e={class:"value-cell",colspan:"5"},ue=s(()=>e("td",{class:"label-cell"},"养老机构",-1)),ve={class:"value-cell"},he=s(()=>e("td",{class:"label-cell"},"床位号",-1)),pe={class:"value-cell"},me=s(()=>e("td",{class:"label-cell"},"房间类型",-1)),be={class:"value-cell"},ye=s(()=>e("td",{class:"label-cell"},"合同编号",-1)),fe={class:"value-cell"},ge=s(()=>e("td",{class:"label-cell"},"签约时间",-1)),ke={class:"value-cell"},Ce=s(()=>e("td",{class:"label-cell"},"支付类型",-1)),Ie={class:"value-cell"},Le=s(()=>e("td",{class:"label-cell"},"照护等级",-1)),we={class:"value-cell"},Ne=s(()=>e("td",{class:"label-cell"},"护理等级",-1)),xe={class:"value-cell"},Se=s(()=>e("td",{class:"label-cell"},"能力评估",-1)),Ae={class:"value-cell"},De=s(()=>e("td",{class:"label-cell"},"合同期限",-1)),Ve={class:"value-cell",colspan:"5"},Te=s(()=>e("tr",null,[e("td",{class:"table-header",colspan:"3"},"收费项目"),e("td",{class:"table-header",colspan:"3"},"费用")],-1)),Pe={class:"value-cell",colspan:"3"},Be={class:"value-cell",colspan:"3"},Ee={class:"table-header",colspan:"6"},Je={colspan:"6",class:"care-items-cell"},Me={class:"care-items-container"},Re={class:"care-items-grid"},qe={class:"label-cell"},We={class:"value-cell",colspan:"5"},je=s(()=>e("td",{class:"label-cell"},"其他事项",-1)),Fe={class:"value-cell",colspan:"5"},He={class:"print-button-container"},Oe={__name:"ContractPrintView",props:{contractId:{type:[String,Number],required:!1}},emits:["close"],setup(u,{emit:N}){const x=u,S=N,A=B(),l=m({}),I=m([]),h=m([]),p=m([]),D=["整理床单元","床头柜整理","床单，被套更换洗","老人衣服换洗","物品整齐摆放","出轨内衣物整理","晨间协助老人洗漱","房间内垃圾桶倾倒","老人足部洗脚","加压清洗","定时洗澡","胃管老人口腔护理","气垫床使用","协助排便","提醒，协助老人服药","每月flix","水瓶内接水","尿不湿会阴区护理","尿袋倒尿","按时喂水，提醒喝水","定时更换导尿管","生活用品清洗","护理垫，纸尿裤更换","失能老人每2小时翻身"];E(async()=>{const i=x.contractId||A.params.id;if(i)try{const a=await q(i),{contract:d,contractService:c,feeDetails:r}=a.data||{};if(l.value=d||{},d&&d.paymentDate)try{p.value=d.paymentDate?JSON.parse(d.paymentDate):[],l.value.paymentDates=p.value.join(", ")}catch{p.value=[],l.value.paymentDates=""}else p.value=[],l.value.paymentDates="";if(c&&(l.value={...l.value,careLevel:c.careLevel,nursingLevel:c.nursingLevel,abilityAssessment:c.abilityAssessmentResult,carePlan:c.carePlan,remarks:c.remark,recorderName:c.recorderName,careLevel2:c.careLevel2,care_level_2:c.care_level_2||c.careLevel2||""},c.serviceItemsJson))try{h.value=JSON.parse(c.serviceItemsJson)}catch{h.value=[]}if(I.value=Array.isArray(r)?r:[],d&&d.elderId)try{const o=await W(d.elderId),n=o.data||o.rows&&o.rows[0];n&&(l.value={...l.value,elderName:n.elderName,idCard:n.idCard,age:n.age,gender:n.gender==="1"?"男":n.gender==="0"?"女":"-",phone:n.phone,elderCode:n.elderCode})}catch(o){console.error("获取老人信息失败:",o)}}catch(a){console.error("获取合同详情失败:",a)}});const V=()=>{const i=document.querySelector(".print-content");if(!i)return;let a=document.createElement("iframe");a.style.position="fixed",a.style.right="0",a.style.bottom="0",a.style.width="0",a.style.height="0",a.style.border="0",document.body.appendChild(a);const d=Array.from(document.querySelectorAll('style, link[rel="stylesheet"]'));let c="";d.forEach(o=>{c+=o.outerHTML});const r=a.contentWindow.document;r.open(),r.write(`
    <html>
      <head>
        <title>打印</title>
        ${c}
        <style>
          body { background: #fff; margin: 0; }
          .print-button-container { display: none; }
          .close-btn { display: none; }
        </style>
      </head>
      <body>
        <div class="print-content">${i.innerHTML}</div>
      </body>
    </html>
  `),r.close(),a.onload=()=>{a.contentWindow.focus(),a.contentWindow.print(),setTimeout(()=>{document.body.removeChild(a)},100)}},T=()=>{S("close")};return(i,a)=>{const d=y("el-checkbox"),c=y("el-checkbox-group"),r=y("el-button");return _(),v("div",j,[e("div",F,[H,e("table",O,[$,e("tr",null,[K,e("td",U,t(l.value.elderName),1),z,e("td",G,t(l.value.idCard),1)]),e("tr",null,[Q,e("td",X,t(l.value.age),1),Y,e("td",Z,t(l.value.gender),1),ee,e("td",le,t(l.value.phone),1)]),e("tr",null,[te,e("td",se,t(l.value.guardianName),1),ae,e("td",ce,t(l.value.guardianRelation),1),oe,e("td",ne,t(l.value.guardianPhone),1)]),e("tr",null,[de,e("td",re,t(l.value.guardianIdcard),1)]),e("tr",null,[ie,e("td",_e,t(l.value.guardianAddress),1)]),e("tr",null,[ue,e("td",ve,t(l.value.orgName),1),he,e("td",pe,t(l.value.bedNo),1),me,e("td",be,t(l.value.roomType),1)]),e("tr",null,[ye,e("td",fe,t(l.value.contractNo),1),ge,e("td",ke,t(l.value.signTime),1),Ce,e("td",Ie,t(l.value.paymentType),1)]),e("tr",null,[Le,e("td",we,t(l.value.careLevel),1),Ne,e("td",xe,t(l.value.nursingLevel),1),Se,e("td",Ae,t(l.value.abilityAssessment),1)]),e("tr",null,[De,e("td",Ve,t(l.value.contractStarttime)+" ~ "+t(l.value.contractEndtime),1)]),Te,(_(!0),v(f,null,g(I.value,(o,n)=>(_(),v("tr",{key:n},[e("td",Pe,t(o.feeItem),1),e("td",Be,t(o.feeStandard),1)]))),128)),e("tr",null,[e("td",Ee,t(l.value.nursingLevel),1)]),e("tr",null,[e("td",Je,[e("div",Me,[e("div",Re,[k(c,{modelValue:h.value,"onUpdate:modelValue":a[0]||(a[0]=o=>h.value=o),disabled:i.isViewMode},{default:C(()=>[(_(),v(f,null,g(3,(o,n)=>e("div",{key:n,class:"care-column"},[(_(!0),v(f,null,g(D.filter((b,L)=>L%3===n),(b,L)=>(_(),J(d,{disabled:"",key:b,label:b,class:"care-checkbox"},null,8,["label"]))),128))])),64))]),_:1},8,["modelValue","disabled"])])])])]),e("tr",null,[e("td",qe,t(l.value.careLevel),1),e("td",We,t(l.value.care_level_2||l.value.careLevel2),1)]),e("tr",null,[je,e("td",Fe,t(l.value.remark),1)])]),e("div",He,[k(r,{type:"primary",onClick:V},{default:C(()=>[w("打印")]),_:1}),k(r,{onClick:T},{default:C(()=>[w("关闭")]),_:1})])])])}}},Ue=P(Oe,[["__scopeId","data-v-0a57fc66"]]);export{Ue as default};
