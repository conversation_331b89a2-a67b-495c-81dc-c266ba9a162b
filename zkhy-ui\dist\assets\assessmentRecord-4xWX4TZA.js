import{X as e}from"./index-B0qHf98Y.js";function n(s){return e({url:"/assessment/assessmentRecord/list",method:"get",params:s})}function r(s){return e({url:"/assessment/assessmentRecord/"+s,method:"get"})}function a(s){return e({url:"/assessment/assessmentRecord",method:"post",data:s})}function o(s){return e({url:"/assessment/assessmentRecord",method:"put",data:s})}function d(s){return e({url:"/assessment/assessmentRecord/"+s,method:"delete"})}export{a,d,r as g,n as l,o as u};
