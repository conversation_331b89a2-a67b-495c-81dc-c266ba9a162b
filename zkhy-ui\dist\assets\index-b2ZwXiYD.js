import{_ as V,a as P,r as w,F as E,e as C,c as u,o as d,i as e,f as l,h as m,n as x,K as b,L as I,l as p,bo as q,bc as G,Q as B,t as _,bp as K,j as Q,b0 as R,a1 as U,G as k,v as H,x as J,E as W}from"./index-B0qHf98Y.js";import{c as X,r as Y}from"./index-CjVbTVeO.js";import{l as Z}from"./telderAttachement-C4ARfNBy.js";const g=v=>(H("data-v-108d0ffa"),v=v(),J(),v),ee={class:"org-management-container"},se={class:"header-bar"},ae=g(()=>e("div",{class:"page-title"},"机构管理",-1)),te={class:"org-list"},ne={class:"image-section"},oe={class:"main-image"},ce=["src"],ie=["onClick"],le=["onClick"],re={class:"thumbnail-list"},de=["onClick"],ue=["src"],me={class:"info-section"},_e={class:"org-name"},ge={class:"rating-section"},he={class:"stars"},pe={class:"basic-info"},ve={class:"info-item"},fe=g(()=>e("span",{class:"label"},"床位数：",-1)),be={class:"value"},Ie={class:"info-item"},ke=g(()=>e("span",{class:"label"},"建筑面积：",-1)),ye={class:"value"},we={class:"info-item"},Ce=g(()=>e("span",{class:"label"},"咨询电话：",-1)),xe={class:"value"},Be={class:"info-item"},Le=g(()=>e("span",{class:"label"},"机构地址：",-1)),je={class:"value"},Ee={class:"service-tags"},Ne={class:"action-buttons"},Se=g(()=>e("div",{class:"card-divider"},null,-1)),$e={__name:"index",setup(v){const L=P(),i=w([]),N=w(0),j=w(!1),S=async a=>{try{const s=await Z({pageNum:1,pageSize:100,elderId:a,attachmentType:"cover_photo"});if(s.code===200&&s.rows&&s.rows.length>0){const t=s.rows.map(o=>o.filePath).filter(Boolean);return{mainImages:t,thumbnails:t}}else return{mainImages:["/src/assets/images/login-background.jpg"],thumbnails:["/src/assets/images/login-background.jpg"]}}catch(s){return console.error(`加载机构${a}图片失败:`,s),{mainImages:["/src/assets/images/login-background.jpg"],thumbnails:["/src/assets/images/login-background.jpg"]}}},y=async()=>{try{j.value=!0;const a=await X({pageNum:1,pageSize:10});if(a.code===200){const s=a.rows.map(t=>{var o,r,n;return{id:t.id,name:t.orgName,doorNumber:((r=(o=t.address)==null?void 0:o.match(/\d+/))==null?void 0:r[0])||"",starLevel:t.starLevel,bedCount:t.bedCount,buildingArea:t.floorArea,phone:t.consultPhone,address:t.address,mainImages:["/src/assets/images/login-background.jpg"],thumbnails:["/src/assets/images/login-background.jpg"],services:((n=t.serviceItems)==null?void 0:n.split(",").filter(Boolean))||[],currentImageIndex:0,rawData:t}});i.value=s,N.value=a.total,s.forEach(async(t,o)=>{const r=await S(t.id);i.value[o]&&i.value[o].id===t.id&&(i.value[o].mainImages=r.mainImages,i.value[o].thumbnails=r.thumbnails)})}}catch(a){console.error("加载机构列表失败:",a),k.error("加载机构列表失败")}finally{j.value=!1}};E(()=>{y()});const $=(a,s)=>{i.value[a].currentImageIndex=s},z=a=>{const s=i.value[a];s.currentImageIndex=(s.currentImageIndex-1+s.mainImages.length)%s.mainImages.length},F=(a,s)=>{const t=i.value[a];t.currentImageIndex=(t.currentImageIndex+1)%s},O=a=>{switch(a){case"助医服务":return"tag-blue";case"康复服务":return"tag-orange";case"文化娱乐":return"tag-purple";case"健康指导":return"tag-pink";case"呼叫服务":return"tag-yellow";case"代办服务":return"tag-green";case"专业护理":return"tag-lightgreen";case"助餐服务":return"tag-lightpurple";case"助浴服务":return"tag-lightblue";default:return"tag-default"}},T=()=>{L.push({path:"/orgmanagement/addForm/add/0/add"})},A=a=>{L.push({path:`/orgmanagement/addForm/add/${a}/edit`,query:{mode:"edit"}})},M=async a=>{try{await W.confirm("确定要删除该机构吗？删除后将无法恢复！","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const s=await Y(a);s.code===200?(k.success("删除成功"),await y()):k.error(s.msg||"删除失败")}catch(s){s!=="cancel"&&(console.error("删除机构失败:",s),k.error("删除失败："+(s.message||"未知错误")))}},D=()=>{i.value.forEach((a,s)=>{a.mainImages&&a.mainImages.length>0&&(a.currentImageIndex=0)})};return E(()=>{y(),D()}),(a,s)=>{const t=C("el-button"),o=C("el-icon"),r=C("el-tag");return d(),u("div",ee,[e("div",se,[ae,l(t,{type:"primary",class:"add-org-btn",onClick:T},{default:m(()=>[x("+ 新增机构")]),_:1})]),e("div",te,[(d(!0),u(b,null,I(i.value,(n,f)=>(d(),u("div",{key:n.id||f,class:"org-card"},[e("div",ne,[e("div",oe,[e("img",{src:n.mainImages[n.currentImageIndex||0],class:"carousel-image"},null,8,ce),e("button",{class:"arrow left",onClick:c=>z(f)},[l(o,null,{default:m(()=>[l(p(q))]),_:1})],8,ie),e("button",{class:"arrow right",onClick:c=>F(f,n.mainImages.length)},[l(o,null,{default:m(()=>[l(p(G))]),_:1})],8,le)]),e("div",re,[(d(!0),u(b,null,I(n.thumbnails,(c,h)=>(d(),u("div",{key:h,class:B(["thumbnail-item",{active:(n.currentImageIndex||0)===h}]),onClick:ze=>$(f,h)},[e("img",{src:c},null,8,ue)],10,de))),128))])]),e("div",me,[e("div",_e,_(n.name),1),e("div",ge,[l(r,{type:"primary",class:"star-level"},{default:m(()=>[x("星级")]),_:1}),e("div",he,[(d(),u(b,null,I(5,c=>l(o,{key:c,class:B(["star-icon",{active:c<=(n.starLevel||5)}])},{default:m(()=>[l(p(K))]),_:2},1032,["class"])),64))])]),e("div",pe,[e("div",ve,[fe,e("span",be,_(n.bedCount||"2000")+"张",1)]),e("div",Ie,[ke,e("span",ye,_(n.buildingArea||"899971.61")+"㎡",1)]),e("div",we,[Ce,e("span",xe,_(n.phone||"010-89869008"),1)]),e("div",Be,[Le,e("span",je,_(n.address||"北京市朝阳区百子湾南二路92号"),1)])]),e("div",Ee,[(d(!0),u(b,null,I(n.services,(c,h)=>(d(),Q(r,{key:h,class:B("service-tag "+O(c)),"disable-transitions":""},{default:m(()=>[x(_(c),1)]),_:2},1032,["class"]))),128))]),e("div",Ne,[l(t,{type:"success",size:"small",icon:p(R),circle:"",onClick:c=>A(n.id)},null,8,["icon","onClick"]),l(t,{type:"danger",size:"small",icon:p(U),circle:"",onClick:c=>M(n.id)},null,8,["icon","onClick"])])]),Se]))),128))])])}}},Ae=V($e,[["__scopeId","data-v-108d0ffa"]]);export{Ae as default};
