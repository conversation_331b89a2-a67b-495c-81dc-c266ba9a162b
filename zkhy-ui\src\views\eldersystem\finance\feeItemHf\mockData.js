// 生成随机收费项目数据
import {toRaw} from "vue";

export function generateFeeItems(param1 = {
    pageNum : 0,
    pageSize: 10,
}) {
    let param = toRaw(param1);
    console.log(param);
    const feeTypes = ["bed", "meal", "nursing", "other"];
    const feeLevels = {
        bed    : ["standard", "vip", "luxury"],
        meal   : ["basic", "premium"],
        nursing: ["level1", "level2", "level3"],
        other  : ["other1", "other2"],
    };
    const billingCycles = ["daily", "weekly", "monthly", "quarterly", "yearly", "once"];
    const statuses = ["enabled", "disabled"];

    const items = [];
    const now = new Date();

    // 生成随机日期（过去2年内）
    function randomPastDate() {
        const pastDate = new Date();
        pastDate.setFullYear(pastDate.getFullYear() - 2);
        pastDate.setDate(pastDate.getDate() + Math.floor(Math.random() * 730));
        return pastDate.toISOString()
                       .split("T")[0];
    }

    // 生成随机最近日期（过去30天内）
    function randomRecentDate() {
        const recentDate = new Date();
        recentDate.setDate(recentDate.getDate() - Math.floor(Math.random() * 30));
        return recentDate.toISOString()
                         .split("T")[0];
    }

    // 生成随机单词
    function randomWords(count) {
        const words = ["服务", "费用", "标准", "高级", "基础", "护理", "床位", "餐饮", "其他", "特殊"];
        let result = "";
        for (let i = 0; i < count; i++) {
            result += words[Math.floor(Math.random() * words.length)];
            if (i < count - 1) result += " ";
        }
        return result;
    }

    // 生成随机数字
    function randomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 生成床位费
    for (let i = 1; i <= 30; i++) {
        const feeType = "bed";
        const feeLevel = feeLevels[feeType][Math.floor(Math.random() * feeLevels[feeType].length)];
        const price = randomNumber(800, 5000);
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        items.push({
                       id           : `bed-${i}`,
                       name         : `${feeLevel === "standard" ? "标准" : feeLevel === "vip" ? "VIP" : "豪华"}床位费${i}型`,
                       feeType,
                       feeLevel,
                       price,
                       allowDiscount: Math.random() > 0.5 ? "Y" : "N",
                       billingCycle : "monthly",
                       effectiveDate: randomPastDate(),
                       expiryDate   : status === "disabled" ? randomRecentDate() : null,
                       status,
                       description  : `${feeLevel === "standard" ? "标准" : feeLevel === "vip" ? "VIP" : "豪华"}床位服务`,
                       remark       : randomWords(3),
                   });
    }

    // 生成餐费
    for (let i = 1; i <= 20; i++) {
        const feeType = "meal";
        const feeLevel = feeLevels[feeType][Math.floor(Math.random() * feeLevels[feeType].length)];
        const price = randomNumber(15, 80);
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        items.push({
                       id           : `meal-${i}`,
                       name         : `${feeLevel === "basic" ? "基础" : "高级"}餐费${i}型`,
                       feeType,
                       feeLevel,
                       price,
                       allowDiscount: "N",
                       billingCycle : "daily",
                       effectiveDate: randomPastDate(),
                       expiryDate   : status === "disabled" ? randomRecentDate() : null,
                       status,
                       description  : `${feeLevel === "basic" ? "基础" : "高级"}餐饮服务`,
                       remark       : randomWords(3),
                   });
    }

    // 生成护理费
    for (let i = 1; i <= 30; i++) {
        const feeType = "nursing";
        const feeLevel = feeLevels[feeType][Math.floor(Math.random() * feeLevels[feeType].length)];
        const price = randomNumber(50, 300);
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        items.push({
                       id           : `nursing-${i}`,
                       name         : `${feeLevel === "level1" ? "一级" : feeLevel === "level2" ? "二级" : "三级"}护理费`,
                       feeType,
                       feeLevel,
                       price,
                       allowDiscount: Math.random() > 0.5 ? "Y" : "N",
                       billingCycle : "daily",
                       effectiveDate: randomPastDate(),
                       expiryDate   : status === "disabled" ? randomRecentDate() : null,
                       status,
                       description  : `${feeLevel === "level1" ? "基础" : feeLevel === "level2" ? "中等" : "高级"}护理服务`,
                       remark       : randomWords(3),
                   });
    }

    // 生成杂项费用
    for (let i = 1; i <= 20; i++) {
        const feeType = "other";
        const feeLevel = feeLevels[feeType][Math.floor(Math.random() * feeLevels[feeType].length)];
        const price = randomNumber(10, 500);
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        items.push({
                       id           : `other-${i}`,
                       name         : `杂项费用${i}`,
                       feeType,
                       feeLevel,
                       price,
                       allowDiscount: Math.random() > 0.5 ? "Y" : "N",
                       billingCycle : billingCycles[Math.floor(Math.random() * billingCycles.length)],
                       effectiveDate: randomPastDate(),
                       expiryDate   : status === "disabled" ? randomRecentDate() : null,
                       status,
                       description  : `其他服务费用${i}`,
                       remark       : randomWords(3),
                   });
    }
    console.log((param.pageNum - 1) * param.pageSize, param.pageSize);
    return items.filter(item => {
        console.log(item.name.toLowerCase()
            , (param.name?.toLowerCase()));
        console.log((param.name ? item.name.toLowerCase()
            == (param.name?.toLowerCase()) : true));

        if ((param.name ? item.name.toLowerCase().includes(param.name?.toLowerCase()) : true)
            &&
            // (param.feeType[0] ? item.feeType?.toLowerCase()
            //     == (param.feeType[0]?.toLowerCase()) : true) &&
            (param.feeLevel ? item.feeLevel?.toLowerCase()
                == (param.feeLevel?.toLowerCase()) : true) &&
            (param.billingCycle ? item.billingCycle?.toLowerCase()
                == (param.billingCycle?.toLowerCase()) : true) &&
            (param.status[0] ? item.status.toLowerCase()
                == (param.status[0].toLowerCase()) : true)) {
            return item;
        }
        return false;
    }).slice((param.pageNum - 1) * param.pageSize, param.pageSize);
}

// 示例数据导出
export const sampleFeeItems = [
    {
        id           : "bed-1",
        name         : "标准床位费A型",
        feeType      : "bed",
        feeLevel     : "standard",
        price        : 1200,
        allowDiscount: "Y",
        billingCycle : "monthly",
        effectiveDate: "2023-01-01",
        expiryDate   : null,
        status       : "enabled",
        description  : "标准床位服务",
        remark       : "包含基本设施使用",
    },
    {
        id           : "meal-1",
        name         : "基础餐费A型",
        feeType      : "meal",
        feeLevel     : "basic",
        price        : 25,
        allowDiscount: "N",
        billingCycle : "daily",
        effectiveDate: "2023-01-01",
        expiryDate   : null,
        status       : "enabled",
        description  : "基础餐饮服务",
        remark       : "一日三餐",
    },
    {
        id           : "nursing-1",
        name         : "一级护理费",
        feeType      : "nursing",
        feeLevel     : "level1",
        price        : 80,
        allowDiscount: "Y",
        billingCycle : "daily",
        effectiveDate: "2023-01-01",
        expiryDate   : null,
        status       : "enabled",
        description  : "基础护理服务",
        remark       : "每日一次基础护理",
    }];
