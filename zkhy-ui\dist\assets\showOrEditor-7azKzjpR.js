import{g as H,u as K,a as j}from"./tMedicationInventoryRecord-DEKqwOhj.js";import{g as G}from"./telderinfo-BSpoeVyZ.js";import{_ as J,B as W,d as X,r as h,C as Z,N as ee,e as d,c as T,o as v,f as a,h as o,l,i as t,t as c,j as g,k as x,n as w,K as te,L as le,v as ae,x as oe}from"./index-B0qHf98Y.js";const u=b=>(ae("data-v-48bc17d8"),b=b(),oe(),b),se={class:"app-container"},ne={class:"section"},de=u(()=>t("div",{class:"section-title"},"老人信息",-1)),re={class:"tbcss"},ue=u(()=>t("th",{class:"tbTr"},"老人姓名",-1)),ie={class:"tbTrVal"},ce=u(()=>t("th",{class:"tbTr"},"老人编号",-1)),_e={class:"tbTrVal"},pe=u(()=>t("th",{class:"tbTr"},"性       别",-1)),me={class:"tbTrVal"},fe={key:1},ve=u(()=>t("th",{class:"tbTr"},"床位编号",-1)),he={class:"tbTrVal"},be=u(()=>t("th",{class:"tbTr"},"房间信息",-1)),ye={class:"tbTrVal"},ge=u(()=>t("th",{class:"tbTr"},"年       龄",-1)),Ve={class:"tbTrVal"},ke=u(()=>t("th",{class:"tbTr"},"楼栋信息",-1)),Te={class:"tbTrVal"},xe=u(()=>t("th",{class:"tbTr"},"楼层信息",-1)),we={class:"tbTrVal"},Ie=u(()=>t("th",{class:"tbTr"},"护理等级",-1)),Ne={class:"tbTrVal"},Ce=u(()=>t("th",{class:"tbTr"},"入住时间",-1)),Ue={class:"tbTrVal"},De={class:"section"},Re=u(()=>t("div",{class:"section-title"},"药品清点",-1)),Be={style:{margin:"0px 8px 12px 10px","font-weight":"600",color:"#555"}},Se={style:{"margin-left":"10px"}},Ye={class:"footerLeft"},Le={class:"footerLeftMargin"},Qe={class:"dialog-footer"},Me=W({name:"Notice"}),Pe=Object.assign(Me,{emits:"close",setup(b,{expose:U,emit:D}){const{proxy:V}=X(),{inventory_results:R,sys_user_sex:B}=V.useDict("inventory_results","sys_user_sex"),I=D;h([]);const m=h(!1);h(!0);const i=h(!1),k=h(""),N=h(!1),S=Z({form:{},queryParams:{pageNum:1,pageSize:10},rules:{}}),{queryParams:Ee,form:e,rules:Y}=ee(S);function L(p){m.value=!0,p.type=="show"?(i.value=!0,k.value="查看药品清点记录"):p.type=="edit"&&(i.value=!1,N.value=!0,k.value="修改药品清点记录"),H(p.id).then(s=>{console.log(s,"res21212"),e.value=s.data,m.value=!0,G(s.data.elderId).then(r=>{e.value.gender=r.data.elderInfo.gender,e.value.age=r.data.elderInfo.age,e.value.nursingLevel=r.data.elderInfo.nursingLevel,e.value.checkInDate=r.data.elderInfo.checkInDate,e.value.avatar=r.data.elderInfo.avatar})})}function Q(){console.log(e.value,"111111"),e.value.id!=null?(console.log(e.value,"222222"),K(e.value).then(p=>{V.$modal.msgSuccess("修改成功"),m.value=!1,I("close")})):(console.log(e.value,"333333"),j(e.value).then(p=>{V.$modal.msgSuccess("新增成功"),m.value=!1,I("close")}))}function M(){m.value=!1,reset()}return U({init:L}),(p,s)=>{const r=d("el-input"),P=d("dict-tag-span"),y=d("el-row"),_=d("el-col"),E=d("el-avatar"),$=d("el-date-picker"),f=d("el-form-item"),q=d("el-option"),z=d("el-select"),F=d("el-card"),O=d("el-form"),C=d("el-button"),A=d("el-dialog");return v(),T("div",se,[a(A,{title:k.value,modelValue:m.value,"onUpdate:modelValue":s[9]||(s[9]=n=>m.value=n),width:"60%","append-to-body":""},{footer:o(()=>[t("div",Ye,[t("div",Le,[a(f,{label:"记录人",prop:"recorder"},{default:o(()=>[a(r,{modelValue:l(e).recorder,"onUpdate:modelValue":s[8]||(s[8]=n=>l(e).recorder=n),placeholder:"请输入记录人",disabled:!0},null,8,["modelValue"])]),_:1})]),t("div",Qe,[i.value?x("",!0):(v(),g(C,{key:0,type:"primary",onClick:Q},{default:o(()=>[w("确 定")]),_:1})),a(C,{onClick:M},{default:o(()=>[w("返 回")]),_:1})])])]),default:o(()=>[a(O,{ref:"inventoryRecordRef",model:p.card,rules:l(Y),"label-width":"80px"},{default:o(()=>[t("div",ne,[de,a(y,null,{default:o(()=>[a(_,{span:20},{default:o(()=>[a(y,{gutter:24},{default:o(()=>[t("table",re,[t("tr",null,[ue,t("th",ie,[a(r,{modelValue:l(e).elderName,"onUpdate:modelValue":s[0]||(s[0]=n=>l(e).elderName=n),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},onClick:p.searchElderHandle,disabled:i.value},null,8,["modelValue","onClick","disabled"])]),ce,t("th",_e,c(l(e).elderCode||"-"),1),pe,t("th",me,[l(e).gender?(v(),g(P,{key:0,options:l(B),value:l(e).gender},null,8,["options","value"])):(v(),T("span",fe,"-"))])]),t("tr",null,[ve,t("th",he,c(l(e).roomNumber||"")+"-"+c(l(e).bedNumber||""),1),be,t("th",ye,c(l(e).roomNumber||"-"),1),ge,t("th",Ve,c(l(e).age||"-"),1)]),t("tr",null,[ke,t("th",Te,c(l(e).buildingName||"-"),1),xe,t("th",we,c(l(e).floorNumber||"-"),1),Ie,t("th",Ne,c(l(e).nursingLevel||"-"),1)]),t("tr",null,[Ce,t("th",Ue,c(l(e).checkInDate||"-"),1)])])]),_:1})]),_:1}),a(_,{span:4},{default:o(()=>[l(e).avatar?(v(),g(E,{key:0,shape:"square",size:140,fit:"fill",src:l(e).avatar},null,8,["src"])):x("",!0)]),_:1})]),_:1})]),t("div",De,[Re,a(F,{class:"shadow-md hover:shadow-lg transition-shadow",style:{"margin-bottom":"10px"}},{default:o(()=>[a(y,null,{default:o(()=>[a(_,{span:23},{default:o(()=>[t("div",Be,[w(" 药品名称 "),t("span",Se,c(l(e).medicineName),1),x("",!0)]),a(y,null,{default:o(()=>[a(_,{span:8},{default:o(()=>[a(f,{label:"清点日期",prop:"recordTime"},{default:o(()=>[a($,{clearable:"",modelValue:l(e).recordTime,"onUpdate:modelValue":s[2]||(s[2]=n=>l(e).recordTime=n),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择清点日期",format:"YYYY-MM-DD",style:{width:"200px"},disabled:i.value||N.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(f,{label:"已派发",prop:"distributedQuantity"},{default:o(()=>[a(r,{modelValue:l(e).distributedQuantity,"onUpdate:modelValue":s[3]||(s[3]=n=>l(e).distributedQuantity=n),placeholder:"请输入已派发",style:{width:"200px"},disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(f,{label:"剩余数量",prop:"remainingQuantity"},{default:o(()=>[a(r,{modelValue:l(e).remainingQuantity,"onUpdate:modelValue":s[4]||(s[4]=n=>l(e).remainingQuantity=n),placeholder:"请输入剩余数量",style:{width:"200px"},disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(f,{label:"清点结果",prop:"inventoryResult"},{default:o(()=>[a(z,{modelValue:l(e).inventoryResult,"onUpdate:modelValue":s[5]||(s[5]=n=>l(e).inventoryResult=n),placeholder:"请选择清点结果",clearable:"",style:{width:"200px"},disabled:i.value},{default:o(()=>[(v(!0),T(te,null,le(l(R),n=>(v(),g(q,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:8},{default:o(()=>[a(f,{label:"清点人",prop:"inventoryPerson"},{default:o(()=>[a(r,{modelValue:l(e).inventoryPerson,"onUpdate:modelValue":s[6]||(s[6]=n=>l(e).inventoryPerson=n),placeholder:"请输入清点人",style:{width:"200px"},disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(_,{span:24},{default:o(()=>[a(f,{label:"清点备注",prop:"remark"},{default:o(()=>[a(r,{modelValue:l(e).remark,"onUpdate:modelValue":s[7]||(s[7]=n=>l(e).remark=n),type:"textarea",rows:"3",placeholder:"请输入备注",disabled:i.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Fe=J(Pe,[["__scopeId","data-v-48bc17d8"]]);export{Fe as default};
