import{_ as Y,d as Z,r,e as s,I as ee,c as te,o as B,i as c,J as F,f as t,h as a,n as i,j as le,t as ae,O as oe,v as ne,x as re,G as d}from"./index-B0qHf98Y.js";import{c as se}from"./contract-DgThwd93.js";import{l as de,d as ie,a as ue}from"./tcontractTemplate-gMd2SxUp.js";const I=f=>(ne("data-v-2861bdf9"),f=f(),re(),f),ce={class:"app-container"},pe={class:"contract-header"},me={class:"left-buttons"},fe=I(()=>c("div",{class:"right-buttons"},null,-1)),_e=I(()=>c("div",{class:"el-upload__tip"}," 只能上传 Word/PDF 文件，且不超过 10MB ",-1)),ve={class:"dialog-footer"},ge={class:"preview-wrapper"},we=["src"],he={__name:"index copy",setup(f){const{proxy:C}=Z(),_=r(!1),V=r([]),P=r(!0),$=r(!0),h=r(0),k=r([]),v=r(!1),y=r(!1),b=r(""),q=r([]),u=r({templateName:"",templateCode:"",status:"1",file:null}),p=r({pageNum:1,pageSize:10,templateName:null,templateCode:null}),E={templateName:[{required:!0,message:"请输入模板名称",trigger:"blur"}],templateCode:[{required:!0,message:"请输入模板编码",trigger:"blur"}],file:[{required:!0,message:"请上传合同文件",trigger:"change"}]};async function z(e){const l=new FormData;l.append("category","contract_manage"),l.append("attachmentType","contract_template"),l.append("file",e.file);try{const n=await se(l,"");n.data&&n.data.url?(b.value=n.data.url,d.success("上传成功")):d.error("上传失败"),e.onSuccess&&e.onSuccess(n.data)}catch(n){d.error("上传失败"),e.onError&&e.onError(n)}}function g(){_.value=!0,de(p.value).then(e=>{k.value=e.rows,h.value=e.total,_.value=!1}).catch(()=>{_.value=!1})}function L(e){V.value=e.map(l=>l.id),P.value=e.length!=1,$.value=!e.length}function M(e){b.value=e.previewUrl,y.value=!0}function j(e){window.open(e.downloadUrl)}function U(e){const l=e.id||V.value;C.$modal.confirm("是否确认删除所选合同模板？").then(function(){return ie(l)}).then(()=>{g(),C.$modal.msgSuccess("删除成功")}).catch(()=>{})}const O=e=>{const l=e.type==="application/pdf"||e.type==="application/msword"||e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document",n=e.size/1024/1024<10;return l?n?!0:(d.error("文件大小不能超过 10MB!"),!1):(d.error("只能上传Word/PDF文件!"),!1)},R=(e,l)=>{e.code===200?(u.value.file=e.data,d.success("文件上传成功")):d.error(e.msg||"文件上传失败")},W=()=>{d.error("文件上传失败")},A=async()=>{if(!u.value.file){d.warning("请先上传合同文件");return}try{const e=await ue(u.value);e.code===200?(d.success("合同模板上传成功"),v.value=!1,g()):d.error(e.msg||"上传失败")}catch(e){console.error("上传错误:",e),d.error("系统错误，请稍后重试")}};return g(),(e,l)=>{const n=s("el-button"),S=s("el-upload"),m=s("el-table-column"),G=s("el-tag"),J=s("el-table"),H=s("pagination"),x=s("el-input"),w=s("el-form-item"),N=s("el-radio"),K=s("el-radio-group"),Q=s("el-form"),D=s("el-dialog"),X=ee("loading");return B(),te("div",ce,[c("div",pe,[c("div",me,[t(S,{class:"contract-uploader","http-request":z,"show-file-list":!1,accept:".doc,.docx,.pdf,.png,.jpg,.jpeg"},{default:a(()=>[t(n,{type:"primary"},{default:a(()=>[i("上传合同模板")]),_:1})]),_:1}),t(n,{type:"danger",onClick:U},{default:a(()=>[i("删除模板")]),_:1})]),fe]),F((B(),le(J,{data:k.value,onSelectionChange:L,"highlight-current-row":""},{default:a(()=>[t(m,{type:"selection",width:"55",align:"center"}),t(m,{label:"模板名称",prop:"templateName","min-width":"150","show-overflow-tooltip":""}),t(m,{label:"创建时间",prop:"createTime","min-width":"150","show-overflow-tooltip":""}),t(m,{label:"状态",prop:"status",width:"80",align:"center"},{default:a(o=>[t(G,{type:o.row.status==="1"?"success":"info"},{default:a(()=>[i(ae(o.row.status==="1"?"启用":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(m,{label:"操作",width:"180",align:"center"},{default:a(o=>[t(n,{link:"",type:"primary",onClick:T=>M(o.row)},{default:a(()=>[i("预览")]),_:2},1032,["onClick"]),t(n,{link:"",type:"primary",onClick:T=>j(o.row)},{default:a(()=>[i("下载")]),_:2},1032,["onClick"]),t(n,{link:"",type:"primary",onClick:T=>U(o.row)},{default:a(()=>[i("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,_.value]]),F(t(H,{total:h.value,page:p.value.pageNum,"onUpdate:page":l[0]||(l[0]=o=>p.value.pageNum=o),limit:p.value.pageSize,"onUpdate:limit":l[1]||(l[1]=o=>p.value.pageSize=o),onPagination:g},null,8,["total","page","limit"]),[[oe,h.value>0]]),t(D,{modelValue:v.value,"onUpdate:modelValue":l[6]||(l[6]=o=>v.value=o),title:"上传合同模板",width:"500px","close-on-click-modal":!1},{footer:a(()=>[c("div",ve,[t(n,{onClick:l[5]||(l[5]=o=>v.value=!1)},{default:a(()=>[i("取 消")]),_:1}),t(n,{type:"primary",onClick:A},{default:a(()=>[i("确 定")]),_:1})])]),default:a(()=>[t(Q,{ref:"uploadFormRef",model:u.value,rules:E,"label-width":"100px"},{default:a(()=>[t(w,{label:"模板名称",prop:"templateName"},{default:a(()=>[t(x,{modelValue:u.value.templateName,"onUpdate:modelValue":l[2]||(l[2]=o=>u.value.templateName=o),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1}),t(w,{label:"模板编码",prop:"templateCode"},{default:a(()=>[t(x,{modelValue:u.value.templateCode,"onUpdate:modelValue":l[3]||(l[3]=o=>u.value.templateCode=o),placeholder:"请输入模板编码"},null,8,["modelValue"])]),_:1}),t(w,{label:"合同文件",prop:"file"},{default:a(()=>[t(S,{class:"contract-uploader",action:e.uploadUrl,"before-upload":O,"on-success":R,"on-error":W,"file-list":q.value,accept:".doc,.docx,.pdf"},{tip:a(()=>[_e]),default:a(()=>[t(n,{type:"primary"},{default:a(()=>[i("选择文件")]),_:1})]),_:1},8,["action","file-list"])]),_:1}),t(w,{label:"状态",prop:"status"},{default:a(()=>[t(K,{modelValue:u.value.status,"onUpdate:modelValue":l[4]||(l[4]=o=>u.value.status=o)},{default:a(()=>[t(N,{label:"1"},{default:a(()=>[i("启用")]),_:1}),t(N,{label:"0"},{default:a(()=>[i("停用")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(D,{modelValue:y.value,"onUpdate:modelValue":l[7]||(l[7]=o=>y.value=o),title:"合同预览",width:"80%","close-on-click-modal":!1,fullscreen:""},{default:a(()=>[c("div",ge,[c("iframe",{src:b.value,frameborder:"0"},null,8,we)])]),_:1},8,["modelValue"])])}}},Ve=Y(he,[["__scopeId","data-v-2861bdf9"]]);export{Ve as default};
