import{_ as Ve,d as Ne,r as p,w as z,C as H,N as we,e as m,j as v,o as r,h as t,f as l,i as c,l as d,k as j,n as g,t as b,c as _,K as y,L as V,Q as Ie,v as Re,x as Ce}from"./index-B0qHf98Y.js";import{l as De}from"./telderinfo-BSpoeVyZ.js";import{l as ke,a as Be,b as Ee,c as M}from"./tProcessApprovalRecord-OlTXqvFr.js";import{l as Se}from"./tLiveBed-B9bJPM9s.js";import{l as je}from"./tLiveRoom-DmSXfHxo.js";import{l as Ae}from"./telderAttachement-C4ARfNBy.js";import{a as Pe,b as xe,i as Oe}from"./yjj-DmX1NTQH.js";const x=C=>(Re("data-v-cd9dbd15"),C=C(),Ce(),C),Te={class:"section"},Fe=x(()=>c("div",{class:"section-title"},"老人信息",-1)),Ue=["src"],Le={class:"section"},qe=x(()=>c("div",{class:"section-title"},"床位调整信息",-1)),Ge={class:"section"},$e=x(()=>c("div",{class:"section-title"},"审批信息",-1)),Je={class:"audit-flow"},ze={class:"audit-step-content"},He={class:"audit-step-info"},Me={class:"audit-step-time"},Qe={class:"audit-step-name"},Ke={key:0,class:"audit-step-line"},We={class:"dialog-footer"},Xe={__name:"badadjustFormAudit",props:{isView:{type:Boolean,default:!1},formData:{type:Object,default:()=>null},businessId:{type:String,default:null}},emits:["close"],setup(C,{emit:Q}){const{proxy:D}=Ne(),O=p([]),T=p([]),F=p([]),U=p([]),B=p([]);p();const L=p([]),{sys_yes_no:q,sys_user_sex:K,bed_adjust_type:W}=D.useDict("sys_yes_no","sys_user_sex","bed_adjust_type"),n=C,A=Q,k=p(!0),X=p(null),N=p(!1),Y=p(),Z=p(0);p(),p([]);const P=p([]),h=p({});z(()=>n.formData,e=>{console.log(n.formData,e,"-----------"),e&&(h.value.name=e.elderName,h.value.no=e.elderNo,h.value.age=e.age||"",h.value.gender=e.gender||"",h.value.phone=e.phone||"",h.value.bedNo=e.oldBed,h.value.agent=e.agent,h.value.applyTime=e.applyTime)}),console.log(n.formData,"formData");const ee=H({type:"",newBed:[],change:"",date:"",feeDiff:"",reason:"",contract:""}),le=H({adjustForm:{},adjustRules:{adjustmentType:[{required:!0,message:"请选择调整类型",trigger:"change"}],newBed:[{required:!0,message:"请选择调整后床位号",trigger:"change"}],isPriceChanged:[{required:!0,message:"请选择床位费是否变化",trigger:"change"}],adjustmentDate:[{required:!0,message:"请选择调整日期",trigger:"change"}],priceDifference:[{required:!0,message:"请输入床位费差额",trigger:"blur"}],changeReason:[{required:!0,message:"请输入更换原因",trigger:"blur"}],isContractChanged:[{required:!0,message:"请选择是否更换合同",trigger:"change"}]},AuditRules:{approvalOpinion:[{required:!0,message:"请填写拒绝理由",trigger:"change"}]},auditInfo:{},auditForm:{},AuditRefRules:{approvalOpinion:[{required:!0,message:"请填写拒绝理由",trigger:"change"}]}});p("");const{adjustForm:s,rules:Ye,auditInfo:Ze,auditForm:f}=we(le),ae=["申请","审核","归档"];p(0);function te(e){return e.approvalStatus=="APPROVED"||e.approvalStatus=="PENDING"}function oe(){console.log(n.formData,"formData"),n.isView?n.formData&&(console.log("edit"),s.value=n.formData):console.log("add"),ke().then(o=>{O.value=o.rows}),Be({businessId:n.businessId,processName:"床位调整"}).then(o=>{console.log(o,"listRecord"),L.value=o.rows});let e={elderId:n.businessId,category:"room_bed_change",attachment_type:"roombed_contract_change"};Ae(e).then(o=>{console.log(o,"res"),P.value=o.rows.map(i=>i.filePath)})}z(k,e=>{e||A("close")});function se(){N.value=!0,De(elderQueryParams.value).then(e=>{console.log(e,"res"),Y.value=e.rows,Z.value=e.total})}function de(e){s.value.targetBuildingId=e.id,s.value.targetBuildingName=e.buildingName,Ee({buildingId:e.id}).then(o=>{console.log(o,"build-----"),T.value=o.rows})}function ne(e){s.value.targetFloorId=e.id,s.value.targetFloorName=e.floorName,je({floorId:e.id}).then(o=>{console.log(o,"floor-----"),F.value=o.rows})}function ue(e){s.value.targetRoomId=e.roomNumber,s.value.targetRoomName=e.roomName,Se({roomId:e.id}).then(o=>{console.log(o,"room-----"),U.value=o.rows})}function re(e){s.value.targetBedId=e.id,s.value.targetBedNumber=e.bedNumber}function ie(e){console.log(e,"handleGetFile---------"),e&&(Array.isArray(e)?B.value=B.value.concat(e.map(o=>o)):B.value.push(e)),console.log(B.value,"handleGetFile---------")}function pe(){if(f.value.approvalOpinion==null){D.$modal.msgError("请填写拒绝理由");return}D.$refs.auditRef.validate(e=>{e&&(f.value.businessId=n.businessId,f.value.approvalStatus="REJECTED",M(f.value).then(o=>{D.$modal.msgSuccess("提交成功"),N.value=!1,k.value=!1,A("close")}))})}function ce(){N.value=!0}function me(){f.value.businessId=n.businessId,f.value.approvalStatus="APPROVED",M(f.value).then(e=>{D.$modal.msgSuccess("提交成功"),N.value=!1,k.value=!1,A("close")})}function fe(e){if(console.log(e,"item----------"),e.stepOrder=="1"){if(e.approvalStatus=="APPROVED")return e.currentApproverName+" 发起申请"}else if(e.stepOrder=="2"){if(e.approvalStatus=="REJECTED")return e.currentApproverName+" 驳回了申请";if(e.approvalStatus=="PENDING")return e.currentApproverName+" 待审核";if(e.approvalStatus=="COMPLETE")return e.currentApproverName+" 通过了申请"}else if(e.stepOrder=="3")return"已归档"}function ge(e){if(console.log(e,"status"),!!e){if(e=="APPROVED"||e=="COMPLETE")return Pe;if(e=="REJECTED")return xe;if(e=="PENDING")return Oe}}function _e(){f.value.approvalOpinion=null,N.value=!1}return oe(),(e,o)=>{const i=m("el-form-item"),u=m("el-col"),E=m("el-input"),S=m("el-button"),ve=m("dict-tag-span"),R=m("el-row"),be=m("el-avatar"),w=m("el-option"),I=m("el-select"),he=m("el-date-picker"),ye=m("ImageUpload"),G=m("el-form"),$=m("el-dialog");return r(),v($,{modelValue:k.value,"onUpdate:modelValue":o[14]||(o[14]=a=>k.value=a),title:"床位调整审批页面",width:"70%","append-to-body":""},{footer:t(()=>[l(S,{type:"info",onClick:ce},{default:t(()=>[g("申请拒绝")]),_:1}),l(S,{type:"primary",onClick:me},{default:t(()=>[g("申请通过")]),_:1})]),default:t(()=>[l(G,{ref_key:"formRef",ref:X,model:d(s),rules:e.adjustRules,"label-width":"110px"},{default:t(()=>[c("div",Te,[Fe,l(R,null,{default:t(()=>[l(u,{span:16},{default:t(()=>[l(R,{gutter:20},{default:t(()=>[l(u,{span:12},{default:t(()=>[l(i,{label:"经办人",prop:"change"},{default:t(()=>[g(b(d(s).handlerName),1)]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"申请时间",prop:"change"},{default:t(()=>[g(b(d(s).adjustmentTime),1)]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"老人姓名",prop:"change"},{default:t(()=>[l(E,{modelValue:d(s).elderName,"onUpdate:modelValue":o[0]||(o[0]=a=>d(s).elderName=a),placeholder:"请选择老人",style:{width:"100%",display:"inline-block"},onClick:se,disabled:n.isView},null,8,["modelValue","disabled"]),j("",!0)]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"床位编号",prop:"originalBedNumber"},{default:t(()=>[g(b(d(s).originalBedNumber),1)]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"老人编号",prop:"change"},{default:t(()=>[g(b(d(s).elderCode),1)]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"性别",prop:"change"},{default:t(()=>[l(ve,{options:d(K),value:d(s).elderGender},null,8,["options","value"])]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"联系电话",prop:"change"},{default:t(()=>[g(b(d(s).elderPhone),1)]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"年龄",prop:"change"},{default:t(()=>[g(b(d(s).elderAge),1)]),_:1})]),_:1}),d(s).avatar?(r(),v(u,{key:0,span:6})):j("",!0)]),_:1})]),_:1}),l(u,{span:4},{default:t(()=>[l(be,{shape:"square",size:140,fit:"fill",src:d(s).avatar},null,8,["src"])]),_:1}),l(u,{span:4},{default:t(()=>[c("img",{src:ge(d(s).status),alt:"",style:{width:"100%",height:"auto"}},null,8,Ue)]),_:1})]),_:1})]),c("div",Le,[qe,l(R,{gutter:16},{default:t(()=>[l(u,{span:12},{default:t(()=>[l(i,{label:"调整类型",prop:"adjustmentType"},{default:t(()=>[l(I,{modelValue:d(s).adjustmentType,"onUpdate:modelValue":o[1]||(o[1]=a=>d(s).adjustmentType=a),placeholder:"请选择",style:{width:"100%"},disabled:n.isView},{default:t(()=>[(r(!0),_(y,null,V(d(W),a=>(r(),v(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"调整后床位号",prop:"targetBuildingName"},{default:t(()=>[l(I,{modelValue:d(s).targetBuildingName,"onUpdate:modelValue":o[2]||(o[2]=a=>d(s).targetBuildingName=a),placeholder:"请选择楼栋",style:{width:"120px"},disabled:n.isView,onChange:de},{default:t(()=>[(r(!0),_(y,null,V(O.value,a=>(r(),v(w,{key:a.value,label:a.buildingName,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(I,{modelValue:d(s).targetFloorName,"onUpdate:modelValue":o[3]||(o[3]=a=>d(s).targetFloorName=a),placeholder:"请选择楼层",style:{width:"120px"},disabled:n.isView,onChange:ne},{default:t(()=>[(r(!0),_(y,null,V(T.value,a=>(r(),v(w,{key:a.value,label:a.floorName,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(I,{modelValue:d(s).targetRoomName,"onUpdate:modelValue":o[4]||(o[4]=a=>d(s).targetRoomName=a),placeholder:"请选择房间",style:{width:"120px"},disabled:n.isView,onChange:ue},{default:t(()=>[(r(!0),_(y,null,V(F.value,a=>(r(),v(w,{key:a.value,label:a.roomName,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(I,{modelValue:d(s).targetBedNumber,"onUpdate:modelValue":o[5]||(o[5]=a=>d(s).targetBedNumber=a),placeholder:"请选择床位",style:{width:"120px"},disabled:n.isView,onChange:re},{default:t(()=>[(r(!0),_(y,null,V(U.value,a=>(r(),v(w,{key:a.value,label:a.bedNumber,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(R,{gutter:16},{default:t(()=>[l(u,{span:12},{default:t(()=>[l(i,{label:"调整日期",prop:"adjustmentDate"},{default:t(()=>[l(he,{modelValue:d(s).adjustmentDate,"onUpdate:modelValue":o[6]||(o[6]=a=>d(s).adjustmentDate=a),type:"date",placeholder:"请选择",style:{width:"100%"},disabled:n.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"床位费变化",prop:"isPriceChanged"},{default:t(()=>[l(I,{modelValue:d(s).isPriceChanged,"onUpdate:modelValue":o[7]||(o[7]=a=>d(s).isPriceChanged=a),placeholder:"请选择",style:{width:"100%"},disabled:n.isView},{default:t(()=>[(r(!0),_(y,null,V(d(q),a=>(r(),v(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(R,{gutter:16},{default:t(()=>[l(u,{span:12},{default:t(()=>[l(i,{label:"床位费差额",prop:"priceDifference"},{default:t(()=>[l(E,{modelValue:d(s).priceDifference,"onUpdate:modelValue":o[8]||(o[8]=a=>d(s).priceDifference=a),placeholder:"请输入",disabled:n.isView||d(ee).change==="否"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(u,{span:12},{default:t(()=>[l(i,{label:"更换合同",prop:"isContractChanged"},{default:t(()=>[l(I,{modelValue:d(s).isContractChanged,"onUpdate:modelValue":o[9]||(o[9]=a=>d(s).isContractChanged=a),placeholder:"请选择",style:{width:"100%"},disabled:n.isView},{default:t(()=>[(r(!0),_(y,null,V(d(q),a=>(r(),v(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(u,{span:24},{default:t(()=>[l(i,{label:"更换原因",prop:"changeReason"},{default:t(()=>[l(E,{modelValue:d(s).changeReason,"onUpdate:modelValue":o[10]||(o[10]=a=>d(s).changeReason=a),type:"textarea",rows:4,placeholder:"请输入更换原因",disabled:n.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(R,{gutter:16},{default:t(()=>[l(u,{span:12},{default:t(()=>[l(i,{label:"上传附件"},{default:t(()=>[l(ye,{modelValue:P.value,"onUpdate:modelValue":o[11]||(o[11]=a=>P.value=a),fileData:{category:"room_bed_change",attachmentType:"roombed_contract_change"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!0,limit:10,disabled:!0,onRemoveAtt:e.removeImage,onSubmitParentValue:ie},null,8,["modelValue","onRemoveAtt"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"]),c("div",Ge,[$e,c("div",Je,[(r(!0),_(y,null,V(L.value,(a,J)=>(r(),_("div",{class:"audit-step",key:J},[c("div",ze,[c("div",{class:Ie(["audit-step-title",{active:te(a)}])},b(a.stepName),3),c("div",He,[c("div",Me,b(a.approvalTime||"-"),1),c("div",Qe,b(fe(a)),1)])]),J<ae.length-1?(r(),_("div",Ke)):j("",!0)]))),128))])]),l($,{modelValue:N.value,"onUpdate:modelValue":o[13]||(o[13]=a=>N.value=a),class:"elder-dialog-custom",title:"拒绝理由",width:"40%"},{footer:t(()=>[c("div",We,[l(S,{type:"primary",onClick:pe},{default:t(()=>[g("确 定")]),_:1}),l(S,{onClick:_e},{default:t(()=>[g("取 消")]),_:1})])]),default:t(()=>[l(G,{model:d(f),rules:e.AuditRefRules,ref:"auditRef","label-width":"80px"},{default:t(()=>[l(E,{modelValue:d(f).approvalOpinion,"onUpdate:modelValue":o[12]||(o[12]=a=>d(f).approvalOpinion=a),placeholder:"请填写拒绝理由",type:"textarea",style:{width:"100%"},rows:"6",maxlength:"30"},null,8,["modelValue"]),j("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}},nl=Ve(Xe,[["__scopeId","data-v-cd9dbd15"]]);export{nl as default};
