import le from"./index-CCXF19OR.js";import{X as U,_ as ae,d as de,r as y,z as C,w as M,e as b,c as k,o as _,f as e,h as a,i as m,k as V,t as h,l as E,j as w,n as D,G as S,v as ue,x as te}from"./index-B0qHf98Y.js";function Ne(p){return U({url:"/elderclothing/elderClothingWashRecord/list",method:"get",params:p})}function T(p){return U({url:"/elderclothing/elderClothingWashRecord/"+p,method:"get"})}function se(p){return U({url:"/elderclothing/elderClothingWashRecord",method:"post",data:p})}function oe(p){return U({url:"/elderclothing/elderClothingWashRecord",method:"put",data:p})}function Re(p){return U({url:"/elderclothing/elderClothingWashRecord/"+p,method:"delete"})}const L=p=>(ue("data-v-3bc07dbb"),p=p(),te(),p),re={class:"addElderClothingWashRecord"},ne={class:"record-dialog"},ie={class:"section"},ve=L(()=>m("h3",null,"老人信息",-1)),me={class:"value"},pe={key:0,class:"avatar-container"},ce={class:"value"},fe={class:"value"},be={class:"value"},_e={class:"value"},ge={class:"value"},Ve={class:"value"},he={class:"value"},Se={class:"section"},we=L(()=>m("h3",null,"衣物清洗信息",-1)),ye=L(()=>m("h4",null,"送洗衣服",-1)),Ce={key:0},Ue={class:"dialog-footer"},Pe={__name:"addElderClothingWashRecord",emits:["success"],setup(p,{expose:O,emit:W}){const{proxy:N}=de(),{sys_user_sex:x}=N.useDict("sys_user_sex"),g=y(!1),f=y(null),t=y("view"),P=y(null),n=C(()=>t.value==="view");C(()=>t.value==="receive");const R=C(()=>t.value==="view"||t.value==="receive"),q=C(()=>({view:"查看衣物清洗记录",add:"新增送洗",edit:"编辑衣物清洗记录",receive:"回收衣服"})[t.value]),l=y({sendCoat:0,sendShirt:0,sendLongSleeve:0,sendLongPants:0,sendUnderwear:0,sendTrousers:0,sendSheet:0,sendCover:0,sendPillowcase:0,sendSheetMiddle:0,sendSocks:0,sendOther:0,sendTotal:0,receiveCoat:0,receiveShirt:0,receiveLongSleeve:0,receiveLongPants:0,receiveUnderwear:0,receiveTrousers:0,receiveSheet:0,receiveCover:0,receivePillowcase:0,receiveSheetMiddle:0,receiveSocks:0,receiveOther:0,receiveTotal:0});M(()=>[l.value.sendCoat,l.value.sendShirt,l.value.sendLongSleeve,l.value.sendLongPants,l.value.sendUnderwear,l.value.sendTrousers,l.value.sendSheet,l.value.sendCover,l.value.sendPillowcase,l.value.sendSheetMiddle,l.value.sendSocks,l.value.sendOther],r=>{l.value.sendTotal=r.reduce((d,c)=>d+(c||0),0)},{deep:!0}),M(()=>[l.value.receiveCoat,l.value.receiveShirt,l.value.receiveLongSleeve,l.value.receiveLongPants,l.value.receiveUnderwear,l.value.receiveTrousers,l.value.receiveSheet,l.value.receiveCover,l.value.receivePillowcase,l.value.receiveSheetMiddle,l.value.receiveSocks,l.value.receiveOther],r=>{l.value.receiveTotal=r.reduce((d,c)=>d+(c||0),0)},{deep:!0});const H=C(()=>{const r={elderName:[{required:!0,message:"请选择老人",trigger:"submit"}],washDate:[{required:!0,message:"请选择清洗日期",trigger:"submit"}]};return t.value==="add"?{...r,sendHandoverPerson:[{required:!0,message:"请输入送洗交接人",trigger:"submit"}],sendReceivePerson:[{required:!0,message:"请输入送洗接收人",trigger:"submit"}]}:t.value==="receive"?{...r,receiveHandoverPerson:[{required:!0,message:"请输入收回交接人",trigger:"submit"}],receiveReceivePerson:[{required:!0,message:"请输入收回接收人",trigger:"submit"}]}:{...r,sendHandoverPerson:[{required:!0,message:"请输入送洗交接人",trigger:"submit"}],sendReceivePerson:[{required:!0,message:"请输入送洗接收人",trigger:"submit"}],receiveHandoverPerson:[{required:!0,message:"请输入收回交接人",trigger:"submit"}],receiveReceivePerson:[{required:!0,message:"请输入收回接收人",trigger:"submit"}]}}),B=W,F=()=>{N.$refs.elderSelectComponentRef.openElderSelect()},Y=r=>{r&&(l.value.elderName=r.elderName,l.value.elderId=r.id,l.value.elderCode=r.elderCode,l.value.gender=r.gender,l.value.avatar=r.avatar,l.value.bedNumber=r.bedNumber,l.value.roomNumber=r.roomNumber,l.value.age=r.age,l.value.buildingName=r.buildingName,l.value.buildingId=r.buildingId,l.value.floorNumber=r.floorNumber,l.value.floorId=r.floorId,l.value.nursingLevel=r.nursingLevel,l.value.checkInDate=r.checkInDate,l.value.roomId=r.roomId,l.value.bedId=r.bedId)},j=()=>{P.value=null,l.value={sendCoat:0,sendShirt:0,sendLongSleeve:0,sendLongPants:0,sendUnderwear:0,sendTrousers:0,sendSheet:0,sendCover:0,sendPillowcase:0,sendSheetMiddle:0,sendSocks:0,sendOther:0,sendTotal:0,receiveCoat:0,receiveShirt:0,receiveLongSleeve:0,receiveLongPants:0,receiveUnderwear:0,receiveTrousers:0,receiveSheet:0,receiveCover:0,receivePillowcase:0,receiveSheetMiddle:0,receiveSocks:0,receiveOther:0,receiveTotal:0},setTimeout(()=>{f.value&&(f.value.resetFields(),f.value.clearValidate())},100)},z=()=>{j(),t.value="add",g.value=!0},A=async r=>{t.value="edit",P.value=r.id;const d=await T(r.id);d.code===200?l.value=d.data:S.error("获取记录详情失败"),g.value=!0,setTimeout(()=>{f.value&&f.value.clearValidate()},100)},$=async r=>{t.value="receive",P.value=r.id;const d=await T(r.id);d.code===200?l.value=d.data:S.error("获取记录详情失败"),g.value=!0,setTimeout(()=>{f.value&&f.value.clearValidate()},100)},G=async r=>{t.value="view",P.value=r.id;const d=await T(r.id);d.code===200?l.value=d.data:S.error("获取记录详情失败"),g.value=!0,setTimeout(()=>{f.value&&f.value.clearValidate()},100)},X=async()=>{try{if(!await f.value.validate().catch(()=>!1)){S.error("请检查表单填写");return}let d;if(t.value==="add"?d=await se(l.value):(t.value==="edit"||t.value==="receive")&&(d=await oe(l.value)),d.code===200){const c=t.value==="add"?"新增成功":t.value==="receive"?"回收成功":"修改成功";S.success(c),g.value=!1,B("success")}else S.error(d.msg)}catch(r){console.error("表单验证失败或提交出错:",r),S.error("请检查表单填写")}};return O({openAdd:z,openEdit:A,openView:G,openReceive:$}),(r,d)=>{const c=b("el-input"),s=b("el-form-item"),o=b("el-col"),J=b("dict-tag-span"),v=b("el-row"),K=b("el-avatar"),Q=b("el-date-picker"),i=b("el-input-number"),Z=b("el-form"),I=b("el-button"),ee=b("el-dialog");return _(),k("div",re,[e(ee,{title:q.value,modelValue:g.value,"onUpdate:modelValue":d[34]||(d[34]=u=>g.value=u),width:"70%","close-on-click-modal":!1},{footer:a(()=>[m("span",Ue,[e(I,{onClick:d[33]||(d[33]=u=>g.value=!1)},{default:a(()=>[D("关闭")]),_:1}),n.value?V("",!0):(_(),w(I,{key:0,type:"primary",onClick:X},{default:a(()=>[D(" 提交 ")]),_:1}))])]),default:a(()=>[e(Z,{ref_key:"recordForm",ref:f,model:l.value,"label-width":"120px",rules:H.value,"label-position":"left"},{default:a(()=>[m("div",ne,[m("div",ie,[ve,e(v,{gutter:24,class:"elder-info"},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"老人姓名",prop:"elderName"},{default:a(()=>[e(c,{modelValue:l.value.elderName,"onUpdate:modelValue":d[0]||(d[0]=u=>l.value.elderName=u),placeholder:"请输入老人姓名",readonly:"",onClick:F,disabled:R.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"老人编号",prop:"elderCode"},{default:a(()=>[m("span",me,h(l.value.elderCode),1)]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"性别",prop:"gender"},{default:a(()=>[e(J,{options:E(x),value:l.value.gender},null,8,["options","value"])]),_:1})]),_:1})]),_:1}),l.value.avatar?(_(),k("div",pe,[e(K,{shape:"square",size:140,fit:"fill",src:l.value.avatar},null,8,["src"])])):V("",!0),e(v,{gutter:24,class:"elder-info"},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"床位编号",prop:"bedNumber"},{default:a(()=>[m("span",ce,h(l.value.roomNumber?l.value.roomNumber+"-"+l.value.bedNumber:l.value.bedNumber),1)]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"房间信息",prop:"roomNumber"},{default:a(()=>[m("span",fe,h(l.value.roomNumber),1)]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"年龄",prop:"age"},{default:a(()=>[m("span",be,h(l.value.age),1)]),_:1})]),_:1})]),_:1}),e(v,{gutter:24,class:"elder-info"},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"楼栋信息",prop:"buildingName"},{default:a(()=>[m("span",_e,h(l.value.buildingName),1)]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"楼层信息",prop:"floorNumber"},{default:a(()=>[m("span",ge,h(l.value.floorNumber),1)]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"护理等级",prop:"nursingLevel"},{default:a(()=>[m("span",Ve,h(l.value.nursingLevel),1)]),_:1})]),_:1})]),_:1}),e(v,{gutter:24,class:"elder-info"},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"入住时间",prop:"checkInDate"},{default:a(()=>[m("span",he,h(l.value.checkInDate),1)]),_:1})]),_:1})]),_:1})]),m("div",Se,[we,e(v,{gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"清洗日期",prop:"washDate"},{default:a(()=>[e(Q,{modelValue:l.value.washDate,"onUpdate:modelValue":d[1]||(d[1]=u=>l.value.washDate=u),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",disabled:R.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),ye,e(v,{gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"外套",prop:"sendCoat"},{default:a(()=>[e(i,{modelValue:l.value.sendCoat,"onUpdate:modelValue":d[2]||(d[2]=u=>l.value.sendCoat=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"衬衣",prop:"sendShirt"},{default:a(()=>[e(i,{modelValue:l.value.sendShirt,"onUpdate:modelValue":d[3]||(d[3]=u=>l.value.sendShirt=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"秋衣",prop:"sendLongSleeve"},{default:a(()=>[e(i,{modelValue:l.value.sendLongSleeve,"onUpdate:modelValue":d[4]||(d[4]=u=>l.value.sendLongSleeve=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"秋裤",prop:"sendLongPants"},{default:a(()=>[e(i,{modelValue:l.value.sendLongPants,"onUpdate:modelValue":d[5]||(d[5]=u=>l.value.sendLongPants=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"内裤",prop:"sendUnderwear"},{default:a(()=>[e(i,{modelValue:l.value.sendUnderwear,"onUpdate:modelValue":d[6]||(d[6]=u=>l.value.sendUnderwear=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"外裤",prop:"sendTrousers"},{default:a(()=>[e(i,{modelValue:l.value.sendTrousers,"onUpdate:modelValue":d[7]||(d[7]=u=>l.value.sendTrousers=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"床单",prop:"sendSheet"},{default:a(()=>[e(i,{modelValue:l.value.sendSheet,"onUpdate:modelValue":d[8]||(d[8]=u=>l.value.sendSheet=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"被罩",prop:"sendCover"},{default:a(()=>[e(i,{modelValue:l.value.sendCover,"onUpdate:modelValue":d[9]||(d[9]=u=>l.value.sendCover=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"枕套",prop:"sendPillowcase"},{default:a(()=>[e(i,{modelValue:l.value.sendPillowcase,"onUpdate:modelValue":d[10]||(d[10]=u=>l.value.sendPillowcase=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"中单",prop:"sendSheetMiddle"},{default:a(()=>[e(i,{modelValue:l.value.sendSheetMiddle,"onUpdate:modelValue":d[11]||(d[11]=u=>l.value.sendSheetMiddle=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"袜子",prop:"sendSocks"},{default:a(()=>[e(i,{modelValue:l.value.sendSocks,"onUpdate:modelValue":d[12]||(d[12]=u=>l.value.sendSocks=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"其他",prop:"sendOther"},{default:a(()=>[e(i,{modelValue:l.value.sendOther,"onUpdate:modelValue":d[13]||(d[13]=u=>l.value.sendOther=u),min:0,disabled:n.value||t.value==="receive",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"总计",prop:"sendTotal"},{default:a(()=>[e(i,{modelValue:l.value.sendTotal,"onUpdate:modelValue":d[14]||(d[14]=u=>l.value.sendTotal=u),min:0,disabled:!0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"交接人",prop:"sendHandoverPerson"},{default:a(()=>[e(c,{modelValue:l.value.sendHandoverPerson,"onUpdate:modelValue":d[15]||(d[15]=u=>l.value.sendHandoverPerson=u),placeholder:"请输入",disabled:n.value||t.value==="receive"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"接收人",prop:"sendReceivePerson"},{default:a(()=>[e(c,{modelValue:l.value.sendReceivePerson,"onUpdate:modelValue":d[16]||(d[16]=u=>l.value.sendReceivePerson=u),placeholder:"请输入",disabled:n.value||t.value==="receive"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),t.value!=="add"?(_(),k("h4",Ce,"收回衣服")):V("",!0),t.value!=="add"?(_(),w(v,{key:1,gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"外套",prop:"receiveCoat"},{default:a(()=>[e(i,{modelValue:l.value.receiveCoat,"onUpdate:modelValue":d[17]||(d[17]=u=>l.value.receiveCoat=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"衬衣",prop:"receiveShirt"},{default:a(()=>[e(i,{modelValue:l.value.receiveShirt,"onUpdate:modelValue":d[18]||(d[18]=u=>l.value.receiveShirt=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"秋衣",prop:"receiveLongSleeve"},{default:a(()=>[e(i,{modelValue:l.value.receiveLongSleeve,"onUpdate:modelValue":d[19]||(d[19]=u=>l.value.receiveLongSleeve=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})):V("",!0),t.value!=="add"?(_(),w(v,{key:2,gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"秋裤",prop:"receiveLongPants"},{default:a(()=>[e(i,{modelValue:l.value.receiveLongPants,"onUpdate:modelValue":d[20]||(d[20]=u=>l.value.receiveLongPants=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"内裤",prop:"receiveUnderwear"},{default:a(()=>[e(i,{modelValue:l.value.receiveUnderwear,"onUpdate:modelValue":d[21]||(d[21]=u=>l.value.receiveUnderwear=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"外裤",prop:"receiveTrousers"},{default:a(()=>[e(i,{modelValue:l.value.receiveTrousers,"onUpdate:modelValue":d[22]||(d[22]=u=>l.value.receiveTrousers=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})):V("",!0),t.value!=="add"?(_(),w(v,{key:3,gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"床单",prop:"receiveSheet"},{default:a(()=>[e(i,{modelValue:l.value.receiveSheet,"onUpdate:modelValue":d[23]||(d[23]=u=>l.value.receiveSheet=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"被罩",prop:"receiveCover"},{default:a(()=>[e(i,{modelValue:l.value.receiveCover,"onUpdate:modelValue":d[24]||(d[24]=u=>l.value.receiveCover=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"枕套",prop:"receivePillowcase"},{default:a(()=>[e(i,{modelValue:l.value.receivePillowcase,"onUpdate:modelValue":d[25]||(d[25]=u=>l.value.receivePillowcase=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})):V("",!0),t.value!=="add"?(_(),w(v,{key:4,gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"中单",prop:"receiveSheetMiddle"},{default:a(()=>[e(i,{modelValue:l.value.receiveSheetMiddle,"onUpdate:modelValue":d[26]||(d[26]=u=>l.value.receiveSheetMiddle=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"袜子",prop:"receiveSocks"},{default:a(()=>[e(i,{modelValue:l.value.receiveSocks,"onUpdate:modelValue":d[27]||(d[27]=u=>l.value.receiveSocks=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"其他",prop:"receiveOther"},{default:a(()=>[e(i,{modelValue:l.value.receiveOther,"onUpdate:modelValue":d[28]||(d[28]=u=>l.value.receiveOther=u),min:0,disabled:n.value||t.value==="edit",style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})):V("",!0),t.value!=="add"?(_(),w(v,{key:5,gutter:24},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(s,{label:"总计",prop:"receiveTotal"},{default:a(()=>[e(i,{modelValue:l.value.receiveTotal,"onUpdate:modelValue":d[29]||(d[29]=u=>l.value.receiveTotal=u),min:0,disabled:!0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"交接人",prop:"receiveHandoverPerson"},{default:a(()=>[e(c,{modelValue:l.value.receiveHandoverPerson,"onUpdate:modelValue":d[30]||(d[30]=u=>l.value.receiveHandoverPerson=u),placeholder:"请输入",disabled:n.value||t.value!=="receive"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(o,{span:8},{default:a(()=>[e(s,{label:"接收人",prop:"receiveReceivePerson"},{default:a(()=>[e(c,{modelValue:l.value.receiveReceivePerson,"onUpdate:modelValue":d[31]||(d[31]=u=>l.value.receiveReceivePerson=u),placeholder:"请输入",disabled:n.value||t.value!=="receive"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})):V("",!0),e(v,{gutter:24},{default:a(()=>[e(o,{span:24},{default:a(()=>[e(s,{label:"备注事项",prop:"remark"},{default:a(()=>[e(c,{modelValue:l.value.remark,"onUpdate:modelValue":d[32]||(d[32]=u=>l.value.remark=u),placeholder:"请输入",type:"textarea",rows:2,disabled:n.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(E(le),{ref:"elderSelectComponentRef",onSelectLerder:Y},null,512)])}}},ke=ae(Pe,[["__scopeId","data-v-3bc07dbb"]]),Ie=Object.freeze(Object.defineProperty({__proto__:null,default:ke},Symbol.toStringTag,{value:"Module"}));export{ke as A,Ie as a,Re as d,Ne as l};
