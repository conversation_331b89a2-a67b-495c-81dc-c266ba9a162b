import{_ as I,r as u,e as b,I as N,J as S,l as a,c as g,o as x,f as y,h as _,i as t,t as i,K as B,L as T,n as v,D as E,aB as q,v as L,x as P}from"./index-B0qHf98Y.js";const w=d=>(L("data-v-ae0bf21a"),d=d(),P(),d),R=w(()=>t("h3",{class:"title_record"},"机构综合查房表",-1)),A={class:"table-style"},H={style:{"text-align":"left"},colspan:"2"},J={style:{"text-align":"left"}},O=w(()=>t("tr",null,[t("td",{style:{"text-align":"center","font-weight":"bold",width:"50px"}},"序号"),t("td",{style:{"text-align":"center",width:"80px","font-weight":"bold"}},"检查项目"),t("td",{style:{"text-align":"center",width:"300px","font-weight":"bold"}},"检查内容")],-1)),W={style:{"text-align":"center"}},j={style:{"text-align":"center"}},z={style:{"text-align":"center",color:"#666"}},F={class:"dialog-footer"},K={__name:"jgrecord",setup(d,{expose:k}){const o=u(!1),m=u(null),c=u({}),h=u(!1),C=l=>{h.value=!0,q(l.id).then(n=>{o.value=!0,c.value=n.data||[]}).finally(()=>{h.value=!1})},D=()=>{o.value=!1},V=()=>{const l=m.value.cloneNode(!0);l.querySelectorAll(".el-input, .el-textarea").forEach(r=>{var p;const f=((p=r.querySelector("input, textarea"))==null?void 0:p.value)||"",e=document.createElement("div");e.textContent=f,e.style.padding="8px",r.replaceWith(e)});const s=window.open("","_blank");s.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>机构综合查房表</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${l.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `),s.document.close()};return k({openDialog:C}),(l,n)=>{const s=b("el-button"),r=b("el-dialog"),f=N("loading");return S((x(),g("div",null,[y(r,{modelValue:a(o),"onUpdate:modelValue":n[0]||(n[0]=e=>E(o)?o.value=e:null),title:"详情",width:"70%","close-on-click-modal":!1,"append-to-body":""},{footer:_(()=>[t("div",F,[y(s,{onClick:D},{default:_(()=>[v("返 回")]),_:1}),y(s,{type:"primary",onClick:V},{default:_(()=>[v("打 印")]),_:1})])]),default:_(()=>[t("div",{class:"detail-content",ref_key:"printContent",ref:m},[R,t("table",A,[t("tbody",null,[t("tr",null,[t("td",H,"查房时间："+i(a(c)[0].roundTime||"-"),1),t("td",J,"查房人:"+i(a(c)[0].roundPerson||"-"),1)]),O,(x(!0),g(B,null,T(a(c),(e,p)=>(x(),g("tr",{key:p},[t("td",W,i(e.seqNo||"-"),1),t("td",j,i(e.checkItems||"-"),1),t("td",z,i(e.checkContent||"-"),1)]))),128))])])],512)]),_:1},8,["modelValue"])])),[[f,a(h)]])}}},U=I(K,[["__scopeId","data-v-ae0bf21a"]]);export{U as default};
