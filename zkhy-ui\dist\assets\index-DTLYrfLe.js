import{X as A,_ as Ne,r as u,C as De,N as Ve,F as xe,e as f,I as ke,c as oe,o as S,i as r,au as ne,l,J as N,j as x,h as t,f as e,k as Ce,bh as Ye,bi as Te,bj as ze,t as d,n as h,Y as z,O as j,D as Ue,G as k,v as Me,x as Re}from"./index-B0qHf98Y.js";import{g as Ie,a as Be,l as Pe}from"./buildmanage-CIqJJJF0.js";function Le(w){return A({url:"/roomdailyrec/cleaningRecord/list",method:"get",params:w})}function Fe(w){return A({url:"/roomdailyrec/disinfectionRecord/list",method:"get",params:w})}function Oe(w){return A({url:"/roomdailyrec/uvRecord/list",method:"get",params:w})}function Ke(w){return A({url:"/roomdailyrec/ventilationRecord/list",method:"get",params:w})}const ie=w=>(Me("data-v-a9a771cb"),w=w(),Re(),w),$e={class:"roomdata-page"},Ee={class:"main-content"},He=ie(()=>r("div",{class:"room-tree-title"},"房间信息",-1)),je={class:"sidebar-center"},Ae={key:0,class:"room-info-card"},Ge={class:"room-header-row"},Je={class:"room-number"},Xe={class:"room-info-row"},qe={class:"room-info-row"},Qe={class:"room-info-row"},We={class:"room-info-row"},Ze=ie(()=>r("div",{class:"room-info-row"},"床位信息：",-1)),el={key:1,class:"room-info-card"},ll=ie(()=>r("div",{class:"room-header-row"},"点击左侧查看房间信息",-1)),al=[ll],tl={class:"content-panel"},ol={class:"vent-search-bar"},nl={class:"vent-search-bar"},il={class:"vent-search-bar"},rl={class:"vent-search-bar"},dl={__name:"index",setup(w){const re=u([]),ce={children:"children",label:"label"},y=u(null),G=u(!1),_=u({}),J=u(!1),D=u("clear"),me=De({clearSearch:{pageSize:10,pageNum:1,cleaningTime:"",cleaningStaff:"",supervisor:""},ventSearch:{pageSize:10,pageNum:1,ventilationDate:"",ventilationStaff:"",supervisor:""},defaultPageInfo:{pageSize:10,pageNum:1},disinfectSearch:{pageSize:10,pageNum:1,date:"",ventPerson:"",supervisor:""},uvSearch:{pageSize:10,pageNum:1,date:"",disinfectionStaffName:"",supervisor:""}}),{clearSearch:p,ventSearch:c,disinfectSearch:g,defaultPageInfo:sl,uvSearch:v}=Ve(me),X=u(!1),de=u([]),q=u(0),Q=u(!1),W=u([]),U=u(0),Z=u(!1),ee=u([]),M=u(0),le=u(!1),ae=u([]),R=u(0);function fe(){Y()}async function ge(i){if(y.value=i.id,i.type==="room"){J.value=!0;try{const o=await Be(y.value);if(o.code===200&&o.data){const s=o.data,b=_.value.beds||[];_.value={...s,roomNo:s.roomNumber,roomType:s.roomType,direction:s.roomOrientation,area:s.roomArea,zone:s.areaName,facilities:s.roomFacilities?s.roomFacilities.split(","):[],remark:s.remark,status:s.status,beds:b}}await se(i.id),D.value="clear",ve()}catch(o){console.error("获取房间详情失败:",o),k.error("获取房间详情失败"),await se(i.id)}J.value=!1}else i.type==="floor"?(currentFloorNode.value=i,node&&node.parent&&(currentBuildingNode.value=node.parent.data),_.value=null):(i.type==="building"&&(currentBuildingNode.value=i,currentFloorNode.value=null),_.value=null)}async function se(i){try{const o=await Pe({roomId:i});if(o.code===200){const s=o.rows.map(b=>({id:b.id,bedNo:b.bedNumber,bedCode:b.bedCode,type:b.bedType,price:b.bedPrice||0,remark:b.remark,elderId:b.elderId,elderName:b.elderName,elderCode:b.elderCode,status:b.status}));_.value.beds=s}else k.error(o.msg||"获取床位信息失败")}catch(o){console.error("获取床位信息失败:",o),k.error("获取床位信息失败")}}let I=function(){W.value=[],U.value=0,ee.value=[],M.value=0,ae.value=[],R.value=0};function ve(){console.log("getDataByLeftTreeNode"),D.value==="clear"?Y():D.value==="vent"?B():D.value==="disinfect"?P():D.value==="uv"&&handleSearchuv()}function Y(){X.value=!0,y.value&&(p.value.roomId=y.value),console.log("clearSearch.value",p.value),Le(p.value).then(i=>{de.value=i.rows,q.value=i.total,X.value=!1})}function B(){Q.value=!0,y.value&&(c.value.roomId=y.value),console.log(c.value,"444444444"),Ke(c.value).then(i=>{i.code===200?(W.value=i.rows,U.value=i.total,Q.value=!1):k.error(i.msg||"查询房间通风记录失败")})}function P(){Z.value=!0,y.value&&(g.value.roomId=y.value),Fe(g.value).then(i=>{i.code===200?(ee.value=i.rows,M.value=i.total,Z.value=!1):k.error(i.msg||"查询房间消毒记录失败")})}function _e(i){switch(i){case"clear":I(),Y();break;case"vent":I(),B();break;case"disinfect":I(),P();break;case"uv":I(),te()}}function te(){le.value=!0,y.value&&(v.value.roomId=y.value),Oe(v.value).then(i=>{i.code===200?(ae.value=i.rows,R.value=i.total,le.value=!1):k.error(i.msg||"查询房间紫外线消毒记录失败")})}const be=async()=>{G.value=!0;try{const i=await Ie();i.code===200&&(re.value=i.data)}catch(i){console.error("获取房间树形数据失败:",i),k.error("获取楼栋信息失败")}G.value=!1},ue=u(window.innerHeight);return xe(()=>{be()}),fe(),(i,o)=>{var pe;const s=f("el-icon"),b=f("el-tree"),he=f("el-scrollbar"),we=f("el-tag"),n=f("el-table-column"),T=f("el-table"),L=f("el-date-picker"),m=f("el-form-item"),V=f("el-input"),F=f("el-button"),O=f("el-form"),K=f("Check"),ye=f("Close"),$=f("pagination"),E=f("el-tab-pane"),Se=f("el-tabs"),C=ke("loading");return S(),oe("div",$e,[r("div",Ee,[r("div",{style:ne({height:l(ue)-84-24+"px"}),class:"sidebar-left"},[He,N((S(),x(he,{style:ne({maxHeight:l(ue)-84-24-17-31+"px"})},{default:t(()=>[e(b,{"current-node-key":l(y),data:l(re),"expand-on-click-node":!1,props:ce,"default-expand-all":"","highlight-current":"","node-key":"id",onNodeClick:ge},{default:t(({node:a,data:H})=>[e(s,{size:16,color:"#409EFF",style:{"margin-right":"4px"}},{default:t(()=>[H.type==="building"?(S(),x(l(Ye),{key:0})):H.type==="floor"?(S(),x(l(Te),{key:1})):H.type==="room"?(S(),x(l(ze),{key:2})):Ce("",!0)]),_:2},1024),r("span",null,d(H.label),1)]),_:1},8,["current-node-key","data"])]),_:1},8,["style"])),[[C,l(G)]])],4),r("div",je,[l(_).roomNo?N((S(),oe("div",Ae,[r("div",Ge,[r("span",Je,d(l(_).roomNo??"-"),1),e(we,{size:"small",style:{"margin-left":"8px"},type:"danger"},{default:t(()=>[h(d(l(_).status||"未知"),1)]),_:1})]),r("div",Xe,"房间朝向："+d(l(_).direction??"-"),1),r("div",qe,"房间类型："+d(l(_).roomType??"-"),1),r("div",Qe,"房间区域："+d(l(_).zone??"-"),1),r("div",We,"负责人："+d(l(_).managerName??"-"),1),Ze,e(T,{data:((pe=l(_))==null?void 0:pe.beds)??[],border:"",size:"small",style:{"margin-top":"8px"}},{default:t(()=>[e(n,{align:"center",label:"序号",prop:"bedNo",width:"45"}),e(n,{label:"姓名",prop:"elderCode"},{default:t(a=>[r("span",{style:ne(a.row.elderCode?"":"color: #41a635;")},d(a.row.elderCode?a.row.elderName+"  |  "+a.row.elderCode:"空闲"),5)]),_:1}),e(n,{align:"center",label:"床位类型",prop:"type",width:"80"})]),_:1},8,["data"])])),[[C,l(J)]]):(S(),oe("div",el,al))]),r("div",tl,[e(Se,{modelValue:l(D),"onUpdate:modelValue":o[20]||(o[20]=a=>Ue(D)?D.value=a:null),class:"data-tabs",onTabChange:_e},{default:t(()=>[e(E,{label:"清洁记录",name:"clear"},{default:t(()=>[r("div",ol,[e(O,{inline:!0,model:l(p)},{default:t(()=>[e(m,{label:"清洁日期："},{default:t(()=>[e(L,{modelValue:l(p).cleaningDate,"onUpdate:modelValue":o[0]||(o[0]=a=>l(p).cleaningDate=a),placeholder:"选择",style:{width:"140px"},type:"date","value-format":"YYYY-MM-DD",value:"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"清洁人员："},{default:t(()=>[e(V,{modelValue:l(p).cleaningStaff,"onUpdate:modelValue":o[1]||(o[1]=a=>l(p).cleaningStaff=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"监管人员："},{default:t(()=>[e(V,{modelValue:l(p).supervisor,"onUpdate:modelValue":o[2]||(o[2]=a=>l(p).supervisor=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(F,{type:"primary",onClick:Y},{default:t(()=>[h("查询")]),_:1})]),_:1})]),_:1},8,["model"])]),N((S(),x(T,{data:l(de),border:"",stripe:"",style:{"margin-top":"10px"}},{default:t(()=>[e(n,{align:"center",label:"序号",width:"60",type:"index"},{default:t(a=>[r("span",null,d((l(p).pageNum-1)*l(p).pageSize+a.$index+1),1)]),_:1}),e(n,{align:"center",label:"清洁日期",prop:"cleaningDate",width:"110"}),e(n,{align:"center",label:"清洁时间",prop:"cleaningTime",width:"90"},{default:t(({row:a})=>[h(d(l(z)(a.cleaningTime,"{h}:{m}")),1)]),_:1}),e(n,{align:"center",label:"清洁内容","min-width":"280",prop:"cleaningContent"},{default:t(a=>[e(s,{class:"iconCssOK"},{default:t(()=>[e(K)]),_:1}),h("地面 "),e(s,{class:"iconCssOK"},{default:t(()=>[e(K)]),_:1}),h("桌椅 "),e(s,{class:"iconCssOK"},{default:t(()=>[e(K)]),_:1}),h("卫生间 "),e(s,{class:"iconCssOK"},{default:t(()=>[e(K)]),_:1}),h("楼道"),e(s,{class:"iconCssNO"},{default:t(()=>[e(ye)]),_:1}),h("垃圾桶 ")]),_:1}),e(n,{align:"center",label:"清洁人员","min-width":"100",prop:"cleaningStaff"}),e(n,{align:"center",label:"监管人员","min-width":"100",prop:"supervisor"}),e(n,{align:"center",label:"记录人","min-width":"100",prop:"recorder"}),e(n,{align:"center",label:"备注","min-width":"100",prop:"remark"})]),_:1},8,["data"])),[[C,l(X)]]),N(e($,{total:l(q),page:l(p).pageNum,"onUpdate:page":o[3]||(o[3]=a=>l(p).pageNum=a),limit:l(p).pageSize,"onUpdate:limit":o[4]||(o[4]=a=>l(p).pageSize=a),onPagination:Y},null,8,["total","page","limit"]),[[j,l(q)>0]])]),_:1}),e(E,{label:"通风记录",name:"vent"},{default:t(()=>[r("div",nl,[e(O,{inline:!0,model:l(c)},{default:t(()=>[e(m,{label:"通风日期："},{default:t(()=>[e(L,{modelValue:l(c).ventilationDate,"onUpdate:modelValue":o[5]||(o[5]=a=>l(c).ventilationDate=a),placeholder:"选择",style:{width:"140px"},type:"date","value-format":"YYYY-MM-DD",value:"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"通风人员："},{default:t(()=>[e(V,{modelValue:l(c).ventilationStaff,"onUpdate:modelValue":o[6]||(o[6]=a=>l(c).ventilationStaff=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"监管人员："},{default:t(()=>[e(V,{modelValue:l(c).supervisor,"onUpdate:modelValue":o[7]||(o[7]=a=>l(c).supervisor=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(F,{type:"primary",onClick:B},{default:t(()=>[h("查询")]),_:1})]),_:1})]),_:1},8,["model"])]),N((S(),x(T,{data:l(W),border:"",stripe:"",style:{"margin-top":"10px"}},{default:t(()=>[e(n,{align:"center",label:"序号",width:"60",type:"index"},{default:t(a=>[r("span",null,d((l(c).pageNum-1)*l(c).pageSize+a.$index+1),1)]),_:1}),e(n,{align:"center",label:"通风日期",prop:"ventilationDate",width:"120"}),e(n,{align:"center",label:"通风时间",prop:"ventilationTime",width:"120"},{default:t(({row:a})=>[h(d(l(z)(a.ventilationTime,"{h}:{i}")),1)]),_:1}),e(n,{align:"center",label:"通风措施",prop:"ventilationType"}),e(n,{align:"center",label:"通风时长",width:"120",prop:"duration"}),e(n,{align:"center",label:"通风人员",width:"120",prop:"ventilationStaff"}),e(n,{align:"center",label:"监管人员",width:"120",prop:"supervisor"}),e(n,{align:"center",label:"记录人",width:"120",prop:"recorder"})]),_:1},8,["data"])),[[C,l(Q)]]),N(e($,{total:l(U),page:l(c).pageNum,"onUpdate:page":o[8]||(o[8]=a=>l(c).pageNum=a),limit:l(c).pageSize,"onUpdate:limit":o[9]||(o[9]=a=>l(c).pageSize=a),onPagination:B},null,8,["total","page","limit"]),[[j,l(U)>0]])]),_:1}),e(E,{label:"消毒记录",name:"disinfect"},{default:t(()=>[r("div",il,[e(O,{inline:!0,model:l(g)},{default:t(()=>[e(m,{label:"消毒日期："},{default:t(()=>[e(L,{modelValue:l(g).disinfectionDate,"onUpdate:modelValue":o[10]||(o[10]=a=>l(g).disinfectionDate=a),placeholder:"选择",style:{width:"140px"},type:"date","value-format":"YYYY-MM-DD",value:"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"消毒人员："},{default:t(()=>[e(V,{modelValue:l(g).ventPerson,"onUpdate:modelValue":o[11]||(o[11]=a=>l(g).ventPerson=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"监管人员："},{default:t(()=>[e(V,{modelValue:l(g).supervisor,"onUpdate:modelValue":o[12]||(o[12]=a=>l(g).supervisor=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(F,{type:"primary",onClick:P},{default:t(()=>[h("查询")]),_:1})]),_:1})]),_:1},8,["model"])]),N((S(),x(T,{data:l(ee),border:"",stripe:"",style:{"margin-top":"10px"}},{default:t(()=>[e(n,{align:"center",label:"序号",width:"60",type:"index"},{default:t(a=>[r("span",null,d((l(g).pageNum-1)*l(g).pageSize+a.$index+1),1)]),_:1}),e(n,{label:"消毒日期",align:"center",prop:"disinfectionDate",width:"120"},{default:t(a=>[r("span",null,d(l(z)(a.row.disinfectionDate,"{y}-{m}-{d}")),1)]),_:1}),e(n,{label:"消毒时间",align:"center",prop:"disinfectionTime",width:"120"},{default:t(a=>[r("span",null,d(l(z)(a.row.disinfectionTime,"{h}-{m}")),1)]),_:1}),e(n,{label:"消毒内容",align:"center",prop:"disinfectionTarget",width:"120"}),e(n,{label:"消毒方法",align:"center",prop:"disinfectionMethod"}),e(n,{label:"消毒人员",align:"center",prop:"disinfectionStaff",width:"120"}),e(n,{label:"监督人员",align:"center",prop:"supervisor",width:"120"}),e(n,{label:"记录人",align:"center",prop:"recorder",width:"120"}),e(n,{label:"备注",align:"center",prop:"remark",width:"120"})]),_:1},8,["data"])),[[C,l(Z)]]),N(e($,{total:l(M),page:l(g).pageNum,"onUpdate:page":o[13]||(o[13]=a=>l(g).pageNum=a),limit:l(g).pageSize,"onUpdate:limit":o[14]||(o[14]=a=>l(g).pageSize=a),onPagination:P},null,8,["total","page","limit"]),[[j,l(M)>0]])]),_:1}),e(E,{label:"紫外线记录",name:"uv"},{default:t(()=>[r("div",rl,[e(O,{inline:!0,model:l(v)},{default:t(()=>[e(m,{label:"消毒日期："},{default:t(()=>[e(L,{modelValue:l(v).recordDate,"onUpdate:modelValue":o[15]||(o[15]=a=>l(v).recordDate=a),placeholder:"选择消毒日期",style:{width:"140px"},type:"date","value-format":"YYYY-MM-DD",value:"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"消毒人员："},{default:t(()=>[e(V,{modelValue:l(v).disinfectionStaffName,"onUpdate:modelValue":o[16]||(o[16]=a=>l(v).disinfectionStaffName=a),placeholder:"请输入消毒人员",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"监管人员："},{default:t(()=>[e(V,{modelValue:l(v).supervisor,"onUpdate:modelValue":o[17]||(o[17]=a=>l(v).supervisor=a),placeholder:"请输入",style:{width:"140px"},clearable:""},null,8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(F,{type:"primary",onClick:te},{default:t(()=>[h("查询")]),_:1})]),_:1})]),_:1},8,["model"])]),N((S(),x(T,{data:l(ae),border:"",stripe:"",style:{"margin-top":"10px"}},{default:t(()=>[e(n,{align:"center",label:"序号",width:"60",type:"index"},{default:t(a=>[r("span",null,d((l(v).pageNum-1)*l(v).pageSize+a.$index+1),1)]),_:1}),e(n,{label:"日期时间",align:"center",prop:"recordDate",width:"120"},{default:t(a=>[r("span",null,d(l(z)(a.row.recordDate,"{y}-{m}-{d}")),1)]),_:1}),e(n,{label:"紫外线灯编号",align:"center",prop:"uvLampCode",width:"180"}),e(n,{label:"消毒对象",align:"center",prop:"disinfectionTarget",width:"180"}),e(n,{label:"开始时间",align:"center",prop:"startTime",width:"120"},{default:t(a=>[h(d(a.row.startTime),1)]),_:1}),e(n,{label:"结束时间",align:"center",prop:"endTime",width:"120"},{default:t(a=>[r("span",null,d(a.row.endTime),1)]),_:1}),e(n,{label:"消毒时长",align:"center",prop:"duration",width:"100"},{default:t(a=>[r("span",null,d(a.row.duration+" 分钟"),1)]),_:1}),e(n,{label:"辐照强度结果",align:"center",prop:"monitoringResult",width:"120"},{default:t(a=>[r("span",null,d(a.row.monitoringResult=="ok"?"正常":"异常"),1)]),_:1}),e(n,{label:"消毒人员",align:"center",prop:"disinfectionStaffName",width:"120"}),e(n,{label:"监督人员",align:"center",prop:"supervisor",width:"120"}),e(n,{label:"记录人",align:"center",prop:"recorder",width:"120"}),e(n,{label:"备注",align:"center",prop:"remark",width:"120"})]),_:1},8,["data"])),[[C,l(le)]]),N(e($,{total:l(R),page:l(v).pageNum,"onUpdate:page":o[18]||(o[18]=a=>l(v).pageNum=a),limit:l(v).pageSize,"onUpdate:limit":o[19]||(o[19]=a=>l(v).pageSize=a),onPagination:te},null,8,["total","page","limit"]),[[j,l(R)>0]])]),_:1})]),_:1},8,["modelValue"])])])])}}},cl=Ne(dl,[["__scopeId","data-v-a9a771cb"]]);export{cl as default};
