<template>
    <div class="app-container">
        <!-- 筛选条件 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="filter-container" label-position="right"
            label-width="auto">
            <el-form-item label="排班日期">
                <el-date-picker style="width: 220px" v-model="dateRange" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="handleDateChange"></el-date-picker>
            </el-form-item>
            <el-form-item label="科室/部门" prop="deptIds">
                <el-select v-model="queryParams.deptIds" placeholder="请选择" clearable multiple style="width: 220px">
                    <el-option v-for="dept in deptOptions" :key="dept.value" :label="dept.label" :value="dept.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="区域" prop="areaIds">
                <el-select v-model="queryParams.areaIds" placeholder="请选择" clearable multiple style="width: 220px">
                    <el-option v-for="area in areaOptions" :key="area.value" :label="area.label" :value="area.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="班次" prop="shiftIds">
                <el-select v-model="queryParams.shiftIds" placeholder="请选择" clearable multiple style="width: 220px">
                    <el-option v-for="shift in shiftOptions" :key="shift.value" :label="shift.label"
                        :value="shift.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员" prop="person">
                <el-input v-model="queryParams.person" placeholder="姓名/工号" clearable style="width: 220px" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 220px">
                    <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
                        :value="status.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
                <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <el-row :gutter="10" class="mb8" justify="end">
            <el-col :span="1.5"><el-button type="primary" :icon="Plus"
                    @click="showAddPersonDialog">增加人员</el-button></el-col>
            <el-col :span="1.5"><el-button type="info" plain :icon="Upload"
                    @click="showImportDialog">导入</el-button></el-col>
            <!-- <el-col :span="1.5"><el-button type="warning" plain :icon="DocumentCopy" @click="handleCopy">复制</el-button></el-col>
      <el-col :span="1.5"><el-button type="danger" plain :icon="Unlock" @click="handleUnlock">解除锁定</el-button></el-col> -->
            <el-col :span="1.5"><el-button type="success" plain :icon="DataAnalysis"
                    @click="showWorkHoursDialog">工时统计</el-button></el-col>
            <el-col :span="1.5"><el-button type="info" plain :icon="InfoFilled"
                    @click="showScheduleHelpDialog">排班使用说明</el-button></el-col>
            
            <!-- <el-col :span="1.5">
        <el-button-group>
          <el-button type="default" @click="setView('week')">周视图</el-button>
          <el-button type="default" @click="setView('month')">月视图</el-button>
        </el-button-group>
      </el-col> -->
        </el-row>

        <!-- 数据表 -->
        <div class="schedule-header">
            <el-button type="primary" plain :icon="ArrowLeft" @click="prevPeriod">上一周期</el-button>
            <h3>{{ currentYear }}年{{ currentMonth }}月排班表</h3>
            <el-button type="primary" plain @click="nextPeriod">下一周期<el-icon class="el-icon--right">
                    <ArrowRight />
                </el-icon></el-button>
        </div>
        <el-table :data="scheduleData" border style="width: 100%" @cell-mouseover="handleCellMouseOver"
            ref="scheduleTable">
            <el-table-column prop="name" label="姓名" width="100" fixed></el-table-column>
            <el-table-column v-for="day in dateColumns" :key="day.prop" :prop="day.prop" :label="day.label" width="120"
                align="center">
                <template #default="scope">
                    <div :class="getCellClass(scope.row, day.prop)"
                        @mousedown="(e) => handleCellMouseDown(scope.row, { property: day.prop }, e.target, e)"
                        @mouseover="(e) => handleCellMouseOver(scope.row, { property: day.prop }, e.target, e)"
                        @click="(e) => handleCellClick(scope.row, { property: day.prop }, e.target, e)"
                        @dblclick="(e) => openCellEditor(scope.row, { property: day.prop })"
                        @contextmenu="(e) => handleCellContextMenu(scope.row, { property: day.prop }, e.target, e)"
                        style="cursor: pointer; user-select: none;">
                        <div>{{ getShiftText(scope.row[day.prop]) }}</div>
                        <div>{{ getAreaText(scope.row[day.prop]) }}</div>
                        <div v-if="isConflict(scope.row, day.prop)" class="conflict-marker"></div>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 右键菜单 -->
        <div v-if="contextMenuVisible"
            :style="{ top: contextMenuPosition.top + 'px', left: contextMenuPosition.left + 'px' }" class="context-menu"
            @click.stop>
            <ul class="context-menu-list">
                <li @click="handleSetShift">设置班次</li>
                <li @click="handleCancelShift">取消班次</li>
            </ul>
        </div>

        <!-- 单元格编辑弹窗 -->
        <el-dialog v-model="cellDialogVisible" title="设置排班" width="300px">
            <el-form :model="cellForm" label-width="60px">
                <el-form-item label="班次">
                    <el-select v-model="cellForm.shift" placeholder="请选择班次">
                        <el-option v-for="shift in shiftOptions" :key="shift.value" :label="shift.label"
                            :value="shift.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="区域">
                    <el-select v-model="cellForm.area" placeholder="请选择区域">
                        <el-option label="-" value="-"></el-option>
                        <el-option v-for="area in areaOptions" :key="area.value" :label="area.label"
                            :value="area.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span>
                    <el-button @click="cellDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="saveCellData">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 批量设置弹窗 -->
        <el-dialog v-model="batchEditDialogVisible" title="批量设置排班" width="300px">
            <el-form :model="batchEditForm" label-width="60px">
                <el-form-item label="班次">
                    <el-select v-model="batchEditForm.shift" placeholder="请选择班次" clearable>
                        <el-option v-for="shift in shiftOptions" :key="shift.value" :label="shift.label"
                            :value="shift.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="区域">
                    <el-select v-model="batchEditForm.area" placeholder="请选择区域" clearable>
                        <el-option label="-" value="-"></el-option>
                        <el-option v-for="area in areaOptions" :key="area.value" :label="area.label"
                            :value="area.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span>
                    <el-button @click="batchEditDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="applyBatchEdit">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 增加人员弹窗 -->
        <el-dialog v-model="addPersonDialogVisible" title="增加待排班人员" width="600px">
            <el-transfer v-model="selectedPersons" :data="allPersons" :titles="['待选列表', '已选列表']"
                :props="{ key: 'id', label: 'name' }" :filter-method="filterMethod" filterable></el-transfer>
            <template #footer>
                <span>
                    <el-button @click="addPersonDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="addPersonConfirm">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 工时统计弹窗 -->
        <el-dialog v-model="workHoursDialogVisible" title="工时统计" width="700px">
            <el-form :inline="true">
                <el-form-item label="统计日期">
                    <el-date-picker v-model="workHoursDateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="calculateWorkHours">统计</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="workHoursData" border>
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="workDays" label="出勤天数"></el-table-column>
                <el-table-column prop="restDays" label="休息天数"></el-table-column>
                <el-table-column prop="workHours" label="总工时 (小时)"></el-table-column>
            </el-table>
        </el-dialog>

        <!-- 导入弹窗 -->
        <el-dialog v-model="importDialogVisible" title="导入排班表" width="400px">
            <div>
                <p>请按照数据模板的格式准备导入数据。</p>
                <el-link type="primary" href="/template.xlsx" download>下载模板</el-link>
                <el-upload class="upload-demo" drag action="https://jsonplaceholder.typicode.com/posts/"
                    :on-success="handleImportSuccess" style="margin-top: 20px;">
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
            </div>
        </el-dialog>

        <!-- 排班使用说明弹窗 -->
        <el-dialog v-model="helpDialogVisible" title="排班使用说明" width="600px">
            <div style="line-height: 2;">
                <h3>1. 单元格设置</h3>
                <p>(1) 点击单元格可选中单元格；</p>
                <p>(2) 在单元格上点击右键可弹出菜单：设置班次、取消班次；</p>
                
                <h3>2. 批量设置</h3>
                <p>(1) 按住Ctrl键点击可选择多个不连续的单元格；</p>
                <p>(2) 按住鼠标左键拖动可选择连续的单元格</p>
                <p>(3) 在选中的单元格上点击右键可弹出菜单：设置班次、取消班次</p>
                <p>(4) 点击"设置班次"会弹出设置窗口，选择班次和区域后点击确定更新所有选中单元格</p>
                <p>(5) 点击"取消班次"会清空所有选中单元格的内容</p>
            </div>
            <template #footer>
                <el-button type="primary" @click="helpDialogVisible = false">知道了</el-button>
            </template>
        </el-dialog>



    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import moment from 'moment';
import { ElMessage } from 'element-plus';
import {
    ArrowLeft,
    ArrowRight,
    Plus,
    Upload,
    DocumentCopy,
    Unlock,
    DataAnalysis,
    Search,
    Refresh,
    UploadFilled,
    InfoFilled
} from '@element-plus/icons-vue';


// --- 响应式状态定义 ---

// 查询参数
const queryParams = reactive({ deptIds: [], areaIds: [], shiftIds: [], person: '', status: null });
const dateRange = ref([]);
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth() + 1);

// 筛选选项
const deptOptions = ref([{ label: '内科', value: 1 }, { label: '外科', value: 2 }, { label: '护理部', value: 3 }]);
const areaOptions = ref([{ label: 'A区', value: 'A' }, { label: 'B区', value: 'B' }, { label: 'C区', value: 'C' }]);
const shiftOptions = ref([
    { label: '早班', value: 'D', hours: 8 },
    { label: '中班', value: 'M', hours: 8 },
    { label: '晚班', value: 'N', hours: 8 },
    { label: '夜班', value: 'E', hours: 8 },
    { label: '休班', value: 'R', hours: 0 },
]);
const statusOptions = ref([{ label: '未排班', value: 0 }, { label: '已排班', value: 1 }]);

// 表格数据
const scheduleData = ref([]);
const dateColumns = ref([]);

// 单元格编辑
const cellDialogVisible = ref(false);
const cellForm = reactive({ shift: '', area: '' });
const currentCell = reactive({ row: null, prop: '' });

// 增加人员
const addPersonDialogVisible = ref(false);
const allPersons = ref([
    { id: 1, name: '张三', disabled: false }, { id: 2, name: '李四', disabled: false },
    { id: 3, name: '王五', disabled: false }, { id: 4, name: '赵六', disabled: false },
    { id: 5, name: '孙七', disabled: false }, { id: 6, name: '周八', disabled: false },
    { id: 7, name: '吴九', disabled: false }, { id: 8, name: '郑十', disabled: false },
    { id: 9, name: '冯十一', disabled: false }, { id: 10, name: '陈十二', disabled: false },
]);
const selectedPersons = ref([]);

// 批量选择
const isSelecting = ref(false);
const startCell = ref(null);
const selectedCells = ref([]);
const contextMenuVisible = ref(false);
const contextMenuPosition = reactive({ top: 0, left: 0 });

// 批量编辑
const batchEditDialogVisible = ref(false);
const batchEditForm = reactive({ shift: '', area: '' });

// 工时统计
const workHoursDialogVisible = ref(false);
const workHoursDateRange = ref([]);
const workHoursData = ref([]);

// 导入
const importDialogVisible = ref(false);

// --- 方法定义 ---

// 初始化
const initDefaultDateRange = () => {
    const today = moment();
    if (today.date() <= 15) {
        dateRange.value = [today.clone().startOf('month').toDate(), today.clone().startOf('month').add(14, 'days').toDate()];
    } else {
        dateRange.value = [today.clone().startOf('month').add(15, 'days').toDate(), today.clone().endOf('month').toDate()];
    }
};

const generateDateColumns = () => {
    if (!dateRange.value || dateRange.value.length !== 2) { dateColumns.value = []; return; }
    const startDate = moment(dateRange.value[0]);
    const endDate = moment(dateRange.value[1]);
    currentYear.value = startDate.year();
    currentMonth.value = startDate.month() + 1;
    const columns = [];
    let currentDate = startDate.clone();
    while (currentDate.isSameOrBefore(endDate)) {
        columns.push({
            prop: currentDate.format('YYYY-MM-DD'),
            label: `${currentDate.format('MM-DD')} (${['日', '一', '二', '三', '四', '五', '六'][currentDate.day()]})`,
        });
        currentDate.add(1, 'day');
    }
    dateColumns.value = columns;
};

const updateAllPersonsDisabledState = () => {
    const scheduledIds = scheduleData.value.map(p => p.id);
    allPersons.value.forEach(p => {
        p.disabled = scheduledIds.includes(p.id);
    });
};

const generateMockData = () => {
    const staff = [
        { id: 1, name: '张三' },
        { id: 2, name: '李四' },
        { id: 3, name: '王五' },
    ];
    const shifts = ['D', 'M', 'N', 'R'];
    const areas = ['A', 'B', 'C', '-'];

    const data = staff.map(person => {
        const row = { id: person.id, name: person.name };
        dateColumns.value.forEach((col, index) => {
            const randomShift = shifts[Math.floor(Math.random() * shifts.length)];
            const randomArea = randomShift === 'R' ? '-' : areas[Math.floor(Math.random() * areas.length)];
            row[col.prop] = { shift: randomShift, area: randomArea };
            if (person.id === 2 && index === 1) {
                row[col.prop].conflict = true;
            }
        });
        return row;
    });
    scheduleData.value = data;
    console.log("generateMockData", data);
    updateAllPersonsDisabledState();
};

const generateSchedule = () => {
    generateDateColumns();
    generateMockData();
};

// 视图切换
const setView = (type) => {
    if (type === 'week') {
        dateRange.value = [moment().startOf('week').toDate(), moment().endOf('week').toDate()];
    } else if (type === 'month') {
        dateRange.value = [moment().startOf('month').toDate(), moment().endOf('month').toDate()];
    }
    handleDateChange();
};

// 周期切换
const handleDateChange = () => generateSchedule();
const prevPeriod = () => {
    const startDate = moment(dateRange.value[0]);
    const endDate = moment(dateRange.value[1]);
    const duration = moment.duration(endDate.diff(startDate)).asDays() + 1;
    dateRange.value = [startDate.clone().subtract(duration, 'days').toDate(), endDate.clone().subtract(duration, 'days').toDate()];
    handleDateChange();
};
const nextPeriod = () => {
    const startDate = moment(dateRange.value[0]);
    const endDate = moment(dateRange.value[1]);
    const duration = moment.duration(endDate.diff(startDate)).asDays() + 1;
    dateRange.value = [startDate.clone().add(duration, 'days').toDate(), endDate.clone().add(duration, 'days').toDate()];
    handleDateChange();
};

// 查询
const handleQuery = () => ElMessage.info('模拟搜索，参数：' + JSON.stringify(queryParams));
const resetQuery = () => {
    Object.assign(queryParams, { deptIds: [], areaIds: [], shiftIds: [], person: '', status: null });
    initDefaultDateRange();
    handleDateChange();
};

// 操作按钮
const handleCopy = () => ElMessage.info('打开复制周期对话框');
const handleUnlock = () => ElMessage.warning('历史周期已锁定，确认解除？');
const showImportDialog = () => { importDialogVisible.value = true; };
const handleImportSuccess = (res, file) => {
    ElMessage.success(`${file.name} 导入成功`);
    importDialogVisible.value = false;
};

// 增加人员
const showAddPersonDialog = () => {
    selectedPersons.value = scheduleData.value.map(p => p.id);
    addPersonDialogVisible.value = true;
};
const filterMethod = (query, item) => item.name.indexOf(query) > -1;
const addPersonConfirm = () => {
    const newPersons = selectedPersons.value
        .map(id => allPersons.value.find(p => p.id === id))
        .filter(p => p && !scheduleData.value.some(sp => sp.id === p.id));

    newPersons.forEach(person => {
        const newRow = { id: person.id, name: person.name };
        dateColumns.value.forEach(col => {
            newRow[col.prop] = { shift: '', area: '-' };
        });
        scheduleData.value.push(newRow);
    });
    updateAllPersonsDisabledState();
    addPersonDialogVisible.value = false;
};

// 工时统计
const showWorkHoursDialog = () => {
    workHoursDateRange.value = dateRange.value;
    calculateWorkHours();
    workHoursDialogVisible.value = true;
};
const calculateWorkHours = () => {
    if (!workHoursDateRange.value || workHoursDateRange.value.length !== 2) return;
    const startDate = moment(workHoursDateRange.value[0]);
    const endDate = moment(workHoursDateRange.value[1]);
    workHoursData.value = scheduleData.value.map(person => {
        let workDays = 0, restDays = 0, workHours = 0;
        let currentDate = startDate.clone();
        while (currentDate.isSameOrBefore(endDate)) {
            const dateStr = currentDate.format('YYYY-MM-DD');
            const schedule = person[dateStr];
            if (schedule) {
                const shiftInfo = shiftOptions.value.find(s => s.value === schedule.shift);
                if (shiftInfo) {
                    if (shiftInfo.value === 'R') {
                        restDays++;
                    } else {
                        workDays++;
                        workHours += shiftInfo.hours;
                    }
                }
            }
            currentDate.add(1, 'day');
        }
        return { name: person.name, workDays, restDays, workHours };
    });
};

// 单元格操作
const openCellEditor = (row, column) => {
    if (!column.property || column.property === 'name') return;

    const cellId = `${row.id}|${column.property}`;
    if (!selectedCells.value.includes(cellId)) {
        selectedCells.value = [cellId];
    }

    currentCell.row = row;
    currentCell.prop = column.property;
    const data = row[column.property] || {};
    cellForm.shift = data.shift || '';
    cellForm.area = data.area || '';
    cellDialogVisible.value = true;
};

const handleCellClick = (row, column, cell, event) => {
    if (!column.property || column.property === 'name') return;

    // 检查是否过期日期
    const currentDate = moment(column.property);
    if (currentDate.isBefore(moment(), 'day')) {
        ElMessage.warning('当前日期已过期，不允许修改');
        return;
    }

    const cellId = `${row.id}|${column.property}`;

    // 如果是拖拽完成后的点击，忽略
    if (isSelecting.value) {
        return;
    }

    if (event.ctrlKey || event.metaKey) {
        // Ctrl+点击或Cmd+点击，切换选择状态
        event.preventDefault();
        const index = selectedCells.value.indexOf(cellId);
        if (index > -1) {
            selectedCells.value.splice(index, 1);
        } else {
            selectedCells.value.push(cellId);
        }
    } else {
        // 普通点击，单独选择
        selectedCells.value = [cellId];
    }
};

const saveCellData = () => {
    if (currentCell.row && currentCell.prop) {
        currentCell.row[currentCell.prop] = { ...cellForm };
    }
    cellDialogVisible.value = false;
    ElMessage.success('保存成功（自动保存）');
};

// 批量选择
const handleCellMouseDown = (row, column, cell, event) => {
    if (event.button !== 0 || !column.property || column.property === 'name') return;

    // 如果是Ctrl+点击，不进行拖拽选择
    if (event.ctrlKey || event.metaKey) {
        return;
    }

    event.preventDefault();
    isSelecting.value = true;
    selectedCells.value = [];
    startCell.value = { rowId: row.id, colProp: column.property };
    document.body.style.userSelect = 'none';

    // 立即选择当前单元格
    const cellId = `${row.id}|${column.property}`;
    selectedCells.value = [cellId];
};
const handleCellMouseOver = (row, column, cell, event) => {
    if (!isSelecting.value || !column.property || column.property === 'name') return;
    const endCell = { rowId: row.id, colProp: column.property };
    updateSelection(startCell.value, endCell);
};
const handleMouseUp = (event) => {
    if (isSelecting.value) {
        isSelecting.value = false;
        startCell.value = null;
        document.body.style.userSelect = '';
    }
};
const updateSelection = (start, end) => {
    const selected = [];
    const rowIndexes = [
        scheduleData.value.findIndex(r => r.id === start.rowId),
        scheduleData.value.findIndex(r => r.id === end.rowId)
    ].sort((a, b) => a - b);
    const colIndexes = [
        dateColumns.value.findIndex(c => c.prop === start.colProp),
        dateColumns.value.findIndex(c => c.prop === end.colProp)
    ].sort((a, b) => a - b);

    for (let i = rowIndexes[0]; i <= rowIndexes[1]; i++) {
        for (let j = colIndexes[0]; j <= colIndexes[1]; j++) {
            const row = scheduleData.value[i];
            const col = dateColumns.value[j];
            if (row && col) {
                selected.push(`${row.id}|${col.prop}`);
            }
        }
    }
    selectedCells.value = selected;
};

const handleCellContextMenu = (row, column, cell, event) => {
    event.preventDefault();
    if (!column.property || column.property === 'name') return;

    // 检查是否过期日期
    const currentDate = moment(column.property);
    if (currentDate.isBefore(moment(), 'day')) {
        ElMessage.warning('当前日期已过期，不允许修改');
        return;
    }

    const cellId = `${row.id}|${column.property}`;
    if (!selectedCells.value.includes(cellId)) {
        selectedCells.value = [cellId];
    }

    contextMenuVisible.value = true;
    contextMenuPosition.top = event.clientY;
    contextMenuPosition.left = event.clientX;

    document.addEventListener('click', closeContextMenu);
};

const closeContextMenu = () => {
    contextMenuVisible.value = false;
    document.removeEventListener('click', closeContextMenu);
};

const handleSetShift = () => {
    if (selectedCells.value.length === 0) {
        ElMessage.warning('没有选中的单元格');
        return;
    }
    
    // 检查是否有过期日期
    const hasExpired = selectedCells.value.some(cellId => {
        const [, colProp] = cellId.split('|');
        return moment(colProp).isBefore(moment(), 'day');
    });
    
    if (hasExpired) {
        ElMessage.warning('选中的单元格中包含已过期日期，不允许修改');
        return;
    }
    
    batchEditForm.shift = '';
    batchEditForm.area = '';
    batchEditDialogVisible.value = true;
    closeContextMenu();
};

const handleCancelShift = () => {
    if (selectedCells.value.length === 0) {
        ElMessage.warning('没有选中的单元格');
        return;
    }
    
    // 检查是否有过期日期
    const hasExpired = selectedCells.value.some(cellId => {
        const [, colProp] = cellId.split('|');
        return moment(colProp).isBefore(moment(), 'day');
    });
    
    if (hasExpired) {
        ElMessage.warning('选中的单元格中包含已过期日期，不允许修改');
        return;
    }
    
    console.log('取消所有班次', selectedCells.value);
    selectedCells.value.forEach(cellId => {
        const [rowId, colProp] = cellId.split('|');
        const rowIndex = scheduleData.value.findIndex(r => r.id == rowId);
        if (rowIndex > -1) {
            const row = scheduleData.value[rowIndex];
            // Create a new row object to ensure reactivity
            const newRow = { ...row, [colProp]: { shift: '', area: '-' } };
            scheduleData.value[rowIndex] = newRow;
        }
    });
    ElMessage.success(`已取消 ${selectedCells.value.length} 个单元格的班次`);
    selectedCells.value = [];
    closeContextMenu();
};

const applyBatchEdit = () => {
    if (selectedCells.value.length === 0) return;
    const { shift, area } = batchEditForm;
    if (!shift && !area) {
        ElMessage.warning('请至少选择班次或区域进行应用');
        return;
    }
    console.log('applyBatchEdit', selectedCells.value);
    selectedCells.value.forEach(cellId => {
        const [rowId, colProp] = cellId.split('|');
        const rowIndex = scheduleData.value.findIndex(r => r.id == rowId);
        console.log('rowIndex', rowIndex, colProp, rowId);
        if (rowIndex > -1) {
            const row = scheduleData.value[rowIndex];
            const currentData = row[colProp] || {};
            const newData = { ...currentData };
            if (shift) {
                newData.shift = shift;
                if (shift === 'R') {
                    newData.area = '-';
                }
            }
            if (area && newData.shift !== 'R') {
                newData.area = area;
            }
            console.log("newData", newData);
            // Create a new row object to ensure reactivity
            //要求更新原始日期数据格式
            //const newRow = { ...row };
            const newRow = { ...row, [colProp]: newData };
            scheduleData.value[rowIndex] = newRow;
            // console.log('修改后的行数据:', newRow);
        }
    });
    ElMessage.success(`已批量修改 ${selectedCells.value.length} 个单元格`);
    batchEditDialogVisible.value = false;
    selectedCells.value = [];
};


// 渲染辅助
const getShiftText = (data) => {
    if (!data || !data.shift) return '-';
    const shift = shiftOptions.value.find(s => s.value === data.shift);
    return shift ? shift.label : '-';
};
const getAreaText = (data) => {
    if (!data || !data.area) return '-';
    return data.area;
};
const getCellClass = (row, prop) => {
    if (!row) return ''; // 安全保护
    const cellId = `${row.id}|${prop}`;
    const data = row[prop];
    let classes = [];
    if (selectedCells.value.includes(cellId)) {
        classes.push('cell-selected');
    }
    if (!data || !data.shift) {
        classes.push('cell-empty');
    } else {
        classes.push(`cell-shift-${data.shift.toLowerCase()}`);
    }
    return classes.join(' ');
};
const isConflict = (row, prop) => {
    if (!row) return false; // 安全保护
    const data = row[prop];
    return data && data.conflict;
};

// --- 生命周期钩子 ---
onMounted(() => {
    initDefaultDateRange();
    generateSchedule();
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseUp);
});

onBeforeUnmount(() => {
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('mouseleave', handleMouseUp);
    document.removeEventListener('click', closeContextMenu);
});

// 添加状态变量
const helpDialogVisible = ref(false);

// 添加方法
const showScheduleHelpDialog = () => {
    helpDialogVisible.value = true;
};

</script>

<style scoped>
.app-container {
    padding: 20px;
}

.filter-container {
    padding-bottom: 10px;
}

.mb8 {
    margin-bottom: 8px;
}

.schedule-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
}

.schedule-header h3 {
    margin: 0 20px;
}

.el-table .cell {
    padding: 0 !important;
}

.el-table td,
.el-table th {
    padding: 4px 0 !important;
}

.el-table__row>td>.cell {
    min-height: 40px;
    line-height: 1.4;
    padding: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.cell-shift-d {
    background-color: #f0f9eb;
}

/* 早班 */
.cell-shift-m {
    background-color: #fdf6ec;
}

/* 中班 */
.cell-shift-n {
    background-color: #ecf5ff;
}

/* 晚班 */
.cell-shift-e {
    background-color: #fef0f0;
}

/* 夜班 */
.cell-shift-r {
    background-color: #f4f4f5;
}

/* 休班 */
.cell-empty {
    background-color: #ffffff;
}

.cell-selected {
    box-shadow: inset 0 0 0 2px #409EFF;
    background-color: rgba(64, 158, 255, 0.1) !important;
}

.el-table .el-table__row:nth-child(even) {
    background-color: #fafafa;
}

.conflict-marker {
    position: absolute;
    bottom: 2px;
    left: 0;
    width: 100%;
    height: 3px;
    background-image: linear-gradient(to right, red 50%, transparent 50%);
    background-size: 6px 3px;
    background-repeat: repeat-x;
    content: '';
}

.context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    border-radius: 4px;
    padding: 5px 0;
    z-index: 3000;
}

.context-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.context-menu-list li {
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

.context-menu-list li:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

.upload-demo {
    text-align: center;
}

.el-icon--upload {
    font-size: 60px;
    margin: 20px 0;
}
</style>