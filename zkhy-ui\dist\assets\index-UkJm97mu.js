import G from"./addMedicationReceive-GHFSjs7r.js";import{a as K,g as Q}from"./roommanage-DBG5TiIR.js";import{n as H,w as J}from"./index-2bfkpdNb.js";import{_ as O,d as W,r as v,F as X,e as r,c as b,o as d,f as e,k as Z,h as o,i as ee,K as w,L as x,j as N,l as z,n as p,t as le,E as ae,G as U}from"./index-B0qHf98Y.js";import"./index-CCXF19OR.js";import"./leave-Dd4WELmg.js";import"./telderAttachement-C4ARfNBy.js";const te={class:"drug-receive-record-container"},oe={class:"button-group",style:{"text-align":"right"}},ne={key:0,class:"pagination-container"},ie={__name:"index",setup(re){const{proxy:f}=W(),{inventory_results:h}=f.useDict("inventory_results"),k=v([]),_=v([]),a=v({pageSize:10,pageNum:1}),C=v([]),y=v(0),I=()=>{console.log("查询",a.value),a.value.pageNum=1,s()},D=()=>{a.value={pageSize:10,pageNum:1},s()},L=()=>{f.$refs.addMedicationReceiveRef.openAdd()},R=i=>{f.$refs.addMedicationReceiveRef.openView(i)},B=i=>{f.$refs.addMedicationReceiveRef.openEdit(i)},T=i=>{console.log("删除",i),ae.confirm("注：无摆药计划药品支持删除，删除药品将失去原始数据，请慎重删除","确定删除该药品数据吗？",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await J(i.id)).code==200?(U.success("删除成功"),a.value.pageNum=1,s()):U.success("删除失败")})},Y=async i=>{_.value=[],a.value.floorId="";const t=await K(i);_.value=t.rows},E=i=>{a.value.pageSize=i,s()},$=i=>{a.value.pageNum=i,s()},s=async()=>{F();const i=await H({...a.value});C.value=i.rows||[],y.value=i.total||0},F=async()=>{const i=await Q();k.value=i.rows||[]};return X(()=>{s()}),(i,t)=>{const S=r("el-date-picker"),u=r("el-form-item"),m=r("el-input"),g=r("el-option"),V=r("el-select"),c=r("el-button"),A=r("el-form"),n=r("el-table-column"),P=r("dict-tag"),j=r("el-table"),q=r("el-pagination");return d(),b("div",te,[e(A,{inline:!0,model:a.value,class:"search-form","label-width":"100px"},{default:o(()=>[e(u,{label:"收药时间",prop:"collectionTime"},{default:o(()=>[e(S,{modelValue:a.value.collectionTime,"onUpdate:modelValue":t[0]||(t[0]=l=>a.value.collectionTime=l),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(u,{label:"老人姓名",prop:"elderName"},{default:o(()=>[e(m,{modelValue:a.value.elderName,"onUpdate:modelValue":t[1]||(t[1]=l=>a.value.elderName=l),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"楼栋信息",prop:"buildingId"},{default:o(()=>[e(V,{modelValue:a.value.buildingId,"onUpdate:modelValue":t[2]||(t[2]=l=>a.value.buildingId=l),placeholder:"全部",style:{width:"200px"},clearable:"",onChange:Y},{default:o(()=>[e(g,{label:"全部",value:""}),(d(!0),b(w,null,x(k.value,l=>(d(),N(g,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"楼栋层数",prop:"floorId"},{default:o(()=>[e(V,{modelValue:a.value.floorId,"onUpdate:modelValue":t[3]||(t[3]=l=>a.value.floorId=l),placeholder:"全部",style:{width:"200px"},clearable:"",disabled:!a.value.buildingId},{default:o(()=>[e(g,{label:"全部",value:""}),(d(!0),b(w,null,x(_.value,l=>(d(),N(g,{key:l.value,label:l.floorName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(u,{label:"房间号",prop:"roomNumber"},{default:o(()=>[e(m,{modelValue:a.value.roomNumber,"onUpdate:modelValue":t[4]||(t[4]=l=>a.value.roomNumber=l),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"药品名称",prop:"medicationName"},{default:o(()=>[e(m,{modelValue:a.value.medicationName,"onUpdate:modelValue":t[5]||(t[5]=l=>a.value.medicationName=l),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"有效期",prop:"expiryDate"},{default:o(()=>[e(S,{modelValue:a.value.expiryDate,"onUpdate:modelValue":t[6]||(t[6]=l=>a.value.expiryDate=l),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(u,{label:"送药人",prop:"delegator"},{default:o(()=>[e(m,{modelValue:a.value.delegator,"onUpdate:modelValue":t[7]||(t[7]=l=>a.value.delegator=l),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"收取人",prop:"collector"},{default:o(()=>[e(m,{modelValue:a.value.collector,"onUpdate:modelValue":t[8]||(t[8]=l=>a.value.collector=l),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),e(u,{label:"状态",prop:"medicationStatus"},{default:o(()=>[e(V,{modelValue:a.value.medicationStatus,"onUpdate:modelValue":t[9]||(t[9]=l=>a.value.medicationStatus=l),placeholder:"全部",style:{width:"200px"},clearable:""},{default:o(()=>[(d(!0),b(w,null,x(z(h),l=>(d(),N(g,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),ee("div",oe,[e(c,{type:"primary",onClick:I,icon:"search"},{default:o(()=>[p("查询")]),_:1}),e(c,{onClick:D,icon:"refresh"},{default:o(()=>[p("重置")]),_:1}),e(c,{icon:"Plus",type:"primary",onClick:L,plain:""},{default:o(()=>[p("新增药品")]),_:1})])]),_:1},8,["model"]),e(j,{data:C.value,border:"",style:{width:"100%"}},{default:o(()=>[e(n,{prop:"id",label:"序号",width:"60",align:"center"},{default:o(l=>[p(le(l.$index+1),1)]),_:1}),e(n,{prop:"collectionTime",label:"收药时间",align:"center","min-width":"120"}),e(n,{prop:"elderName",label:"老人姓名",align:"center"}),e(n,{prop:"floorNumber",label:"楼层信息",align:"center"}),e(n,{prop:"roomNumber",label:"房间号",align:"center"}),e(n,{prop:"buildingName",label:"楼栋信息",align:"center"}),e(n,{prop:"medicationId",label:"药品编号",align:"center"}),e(n,{prop:"medicationName",label:"药品名称",align:"center","min-width":"140"}),e(n,{prop:"specification",label:"药品规格",align:"center","min-width":"180"}),e(n,{prop:"specificationQuantity",label:"规格数量",align:"center"}),e(n,{prop:"quantity",label:"药品数量",align:"center"}),e(n,{prop:"dosage",label:"用量",align:"center"}),e(n,{prop:"administrationMethod",label:"服用方法",align:"center"}),e(n,{prop:"expiryDate",label:"有效期",align:"center","min-width":"180"}),e(n,{prop:"manufacturer",label:"生产厂家",align:"center"}),e(n,{prop:"delegator",label:"送药人",align:"center"}),e(n,{prop:"collector",label:"收取人",align:"center"}),e(n,{prop:"medicationStatus",label:"状态",align:"center"},{default:o(l=>[e(P,{options:z(h),value:l.row.medicationStatus},null,8,["options","value"])]),_:1}),e(n,{label:"操作","min-width":"220",fixed:"right",align:"center"},{default:o(l=>[e(c,{link:"",type:"primary",onClick:M=>R(l.row),icon:"Search"},{default:o(()=>[p("查看")]),_:2},1032,["onClick"]),e(c,{link:"",type:"primary",onClick:M=>B(l.row),icon:"Edit"},{default:o(()=>[p("修改")]),_:2},1032,["onClick"]),e(c,{link:"",type:"primary",onClick:M=>T(l.row),icon:"Delete"},{default:o(()=>[p("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),y.value>0?(d(),b("div",ne,[e(q,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[10]||(t[10]=l=>a.value.pageNum=l),"page-size":a.value.pageSize,"onUpdate:pageSize":t[11]||(t[11]=l=>a.value.pageSize=l),"page-sizes":[10,20,30,40],total:y.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:E,onCurrentChange:$},null,8,["current-page","page-size","total"])])):Z("",!0),e(G,{ref:"addMedicationReceiveRef",onSuccess:D},null,512)])}}},ve=O(ie,[["__scopeId","data-v-d705f2d0"]]);export{ve as default};
