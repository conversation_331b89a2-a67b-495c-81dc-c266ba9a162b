import{g as Q,u as J,a as M,b as pe}from"./tWarehouseMedication-Ycu1QDaW.js";import{_ as me,B as be,u as fe,a as ce,d as _e,r as v,C as ve,N as Ve,e as c,c as V,f as e,h as a,l as o,i as p,k as S,t as X,o as b,j as g,K as y,L as h,n as Y,v as ge,x as ye}from"./index-B0qHf98Y.js";const C=I=>(ge("data-v-998580b4"),I=I(),ye(),I),he={class:"app-container contentDiv"},Ue={class:"bottom_room_table"},we={class:"title_room"},Ce=C(()=>p("h3",{class:"page-title"},"基本信息",-1)),ke={class:"hfyCodeCSS"},xe={class:"bottom_room_table"},Ie={class:"title_room"},Fe=C(()=>p("h3",null,"包装信息",-1)),Ne={class:"bottom_room_table"},$e={class:"title_room"},Oe=C(()=>p("h3",{class:"titleCss"},"库存信息",-1)),De={class:"bottom_room_table"},Pe={class:"title_room"},Me=C(()=>p("h3",{class:"page-title"},"基本信息",-1)),Se={class:"hfyCodeCSS"},Ye={class:"bottom_room_table"},Be={class:"title_room"},Te=C(()=>p("h3",null,"包装信息",-1)),We={class:"bottom_room_table"},Re={class:"title_room"},Ee=C(()=>p("h3",{class:"titleCss"},"服用信息",-1)),Qe={class:"bottom_room_table"},He={class:"title_room"},qe=C(()=>p("h3",{class:"titleCss"},"库存信息",-1)),Ge=be({name:"AddMedication"}),Le=Object.assign(Ge,{setup(I){const _=fe(),U=ce(),{proxy:f}=_e(),{medication_type:Z,medication_dosage:ee,is_otc:le,invoice_items:H,packing_unit:q,dosage_unit:je,usage_type:Ae,goods_status:G}=f.useDict("medication_type","medication_dosage","is_otc","invoice_items","packing_unit","dosage_unit","usage_type","goods_status");v([]);const F=v(!1);v(!0);const u=v(!0);v([]);const N=v(""),B=v(""),$=v(""),O=v("first"),T=v(""),L=v(""),ae=ve({form:{status:"1"},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0},rules:{medicineName:[{required:!0,message:"请输入药品名称",trigger:"blur"}]},form2:{status:"1"},rules2:{medicineName:[{required:!0,message:"请输入药品名称",trigger:"blur"}]}}),{queryParams:ze,form:t,rules:de,form2:n,rules2:oe}=Ve(ae);function te(){_.params.type=="add"?(u.value=!1,$.value="新增商品信息",Q({prefix:"HFW"}).then(m=>{B.value=m.msg,console.log(m,"newcode")})):_.params.type=="edit"?($.value="修改商品信息",u.value=!1,W()):_.params.type=="show"?($.value="查看商品信息",W(),u.value=!0):_.params.type=="copy"&&($.value="复制商品信息",W(),u.value=!1)}function W(){_.params.id&&pe(_.params.id).then(m=>{console.log(m.data,"res"),m.data.category=="物品"?(O.value="first",n.value.isOtc=m.data.isOtc,n.value=m.data,L.value="first"):m.data.category=="药品"&&(O.value="second",t.value.isOtc=m.data.isOtc,t.value=m.data,L.value="second")})}function j(){ue(),f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}function ue(){t.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},f.resetForm("addMedicationRef")}function se(){f.$refs.addMedicationRef.validate(m=>{m&&(t.value.goodsCategory="药品",t.value.id!=null&&_.params.type=="edit"?J(t.value).then(d=>{f.$modal.msgSuccess("修改成功"),f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}):_.params.type=="add"?(t.value.medicineCode=N.value,M(t.value).then(d=>{f.$modal.msgSuccess("新增成功"),F.value=!1,f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")})):_.params.type=="copy"&&M(t.value).then(d=>{f.$modal.msgSuccess("复制添加成功"),F.value=!1,f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}))})}function ne(){f.$refs.addGoodsRef.validate(m=>{m&&(n.value.goodsCategory="物品",n.value.id!=null&&_.params.type=="edit"?J(n.value).then(d=>{f.$modal.msgSuccess("修改成功"),f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}):_.params.type=="add"?(n.value.medicineCode=N.value,M(n.value).then(d=>{f.$modal.msgSuccess("新增成功"),F.value=!1,f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")})):_.params.type=="copy"&&M(n.value).then(d=>{f.$modal.msgSuccess("复制添加成功"),F.value=!1,f.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}))})}function ie(m){console.log(m,"tab"),m=="first"?(T.value="1",Q({prefix:"HFW"}).then(d=>{B.value=d.msg,console.log(d,"newcode")})):m=="second"&&(T.value="2",Q({prefix:"HFY"}).then(d=>{N.value=d.msg,console.log(d,"newcode")})),console.log(T.value,"goodsTypes")}return te(),(m,d)=>{const r=c("el-input"),s=c("el-form-item"),i=c("el-col"),k=c("el-option"),x=c("el-select"),R=c("el-radio"),E=c("el-radio-group"),w=c("el-row"),D=c("el-input-number"),A=c("el-date-picker"),P=c("el-button"),z=c("el-form"),K=c("el-tab-pane"),re=c("el-tabs");return b(),V("div",he,[e(re,{modelValue:O.value,"onUpdate:modelValue":d[49]||(d[49]=l=>O.value=l),class:"demo-tabs",onTabChange:ie},{default:a(()=>[e(K,{label:"物品信息",name:"first"},{default:a(()=>[e(z,{ref:"addGoodsRef",model:o(n),rules:o(oe),"label-width":"100px"},{default:a(()=>[p("div",Ue,[p("div",we,[Ce,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"物品编码",prop:"medicineCode"},{default:a(()=>[p("span",ke,X(B.value),1),S("",!0)]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"条形码",prop:"barcode"},{default:a(()=>[e(r,{modelValue:o(n).barcode,"onUpdate:modelValue":d[1]||(d[1]=l=>o(n).barcode=l),placeholder:"请输入条形码",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"物品名称",prop:"medicineName"},{default:a(()=>[e(r,{modelValue:o(n).medicineName,"onUpdate:modelValue":d[2]||(d[2]=l=>o(n).medicineName=l),placeholder:"请输入",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"生产厂家",prop:"manufacturer"},{default:a(()=>[e(r,{modelValue:o(n).manufacturer,"onUpdate:modelValue":d[3]||(d[3]=l=>o(n).manufacturer=l),placeholder:"请输入生产厂家",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"物品分类",prop:"category"},{default:a(()=>[e(r,{modelValue:o(n).category,"onUpdate:modelValue":d[4]||(d[4]=l=>o(n).category=l),placeholder:"请输入物品分类",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"物品规格",prop:"specification"},{default:a(()=>[e(r,{modelValue:o(n).specification,"onUpdate:modelValue":d[5]||(d[5]=l=>o(n).specification=l),placeholder:"请输入物品规格",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"发票项目",prop:"invoiceItem"},{default:a(()=>[e(x,{modelValue:o(n).invoiceItem,"onUpdate:modelValue":d[6]||(d[6]=l=>o(n).invoiceItem=l),placeholder:"请选择发票项目",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(H),l=>(b(),g(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"状态",prop:"manufacturer"},{default:a(()=>[e(E,{modelValue:o(n).status,"onUpdate:modelValue":d[7]||(d[7]=l=>o(n).status=l),placeholder:"请选择状态",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(G),l=>(b(),g(R,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",xe,[p("div",Ie,[Fe,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"包装单位",prop:"packageUnit"},{default:a(()=>[e(x,{modelValue:o(n).packageUnit,"onUpdate:modelValue":d[8]||(d[8]=l=>o(n).packageUnit=l),placeholder:"请选择包装单位",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(q),l=>(b(),g(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本系数",prop:"baseFactor"},{default:a(()=>[e(r,{modelValue:o(n).baseFactor,"onUpdate:modelValue":d[9]||(d[9]=l=>o(n).baseFactor=l),placeholder:"请输入基本系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本单位",prop:"baseUnit"},{default:a(()=>[e(r,{modelValue:o(n).baseUnit,"onUpdate:modelValue":d[10]||(d[10]=l=>o(n).baseUnit=l),placeholder:"请输入基本单位",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"剂量系数",prop:"dosageFactor"},{default:a(()=>[e(r,{modelValue:o(n).dosageFactor,"onUpdate:modelValue":d[11]||(d[11]=l=>o(n).dosageFactor=l),placeholder:"请输入剂量系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"采购价(元)",prop:"purchasePrice"},{default:a(()=>[e(r,{modelValue:o(n).purchasePrice,"onUpdate:modelValue":d[12]||(d[12]=l=>o(n).purchasePrice=l),placeholder:"请输入采购价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"零售价(元)",prop:"retailPrice"},{default:a(()=>[e(r,{modelValue:o(n).retailPrice,"onUpdate:modelValue":d[13]||(d[13]=l=>o(n).retailPrice=l),placeholder:"请输入零售价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",Ne,[p("div",$e,[Oe,e(w,null,{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"库存上限",prop:"maxInventory"},{default:a(()=>[e(D,{modelValue:o(n).maxInventory,"onUpdate:modelValue":d[14]||(d[14]=l=>o(n).maxInventory=l),placeholder:"请输入库存上限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"库存下限",prop:"minInventory"},{default:a(()=>[e(D,{modelValue:o(n).minInventory,"onUpdate:modelValue":d[15]||(d[15]=l=>o(n).minInventory=l),placeholder:"请输入库存下限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"仓库",prop:"warehouse"},{default:a(()=>[e(r,{modelValue:o(n).warehouse,"onUpdate:modelValue":d[16]||(d[16]=l=>o(n).warehouse=l),placeholder:"请输入仓库",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"货位号",prop:"locationCode"},{default:a(()=>[e(r,{modelValue:o(n).locationCode,"onUpdate:modelValue":d[17]||(d[17]=l=>o(n).locationCode=l),placeholder:"请输入货位号",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"有效期预警",prop:"expiryWarningDays"},{default:a(()=>[e(A,{clearable:"",modelValue:o(n).expiryWarningDays,"onUpdate:modelValue":d[18]||(d[18]=l=>o(n).expiryWarningDays=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择有效期预警(天)",value:"YYYY-MM-DD",disabled:u.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:24},{default:a(()=>[e(s,{label:"备注",prop:"remark"},{default:a(()=>[e(r,{modelValue:o(n).remark,"onUpdate:modelValue":d[19]||(d[19]=l=>o(n).remark=l),type:"textarea",placeholder:"请输入内容",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),S("",!0)]),_:1})])]),e(s,{class:"footer_btn",style:{"margin-left":"80%"}},{default:a(()=>[e(P,{type:"primary",onClick:ne,disabled:u.value},{default:a(()=>[Y("提 交")]),_:1},8,["disabled"]),e(P,{onClick:j},{default:a(()=>[Y("取 消")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1}),e(K,{label:"药品信息",name:"second"},{default:a(()=>[e(z,{ref:"addMedicationRef",model:o(t),rules:o(de),"label-width":"100px"},{default:a(()=>[p("div",De,[p("div",Pe,[Me,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"药品编码",prop:"medicineCode"},{default:a(()=>[p("span",Se,X(N.value),1),S("",!0)]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"条形码",prop:"barcode"},{default:a(()=>[e(r,{modelValue:o(t).barcode,"onUpdate:modelValue":d[22]||(d[22]=l=>o(t).barcode=l),placeholder:"请输入条形码",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品名称",prop:"medicineName"},{default:a(()=>[e(r,{modelValue:o(t).medicineName,"onUpdate:modelValue":d[23]||(d[23]=l=>o(t).medicineName=l),placeholder:"请输入",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"拼音码",prop:"pinyinCode"},{default:a(()=>[e(r,{modelValue:o(t).pinyinCode,"onUpdate:modelValue":d[24]||(d[24]=l=>o(t).pinyinCode=l),placeholder:"请输入拼音码",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品分类",prop:"category"},{default:a(()=>[e(x,{modelValue:o(t).category,"onUpdate:modelValue":d[25]||(d[25]=l=>o(t).category=l),placeholder:"请选择药品分类",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(Z),l=>(b(),g(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品规格",prop:"specification"},{default:a(()=>[e(r,{modelValue:o(t).specification,"onUpdate:modelValue":d[26]||(d[26]=l=>o(t).specification=l),placeholder:"请输入药品规格",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品剂型",prop:"dosageForm"},{default:a(()=>[e(x,{modelValue:o(t).dosageForm,"onUpdate:modelValue":d[27]||(d[27]=l=>o(t).dosageForm=l),placeholder:"请选择药品剂型",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(ee),l=>(b(),g(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"OTC药品",prop:"isOtc"},{default:a(()=>[e(E,{modelValue:o(t).isOtc,"onUpdate:modelValue":d[28]||(d[28]=l=>o(t).isOtc=l),placeholder:"请选择OTC药品",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(le),l=>(b(),g(R,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"发票项目",prop:"invoiceItem"},{default:a(()=>[e(x,{modelValue:o(t).invoiceItem,"onUpdate:modelValue":d[29]||(d[29]=l=>o(t).invoiceItem=l),placeholder:"请选择发票项目",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(H),l=>(b(),g(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"批准文号",prop:"approvalNumber"},{default:a(()=>[e(r,{modelValue:o(t).approvalNumber,"onUpdate:modelValue":d[30]||(d[30]=l=>o(t).approvalNumber=l),placeholder:"请输入批准文号",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"生产厂家",prop:"manufacturer"},{default:a(()=>[e(r,{modelValue:o(t).manufacturer,"onUpdate:modelValue":d[31]||(d[31]=l=>o(t).manufacturer=l),placeholder:"请输入生产厂家",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"状态",prop:"manufacturer"},{default:a(()=>[e(E,{modelValue:o(t).status,"onUpdate:modelValue":d[32]||(d[32]=l=>o(t).status=l),placeholder:"请选择状态",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(G),l=>(b(),g(R,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",Ye,[p("div",Be,[Te,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"包装单位",prop:"packageUnit"},{default:a(()=>[e(x,{modelValue:o(t).packageUnit,"onUpdate:modelValue":d[33]||(d[33]=l=>o(t).packageUnit=l),placeholder:"请选择包装单位",clearable:"",disabled:u.value},{default:a(()=>[(b(!0),V(y,null,h(o(q),l=>(b(),g(k,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本系数",prop:"baseFactor"},{default:a(()=>[e(r,{modelValue:o(t).baseFactor,"onUpdate:modelValue":d[34]||(d[34]=l=>o(t).baseFactor=l),placeholder:"请输入基本系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本单位",prop:"baseUnit"},{default:a(()=>[e(r,{modelValue:o(t).baseUnit,"onUpdate:modelValue":d[35]||(d[35]=l=>o(t).baseUnit=l),placeholder:"请输入基本单位",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"剂量系数",prop:"dosageFactor"},{default:a(()=>[e(r,{modelValue:o(t).dosageFactor,"onUpdate:modelValue":d[36]||(d[36]=l=>o(t).dosageFactor=l),placeholder:"请输入剂量系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"剂量单位",prop:"dosageUnit"},{default:a(()=>[e(r,{modelValue:o(t).dosageUnit,"onUpdate:modelValue":d[37]||(d[37]=l=>o(t).dosageUnit=l),placeholder:"请输入剂量单位",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"采购价(元)",prop:"purchasePrice"},{default:a(()=>[e(r,{modelValue:o(t).purchasePrice,"onUpdate:modelValue":d[38]||(d[38]=l=>o(t).purchasePrice=l),placeholder:"请输入采购价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"零售价(元)",prop:"retailPrice"},{default:a(()=>[e(r,{modelValue:o(t).retailPrice,"onUpdate:modelValue":d[39]||(d[39]=l=>o(t).retailPrice=l),placeholder:"请输入零售价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",We,[p("div",Re,[Ee,e(w,null,{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"用法",prop:"usageMethod"},{default:a(()=>[e(r,{modelValue:o(t).usageMethod,"onUpdate:modelValue":d[40]||(d[40]=l=>o(t).usageMethod=l),placeholder:"请输入用法",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"单次用量",prop:"singleDose"},{default:a(()=>[e(r,{modelValue:o(t).singleDose,"onUpdate:modelValue":d[41]||(d[41]=l=>o(t).singleDose=l),placeholder:"请输入单次用量",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",Qe,[p("div",He,[qe,e(w,null,{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"库存上限",prop:"maxInventory"},{default:a(()=>[e(D,{modelValue:o(t).maxInventory,"onUpdate:modelValue":d[42]||(d[42]=l=>o(t).maxInventory=l),placeholder:"请输入库存上限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"库存下限",prop:"minInventory"},{default:a(()=>[e(D,{modelValue:o(t).minInventory,"onUpdate:modelValue":d[43]||(d[43]=l=>o(t).minInventory=l),placeholder:"请输入库存下限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"仓库",prop:"warehouse"},{default:a(()=>[e(r,{modelValue:o(t).warehouse,"onUpdate:modelValue":d[44]||(d[44]=l=>o(t).warehouse=l),placeholder:"请输入仓库",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"货位号",prop:"locationCode"},{default:a(()=>[e(r,{modelValue:o(t).locationCode,"onUpdate:modelValue":d[45]||(d[45]=l=>o(t).locationCode=l),placeholder:"请输入货位号",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"有效期预警",prop:"expiryWarningDays"},{default:a(()=>[e(A,{clearable:"",modelValue:o(t).expiryWarningDays,"onUpdate:modelValue":d[46]||(d[46]=l=>o(t).expiryWarningDays=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择有效期预警(天)",value:"YYYY-MM-DD",disabled:u.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:24},{default:a(()=>[e(s,{label:"备注",prop:"remark"},{default:a(()=>[e(r,{modelValue:o(t).remark,"onUpdate:modelValue":d[47]||(d[47]=l=>o(t).remark=l),type:"textarea",placeholder:"请输入内容",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),S("",!0)]),_:1})])]),e(s,{class:"footer_btn",style:{"margin-left":"80%"}},{default:a(()=>[e(P,{type:"primary",onClick:se,disabled:u.value},{default:a(()=>[Y("提 交")]),_:1},8,["disabled"]),e(P,{onClick:j},{default:a(()=>[Y("取 消")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1},8,["modelValue"])])}}}),Xe=me(Le,[["__scopeId","data-v-998580b4"]]);export{Xe as default};
