import{_ as Il,B as kl,d as xl,u as Cl,r as g,C as wl,N as Tl,e as C,I as Ul,c as h,o as p,f as l,h as t,i as y,Q as P,j as m,k as x,l as r,K as I,L as k,n as _,t as fe,m as zl,J as Te,v as Sl,x as Nl,P as jl}from"./index-B0qHf98Y.js";import{g as Dl,a as El,C as Al}from"./tcheckin-BMjBTZf-.js";import{r as Rl,l as Ue,u as Bl}from"./telderAttachement-C4ARfNBy.js";import{l as Pl}from"./tFeeItem-CPd7lByO.js";import{g as ze}from"./paramUtil-DJB1oWef.js";import{l as Se}from"./tLiveRoom-DmSXfHxo.js";import{g as Ne,a as je}from"./roommanage-DBG5TiIR.js";import{l as De}from"./tLiveBed-B9bJPM9s.js";const Y=J=>(Sl("data-v-4fcc3f78"),J=J(),Nl(),J),Fl={class:"app-container"},Ll={class:"stepList"},$l=Y(()=>y("div",{style:{"margin-top":"10px"}},null,-1)),Yl={class:"formAll"},Ml={class:"formCss"},Ol=Y(()=>y("div",{class:"baseTitle"},"经办人信息",-1)),ql=Y(()=>y("div",{class:"baseTitle"},"基本信息",-1)),Kl=Y(()=>y("div",{class:"baseTitle"},"其他信息",-1)),Gl=Y(()=>y("div",{class:"baseTitle"},"费用信息",-1)),Hl={class:"fixed-bottom-actions"},Jl=Y(()=>y("h4",null,"添加监护人",-1)),Ql={style:{flex:"auto"}},Wl=Y(()=>y("h4",null,"费用详情",-1)),Xl={style:{flex:"auto"}},Zl=kl({name:"addElder"}),ea=Object.assign(Zl,{setup(J){const{proxy:U}=xl(),Q=Cl(),K=g(""),M=g([]),de=g(!1),Ee=g(),W=g(""),F=g([]),s=g(!1);g(!1);const Ae=g([]),X=g(!1),G=g(!1),Z=g("add"),O=g(!1),re=g([]),{sys_normal_disable:la,sys_user_sex:Re,self_careability:Be,capability_level:Pe,care_level:Fe,nursing_grade:Le,political_status:$e,residential_type:Ye,occupation_type:aa,educational_level:Me,marital_status:Oe,elderly_blood_type:qe,financial_type:ta,elderly_label:oa,relationship_elderly:me,emergency_contact:ve,payment_status:Ke,payment_method:Ge}=U.useDict("sys_normal_disable","sys_user_sex","self_careability","capability_level","care_level","nursing_grade","political_status","residential_type","occupation_type","educational_level","marital_status","elderly_blood_type","financial_type","elderly_label","relationship_elderly","emergency_contact","payment_status","payment_method"),He=wl({form:{elderInfo:{},checkIn:{roomId:null,bedId:null},guardians:[],feeContract:{},feeDetails:[],checkIns:{}},jhrform:{relationship:"01",isEmergencyContact:"1"},jhrrules:{},feeForm:{},feerules:[],rules:{elderName:[{required:!0,message:"请输入老人姓名",trigger:"blur",validator:(n,e,o)=>{console.log("form.value.elderInfo.elderName",d.value.elderInfo),d.value.elderInfo.elderName?o():o(new Error("老人姓名不能为空"))}},{min:0,max:50,message:"老人姓名长度50个字符以内",trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.elderName)==null?void 0:u.length)>50?o(new Error("老人姓名长度50个字符以内")):o()}}],elderCode:[{required:!0,message:"请输入老人编号",trigger:"blur",validator:(n,e,o)=>{d.value.elderInfo.elderCode?o():o(new Error("老人编号不能为空"))}},{min:0,max:20,message:"c",trigger:"blur",validator:(n,e,o)=>{d.value.elderInfo.elderCode.length>20?o(new Error("老人编号长度20个字符以内")):o()}}],handlerName:[{min:0,max:50,trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.feeContract.handlerName)==null?void 0:u.length)>50?o(new Error("经办人长度50个字符以内")):o()}}],orgName:[{min:0,max:50,trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.feeContract.orgName)==null?void 0:u.length)>100?o(new Error("机构名称长度100个字符以内")):o()}}],idCard:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.idCard)==null?void 0:u.length)>50?o(new Error("身份证号长度50个字符以内")):o()}}],age:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.age)==null?void 0:u.length)>4?o(new Error("老人年龄不能超过4位")):o()}}],phone:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.phone)==null?void 0:u.length)>50?o(new Error("老人电话长度50个字符以内")):o()}}],nation:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.nation)==null?void 0:u.length)>50?o(new Error("老人民族长度50个字符以内")):o()}}],buildingId:[{required:!0,message:"请输入楼栋信息",trigger:"change",validator:(n,e,o)=>{d.value.checkIn.buildingId?o():o(new Error("楼栋信息不能为空"))}}],floorId:[{required:!0,message:"请输入楼栋层数",trigger:"change",validator:(n,e,o)=>{d.value.checkIn.floorId?o():o(new Error("楼栋层数不能为空"))}}],roomId:[{required:!0,message:"请输入房间号",trigger:"change",validator:(n,e,o)=>{d.value.checkIn.roomId?o():o(new Error("房间号不能为空"))}}],bedId:[{required:!0,message:"请输入房间/床位",trigger:"change",validator:(n,e,o)=>{d.value.checkIn.bedId?o():o(new Error("房间/床位不能为空"))}}],homeAddress:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.homeAddress)==null?void 0:u.length)>200?o(new Error("家庭住址长度200个字符以内")):o()}}],workUnit:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.workUnit)==null?void 0:u.length)>100?o(new Error("工作单位长度100个字符以内")):o()}}],formerOccupation:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.formerOccupation)==null?void 0:u.length)>50?o(new Error("老人职业长度50个字符以内")):o()}}],hometown:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.hometown)==null?void 0:u.length)>50?o(new Error("籍贯长度50个字符以内")):o()}}],socialSecurityCode:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.socialSecurityCode)==null?void 0:u.length)>20?o(new Error("社保号码长度20个字符以内")):o()}}],economicSource:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.economicSource)==null?void 0:u.length)>50?o(new Error("经济来源长度50个字符以内")):o()}}],remark:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.elderInfo.remark)==null?void 0:u.length)>2e3?o(new Error("老人备注长度2000个字符以内")):o()}}],contractNo:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.feeContract.contractNo)==null?void 0:u.length)>50?o(new Error("合同编号长度50个字符以内")):o()}}],collectorName:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.feeContract.collectorName)==null?void 0:u.length)>50?o(new Error("收费人员长度50个字符以内")):o()}}],actualAmount:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.feeContract.actualAmount)==null?void 0:u.length)>12?o(new Error("收费金额长度12个字符以内")):o()}}],remark:[{trigger:"blur",validator:(n,e,o)=>{var u;((u=d.value.feeContract.remark)==null?void 0:u.length)>255?o(new Error("备注长度255个字符以内")):o()}}]},queryParamsFiles:{pageNum:1,pageSize:2e3,elderId:null},queryParamsfee:{pageNum:1,pageSize:40,elderId:null},queryParams:{pageNum:1,pageSize:100},feerules:{feeItem:[{required:!0,message:"请选择费用项目",trigger:"blur"}]},jhrrules:{name:[{required:!0,message:"请选择姓名",trigger:"blur"}]}}),{form:d,jhrform:b,jhrrules:Je,rules:Qe,feeForm:f,queryParamsFiles:ee,queryParamsfee:da,queryParams:ce,feerules:We}=Tl(He),be=g(!1),le=g(!1),ae=g([]),H=g([]),ne=g([]),ue=g([]),D=100,ge=g([]),Xe=function(n){Ue(n).then(e=>{d.value.assessment_form||(d.value.assessment_form=[]),Ae.value=e.rows.map(o=>{if(o.attachmentType=="id_card_front_photo")d.value.elderInfo.id_card_front_photo=o.filePath;else if(o.attachmentType=="id_card_back_photo")d.value.elderInfo.id_card_back_photo=o.filePath;else if(o.attachmentType=="avatar")d.value.elderInfo.avatar=o.filePath;else if(o.attachmentType=="assessment_form"||o.attachmentType=="elderl_care_level_assessment"||o.attachmentType=="personal_care_plan"||o.attachmentType=="health_checkup_form"||o.attachmentType=="pressure_ulcer_risk"||o.attachmentType=="fall_risk_informed"||o.attachmentType=="accommodation_contract"||o.attachmentType=="accommodation_document"||o.attachmentType=="notification_to_elder"||o.attachmentType=="notification_risks"||o.attachmentType=="oral_medication_authorization_form"||o.attachmentType=="notification_safety"){d.value[o.attachmentType]||(d.value[o.attachmentType]=[]);let u={};u.id=o.id,u.name=o.fileName,u.url=o.filePath,u.type=o.attachmentType,d.value[o.attachmentType].push(u)}})})};function Ze(){var n=Q.params.type,e=Q.params.id;n=="show"?(s.value=!0,ye(),_e()):n=="edit"?(s.value=!1,Z.value="edit",O.value=!0,ye(),_e()):n=="add"&&(Z.value="add",Ne().then(o=>{ae.value=o.rows}),console.log(e,n,"init----")),Pl().then(o=>{console.log(o,"init"),re.value=o.rows})}function _e(){if(Q.params.id){var n=Q.params.id;Dl(n).then(e=>{var o,u,v,i;console.log(e,"getAggregateInfo"),d.value.elderInfo=e.data.elderInfo||{},d.value.checkIn=e.data.checkIn||{},d.value.guardians=A.value=e.data.guardians||[],d.value.feeContract=e.data.feeContract?{...e.data.feeContract}:{},e.data.feeContract&&(d.value.feeContract.contractNo=e.data.feeContract.contractNo||e.data.contractNo||"",d.value.feeContract.signTime=e.data.feeContract.signTime||e.data.signTime||""),f.value=e.data.feeContract?{...e.data.feeContract}:{},d.value.feeDetails=j.value=Array.isArray(e.data.feeDetails)?[...e.data.feeDetails]:[],M.value=(o=e.data.elderInfo)!=null&&o.elderTags?(u=e.data.elderInfo)==null?void 0:u.elderTags.split(","):[],d.value.checkIn.roomId=parseInt(e.data.checkIn.roomId)||null,d.value.checkIn.bedId=parseInt(e.data.checkIn.bedId)||null,j.value=Array.isArray(e.data.feeDetails)?[...e.data.feeDetails]:[],d.value.checkIns.roomdIdbedId=(((v=e.data.elderInfo)==null?void 0:v.roomId)||"")+"-"+(((i=e.data.elderInfo)==null?void 0:i.bedId)||""),ee.value.elderId=e.data.elderInfo.id,Xe(ee.value),be.value=!1})}}function el(n){console.log(n,"item"),re.value.map(e=>{e.itemName==n&&(f.value.feeStandard=e.unitPrice)})}function ye(){Ne().then(n=>{ae.value=n.rows}),je().then(n=>{H.value=n.rows||[]}),Se(ce.value).then(n=>{console.log(n,"room"),ne.value=n.rows||[]}),De(ce.value).then(n=>{console.log(n,"bed"),ue.value=n.rows||[]})}function ll(n){d.value.elderInfo.buildingName=n,console.log(n,"val");const e=ae.value.filter(o=>o.id==n);je(e[0].id).then(o=>{console.log(o,"getFloorListByBuild"),H.value=o.rows})}function al(n){console.log(n,"111"),d.value.elderInfo.floorNumber=n,console.log(H.value,"floorList");const e=H.value.filter(o=>o.floorNumber==n);Se({floorId:e[0].id}).then(o=>{console.log(o,"getRoomListByBuild"),ne.value=o.rows})}function tl(n){De({roomId:n,checkUsed:!0}).then(e=>{console.log(e,"getUserByRoomId"),ue.value=e.rows})}function ie(){b.value={name:null,phone:null,address:null,relationship:"01",isEmergencyContact:"1"},U.resetForm("jhrRef")}function se(){f.value={id:null,feeItem:null,feeStandard:null,description:null,startTime:null,endTime:null,discount:null,actualAmount:null},U.resetForm("feeRef")}const L=g(!1),$=g(!1),A=g([]),j=g([]),c=g("01");function R(n){n==1?c.value=1:n==2?c.value==1&&(te(),U.$refs.checkInRef.validate(e=>{e&&(c.value=2)})):n==3?c.value==1&&(te(),U.$refs.checkInRef.validate(e=>{e&&(c.value=3)})):n==4?(c.value==1&&te(),U.$refs.checkInRef.validate(e=>{e&&(c.value=4)})):n==0?c.value++:n==9&&c.value>0?c.value--:n=="save"&&(te(),U.$refs.checkInRef.validate(e=>{e&&(c.value=2)}))}function te(){U.$refs.checkInRef.validate(n=>{n?(A.value.map(e=>{console.log(e.id,"item.id..."),e.id=!e.id||String(e.id).startsWith("tmp-")?"":e.id}),d.value.guardians=A.value,d.value.elderInfo.elderTags=M.value.join(","),d.value.contractNo=d.value.feeContract.contractNo||"",d.value.signTime=d.value.feeContract.signTime||"",d.value.feeDetails=j.value,ge.value.map(e=>{e.type=="id_card_front_photo"?d.value.elderInfo.idCardFrontPhoto=e.url:e.type=="id_card_back_photo"?d.value.elderInfo.idCardBackPhoto=e.url:e.type=="avatar"&&(d.value.elderInfo.avatar=e.url)}),Z.value=="add"?El(d.value).then(e=>{W.value=e.data.elderId,console.log(e,"add")}):Z.value=="edit"&&Al(d.value).then(e=>{console.log(e,"修改数据成功")})):console.log("老人信息表单 校验不通过.")})}function ol(){var e,o;le.value=!0;const n={path:"/elderInfo/checkin"};W.value||(W.value=ee.value.elderId),console.log(((e=F.value)==null?void 0:e.length)>0,"是否有附件更新ID?"),((o=F.value)==null?void 0:o.length)>0?Bl(F.value,W.value).then(u=>{le.value=!1,console.log(`最后一步保存成功: ${F.value.length}个附件`),U.$tab.closeOpenPage(n)}):setTimeout(()=>{le.value=!1,console.log("最后一步保存成功: 无附件"),U.$tab.closeOpenPage(n)},100),U.$modal.msgSuccess("保存成功")}function dl(){L.value=!0,G.value=!0,ie(),b.value.relationship="01",b.value.isEmergencyContact="1"}function rl(n){L.value=!0,b.value=n,G.value=!1}function nl(){A.value.map(n=>{n.id==b.value.id&&(n.relationship=b.value.relationship,n.name=b.value.name,n.phone=b.value.phone,n.isEmergencyContact=b.value.isEmergencyContact,n.address=b.value.address)}),L.value=!1}function ul(){U.$refs.jhrRef.validate(n=>{if(n){b.value.id=ze(),console.log(b.value,"jhrform");let e=Object.assign({},b.value);A.value.push(e),L.value=!1}})}function il(){U.$refs.jhrRef.validate(n=>{if(n){b.value.id=ze();let e=Object.assign({},b.value);A.value.push(e),ie(),console.log(b.value,"jhrform1111111")}})}function sl(){L.value=!1,ie()}function pl(n){A.value=A.value.filter(e=>e.id!==n.id)}function fl(n){M.value.splice(M.value.indexOf(n),1)}function ml(){de.value=!0,jl(()=>{Ee.value.input.focus()})}function he(){K.value&&M.value.push(K.value),de.value=!1,K.value=""}function vl(){$.value=!0}function cl(){f.value.id=Math.random()*1e3,U.$refs.feeRef.validate(n=>{if(n){var e=Object.assign({},f.value);j.value.push(e),console.log(j.value,"feeRef1111111"),se()}})}function bl(){f.value.id=Math.random()*1e3,U.$refs.feeRef.validate(n=>{if(n){var e=Object.assign({},f.value);j.value.push(e),console.log(j.value,"feeRef22222222"),$.value=!1,se()}})}function gl(){$.value=!1,se()}const E=(n,e)=>{console.log(n,"handleRemoveAtt",e),Rl(n).then(o=>{console.log(o,"删除附件信息成功"),Ue(ee.value).then(u=>{const v=d.value[e].map(i=>i.id).indexOf(n);d.value[e].splice(v,1)})})};function z(n){console.log(n,"handleGetFile---------"),n&&(Array.isArray(n)?F.value=F.value.concat(n.map(e=>e.ossId)):F.value.push(n)),ge.value.push(n[0])}function _l(n){$.value=!0,f.value=n,X.value=!0,console.log(n,"feehandleUpdate")}function yl(){j.value.map(n=>{n.id==f.value.id&&(n.feeItem=f.value.feeItem,n.feeStandard=f.value.feeStandard,n.description=f.value.description,n.startTime=f.value.startTime,n.endTime=f.value.endTime,n.discount=f.value.discount,n.actualAmount=f.value.actualAmount,n.remark=f.value.remark)}),$.value=!1}function hl(n){console.log(n.id,"rowid"),console.log(j.value,"feeTable.value"),j.value=j.value.filter(e=>(console.log(e,"item"),e.id!=n.id)),console.log(j.value,"feehandleDelete")}return Ze(),(n,e)=>{const o=C("el-col"),u=C("el-row"),v=C("el-input"),i=C("el-form-item"),B=C("el-date-picker"),w=C("el-option"),T=C("el-select"),S=C("el-table-column"),Ve=C("dict-tag"),V=C("el-button"),Ie=C("el-table"),Vl=C("el-tag"),N=C("ImageUpload"),oe=C("el-card"),pe=C("el-form"),ke=C("el-radio-button"),xe=C("el-radio-group"),Ce=C("el-drawer"),we=Ul("loading");return p(),h("div",Fl,[l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[y("div",Ll,[y("div",{class:P(c.value==1?"activeBackCss":"NoactiveBackCss"),onClick:e[0]||(e[0]=a=>R(1))},[y("span",{class:P(c.value==1?"steptitleSelect":"steptitleNoSelect")},"1.老人基本信息",2)],2),y("div",{class:P(c.value==2?"activeBackCss":"NoactiveBackCss"),onClick:e[1]||(e[1]=a=>R(2))},[y("span",{class:P(c.value==2?"steptitleSelect":"steptitleNoSelect")},"2.评估及照护信息",2)],2),y("div",{class:P(c.value==3?"activeBackCss":"NoactiveBackCss"),onClick:e[2]||(e[2]=a=>R(3))},[y("span",{class:P(c.value==3?"steptitleSelect":"steptitleNoSelect")},"3.合同及费用信息",2)],2),y("div",{class:P(c.value==4?"activeBackCss":"NoactiveBackCss"),onClick:e[3]||(e[3]=a=>R(4))},[y("span",{class:P(c.value==4?"steptitleSelect":"steptitleNoSelect")},"4.风险告知及免责声明",2)],2)])]),_:1}),l(o,{span:24},{default:t(()=>[$l]),_:1})]),_:1}),l(pe,{ref:"checkInRef",model:r(d),rules:r(Qe),"label-width":"120px"},{default:t(()=>[y("div",Yl,[y("div",Ml,[c.value==1?(p(),m(oe,{key:0,shadow:"hover"},{default:t(()=>[Ol,l(u,null,{default:t(()=>[l(o,{span:8},{default:t(()=>[l(i,{label:"经办人",prop:"handlerName",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).feeContract.handlerName,"onUpdate:modelValue":e[4]||(e[4]=a=>r(d).feeContract.handlerName=a),disabled:s.value,placeholder:"请输入经办人"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"机构名称",prop:"orgName",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).feeContract.orgName,"onUpdate:modelValue":e[5]||(e[5]=a=>r(d).feeContract.orgName=a),disabled:s.value,placeholder:"请输入机构名称"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),ql,l(u,{gutter:15},{default:t(()=>[l(o,{span:8},{default:t(()=>[l(i,{label:"老人姓名",prop:"elderName",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.elderName,"onUpdate:modelValue":e[6]||(e[6]=a=>r(d).elderInfo.elderName=a),disabled:s.value,placeholder:"请输入老人姓名"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人编号",prop:"elderCode",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.elderCode,"onUpdate:modelValue":e[7]||(e[7]=a=>r(d).elderInfo.elderCode=a),disabled:s.value||O.value,placeholder:"请输入老人编号"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"入住时间",prop:"checkInDate",size:"large"},{default:t(()=>[l(B,{modelValue:r(d).checkIn.checkInDate,"onUpdate:modelValue":e[8]||(e[8]=a=>r(d).checkIn.checkInDate=a),disabled:s.value,clearable:"",placeholder:"请选择入驻时间",style:{width:"100%"},type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"身份证号",prop:"idCard",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.idCard,"onUpdate:modelValue":e[9]||(e[9]=a=>r(d).elderInfo.idCard=a),disabled:s.value,placeholder:"请输入身份证号"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人性别",prop:"gender",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).elderInfo.gender,"onUpdate:modelValue":e[10]||(e[10]=a=>r(d).elderInfo.gender=a),disabled:s.value,placeholder:"请选择"},{default:t(()=>[(p(!0),h(I,null,k(r(Re),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人年龄",prop:"age",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.age,"onUpdate:modelValue":e[11]||(e[11]=a=>r(d).elderInfo.age=a),disabled:s.value,placeholder:"请输入老人年龄"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人生日",prop:"birthDate",size:"large"},{default:t(()=>[l(B,{modelValue:r(d).elderInfo.birthDate,"onUpdate:modelValue":e[12]||(e[12]=a=>r(d).elderInfo.birthDate=a),disabled:s.value,clearable:"",placeholder:"请选择出生日期",style:{width:"100%"},type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人电话",prop:"phone",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.phone,"onUpdate:modelValue":e[13]||(e[13]=a=>r(d).elderInfo.phone=a),disabled:s.value,placeholder:"请输入老人电话"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人民族",prop:"nation",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.nation,"onUpdate:modelValue":e[14]||(e[14]=a=>r(d).elderInfo.nation=a),disabled:s.value,placeholder:"请输入民族"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"能力等级",prop:"abilityLevel",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.abilityLevel,"onUpdate:modelValue":e[15]||(e[15]=a=>r(d).checkIn.abilityLevel=a),disabled:s.value,placeholder:"请选择能力等级"},{default:t(()=>[(p(!0),h(I,null,k(r(Pe),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"自理能力",prop:"selfCareAbility",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.selfCareAbility,"onUpdate:modelValue":e[16]||(e[16]=a=>r(d).checkIn.selfCareAbility=a),disabled:s.value,placeholder:"请选择自理能力"},{default:t(()=>[(p(!0),h(I,null,k(r(Be),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"照护等级",prop:"careLevel",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.careLevel,"onUpdate:modelValue":e[17]||(e[17]=a=>r(d).checkIn.careLevel=a),disabled:s.value,placeholder:"请选择照护等级"},{default:t(()=>[(p(!0),h(I,null,k(r(Fe),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"护理等级",prop:"nursingLevel",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.nursingLevel,"onUpdate:modelValue":e[18]||(e[18]=a=>r(d).checkIn.nursingLevel=a),disabled:s.value,placeholder:"请选择护理等级"},{default:t(()=>[(p(!0),h(I,null,k(r(Le),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"政治面貌",prop:"politicalStatus",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).elderInfo.politicalStatus,"onUpdate:modelValue":e[19]||(e[19]=a=>r(d).elderInfo.politicalStatus=a),disabled:s.value,placeholder:"请选择政治面貌"},{default:t(()=>[(p(!0),h(I,null,k(r($e),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"楼栋信息",prop:"buildingId"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.buildingId,"onUpdate:modelValue":e[20]||(e[20]=a=>r(d).checkIn.buildingId=a),style:{width:"100%"},placeholder:"全部",clearable:"",disabled:s.value||O.value,onChange:ll},{default:t(()=>[(p(!0),h(I,null,k(ae.value,a=>(p(),m(w,{key:a.value,label:a.buildingName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"楼栋层数",prop:"floorId"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.floorId,"onUpdate:modelValue":e[21]||(e[21]=a=>r(d).checkIn.floorId=a),style:{width:"100%"},placeholder:"全部",clearable:"",disabled:s.value||O.value,onChange:al},{default:t(()=>[(p(!0),h(I,null,k(H.value,a=>(p(),m(w,{key:a.value,label:a.floorName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"房  间  号",prop:"roomId"},{default:t(()=>[l(T,{disabled:s.value||O.value,modelValue:r(d).checkIn.roomId,"onUpdate:modelValue":e[22]||(e[22]=a=>r(d).checkIn.roomId=a),style:{width:"100%"},placeholder:"全部",onChange:tl,clearable:""},{default:t(()=>[(p(!0),h(I,null,k(ne.value,a=>(p(),m(w,{key:a.id,label:a.roomNumber,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"房间/床位",prop:"bedId",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.bedId,"onUpdate:modelValue":e[23]||(e[23]=a=>r(d).checkIn.bedId=a),disabled:s.value||O.value,placeholder:"请选择"},{default:t(()=>[(p(!0),h(I,null,k(ue.value,a=>(p(),m(w,{key:a.id,label:a.bedNumber,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"居住类型",prop:"residenceType",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).checkIn.residenceType,"onUpdate:modelValue":e[24]||(e[24]=a=>r(d).checkIn.residenceType=a),disabled:s.value,placeholder:"请选择"},{default:t(()=>[(p(!0),h(I,null,k(r(Ye),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:16},{default:t(()=>[l(i,{label:"家庭住址",prop:"homeAddress",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.homeAddress,"onUpdate:modelValue":e[25]||(e[25]=a=>r(d).elderInfo.homeAddress=a),disabled:s.value,placeholder:"请输入家庭住址"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(u,null,{default:t(()=>[l(o,{span:22},{default:t(()=>[l(i,{label:"监护人信息",prop:"elderName1",size:"large"}),l(Ie,{data:A.value,style:{width:"100%","margin-left":"10%"},border:"",stripe:""},{default:t(()=>[x("",!0),l(S,{align:"center",label:"与老人关系",prop:"relationship",width:"180"},{default:t(a=>[l(Ve,{options:r(me),value:a.row.relationship},null,8,["options","value"])]),_:1}),l(S,{align:"center",label:"姓名",prop:"name",width:"180"}),l(S,{align:"center",label:"联系电话",prop:"phone"}),l(S,{align:"center",label:"是否是紧急联系人",prop:"isEmergencyContact",width:"200"},{default:t(a=>[l(Ve,{options:r(ve),value:a.row.isEmergencyContact},null,8,["options","value"])]),_:1}),l(S,{label:"住址",prop:"address"}),l(S,{align:"center",label:"操作",prop:"careLevel",width:"180px",fixed:"right"},{default:t(a=>[l(V,{disabled:s.value,icon:"Edit",link:"",type:"primary",onClick:q=>rl(a.row)},{default:t(()=>[_("修改")]),_:2},1032,["disabled","onClick"]),l(V,{disabled:s.value,icon:"Delete",link:"",type:"primary",onClick:q=>pl(a.row)},{default:t(()=>[_("删除")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),l(o,{span:2},{default:t(()=>[l(V,{disabled:s.value,type:"primary",onClick:dl},{default:t(()=>[_("新增监护人")]),_:1},8,["disabled"])]),_:1})]),_:1}),Kl,l(u,{style:{"margin-top":"20px"}},{default:t(()=>[l(o,{span:8},{default:t(()=>[l(i,{label:"工作单位",prop:"workUnit",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.workUnit,"onUpdate:modelValue":e[26]||(e[26]=a=>r(d).elderInfo.workUnit=a),disabled:s.value,placeholder:"请输入工作单位"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人职业",prop:"formerOccupation",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.formerOccupation,"onUpdate:modelValue":e[27]||(e[27]=a=>r(d).elderInfo.formerOccupation=a),disabled:s.value,placeholder:"请输入老人职业"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"籍        贯",prop:"hometown",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.hometown,"onUpdate:modelValue":e[28]||(e[28]=a=>r(d).elderInfo.hometown=a),disabled:s.value,placeholder:"请输入籍贯"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"教育程度",prop:"education",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).elderInfo.education,"onUpdate:modelValue":e[29]||(e[29]=a=>r(d).elderInfo.education=a),disabled:s.value,placeholder:"请选择教育程度"},{default:t(()=>[(p(!0),h(I,null,k(r(Me),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"婚姻状况",prop:"maritalStatus",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).elderInfo.maritalStatus,"onUpdate:modelValue":e[30]||(e[30]=a=>r(d).elderInfo.maritalStatus=a),disabled:s.value,placeholder:"请选择婚姻状况"},{default:t(()=>[(p(!0),h(I,null,k(r(Oe),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"老人血型",prop:"bloodType",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).elderInfo.bloodType,"onUpdate:modelValue":e[31]||(e[31]=a=>r(d).elderInfo.bloodType=a),disabled:s.value,placeholder:"请选择老人血型"},{default:t(()=>[(p(!0),h(I,null,k(r(qe),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"社保号码",prop:"socialSecurityCode",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.socialSecurityCode,"onUpdate:modelValue":e[32]||(e[32]=a=>r(d).elderInfo.socialSecurityCode=a),disabled:s.value,placeholder:"请输入社保号码"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"经济来源",prop:"economicSource",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).elderInfo.economicSource,"onUpdate:modelValue":e[33]||(e[33]=a=>r(d).elderInfo.economicSource=a),disabled:s.value,placeholder:"请输入经济来源"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[x("",!0)]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"老人标签",prop:"elderTags"},{default:t(()=>[(p(!0),h(I,null,k(M.value,a=>(p(),m(Vl,{key:a,"disable-transitions":!1,disabled:s.value,closable:"",size:"large",style:{"margin-right":"4px"},onClose:q=>fl(a)},{default:t(()=>[_(fe(a),1)]),_:2},1032,["disabled","onClose"]))),128)),de.value?(p(),m(v,{key:0,ref:"InputRef",modelValue:K.value,"onUpdate:modelValue":e[34]||(e[34]=a=>K.value=a),class:"w-20",size:"default",style:{width:"120px"},onBlur:he,onKeyup:zl(he,["enter"])},null,8,["modelValue"])):(p(),m(V,{key:1,disabled:s.value,class:"button-new-tag",size:"default",onClick:ml},{default:t(()=>[_("+ 新增标签")]),_:1},8,["disabled"]))]),_:1})]),_:1}),l(o,{span:20},{default:t(()=>[l(i,{label:"证件照片",prop:"idCardFrontPhoto",size:"large"},{default:t(()=>[l(N,{modelValue:r(d).elderInfo.id_card_front_photo,"onUpdate:modelValue":e[35]||(e[35]=a=>r(d).elderInfo.id_card_front_photo=a),disabled:s.value,fileData:{category:"elder_profile",attachmentType:"id_card_front_photo"},fileType:["png","jpg","jpeg"],isShowTip:!0,limit:1,onSubmitParentValue:z},null,8,["modelValue","disabled"]),l(N,{modelValue:r(d).elderInfo.id_card_back_photo,"onUpdate:modelValue":e[36]||(e[36]=a=>r(d).elderInfo.id_card_back_photo=a),disabled:s.value,fileData:{category:"elder_profile",attachmentType:"id_card_back_photo"},fileType:["png","jpg","jpeg"],isShowTip:!0,limit:1,onSubmitParentValue:z},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:20},{default:t(()=>[l(i,{label:"头像照片",prop:"avatar",size:"large"},{default:t(()=>[l(N,{modelValue:r(d).elderInfo.avatar,"onUpdate:modelValue":e[37]||(e[37]=a=>r(d).elderInfo.avatar=a),disabled:s.value,fileData:{category:"elder_profile",attachmentType:"avatar"},fileType:["png","jpg","jpeg"],isShowTip:!0,limit:1,onSubmitParentValue:z},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:20},{default:t(()=>[l(i,{label:"老人备注",prop:"remark",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).checkIn.remark,"onUpdate:modelValue":e[38]||(e[38]=a=>r(d).checkIn.remark=a),disabled:s.value,placeholder:"请输入备注内容",rows:"5",type:"textarea"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),Gl,l(u,null,{default:t(()=>[l(o,{span:8},{default:t(()=>[l(i,{label:"合同编号",prop:"contractNo",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).feeContract.contractNo,"onUpdate:modelValue":e[39]||(e[39]=a=>r(d).feeContract.contractNo=a),disabled:s.value,placeholder:"请输入合同编号"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"签订日期",prop:"signTime",size:"large"},{default:t(()=>[l(B,{modelValue:r(d).feeContract.signTime,"onUpdate:modelValue":e[40]||(e[40]=a=>r(d).feeContract.signTime=a),disabled:s.value,placeholder:s.value?"-":"请选择签订日期",clearable:"",style:{width:"100%"},type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled","placeholder"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"合同开始时间",prop:"contractStarttime"},{default:t(()=>[l(B,{modelValue:r(d).feeContract.contractStarttime,"onUpdate:modelValue":e[41]||(e[41]=a=>r(d).feeContract.contractStarttime=a),disabled:s.value,placeholder:s.value?"-":"请选择合同开始时间",style:{width:"100%"},type:"date"},null,8,["modelValue","disabled","placeholder"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"合同结束时间",prop:"contractEndtime"},{default:t(()=>[l(B,{modelValue:r(d).feeContract.contractEndtime,"onUpdate:modelValue":e[42]||(e[42]=a=>r(d).feeContract.contractEndtime=a),disabled:s.value,placeholder:s.value?"-":"请选择合同结束时间",style:{width:"100%"},type:"date"},null,8,["modelValue","disabled","placeholder"])]),_:1})]),_:1})]),_:1}),l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[l(u,{style:{display:"flex","justify-content":"space-between"}},{default:t(()=>[l(i,{label:"费用信息",prop:"elderName",size:"large"}),l(V,{disabled:s.value,type:"primary",onClick:vl},{default:t(()=>[_("新增费用信息")]),_:1},8,["disabled"])]),_:1}),Te((p(),m(Ie,{data:j.value,style:{width:"100%"},border:"",stripe:""},{default:t(()=>[x("",!0),x("",!0),l(S,{align:"center",label:"费用项目",prop:"feeItem"}),l(S,{align:"center",label:"收费标准",prop:"feeStandard"}),l(S,{align:"center",label:"说明",prop:"description",width:"200px"}),l(S,{align:"center",label:"开始时间",prop:"startTime",width:"120px"}),l(S,{align:"center",label:"结束时间",prop:"endTime",width:"120px"}),l(S,{align:"center",label:"折扣/优惠",prop:"discount",width:"150px"}),l(S,{align:"center",label:"实际缴纳",prop:"actualAmount"}),l(S,{align:"center",label:"备注",prop:"careLevel"}),l(S,{align:"center",label:"操作",prop:"careLevel",width:"150px",fixed:"right"},{default:t(a=>[l(V,{disabled:s.value,icon:"Edit",link:"",type:"primary",onClick:q=>_l(a.row)},{default:t(()=>[_("修改")]),_:2},1032,["disabled","onClick"]),l(V,{disabled:s.value,icon:"Delete",link:"",type:"primary",onClick:q=>hl(a.row)},{default:t(()=>[_("删除")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])),[[we,be.value]])]),_:1})]),_:1}),l(u,{style:{"margin-top":"10px"}},{default:t(()=>[l(o,{span:8},{default:t(()=>[l(i,{label:"缴费状态",prop:"paymentStatus",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).feeContract.paymentStatus,"onUpdate:modelValue":e[43]||(e[43]=a=>r(d).feeContract.paymentStatus=a),disabled:s.value,placeholder:"请选择缴费状态"},{default:t(()=>[(p(!0),h(I,null,k(r(Ke),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"收费人员",prop:"collectorName",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).feeContract.collectorName,"onUpdate:modelValue":e[44]||(e[44]=a=>r(d).feeContract.collectorName=a),disabled:s.value,placeholder:"请输入收费人员"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"缴费时间",prop:"paymentTime",size:"large"},{default:t(()=>[l(B,{modelValue:r(d).feeContract.paymentTime,"onUpdate:modelValue":e[45]||(e[45]=a=>r(d).feeContract.paymentTime=a),disabled:s.value,clearable:"",placeholder:"请选择缴费时间",style:{width:"100%"},type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"付款方式",prop:"paymentMethod",size:"large"},{default:t(()=>[l(T,{modelValue:r(d).feeContract.paymentMethod,"onUpdate:modelValue":e[46]||(e[46]=a=>r(d).feeContract.paymentMethod=a),disabled:s.value,placeholder:"请选择付款方式"},{default:t(()=>[(p(!0),h(I,null,k(r(Ge),a=>(p(),m(w,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:8},{default:t(()=>[l(i,{label:"收费金额",prop:"actualAmount",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).feeContract.actualAmount,"onUpdate:modelValue":e[47]||(e[47]=a=>r(d).feeContract.actualAmount=a),disabled:s.value,placeholder:"请输入收费金额"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"备注",prop:"remark",size:"large"},{default:t(()=>[l(v,{modelValue:r(d).feeContract.remark,"onUpdate:modelValue":e[48]||(e[48]=a=>r(d).feeContract.remark=a),disabled:s.value,placeholder:"请输入备注",type:"textarea"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1})):x("",!0),c.value==2?(p(),m(oe,{key:1,shadow:"hover"},{default:t(()=>[l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[l(i,{label:"能力评估表","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).assessment_form,"onUpdate:modelValue":e[49]||(e[49]=a=>r(d).assessment_form=a),fileData:{category:"elder_profile",attachmentType:"assessment_form"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!0,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"和孚长者照护等级评估","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).elderl_care_level_assessment,"onUpdate:modelValue":e[50]||(e[50]=a=>r(d).elderl_care_level_assessment=a),fileData:{category:"elder_profile",attachmentType:"elderl_care_level_assessment"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"个人照料计划表","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).personal_care_plan,"onUpdate:modelValue":e[51]||(e[51]=a=>r(d).personal_care_plan=a),fileData:{category:"elder_profile",attachmentType:"personal_care_plan"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"入住老人健康体检表","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).health_checkup_form,"onUpdate:modelValue":e[52]||(e[52]=a=>r(d).health_checkup_form=a),fileData:{category:"elder_profile",attachmentType:"health_checkup_form"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"压疮风险知青同意书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).pressure_ulcer_risk,"onUpdate:modelValue":e[53]||(e[53]=a=>r(d).pressure_ulcer_risk=a),fileData:{category:"elder_profile",attachmentType:"pressure_ulcer_risk"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"跌倒知青同意书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).fall_risk_informed,"onUpdate:modelValue":e[54]||(e[54]=a=>r(d).fall_risk_informed=a),fileData:{category:"elder_profile",attachmentType:"fall_risk_informed"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})):x("",!0),c.value==3?(p(),m(oe,{key:2,shadow:"hover"},{default:t(()=>[l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[l(i,{label:"入住合同","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).accommodation_contract,"onUpdate:modelValue":e[55]||(e[55]=a=>r(d).accommodation_contract=a),fileData:{category:"elder_profile",attachmentType:"accommodation_contract"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"入住协议书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).accommodation_document,"onUpdate:modelValue":e[56]||(e[56]=a=>r(d).accommodation_document=a),fileData:{category:"elder_profile",attachmentType:"accommodation_document"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})):x("",!0),c.value==4?(p(),m(oe,{key:3,shadow:"hover"},{default:t(()=>[l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[l(i,{label:"入住老人及家属告知书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).notification_to_elder,"onUpdate:modelValue":e[57]||(e[57]=a=>r(d).notification_to_elder=a),fileData:{category:"elder_profile",attachmentType:"notification_to_elder"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"意外及风险告知书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).notification_risks,"onUpdate:modelValue":e[58]||(e[58]=a=>r(d).notification_risks=a),fileData:{category:"elder_profile",attachmentType:"notification_risks"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"自带口服药物委托书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).oral_medication_authorization_form,"onUpdate:modelValue":e[59]||(e[59]=a=>r(d).oral_medication_authorization_form=a),fileData:{category:"elder_profile",attachmentType:"oral_medication_authorization_form"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"长者用药安全告知书","label-width":"200px",prop:"elderName",size:"large",style:{"border-bottom":"1px solid rgb(225, 225, 225)"}},{default:t(()=>[l(N,{modelValue:r(d).notification_safety,"onUpdate:modelValue":e[60]||(e[60]=a=>r(d).notification_safety=a),fileData:{category:"elder_profile",attachmentType:"notification_safety"},fileType:["png","jpg","jpeg","doc","docx","xls","xlsx","ppt","pptx","txt","pdf"],isShowTip:!1,limit:D,onRemoveAtt:E,onSubmitParentValue:z},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})):x("",!0)]),y("div",Hl,[l(V,{disabled:c.value<=1,size:"small",style:{width:"64px",height:"28px","font-size":"13px","margin-right":"4px"},type:"primary",onClick:e[61]||(e[61]=a=>R(9))},{default:t(()=>[_("上一步")]),_:1},8,["disabled"]),c.value==1&&!s.value?(p(),m(V,{key:0,disabled:c.value!=1,size:"small",style:{width:"64px",height:"28px","font-size":"13px","margin-right":"4px"},type:"primary",onClick:e[62]||(e[62]=a=>R("save"))},{default:t(()=>[_("下一步")]),_:1},8,["disabled"])):x("",!0),s.value?(p(),m(V,{key:1,disabled:c.value<=0||c.value>=4,size:"small",style:{width:"64px",height:"28px","font-size":"13px","margin-right":"4px"},type:"primary",onClick:e[63]||(e[63]=a=>R(0))},{default:t(()=>[_("下一步")]),_:1},8,["disabled"])):x("",!0),c.value!=1&&!s.value?(p(),m(V,{key:2,disabled:c.value<=0||c.value>=4,size:"small",style:{width:"64px",height:"28px","font-size":"13px","margin-right":"4px"},type:"primary",onClick:e[64]||(e[64]=a=>R(0))},{default:t(()=>[_("下一步")]),_:1},8,["disabled"])):x("",!0),s.value?x("",!0):Te((p(),m(V,{key:3,disabled:c.value!=4||!r(d).elderInfo.elderName,size:"small",style:{width:"80px",height:"28px","font-size":"13px"},type:"primary",onClick:ol},{default:t(()=>[_("保存")]),_:1},8,["disabled"])),[[we,le.value]])])])]),_:1},8,["model","rules"]),l(Ce,{modelValue:L.value,"onUpdate:modelValue":e[71]||(e[71]=a=>L.value=a),direction:"rtl"},{header:t(()=>[Jl]),default:t(()=>[y("div",null,[l(pe,{ref:"jhrRef",model:r(b),rules:r(Je),"label-width":"120px"},{default:t(()=>[l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[l(i,{label:"姓名",prop:"name",size:"large"},{default:t(()=>[l(v,{modelValue:r(b).name,"onUpdate:modelValue":e[65]||(e[65]=a=>r(b).name=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"联系电话",prop:"phone",size:"large"},{default:t(()=>[l(v,{modelValue:r(b).phone,"onUpdate:modelValue":e[66]||(e[66]=a=>r(b).phone=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"住址",prop:"address",size:"large"},{default:t(()=>[l(v,{modelValue:r(b).address,"onUpdate:modelValue":e[67]||(e[67]=a=>r(b).address=a),placeholder:"请输入住址",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"与老人关系",prop:"relationship",size:"large"},{default:t(()=>[l(xe,{modelValue:r(b).relationship,"onUpdate:modelValue":e[68]||(e[68]=a=>r(b).relationship=a),placeholder:"请选择与老人关系",size:"large",style:{width:"100%"}},{default:t(()=>[(p(!0),h(I,null,k(r(me),a=>(p(),m(ke,{key:a.value,label:a.value},{default:t(()=>[_(fe(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"是否紧急联系人",prop:"isEmergencyContact",size:"large"},{default:t(()=>[l(xe,{modelValue:r(b).isEmergencyContact,"onUpdate:modelValue":e[69]||(e[69]=a=>r(b).isEmergencyContact=a),placeholder:"请选择是否紧急联系人",size:"large",style:{width:"100%"}},{default:t(()=>[(p(!0),h(I,null,k(r(ve),a=>(p(),m(ke,{key:a.value,label:a.value},{default:t(()=>[_(fe(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),x("",!0)]),_:1},8,["model","rules"])])]),footer:t(()=>[y("div",Ql,[G.value?(p(),m(V,{key:0,type:"primary",onClick:il},{default:t(()=>[_("连续添加")]),_:1})):x("",!0),G.value?(p(),m(V,{key:1,type:"primary",onClick:ul},{default:t(()=>[_("添加")]),_:1})):x("",!0),G.value?x("",!0):(p(),m(V,{key:2,type:"primary",onClick:nl},{default:t(()=>[_("修改")]),_:1})),l(V,{onClick:sl},{default:t(()=>[_("取消")]),_:1})])]),_:1},8,["modelValue"]),l(Ce,{modelValue:$.value,"onUpdate:modelValue":e[83]||(e[83]=a=>$.value=a),direction:"rtl"},{header:t(()=>[Wl]),default:t(()=>[y("div",null,[l(pe,{ref:"feeRef",model:r(f),rules:r(We),"label-width":"120px"},{default:t(()=>[l(u,null,{default:t(()=>[l(o,{span:24},{default:t(()=>[l(i,{label:"费用项目",prop:"feeItem",size:"large"},{default:t(()=>[l(T,{modelValue:r(f).feeItem,"onUpdate:modelValue":e[72]||(e[72]=a=>r(f).feeItem=a),disabled:s.value,placeholder:"请选择",onChange:el},{default:t(()=>[(p(!0),h(I,null,k(re.value,(a,q)=>(p(),m(w,{key:q,label:a.itemName,value:a.itemName},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"收费标准",prop:"feeStandard",size:"large"},{default:t(()=>[l(v,{modelValue:r(f).feeStandard,"onUpdate:modelValue":e[73]||(e[73]=a=>r(f).feeStandard=a),placeholder:"请输入收费标准"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"说明",prop:"description",size:"large"},{default:t(()=>[l(v,{modelValue:r(f).description,"onUpdate:modelValue":e[74]||(e[74]=a=>r(f).description=a),placeholder:"请输入说明",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"开始时间",prop:"startTime",size:"large"},{default:t(()=>[l(B,{modelValue:r(f).startTime,"onUpdate:modelValue":e[75]||(e[75]=a=>r(f).startTime=a),clearable:"",placeholder:"请选择开始时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"结束时间",prop:"endTime",size:"large"},{default:t(()=>[l(B,{modelValue:r(f).endTime,"onUpdate:modelValue":e[76]||(e[76]=a=>r(f).endTime=a),clearable:"",placeholder:"请选择结束时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"实际缴纳",prop:"actualAmount",size:"large"},{default:t(()=>[l(v,{modelValue:r(f).actualAmount,"onUpdate:modelValue":e[77]||(e[77]=a=>r(f).actualAmount=a),placeholder:"请输入实际缴纳"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:24},{default:t(()=>[l(i,{label:"备注",prop:"remark",size:"large"},{default:t(()=>[l(v,{modelValue:r(f).remark,"onUpdate:modelValue":e[78]||(e[78]=a=>r(f).remark=a),placeholder:"请输入备注",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1}),l(v,{modelValue:r(f).id,"onUpdate:modelValue":e[79]||(e[79]=a=>r(f).id=a),type:"hidden"},null,8,["modelValue"]),l(v,{modelValue:r(f).elderId,"onUpdate:modelValue":e[80]||(e[80]=a=>r(f).elderId=a),type:"hidden"},null,8,["modelValue"]),l(v,{modelValue:r(f).contractId,"onUpdate:modelValue":e[81]||(e[81]=a=>r(f).contractId=a),type:"hidden"},null,8,["modelValue"]),l(v,{modelValue:r(f).amount,"onUpdate:modelValue":e[82]||(e[82]=a=>r(f).amount=a),type:"hidden"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model","rules"])])]),footer:t(()=>[y("div",Xl,[X.value?x("",!0):(p(),m(V,{key:0,type:"primary",onClick:cl},{default:t(()=>[_("连续添加")]),_:1})),X.value?x("",!0):(p(),m(V,{key:1,type:"primary",onClick:bl},{default:t(()=>[_("添加")]),_:1})),X.value?(p(),m(V,{key:2,type:"primary",onClick:yl},{default:t(()=>[_("修改")]),_:1})):x("",!0),l(V,{onClick:gl},{default:t(()=>[_("取消")]),_:1})])]),_:1},8,["modelValue"])])}}}),va=Il(ea,[["__scopeId","data-v-4fcc3f78"]]);export{va as default};
