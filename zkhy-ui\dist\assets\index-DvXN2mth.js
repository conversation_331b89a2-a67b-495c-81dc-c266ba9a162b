import{j as $,k as Y}from"./wmscheckinOut-C2B7FNiS.js";import{_ as j,d as q,a as L,r as b,F as A,e as r,c as y,o as g,f as e,k as F,h as l,l as o,K as I,L as Q,j as R,n as c,t as G,E as K,G as H}from"./index-B0qHf98Y.js";const J={class:"warehousing-query"},W={key:0,class:"pagination"},X={__name:"index",setup(Z){const{proxy:C}=q(),{stock_out_type:V}=C.useDict("stock_out_type"),x=L(),a=b({stockOutNo:"",stockOutType:"",stockOutPerson:"",stockOutDate:"",manufacturer:"",creator:"",pageNum:1,pageSize:10}),h=b([]),f=b(0),N=()=>{a.value.pageNum=1,d()},D=()=>{a.value={stockOutNo:"",stockOutType:"",stockOutPerson:"",stockOutDate:"",manufacturer:"",creator:"",pageNum:1,pageSize:10},d()},z=u=>{a.value.pageSize=u,d()},S=u=>{a.value.pageNum=u,d()},d=()=>{$({...a.value}).then(u=>{h.value=u.rows,f.value=u.total})},k=(u,n)=>{x.push({path:"/warehouseOutAddorEdit",query:{type:u,id:u==="add"?void 0:n}})},T=u=>{console.log("删除:",u),K.confirm("确认删除该出库单吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Y(u.id).then(()=>{H.success("删除成功"),a.value.pageNum=1,d()})})};return A(()=>{d()}),(u,n)=>{const _=r("el-input"),i=r("el-form-item"),p=r("el-col"),O=r("el-option"),P=r("el-select"),v=r("el-row"),U=r("el-date-picker"),m=r("el-button"),B=r("el-form"),s=r("el-table-column"),E=r("el-table"),M=r("el-pagination");return g(),y("div",J,[e(B,{model:o(a),"label-width":"100px"},{default:l(()=>[e(v,{gutter:20},{default:l(()=>[e(p,{span:6},{default:l(()=>[e(i,{label:"出库单号",prop:"stockOutNo"},{default:l(()=>[e(_,{modelValue:o(a).stockOutNo,"onUpdate:modelValue":n[0]||(n[0]=t=>o(a).stockOutNo=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:l(()=>[e(i,{label:"出库类型",prop:"stockOutType"},{default:l(()=>[e(P,{modelValue:o(a).stockOutType,"onUpdate:modelValue":n[1]||(n[1]=t=>o(a).stockOutType=t),placeholder:"全部",clearable:""},{default:l(()=>[e(O,{label:"全部",value:""}),(g(!0),y(I,null,Q(o(V),t=>(g(),R(O,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:l(()=>[e(i,{label:"出库人员",prop:"stockOutPerson"},{default:l(()=>[e(_,{modelValue:o(a).stockOutPerson,"onUpdate:modelValue":n[2]||(n[2]=t=>o(a).stockOutPerson=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:20},{default:l(()=>[e(p,{span:6},{default:l(()=>[e(i,{label:"出库日期",prop:"stockOutDate"},{default:l(()=>[e(U,{modelValue:o(a).stockOutDate,"onUpdate:modelValue":n[3]||(n[3]=t=>o(a).stockOutDate=t),type:"date",placeholder:"选择","value-format":"YYYY-MM-DD",clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:l(()=>[e(i,{label:"生产厂家",prop:"manufacturer"},{default:l(()=>[e(_,{modelValue:o(a).manufacturer,"onUpdate:modelValue":n[4]||(n[4]=t=>o(a).manufacturer=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6},{default:l(()=>[e(i,{label:"制单人",prop:"creator"},{default:l(()=>[e(_,{modelValue:o(a).creator,"onUpdate:modelValue":n[5]||(n[5]=t=>o(a).creator=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:6,class:"button-group"},{default:l(()=>[e(m,{type:"primary",onClick:N,icon:"Search"},{default:l(()=>[c("查询")]),_:1}),e(m,{onClick:D,icon:"Refresh"},{default:l(()=>[c("重置")]),_:1}),e(m,{type:"primary",onClick:n[6]||(n[6]=t=>k("add")),icon:"Plus",plain:""},{default:l(()=>[c("新增出库")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e(E,{data:o(h),border:"",style:{width:"100%"}},{default:l(()=>[e(s,{prop:"index",label:"序号",width:"60",align:"center"},{default:l(t=>[c(G(t.$index+1),1)]),_:1}),e(s,{prop:"stockOutNo",label:"出库单号",width:"150",align:"center"}),e(s,{prop:"stockOutDate",label:"出库日期",width:"120",align:"center"}),e(s,{prop:"stockOutType",label:"出库类型",width:"120",align:"center"}),e(s,{prop:"manufacturer",label:"生产厂家",width:"180",align:"center"}),e(s,{prop:"creator",label:"制单人",width:"120",align:"center"}),e(s,{prop:"purchaseAmount",label:"采购金额",width:"120",align:"center"}),e(s,{prop:"stockOutPerson",label:"出库人员",width:"120",align:"center"}),e(s,{prop:"createTime",label:"创建时间","min-width":"180",align:"center"}),e(s,{label:"操作","min-width":"220",align:"center",fixed:"right"},{default:l(t=>[e(m,{link:"",type:"primary",icon:"Search",onClick:w=>k("view",t.row.id)},{default:l(()=>[c("详情")]),_:2},1032,["onClick"]),e(m,{link:"",type:"primary",icon:"Edit",onClick:w=>k("edit",t.row.id)},{default:l(()=>[c("修改")]),_:2},1032,["onClick"]),e(m,{link:"",type:"primary",icon:"Delete",onClick:w=>T(t.row)},{default:l(()=>[c("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o(f)>0?(g(),y("div",W,[e(M,{background:"","current-page":o(a).pageNum,"onUpdate:currentPage":n[7]||(n[7]=t=>o(a).pageNum=t),"page-size":o(a).pageSize,"onUpdate:pageSize":n[8]||(n[8]=t=>o(a).pageSize=t),total:o(f),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:z,onCurrentChange:S},null,8,["current-page","page-size","total"])])):F("",!0)])}}},le=j(X,[["__scopeId","data-v-b3a79410"]]);export{le as default};
