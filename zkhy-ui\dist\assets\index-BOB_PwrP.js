import{_ as Y,C as A,r as x,N as H,z as M,e as r,c as v,o as p,f as e,h as a,K as _,L as g,n as c,j as B,k as J,l as b,G as W}from"./index-B0qHf98Y.js";const X={class:"app-container"},Z={__name:"index",setup(ee){const u=A({month:new Date().getMonth()+1,elderName:"",bedNumber:"",nursingLevel:[]}),C=x([{id:1,elderName:"张三",bedNumber:"A001",nursingLevel:"一级护理",month:"2025-07"},{id:2,elderName:"李四",bedNumber:"A002",nursingLevel:"三级护理",month:"2025-07"},{id:3,elderName:"王五",bedNumber:"A201",nursingLevel:"一级护理",month:"2025-07"},{id:4,elderName:"老六",bedNumber:"A214",nursingLevel:"二级护理",month:"2025-07"}]),i=x(!1),E=A({form:{elderName:"",bedNumber:"",nursingLevel:"",month:""}}),{form:n}=H(E),y=x([{type:"护理类型1",item:"护理项1",executed:{1:!1,2:!1,3:!1,4:!1,5:!1,6:!1,7:!1,8:!1,9:!1,10:!1,11:!1,12:!1}},{type:"护理类型2",item:"护理项2",executed:{1:!1,2:!1,3:!1,4:!1,5:!1,6:!1,7:!1,8:!1,9:!1,10:!1,11:!1,12:!1}}]),O=M(()=>!0),L=[{label:"一月",value:1},{label:"二月",value:2},{label:"三月",value:3},{label:"四月",value:4},{label:"五月",value:5},{label:"六月",value:6},{label:"七月",value:7},{label:"八月",value:8},{label:"九月",value:9},{label:"十月",value:10},{label:"十一月",value:11},{label:"十二月",value:12}],$=[{label:"一级护理",value:"level1"},{label:"二级护理",value:"level2"},{label:"三级护理",value:"level3"}],I=[{id:1,name:"张三"},{id:2,name:"李四"}],P=M(()=>{const d=new Date().getFullYear(),t=n.month||new Date().getMonth()+1;return new Date(d,t,0).getDate()}),R=()=>{console.log("查询条件:",u),C.value=[{elderName:"张三",bedNumber:"A001",nursingLevel:"一级护理",month:"2023-10"},{elderName:"李四",bedNumber:"B002",nursingLevel:"二级护理",month:"2023-10"}]},j=()=>{Object.assign(u,{month:new Date().getMonth()+1,elderName:"",bedNumber:"",nursingLevel:[]})},F=d=>{console.log("查看月报表:",d),i.value=!0,n.value=d},S=()=>{console.log("新增"),i.value=!0},q=d=>{console.log("修改月报表:",d),i.value=!0,n.value=d},z=()=>{i.value=!1},G=()=>{console.log("提交表单:",n),W.success("保存成功"),i.value=!1},K=(d,t)=>{console.log(`护理项[${d}]在${t}的变化`)};return(d,t)=>{const h=r("el-option"),V=r("el-select"),s=r("el-form-item"),N=r("el-input"),f=r("el-button"),w=r("el-form"),m=r("el-table-column"),U=r("el-table"),Q=r("el-checkbox"),T=r("el-dialog");return p(),v("div",X,[e(w,{inline:!0,class:"filter-form"},{default:a(()=>[e(s,{label:"月份"},{default:a(()=>[e(V,{modelValue:u.month,"onUpdate:modelValue":t[0]||(t[0]=l=>u.month=l),placeholder:"请选择月份",style:{width:"130px"}},{default:a(()=>[(p(),v(_,null,g(L,(l,o)=>e(h,{key:o,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"老人姓名"},{default:a(()=>[e(N,{modelValue:u.elderName,"onUpdate:modelValue":t[1]||(t[1]=l=>u.elderName=l),placeholder:"请输入老人姓名"},null,8,["modelValue"])]),_:1}),e(s,{label:"床号"},{default:a(()=>[e(N,{modelValue:u.bedNumber,"onUpdate:modelValue":t[2]||(t[2]=l=>u.bedNumber=l),placeholder:"请输入床号"},null,8,["modelValue"])]),_:1}),e(s,{label:"护理等级"},{default:a(()=>[e(V,{modelValue:u.nursingLevel,"onUpdate:modelValue":t[3]||(t[3]=l=>u.nursingLevel=l),multiple:"",placeholder:"请选择护理等级",style:{width:"130px"}},{default:a(()=>[(p(),v(_,null,g($,(l,o)=>e(h,{key:o,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:a(()=>[e(f,{type:"primary",onClick:R},{default:a(()=>[c("查询")]),_:1}),e(f,{onClick:j},{default:a(()=>[c("重置")]),_:1}),e(f,{type:"primary",onClick:S},{default:a(()=>[c("新增")]),_:1})]),_:1})]),_:1}),e(U,{data:C.value,border:"",style:{width:"100%"}},{default:a(()=>[e(m,{type:"index",label:"序号",width:"70",align:"center"}),e(m,{prop:"elderName",label:"老人姓名",align:"center"}),e(m,{prop:"bedNumber",label:"床号",align:"center"}),e(m,{prop:"nursingLevel",label:"护理等级",align:"center"}),e(m,{prop:"month",label:"月份",align:"center"}),e(m,{label:"操作",width:"200"},{default:a(({row:l})=>[e(f,{type:"text",onClick:o=>F(l)},{default:a(()=>[c("查看月报表")]),_:2},1032,["onClick"]),O.value?(p(),B(f,{key:0,type:"text",onClick:o=>q(l)},{default:a(()=>[c(" 修改月报表 ")]),_:2},1032,["onClick"])):J("",!0)]),_:1})]),_:1},8,["data"]),e(T,{modelValue:i.value,"onUpdate:modelValue":t[9]||(t[9]=l=>i.value=l),title:"新增/修改月报表",width:"60%","before-close":z},{footer:a(()=>[e(f,{onClick:t[8]||(t[8]=l=>i.value=!1)},{default:a(()=>[c("取消")]),_:1}),e(f,{type:"primary",onClick:G},{default:a(()=>[c("保存")]),_:1})]),default:a(()=>[e(w,{model:b(n),"label-width":"100px"},{default:a(()=>[e(s,{label:"老人姓名"},{default:a(()=>[e(V,{modelValue:b(n).elderName,"onUpdate:modelValue":t[4]||(t[4]=l=>b(n).elderName=l),placeholder:"请选择老人"},{default:a(()=>[(p(),v(_,null,g(I,(l,o)=>e(h,{key:o,label:l.name,value:l.id},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"床号",prop:"bedNumber"},{default:a(()=>[e(N,{modelValue:b(n).bedNumber,"onUpdate:modelValue":t[5]||(t[5]=l=>b(n).bedNumber=l),placeholder:"请输入床号"},null,8,["modelValue"])]),_:1}),e(s,{label:"护理等级",prop:"nursingLevel"},{default:a(()=>[e(N,{modelValue:b(n).nursingLevel,"onUpdate:modelValue":t[6]||(t[6]=l=>b(n).nursingLevel=l),placeholder:"请输入护理等级"},null,8,["modelValue"])]),_:1}),e(s,{label:"月份"},{default:a(()=>[e(V,{modelValue:b(n).month,"onUpdate:modelValue":t[7]||(t[7]=l=>b(n).month=l),placeholder:"请选择月份"},{default:a(()=>[(p(),v(_,null,g(L,(l,o)=>e(h,{key:o,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"护理类型"},{default:a(()=>[e(U,{data:y.value,border:"",style:{width:"100%"}},{default:a(()=>[e(m,{prop:"type",label:"护理类型",width:"150px",fixed:""}),e(m,{prop:"item",label:"护理项",width:"150px",fixed:""}),(p(!0),v(_,null,g(P.value,(l,o)=>(p(),B(m,{key:o,label:l},{default:a(({$index:k})=>[e(Q,{modelValue:y.value[k].executed[l],"onUpdate:modelValue":D=>y.value[k].executed[l]=D,onChange:D=>K(k,l)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["label"]))),128))]),_:1},8,["data"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ae=Y(Z,[["__scopeId","data-v-912635b5"]]);export{ae as default};
