import Q from"./leaveForm-1aH7g-Bt.js";import{d as H,a as W}from"./leave-Dd4WELmg.js";import{g as X}from"./user-u7DySmj3.js";import{_ as Z,d as ee,r as g,C as ae,F as le,e as p,c as y,o as h,f as e,h as t,n as o,j as M,k as _,t as d,E as te,G as ne}from"./index-B0qHf98Y.js";import"./yjj-DmX1NTQH.js";import"./telderAttachement-C4ARfNBy.js";const re={class:"app-container"},oe={key:0,class:"pagination-container"},ie={key:0,class:"pagination-container"},pe={key:0,class:"pagination-container"},se={key:0,class:"pagination-container"},de={__name:"index",setup(ue){const{proxy:x}=ee(),{leave_status:ce}=x.useDict("leave_status");g(document.documentElement.clientHeight-100);const u=g("all"),v=g([]),s=g(0),b=g(),c=g(""),C=g([]),R=g(!0),n=ae({elderName:"",plannedLeaveTime:[],status:"",statusList:[],pageSize:10,pageNum:1}),V=()=>{n.pageNum=1,w()},O=()=>{I(),x.$refs.queryForm.resetFields(),V()},I=()=>{C.value=[],u.value="all",v.value=[],n.pageNum=1,n.statusList=[]},w=async()=>{console.log("queryParams",n);try{const r=await W(x.addDateRange(n,C.value,"PlannedLeaveTime"));v.value=r.rows,s.value=r.total}catch(r){console.error("获取列表数据失败:",r)}},A=r=>{u.value=r.props.name,u.value==="apply"?n.statusList=["PENDING","REJECTED","COMPLETE"]:u.value==="audit"?n.statusList=["PENDING"]:u.value==="fromLeave"?n.statusList=["APPROVED"]:n.statusList=[],n.pageNum=1,v.value=[],w()},E=r=>{n.pageSize=r,w()},F=r=>{c.value="fromLeave",b.value.openDialog(c.value,r)},N=r=>{n.pageNum=r,w()},G=r=>{c.value="wcsq",b.value.openDialog(c.value,r)},S=r=>{c.value="detail",b.value.openDialog(c.value,r)},B=async r=>{try{await te.confirm("确定要删除该条外出申请吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await H(r.id)).code===200&&(ne.success("删除成功"),w())}catch(i){console.error("删除老人外出申请失败:",i)}},J=r=>{c.value="review",b.value.openDialog(c.value,r)},$=()=>{w(),X().then(r=>{r.data.roles.map(i=>{i.roleKey=="process_approval"||i.roleKey=="admin"?R.value=!0:R.value=!1})})};return le(()=>{$()}),(r,i)=>{const j=p("el-input"),T=p("el-form-item"),q=p("el-date-picker"),f=p("el-option"),U=p("el-select"),m=p("el-button"),Y=p("el-form"),l=p("el-table-column"),k=p("el-avatar"),D=p("el-table"),P=p("el-pagination"),L=p("el-tab-pane"),K=p("el-tabs");return h(),y("div",re,[e(Y,{model:n,ref:"queryForm",inline:!0,class:"search-bar"},{default:t(()=>[e(T,{label:"老人姓名",prop:"elderName"},{default:t(()=>[e(j,{modelValue:n.elderName,"onUpdate:modelValue":i[0]||(i[0]=a=>n.elderName=a),placeholder:"请输入老人姓名",clearable:"",style:{width:"140px"}},null,8,["modelValue"])]),_:1}),e(T,{label:"计划外出时间"},{default:t(()=>[e(q,{modelValue:C.value,"onUpdate:modelValue":i[1]||(i[1]=a=>C.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",style:{width:"220px"}},null,8,["modelValue"])]),_:1}),e(T,{label:"状态",prop:"status"},{default:t(()=>[e(U,{modelValue:n.status,"onUpdate:modelValue":i[2]||(i[2]=a=>n.status=a),placeholder:"请选择状态",clearable:"",style:{width:"200px"}},{default:t(()=>[e(f,{label:"全部",value:""}),e(f,{label:"待审批",value:"PENDING"}),e(f,{label:"已驳回",value:"REJECTED"}),e(f,{label:"待销假",value:"APPROVED"}),e(f,{label:"已完成",value:"COMPLETE"})]),_:1},8,["modelValue"])]),_:1}),e(T,null,{default:t(()=>[e(m,{icon:"Search",type:"primary",onClick:V},{default:t(()=>[o("查询")]),_:1}),e(m,{icon:"Refresh",onClick:O},{default:t(()=>[o("重置")]),_:1}),e(m,{icon:"Plus",type:"primary",onClick:G,plain:""},{default:t(()=>[o("外出申请")]),_:1})]),_:1})]),_:1},8,["model"]),e(K,{modelValue:u.value,"onUpdate:modelValue":i[3]||(i[3]=a=>u.value=a),onTabClick:A},{default:t(()=>[e(L,{label:"所有",name:"all"},{default:t(()=>[e(D,{data:v.value,border:"",stripe:""},{default:t(()=>[e(l,{label:"序号",type:"index",width:"60",align:"center"}),e(l,{prop:"elderName1",label:"老人头像",width:"100",align:"center"},{default:t(a=>[e(k,{shape:"circle",size:60,fit:"fill",src:a.row.avatar},null,8,["src"])]),_:1}),e(l,{prop:"elderName",label:"老人姓名",width:"100",align:"center"}),e(l,{prop:"elderCode",label:"老人编号",width:"120",align:"center"}),e(l,{prop:"bedNumber",label:"床位编号",width:"120",align:"center"}),e(l,{prop:"companionName",label:"陪同人",width:"120",align:"center"}),e(l,{prop:"companionPhone",label:"陪同人电话",width:"120",align:"center"}),e(l,{prop:"relationship",label:"与老人关系",width:"120",align:"center"}),e(l,{prop:"plannedLeaveTime",label:"计划请假时间","min-width":"180",align:"center"},{default:t(a=>[o(d(a.row.plannedLeaveTime+"~"+a.row.plannedReturnTime),1)]),_:1}),e(l,{prop:"plannedDays",label:"计划请假天数",width:"120",align:"center"}),e(l,{prop:"actualLeaveTime",label:"实际请假时间",width:"180",align:"center"},{default:t(a=>[o(d(a.row.actualLeaveTime&&a.row.actualReturnTime?a.row.actualLeaveTime+"~"+a.row.actualReturnTime:""),1)]),_:1}),e(l,{prop:"actualDays",label:"实际请假天数",width:"120",align:"center"}),e(l,{prop:"handlerName",label:"经办人",width:"100",align:"center"}),e(l,{prop:"approverName",label:"审批人",width:"160",align:"center"}),e(l,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(a=>[o(d(a.row.status==="PENDING"?"待审批":a.row.status==="REJECTED"?"已驳回":a.row.status==="APPROVED"?"待销假":a.row.status==="COMPLETE"?"已完成":""),1)]),_:1}),e(l,{label:"操作",align:"center",fixed:"right",width:"120"},{default:t(a=>[e(m,{link:"",type:"primary",onClick:z=>S(a.row)},{default:t(()=>[o("查看")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),s.value>0?(h(),y("div",oe,[e(P,{layout:"prev, pager, next, sizes, jumper",total:s.value,background:"","page-size":n.pageSize,"current-page":n.pageNum,onSizeChange:E,onCurrentChange:N,"page-sizes":[10,20,50]},null,8,["total","page-size","current-page"])])):_("",!0)]),_:1}),e(L,{label:"申请",name:"apply"},{default:t(()=>[e(D,{data:v.value,border:"",stripe:""},{default:t(()=>[e(l,{label:"序号",type:"index",width:"60",align:"center"}),e(l,{prop:"elderName1",label:"老人头像",width:"100",align:"center"},{default:t(a=>[e(k,{shape:"circle",size:60,fit:"fill",src:a.row.avatar},null,8,["src"])]),_:1}),e(l,{prop:"elderName",label:"老人姓名",width:"100",align:"center"}),e(l,{prop:"elderCode",label:"老人编号",width:"120",align:"center"}),e(l,{prop:"bedNumber",label:"床位编号",width:"120",align:"center"}),e(l,{prop:"companionName",label:"陪同人",width:"120",align:"center"}),e(l,{prop:"companionPhone",label:"陪同人电话",width:"120",align:"center"}),e(l,{prop:"relationship",label:"与老人关系",width:"120",align:"center"}),e(l,{prop:"plannedLeaveTime",label:"计划请假时间","min-width":"180",align:"center"},{default:t(a=>[o(d(a.row.plannedLeaveTime+"~"+a.row.plannedReturnTime),1)]),_:1}),e(l,{prop:"plannedDays",label:"计划请假天数",width:"120",align:"center"}),e(l,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(a=>[o(d(a.row.status==="PENDING"?"待审批":a.row.status==="REJECTED"?"已驳回":a.row.status==="APPROVED"?"待销假":a.row.status==="COMPLETE"?"已完成":""),1)]),_:1}),e(l,{label:"操作",align:"center",fixed:"right",width:"120"},{default:t(a=>[e(m,{link:"",type:"primary",onClick:z=>S(a.row)},{default:t(()=>[o("查看")]),_:2},1032,["onClick"]),a.row.status==="PENDING"?(h(),M(m,{key:0,link:"",type:"primary",onClick:z=>B(a.row)},{default:t(()=>[o("删除")]),_:2},1032,["onClick"])):_("",!0)]),_:1})]),_:1},8,["data"]),s.value>0?(h(),y("div",ie,[e(P,{layout:"prev, pager, next, sizes, jumper",total:s.value,background:"","page-size":n.pageSize,"current-page":n.pageNum,onSizeChange:E,onCurrentChange:N,"page-sizes":[10,20,50]},null,8,["total","page-size","current-page"])])):_("",!0)]),_:1}),R.value?(h(),M(L,{key:0,label:"审核",name:"audit"},{default:t(()=>[e(D,{data:v.value,border:"",stripe:""},{default:t(()=>[e(l,{label:"序号",type:"index",width:"60",align:"center"}),e(l,{prop:"elderName1",label:"老人头像",width:"100",align:"center"},{default:t(a=>[e(k,{shape:"circle",size:60,fit:"fill",src:a.row.avatar},null,8,["src"])]),_:1}),e(l,{prop:"elderName",label:"老人姓名",width:"100",align:"center"}),e(l,{prop:"elderCode",label:"老人编号",width:"120",align:"center"}),e(l,{prop:"bedNumber",label:"床位编号",width:"120",align:"center"}),e(l,{prop:"companionName",label:"陪同人",width:"120",align:"center"}),e(l,{prop:"companionPhone",label:"陪同人电话",width:"120",align:"center"}),e(l,{prop:"relationship",label:"与老人关系",width:"120",align:"center"}),e(l,{prop:"plannedLeaveTime",label:"计划请假时间","min-width":"180",align:"center"},{default:t(a=>[o(d(a.row.plannedLeaveTime+"~"+a.row.plannedReturnTime),1)]),_:1}),e(l,{prop:"plannedDays",label:"计划请假天数",width:"120",align:"center"}),e(l,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(a=>[o(d(a.row.status==="PENDING"?"待审批":a.row.status==="REJECTED"?"已驳回":a.row.status==="APPROVED"?"待销假":a.row.status==="COMPLETE"?"已完成":""),1)]),_:1}),e(l,{label:"操作",align:"center",fixed:"right",width:"120"},{default:t(a=>[e(m,{link:"",type:"primary",onClick:z=>J(a.row)},{default:t(()=>[o("审核")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),s.value>0?(h(),y("div",pe,[e(P,{layout:"prev, pager, next, sizes, jumper",total:s.value,background:"","page-size":n.pageSize,"current-page":n.pageNum,onSizeChange:E,onCurrentChange:N,"page-sizes":[10,20,50]},null,8,["total","page-size","current-page"])])):_("",!0)]),_:1})):_("",!0),e(L,{label:"销假",name:"fromLeave"},{default:t(()=>[e(D,{data:v.value,border:"",stripe:""},{default:t(()=>[e(l,{label:"序号",type:"index",width:"60",align:"center"}),e(l,{prop:"elderName1",label:"老人头像",width:"100",align:"center"},{default:t(a=>[e(k,{shape:"circle",size:60,fit:"fill",src:a.row.avatar},null,8,["src"])]),_:1}),e(l,{prop:"elderName",label:"老人姓名",width:"100",align:"center"}),e(l,{prop:"elderCode",label:"老人编号",width:"120",align:"center"}),e(l,{prop:"bedNumber",label:"床位编号",width:"120",align:"center"}),e(l,{prop:"companionName",label:"陪同人",width:"120",align:"center"}),e(l,{prop:"companionPhone",label:"陪同人电话",width:"120",align:"center"}),e(l,{prop:"relationship",label:"与老人关系",width:"120",align:"center"}),e(l,{prop:"plannedLeaveTime",label:"计划请假时间","min-width":"180",align:"center"},{default:t(a=>[o(d(a.row.plannedLeaveTime+"~"+a.row.plannedReturnTime),1)]),_:1}),e(l,{prop:"plannedDays",label:"计划请假天数",width:"120",align:"center"}),e(l,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(a=>[o(d(a.row.status==="PENDING"?"待审批":a.row.status==="REJECTED"?"已驳回":a.row.status==="APPROVED"?"待销假":a.row.status==="COMPLETE"?"已完成":""),1)]),_:1}),e(l,{label:"操作",align:"center",fixed:"right",width:"120"},{default:t(a=>[e(m,{link:"",type:"primary",onClick:z=>F(a.row)},{default:t(()=>[o("销假")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),s.value>0?(h(),y("div",se,[e(P,{layout:"prev, pager, next, sizes, jumper",total:s.value,background:"","page-size":n.pageSize,"current-page":n.pageNum,onSizeChange:E,onCurrentChange:N,"page-sizes":[10,20,50]},null,8,["total","page-size","current-page"])])):_("",!0)]),_:1})]),_:1},8,["modelValue"]),e(Q,{ref_key:"leaveFormRef",ref:b,activeTabValue:u.value,onRefresh:O},null,8,["activeTabValue"])])}}},be=Z(de,[["__scopeId","data-v-0c2bb6ec"]]);export{be as default};
