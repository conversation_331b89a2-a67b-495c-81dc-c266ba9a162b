import{_ as S,r as p,e as g,I,J as R,l as t,c as H,o as j,f as m,h as _,i as e,t as n,n as b,D,aC as k,v as V,x as A}from"./index-B0qHf98Y.js";const d=r=>(V("data-v-053bdb70"),r=r(),A(),r),N=d(()=>e("h3",{class:"title_record"},"护理查房记录",-1)),T={class:"table-style"},B={style:{"text-align":"center"}},Q={style:{width:"40%"}},L={style:{display:"flex","justify-content":"space-around","align-items":"center"}},P=d(()=>e("p",{style:{width:"80px"}},"查房时间:",-1)),W=d(()=>e("tr",null,[e("td",{style:{"text-align":"center"}},"区 域"),e("td",{style:{"text-align":"center"}},"查房情况"),e("td",{style:{"text-align":"center"}},"处理办法及结果")],-1)),q=d(()=>e("td",{style:{"text-align":"center"},rowspan:"8"},"自理区",-1)),z={style:{display:"flex","justify-content":"space-between","align-items":"center"}},J={colspan:"3"},M={style:{display:"flex","justify-content":"space-between","align-items":"center"}},O={colspan:"3"},U={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Y={colspan:"3"},$={style:{display:"flex","justify-content":"space-between","align-items":"center"}},F={colspan:"3"},G=d(()=>e("td",{style:{"text-align":"center"},rowspan:"8"},"介 护 区:",-1)),K={style:{display:"flex","justify-content":"space-between","align-items":"center"}},X={colspan:"3"},Z={style:{display:"flex","justify-content":"space-between","align-items":"center"}},ee={colspan:"3"},te={style:{display:"flex","justify-content":"space-between","align-items":"center"}},le={colspan:"3"},ne={style:{display:"flex","justify-content":"space-between","align-items":"center"}},oe={colspan:"3"},se={class:"dialog-footer"},ae={__name:"hlcxRecord",setup(r,{expose:w}){const o=p(!1),x=p(null),l=p({}),y=p(!1),v=s=>{y.value=!0,k(s.id).then(a=>{o.value=!0,l.value=a.data||{}}).finally(()=>{y.value=!1})},E=()=>{o.value=!1},C=()=>{const s=x.value.cloneNode(!0);s.querySelectorAll(".el-input, .el-textarea").forEach(u=>{var h;const f=((h=u.querySelector("input, textarea"))==null?void 0:h.value)||"",i=document.createElement("div");i.textContent=f,i.style.padding="8px",u.replaceWith(i)});const c=window.open("","_blank");c.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>护理查房记录</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${s.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `),c.document.close()};return w({openDialog:v}),(s,a)=>{const c=g("el-button"),u=g("el-dialog"),f=I("loading");return R((j(),H("div",null,[m(u,{modelValue:t(o),"onUpdate:modelValue":a[0]||(a[0]=i=>D(o)?o.value=i:null),title:"详情",width:"70%","close-on-click-modal":!1,"append-to-body":""},{footer:_(()=>[e("div",se,[m(c,{onClick:E},{default:_(()=>[b("返 回")]),_:1}),m(c,{type:"primary",onClick:C},{default:_(()=>[b("打 印")]),_:1})])]),default:_(()=>[e("div",{class:"detail-content",ref_key:"printContent",ref:x},[N,e("table",T,[e("tbody",null,[e("tr",null,[e("td",B,n(t(l).roundDate||"-"),1),e("td",Q,"查房人:"+n(t(l).roundPerson||"-"),1),e("td",L,[P,e("p",null,"上午 "+n(t(l).morningTime||"-"),1),e("p",null,"下午 "+n(t(l).afternoonTime||"-"),1)])]),W,e("tr",null,[q,e("td",z,"居室卫生:"+n(t(l).selfcareRoomHygiene||"-"),1),e("td",J,n(t(l).selfcareRoomHygieneSolution||"-"),1)]),e("tr",null,[e("td",M,"老人卫生:"+n(t(l).selfcareElderHygiene||"-"),1),e("td",O,n(t(l).selfcareElderHygieneSolution||"-"),1)]),e("tr",null,[e("td",U,"居室物品摆放:"+n(t(l).selfcareRoomArrangement||"-"),1),e("td",Y,n(t(l).selfcareRoomArrangementSolution||"-"),1)]),e("tr",null,[e("td",$,"服务提供质量:"+n(t(l).selfcareServiceQuality||"-"),1),e("td",F,n(t(l).selfcareServiceQualitySolution||"-"),1)]),e("tr",null,[e("td",null,n(t(l).selfcareItemEx1||"-"),1),e("td",null,n(t(l).selfcareContentEx1||"-"),1)]),e("tr",null,[e("td",null,n(t(l).selfcareItemEx2||"-"),1),e("td",null,n(t(l).selfcareContentEx2||"-"),1)]),e("tr",null,[e("td",null,n(t(l).selfcareItemEx3||"-"),1),e("td",null,n(t(l).selfcareContentEx3||"-"),1)]),e("tr",null,[e("td",null,n(t(l).selfcareItemEx4||"-"),1),e("td",null,n(t(l).selfcareContentEx4||"-"),1)]),e("tr",null,[G,e("td",K,"居室卫生:"+n(t(l).careRoomHygiene||"-"),1),e("td",X,n(t(l).careRoomHygieneSolution||"-"),1)]),e("tr",null,[e("td",Z,"老人卫生:"+n(t(l).careElderHygiene||"-"),1),e("td",ee,n(t(l).careElderHygieneSolution||"-"),1)]),e("tr",null,[e("td",te,"居室物品摆放:"+n(t(l).careRoomArrangement||"-"),1),e("td",le,n(t(l).careRoomArrangementSolution||"-"),1)]),e("tr",null,[e("td",ne,"服务提供质量:"+n(t(l).careServiceQuality||"-"),1),e("td",oe,n(t(l).careServiceQualitySolution||"-"),1)]),e("tr",null,[e("td",null,n(t(l).careItemEx1||"-"),1),e("td",null,n(t(l).careContentEx1||"-"),1)]),e("tr",null,[e("td",null,n(t(l).careItemEx2||"-"),1),e("td",null,n(t(l).careContentEx2||"-"),1)]),e("tr",null,[e("td",null,n(t(l).careItemEx3||"-"),1),e("td",null,n(t(l).careContentEx3||"-"),1)]),e("tr",null,[e("td",null,n(t(l).careItemEx4||"-"),1),e("td",null,n(t(l).careContentEx4||"-"),1)])])])],512)]),_:1},8,["modelValue"])])),[[f,t(y)]])}}},ie=S(ae,[["__scopeId","data-v-053bdb70"]]);export{ie as default};
