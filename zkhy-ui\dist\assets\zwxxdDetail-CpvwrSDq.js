import{_ as x,r as p,e as d,c as D,o as N,f as e,h as s,l as m,i as l,t as n,n as _,D as I,aN as V,M as S,v as k,x as T}from"./index-B0qHf98Y.js";const B=i=>(k("data-v-ea515500"),i=i(),T(),i),C={class:"replace-consumables"},R=B(()=>l("div",{class:"headerTitle"},[l("h2",null,"紫外线消毒记录表")],-1)),Y={class:"elder-info"},y={class:"info"},M={class:"roomNumber"},L={class:"leaderName"},U={class:"processIndex"},$={class:"dialog-footer"},q={__name:"zwxxdDetail",setup(i,{expose:f}){const r=p(!1),u=p([]),b=p(JSON.parse(localStorage.getItem("userInfo"))),g=(c,a)=>{V({createBy:b.value.userId,recordDate:a||S().format("YYYY-MM-DD")}).then(o=>{u.value=o.rows||[]})};return f({openDialog(c,a){r.value=!0,g(c,a)}}),(c,a)=>{const o=d("el-table-column"),v=d("el-table"),h=d("el-button"),w=d("el-dialog");return N(),D("div",C,[e(w,{modelValue:m(r),"onUpdate:modelValue":a[1]||(a[1]=t=>I(r)?r.value=t:null),title:"详情",width:"80%"},{footer:s(()=>[l("div",$,[e(h,{onClick:a[0]||(a[0]=t=>r.value=!1)},{default:s(()=>[_("返回")]),_:1})])]),default:s(()=>[R,e(v,{data:m(u),border:"",style:{width:"100%"}},{default:s(()=>[e(o,{label:"房屋信息",align:"center",prop:"avatar"},{default:s(t=>[l("div",Y,[l("div",y,[l("p",M,n(t.row.roomNumber),1),l("p",L,n(t.row.buildingName)+" "+n(t.row.floorNumber),1),l("span",U,n(t.$index+1),1)])])]),_:1}),e(o,{prop:"recordDate",label:"服务日期",align:"center"},{default:s(t=>[_(n(t.row.recordDate),1)]),_:1}),e(o,{prop:"uvLampCode",label:"紫外灯编号",align:"center"}),e(o,{prop:"disinfectionTime",label:"消毒时间",align:"center"},{default:s(t=>[_(n(t.row.startTime)+" - "+n(t.row.endTime),1)]),_:1}),e(o,{prop:"duration",label:"消毒时长",align:"center"}),e(o,{prop:"monitoringResult",label:"辐照强度结果",align:"center"}),e(o,{prop:"disinfectionStaffName",label:"消毒人员",align:"center"}),e(o,{prop:"supervisor",label:"监督人员",align:"center"}),e(o,{prop:"disinfectionTarget",label:"消毒区域",align:"center"}),e(o,{prop:"remark",label:"备注",align:"center"})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}},A=x(q,[["__scopeId","data-v-ea515500"]]);export{A as default};
