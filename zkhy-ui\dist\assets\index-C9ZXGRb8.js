import{_ as w,B as m,r as t,a as _,d as x,c as k,i as a,A as i,v as u,x as C,b as B,o as b}from"./index-B0qHf98Y.js";const n=d=>(u("data-v-f46c532d"),d=d(),C(),d),y={class:"app-container home"},M=n(()=>a("div",{class:"main-title"},"欢迎使用",-1)),V=n(()=>a("div",{class:"subtitle"},"颐佳通智慧养老平台",-1)),H=i('<div class="card-icon" data-v-f46c532d><svg width="44" height="44" viewBox="0 0 48 48" fill="none" data-v-f46c532d><rect x="8" y="8" width="32" height="32" rx="3" stroke="#4989f8" stroke-width="3" data-v-f46c532d></rect><path d="M16 16H32V32" stroke="#4989f8" stroke-width="3" stroke-linecap="round" data-v-f46c532d></path></svg></div><div class="card-title" data-v-f46c532d>老人档案</div><div class="card-desc" data-v-f46c532d>Health records</div><div class="card-wave" data-v-f46c532d><svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-f46c532d><path d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z" fill="#eaf4ff" data-v-f46c532d></path></svg></div>',4),I=[H],S=i('<div class="card-icon" data-v-f46c532d><svg width="44" height="44" viewBox="0 0 48 48" fill="none" data-v-f46c532d><rect x="8" y="8" width="32" height="32" rx="3" stroke="#34bfa3" stroke-width="3" data-v-f46c532d></rect><path d="M16 32L24 24L32 28" stroke="#34bfa3" stroke-width="3" stroke-linecap="round" data-v-f46c532d></path><circle cx="16" cy="32" r="2.5" fill="#34bfa3" data-v-f46c532d></circle><circle cx="24" cy="24" r="2.5" fill="#34bfa3" data-v-f46c532d></circle><circle cx="32" cy="28" r="2.5" fill="#34bfa3" data-v-f46c532d></circle></svg></div><div class="card-title" data-v-f46c532d>健康评估</div><div class="card-desc" data-v-f46c532d>Health assessment</div><div class="card-wave" data-v-f46c532d><svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-f46c532d><path d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z" fill="#eaf4ff" data-v-f46c532d></path></svg></div>',4),L=[S],P=i('<div class="card-icon" data-v-f46c532d><svg width="44" height="44" viewBox="0 0 48 48" fill="none" data-v-f46c532d><rect x="8" y="8" width="32" height="32" rx="3" stroke="#4989f8" stroke-width="3" data-v-f46c532d></rect><rect x="16" y="16" width="16" height="4" rx="2" fill="#4989f8" data-v-f46c532d></rect><rect x="16" y="24" width="8" height="4" rx="2" fill="#4989f8" data-v-f46c532d></rect></svg></div><div class="card-title" data-v-f46c532d>居住管理</div><div class="card-desc" data-v-f46c532d>Health programme</div><div class="card-wave" data-v-f46c532d><svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-f46c532d><path d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z" fill="#eaf4ff" data-v-f46c532d></path></svg></div>',4),W=[P],Z=i('<div class="card-icon" data-v-f46c532d><svg width="44" height="44" viewBox="0 0 48 48" fill="none" data-v-f46c532d><rect x="8" y="8" width="32" height="32" rx="3" stroke="#34bfa3" stroke-width="3" data-v-f46c532d></rect><path d="M18 18L30 30" stroke="#34bfa3" stroke-width="3" stroke-linecap="round" data-v-f46c532d></path><path d="M30 18L18 30" stroke="#34bfa3" stroke-width="3" stroke-linecap="round" data-v-f46c532d></path></svg></div><div class="card-title" data-v-f46c532d>药品管理</div><div class="card-desc" data-v-f46c532d>Task allocation</div><div class="card-wave" data-v-f46c532d><svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-f46c532d><path d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z" fill="#eaf4ff" data-v-f46c532d></path></svg></div>',4),$=[Z],A=m({name:"Index"}),E=Object.assign(A,{setup(d){t("3.8.9");const s=_(),{proxy:e}=x();t(!1),t(!1),t(!1),t(!1);function v(){c(["elderInfo:elderFiles:list"])?s.push("/elderInfo/elderFiles"):e.$modal.msgWarning("您好，您没有分配【老人档案】菜单权限，请联系管理员！")}function f(){c(["eldercheckin:showAssessmentDetails:list"])?s.push("/assessment/assessmentRecord"):e.$modal.msgWarning("您好，您没有分配【健康评估】菜单权限，请联系管理员！")}function h(){c(["eldersystem:roommanage:list"])?s.push("/live/roommanage"):e.$modal.msgWarning("您好，您没有分配【居住管理】菜单权限，请联系管理员！")}function p(){c(["medication:cardscreen:list"])?s.push("/medicationManagement/cardscreenClone"):e.$modal.msgWarning("您好，您没有分配【药品管理】菜单权限，请联系管理员！")}function c(o){const r=B().permissions,g="*:*:*";return r.some(l=>g===l||o.includes(l))}return(o,r)=>(b(),k("div",y,[M,V,a("div",{class:"card-row"},[a("div",{class:"card",onClick:v},I),a("div",{class:"card",onClick:f},L),a("div",{class:"card",onClick:h},W),a("div",{class:"card",onClick:p},$)])]))}}),R=w(E,[["__scopeId","data-v-f46c532d"]]);export{R as default};
