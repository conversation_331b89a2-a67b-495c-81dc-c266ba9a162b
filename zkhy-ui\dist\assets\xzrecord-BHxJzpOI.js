import{_ as D,r as p,e as f,I as V,J as S,l as e,c as I,o as N,f as y,h as u,i as t,t as n,n as v,D as T,aA as z,v as A,x as B}from"./index-B0qHf98Y.js";const o=r=>(A("data-v-b9c71259"),r=r(),B(),r),E=o(()=>t("h3",{class:"title_record"},"行政查房记录",-1)),R={class:"table-style"},W=o(()=>t("td",{style:{"text-align":"center",width:"150px"}},"查房院长",-1)),q={style:{width:"40%"}},H=o(()=>t("td",{style:{"text-align":"center"}},"查房时间",-1)),P={style:{"min-width":"180px"}},J=o(()=>t("td",{style:{"text-align":"center"}},"记录人",-1)),L=o(()=>t("td",{style:{"text-align":"center",width:"150px"}},"检查部门",-1)),M=o(()=>t("td",{style:{"text-align":"center"}},"参加人员",-1)),O={colspan:"3"},U=o(()=>t("tr",null,[t("td",{style:{"text-align":"center"}},"检查内容"),t("td",{colspan:"3",style:{"text-align":"center"}},"处置意见")],-1)),X=o(()=>t("td",{style:{"text-align":"center"}},"生活护理",-1)),Y={colspan:"3"},$=o(()=>t("td",{style:{"text-align":"center"}},"医疗护理",-1)),j={colspan:"3"},F=o(()=>t("td",{style:{"text-align":"center"}},"后勤保障",-1)),G={colspan:"3"},K=o(()=>t("td",{style:{"text-align":"center"}},"安全隐患",-1)),Q={colspan:"3"},Z=o(()=>t("td",{style:{"text-align":"center"}},"意见或建议",-1)),tt={colspan:"3"},et={class:"dialog-footer"},ot={__name:"xzrecord",setup(r,{expose:b}){const s=p(!1),g=p(null),l=p({}),h=p(!1),w=a=>{h.value=!0,z(a.id).then(d=>{s.value=!0,l.value=d.data||{}}).finally(()=>{h.value=!1})},C=()=>{s.value=!1},k=()=>{const a=g.value.cloneNode(!0);a.querySelectorAll(".el-input, .el-textarea").forEach(_=>{var m;const x=((m=_.querySelector("input, textarea"))==null?void 0:m.value)||"",c=document.createElement("div");c.textContent=x,c.style.padding="8px",_.replaceWith(c)});const i=window.open("","_blank");i.document.write(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>行政查房记录</title>
        <style>
          body { font-family: Arial; padding: 20px; }
          .title_record { 
            color: #D9001B; 
            text-align: center; 
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .table-style {
            width: 100%;
            border-collapse: collapse;
          }
          .table-style td {
            border: 1px solid #ebeef5;
            padding: 8px;
          }
          .text-center { text-align: center; }
        </style>
      </head>
      <body>
        ${a.innerHTML}
        <script>
          setTimeout(() => {
            window.print()
            window.close()
          }, 200)
        <\/script>
      </body>
    </html>
  `),i.document.close()};return b({openDialog:w}),(a,d)=>{const i=f("el-button"),_=f("el-dialog"),x=V("loading");return S((N(),I("div",null,[y(_,{modelValue:e(s),"onUpdate:modelValue":d[0]||(d[0]=c=>T(s)?s.value=c:null),title:"详情",width:"70%","close-on-click-modal":!1,"append-to-body":""},{footer:u(()=>[t("div",et,[y(i,{onClick:C},{default:u(()=>[v("返 回")]),_:1}),y(i,{type:"primary",onClick:k},{default:u(()=>[v("打 印")]),_:1})])]),default:u(()=>[t("div",{class:"detail-content",ref_key:"printContent",ref:g},[E,t("table",R,[t("tbody",null,[t("tr",null,[W,t("td",q,n(e(l).director||"-"),1),H,t("td",P,n(e(l).roundTime||"-"),1)]),t("tr",null,[J,t("td",null,n(e(l).recorder||"-"),1),L,t("td",null,n(e(l).department||"-"),1)]),t("tr",null,[M,t("td",O,n(e(l).participants||"-"),1)]),U,t("tr",null,[X,t("td",Y,n(e(l).lifeCare||"-"),1)]),t("tr",null,[$,t("td",j,n(e(l).medicalCare||"-"),1)]),t("tr",null,[F,t("td",G,n(e(l).logistics||"-"),1)]),t("tr",null,[K,t("td",Q,n(e(l).safetyHazard||"-"),1)]),t("tr",null,[Z,t("td",tt,n(e(l).suggestions||"-"),1)])])])],512)]),_:1},8,["modelValue"])])),[[x,e(h)]])}}},nt=D(ot,[["__scopeId","data-v-b9c71259"]]);export{nt as default};
