.medicine-dialog[data-v-8face55c]{min-height:70vh}.section[data-v-8face55c]{margin-bottom:20px}.section[data-v-8face55c]:last-child{border-bottom:none}h3[data-v-8face55c]{font-weight:700;font-size:16px;margin-bottom:16px;color:#2c3e50;border-bottom:1px solid #e0e7ef;padding-bottom:8px}.value[data-v-8face55c]{color:#333}.el-upload__tip[data-v-8face55c]{font-size:12px;color:#999;text-align:center;line-height:1.5;margin-top:5px}[data-v-8face55c] .el-form-item__label{justify-content:flex-end;text-align:right;padding-right:10px}.el-row[data-v-8face55c]{margin-bottom:10px}[data-v-8face55c] .el-textarea__inner{min-height:60px!important}.avatar-container[data-v-8face55c]{position:absolute;right:10px;top:120px}.file-preview[data-v-8face55c],.photo-preview[data-v-8face55c]{display:flex;align-items:center;flex-wrap:wrap;gap:10px}.medication-form[data-v-8face55c]{padding-left:20px}.form-row[data-v-8face55c]{display:flex;align-items:center;padding:5px 10px;border-radius:4px}.form-col[data-v-8face55c]{padding:0 10px}.checkbox-col[data-v-8face55c]{width:120px}.radio-col[data-v-8face55c]{width:180px}.input-col[data-v-8face55c]{width:200px}.select-col[data-v-8face55c]{width:150px}[data-v-8face55c] .el-radio__input.is-disabled+span.el-radio__label,[data-v-8face55c] .el-input.is-disabled .el-input__inner,[data-v-8face55c] .el-select.is-disabled .el-input__inner{color:#999}.medication-title[data-v-8face55c]{padding-left:40px;font-weight:700}.dataTimeList[data-v-8face55c]{width:100%;display:flex;flex-direction:column;border:1px solid #ddd;padding:10px 0}.dataTimeList .dateTime[data-v-8face55c]{height:40px;line-height:40px;width:100%;display:flex;justify-content:center;cursor:pointer}.dataTimeList .dateTime[data-v-8face55c]:hover{background-color:#f5f7fa}.dataTimeList .dateTime.selected[data-v-8face55c]{background-color:#ecf5ff;color:#409eff;font-weight:700}.drawer-switch[data-v-8face55c]{margin-left:20px}.plan-row[data-v-8face55c]{border:1px solid #ddd;border-radius:4px;padding:10px 0;margin-bottom:20px;position:relative}.delete-button[data-v-8face55c]{text-align:right;padding-bottom:10px}.disabled-date[data-v-8face55c]{color:#c0c4cc;cursor:not-allowed}.disabled-date[data-v-8face55c]:hover{background-color:#f5f7fa!important}.preparer-info[data-v-8face55c]{display:flex;justify-content:flex-start}.preparer-info[data-v-8face55c] .el-form-item__label{width:auto!important}.addMedicationReceive[data-v-8face55c] .elder-info .el-col{height:30px}
