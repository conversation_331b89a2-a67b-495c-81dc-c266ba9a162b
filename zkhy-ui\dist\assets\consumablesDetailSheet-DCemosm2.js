import{_ as D,r as c,e as v,I as V,J as x,c as h,o as p,f as r,h as _,k as y,i as s,t as o,n as C,aH as k,v as I,x as S}from"./index-B0qHf98Y.js";const a=t=>(I("data-v-9145b877"),t=t(),S(),t),B={class:"hfrecords"},T=a(()=>s("div",{class:"title_room"}," 老人信息 ",-1)),q={key:0,class:"detail-content"},E={class:"room-info"},H={class:"info-left"},J={class:"info-item"},R=a(()=>s("span",{class:"label"},"老人姓名：",-1)),U={class:"value"},j={class:"info-item"},z=a(()=>s("span",{class:"label"},"楼栋信息：",-1)),A={class:"value"},F={class:"info-item"},G=a(()=>s("span",{class:"label"},"床位号：",-1)),K={class:"value"},L={class:"info-item"},M=a(()=>s("span",{class:"label"},"房间号：",-1)),O={class:"value"},P=a(()=>s("div",{class:"title_room"},[s("span",{class:"label"},"耗材明细")],-1)),Q={class:"table-container"},W={class:"hc_info"},X={class:"info-item-fw"},Y=a(()=>s("span",{class:"label"},"服务项目：",-1)),Z={class:"value"},$={class:"info-item-fw"},ss=a(()=>s("span",{class:"label"},"数       量：",-1)),es={class:"value"},as={class:"info-item-fw"},os=a(()=>s("span",{class:"label"},"操作人：",-1)),ts={class:"value"},ls={class:"info-item-fw"},ns=a(()=>s("span",{class:"label"},"价       格：",-1)),is={class:"value"},cs={class:"info-item-fw"},_s=a(()=>s("span",{class:"label"},"记录时间：",-1)),ds={class:"value"},us={class:"info-items"},vs=a(()=>s("span",{class:"label"},"备       注：",-1)),hs={class:"value"},ps={__name:"consumablesDetailSheet",setup(t,{expose:m}){const l=c(!1),i=c(!1),e=c(null),f=u=>{i.value=!0,k(u.id).then(n=>{e.value=n.data||[],l.value=!0}).finally(()=>{i.value=!1})},d=()=>{l.value=!1,e.value=null};return m({openDialog:f}),(u,n)=>{const b=v("el-button"),g=v("el-dialog"),N=V("loading");return x((p(),h("div",B,[r(g,{modelValue:l.value,"onUpdate:modelValue":n[0]||(n[0]=w=>l.value=w),title:"详情",width:"60%",onClose:d},{footer:_(()=>[r(b,{type:"primary",onClick:d},{default:_(()=>[C("返回")]),_:1})]),default:_(()=>[T,e.value?(p(),h("div",q,[s("div",E,[s("div",H,[s("div",J,[R,s("span",U,o(e.value.elderName||"-"),1)]),s("div",j,[z,s("span",A,o(e.value.buildingName||"-"),1)]),s("div",F,[G,s("span",K,o(e.value.roomNumber||"-")+"-"+o(e.value.bedNumber||"-"),1)]),s("div",L,[M,s("span",O,o(e.value.roomNumber||"-"),1)])])]),P,s("div",Q,[s("div",W,[s("div",X,[Y,s("span",Z,o(e.value.supplyItem||"-"),1)]),s("div",$,[ss,s("span",es,o(e.value.quantity||"-"),1)]),s("div",as,[os,s("span",ts,o(e.value.nurseName||"-"),1)]),s("div",ls,[ns,s("span",is,o(e.value.total||"-"),1)]),s("div",cs,[_s,s("span",ds,o(e.value.updateTime||"-"),1)]),s("div",us,[vs,s("span",hs,o(e.value.remark||"-"),1)])])])])):y("",!0)]),_:1},8,["modelValue"])])),[[N,i.value]])}}},ms=D(ps,[["__scopeId","data-v-9145b877"]]);export{ms as default};
