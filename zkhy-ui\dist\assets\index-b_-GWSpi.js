import H from"./medicationPreparePublic-BtCJf7rD.js";import{a as J,g as O}from"./roommanage-DBG5TiIR.js";import{j as W,k as X}from"./index-2bfkpdNb.js";import{_ as Z,d as ee,r as c,F as le,e as u,c as _,o as p,f as l,k as ae,h as o,i as w,K as C,L as D,j as T,l as te,n as s,t as i,E as oe,G as z}from"./index-B0qHf98Y.js";import"./index-CCXF19OR.js";import"./leave-Dd4WELmg.js";import"./index-e0lvOvDC.js";const ne={class:"drug-receive-record-container"},re={class:"button-group",style:{"text-align":"right"}},ie={class:"medication-plan"},ue={class:"medication-plan"},de={class:"medication-plan"},pe={key:0,class:"pagination-container"},se={__name:"index",setup(ce){const{proxy:v}=ee(),{medication_plan:B}=v.useDict("medication_plan"),M=c([]),V=c([]),a=c({pageSize:10,pageNum:1}),I=c([{value:"片",label:"片"},{value:"粒",label:"粒"},{value:"袋",label:"袋"},{value:"毫升",label:"毫升"},{value:"毫克",label:"毫克"},{value:"克",label:"克"}]),P=c([]),N=c(0),y=c([]),L=()=>{a.value.pageNum=1,m()},U=()=>{y.value=[],a.value={pageSize:10,pageNum:1},m()},E=()=>{v.$refs.MedicationPreparePublicRef.openAdd()},k=n=>{let t="";return I.value.forEach(b=>{b.value==n&&(t=b.label)}),t},Y=n=>{v.$refs.MedicationPreparePublicRef.openView(n)},$=n=>{v.$refs.MedicationPreparePublicRef.openEdit(n)},R=n=>{console.log("删除",n),oe.confirm("注：无服药计划药品支持删除，删除药品将失去原始数据，请慎重删除","确定删除该摆药计划数据吗？",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await X(n.id)).code==200?(z.success("删除成功"),a.value.pageNum=1,m()):z.success("删除失败")})},F=async n=>{V.value=[],a.value.floorId="";const t=await J(n);V.value=t.rows},j=n=>{a.value.pageSize=n,m()},A=n=>{a.value.pageNum=n,m()},m=async()=>{q();const n=await W(v.addDateRange(a.value,y.value,"PreparationStartTime"));P.value=n.rows||[],N.value=n.total||0},q=async()=>{const n=await O();M.value=n.rows||[]};return le(()=>{m()}),(n,t)=>{const b=u("el-date-picker"),d=u("el-form-item"),h=u("el-input"),f=u("el-option"),x=u("el-select"),g=u("el-button"),G=u("el-form"),r=u("el-table-column"),K=u("el-table"),Q=u("el-pagination");return p(),_("div",ne,[l(G,{inline:!0,model:a.value,class:"search-form","label-width":"100px"},{default:o(()=>[l(d,{label:"摆药日期",prop:"updateTime"},{default:o(()=>[l(b,{modelValue:a.value.updateTime,"onUpdate:modelValue":t[0]||(t[0]=e=>a.value.updateTime=e),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(d,{label:"老人姓名",prop:"elderName"},{default:o(()=>[l(h,{modelValue:a.value.elderName,"onUpdate:modelValue":t[1]||(t[1]=e=>a.value.elderName=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(d,{label:"楼栋信息",prop:"buildingId"},{default:o(()=>[l(x,{modelValue:a.value.buildingId,"onUpdate:modelValue":t[2]||(t[2]=e=>a.value.buildingId=e),placeholder:"全部",style:{width:"200px"},clearable:"",onChange:F},{default:o(()=>[l(f,{label:"全部",value:""}),(p(!0),_(C,null,D(M.value,e=>(p(),T(f,{key:e.value,label:e.buildingName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"楼栋层数",prop:"floorId"},{default:o(()=>[l(x,{modelValue:a.value.floorId,"onUpdate:modelValue":t[3]||(t[3]=e=>a.value.floorId=e),placeholder:"全部",style:{width:"200px"},clearable:"",disabled:!a.value.buildingId},{default:o(()=>[l(f,{label:"全部",value:""}),(p(!0),_(C,null,D(V.value,e=>(p(),T(f,{key:e.value,label:e.floorName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),l(d,{label:"房间号",prop:"roomNumber"},{default:o(()=>[l(h,{modelValue:a.value.roomNumber,"onUpdate:modelValue":t[4]||(t[4]=e=>a.value.roomNumber=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(d,{label:"药品名称",prop:"medicationName"},{default:o(()=>[l(h,{modelValue:a.value.medicationName,"onUpdate:modelValue":t[5]||(t[5]=e=>a.value.medicationName=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(d,{label:"摆药周期"},{default:o(()=>[l(b,{modelValue:y.value,"onUpdate:modelValue":t[6]||(t[6]=e=>y.value=e),type:"daterange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(d,{label:"摆药计划",prop:"timeType"},{default:o(()=>[l(x,{modelValue:a.value.timeType,"onUpdate:modelValue":t[7]||(t[7]=e=>a.value.timeType=e),placeholder:"全部",style:{width:"200px"},clearable:""},{default:o(()=>[(p(!0),_(C,null,D(te(B),e=>(p(),T(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"摆药人",prop:"recorder"},{default:o(()=>[l(h,{modelValue:a.value.recorder,"onUpdate:modelValue":t[8]||(t[8]=e=>a.value.recorder=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),w("div",re,[l(g,{type:"primary",onClick:L,icon:"search"},{default:o(()=>[s("查询")]),_:1}),l(g,{onClick:U,icon:"refresh"},{default:o(()=>[s("重置")]),_:1}),l(g,{icon:"Plus",type:"primary",onClick:E,plain:""},{default:o(()=>[s("新增预备")]),_:1})])]),_:1},8,["model"]),l(K,{data:P.value,border:"",style:{width:"100%"}},{default:o(()=>[l(r,{prop:"id",label:"序号",width:"60",align:"center"},{default:o(e=>[s(i(e.$index+1),1)]),_:1}),l(r,{prop:"updateTime",label:"摆药日期",align:"center","min-width":"120"},{default:o(e=>[w("span",null,i(n.parseTime(e.row.updateTime,"{y}-{m}-{d}")),1)]),_:1}),l(r,{prop:"elderName",label:"老人姓名",align:"center"}),l(r,{prop:"floorNumber",label:"楼层信息",align:"center"}),l(r,{prop:"roomNumber",label:"房间号",align:"center"}),l(r,{prop:"buildingName",label:"楼栋信息",align:"center"}),l(r,{prop:"medicationId",label:"药品编号",align:"center"}),l(r,{prop:"medicationName",label:"药品名称",align:"center","min-width":"120"}),l(r,{prop:"dosage",label:"用量",align:"center"}),l(r,{prop:"administrationMethod",label:"服用方法",align:"center"}),l(r,{prop:"quantity",label:"药品数量",align:"center"}),l(r,{prop:"specification",label:"摆药周期",align:"center","min-width":"200"},{default:o(e=>[s(i(e.row.preparationStartTime)+" ~ "+i(e.row.preparationEndTime),1)]),_:1}),l(r,{prop:"specificationQuantity",label:"服药计划",align:"left","min-width":"200","header-align":"center"},{default:o(e=>[w("div",ie,"早晨:"+i(e.row.morningBeforeMeal=="0"?"餐前":"餐后")+" "+i(e.row.morningDosage||"-")+i(k(e.row.morningDosageUnit)||"-"),1),w("div",ue,"中午:"+i(e.row.noonBeforeMeal=="0"?"餐前":"餐后")+" "+i(e.row.noonDosage||"-")+i(k(e.row.noonDosageUnit)||"-"),1),w("div",de,"晚上:"+i(e.row.eveningBeforeMeal=="0"?"餐前":"餐后")+" "+i(e.row.eveningDosage||"-")+i(k(e.row.eveningDosageUnit)||"-"),1)]),_:1}),l(r,{prop:"recorder",label:"摆药人",align:"center","min-width":"180"}),l(r,{prop:"preparer",label:"核对人",align:"center"}),l(r,{label:"操作","min-width":"220",fixed:"right",align:"center"},{default:o(e=>[l(g,{link:"",type:"primary",onClick:S=>Y(e.row),icon:"Search"},{default:o(()=>[s("查看")]),_:2},1032,["onClick"]),l(g,{link:"",type:"primary",onClick:S=>$(e.row),icon:"Edit"},{default:o(()=>[s("修改")]),_:2},1032,["onClick"]),l(g,{link:"",type:"primary",onClick:S=>R(e.row),icon:"Delete"},{default:o(()=>[s("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),N.value>0?(p(),_("div",pe,[l(Q,{background:"","current-page":a.value.pageNum,"onUpdate:currentPage":t[9]||(t[9]=e=>a.value.pageNum=e),"page-size":a.value.pageSize,"onUpdate:pageSize":t[10]||(t[10]=e=>a.value.pageSize=e),"page-sizes":[10,20,30,40],total:N.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:A},null,8,["current-page","page-size","total"])])):ae("",!0),l(H,{ref:"MedicationPreparePublicRef",onSuccess:U},null,512)])}}},ye=Z(se,[["__scopeId","data-v-ae5d4bdb"]]);export{ye as default};
