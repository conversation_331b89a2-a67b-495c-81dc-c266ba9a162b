import{e as x}from"./index-DCxZ1IEc.js";import{_ as B,a as H,r as b,F as I,e as _,I as k,J as L,l as a,c as S,o as F,f as e,h as t,n as l,i as r,t as d,v as R,x as O}from"./index-B0qHf98Y.js";const m=u=>(R("data-v-c30b47a4"),u=u(),O(),u),M={class:"wrapBox"},V={class:"room_info_top"},q=m(()=>r("div",{class:"title_room"},[r("h3",null,"房间信息")],-1)),E={class:"room_form"},J={class:"room_info_top"},j=m(()=>r("div",{class:"title_room"},[r("h3",null,"人员交接信息")],-1)),z={class:"room_form"},A=m(()=>r("div",{class:"title_room_h4"},[r("span",null,"白班交接信息")],-1)),G={class:"room_form"},K={class:"title_room_h5"},P={class:"bottom_room_table"},Q={__name:"nurseShiftDetail",setup(u){const h=H(),p=b(!1),o=b({tNurseHandoverBedList:[]});function v(){p.value=!0,x(h.currentRoute.value.query.id).then(c=>{c.code==200&&(o.value=c.data)}).finally(()=>{p.value=!1})}const g=()=>{h.push("/work/nurseworkstation")};return I(()=>{v()}),(c,U)=>{const C=_("el-button"),n=_("el-form-item"),s=_("el-col"),f=_("el-row"),y=_("Moon"),N=_("el-icon"),i=_("el-table-column"),w=_("el-table"),D=_("el-form"),T=k("loading");return L((F(),S("div",M,[e(C,{onClick:g,type:"primary"},{default:t(()=>[l("返回工作台 ")]),_:1}),e(D,{inline:!0,model:a(o),"label-width":"100px",ref:"formRef"},{default:t(()=>[r("div",V,[q,r("div",E,[e(f,{gutter:24},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(n,{label:"楼栋信息",prop:"buildingId"},{default:t(()=>[l(d(a(o).buildingName||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"楼栋层数",prop:"floorId"},{default:t(()=>[l(d(a(o).floorNumber||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"交接日期",prop:"handoverDate"},{default:t(()=>[l(d(a(o).handoverDate||"-"),1)]),_:1})]),_:1})]),_:1})])]),r("div",J,[j,r("div",z,[A,e(f,{gutter:24},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(n,{label:"白班护士",prop:"dayNurse"},{default:t(()=>[l(d(a(o).dayNurse||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"交班班次",prop:"dayHandoverTime"},{default:t(()=>[l(d(a(o).dayHandoverTime2||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"接班护士",prop:"relievingNurse"},{default:t(()=>[l(d(a(o).relievingNurse||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"交接人数",prop:"dayTotalCount"},{default:t(()=>[l(d(a(o).dayTotalCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"外出人数",prop:"dayOutCount"},{default:t(()=>[l(d(a(o).dayOutCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"离院人数",prop:"dayLeaveCount"},{default:t(()=>[l(d(a(o).dayLeaveCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"病危人数",prop:"dayCriticalCount"},{default:t(()=>[l(d(a(o).dayCriticalCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"死亡人数",prop:"dayDeathCount"},{default:t(()=>[l(d(a(o).dayDeathCount||"-"),1)]),_:1})]),_:1})]),_:1})]),r("div",G,[r("div",K,[r("span",null,[e(N,{color:"#FF00FF"},{default:t(()=>[e(y)]),_:1}),l(" 夜班交接信息 ")])]),e(f,{gutter:24},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(n,{label:"夜班护士",prop:"nightNurse"},{default:t(()=>[l(d(a(o).nightNurse||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"交接班次",prop:"nightHandoverTime"},{default:t(()=>[l(d(a(o).nightHandoverTime2||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"交接人数",prop:"nightTotalCount"},{default:t(()=>[l(d(a(o).nightTotalCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"外出人数",prop:"nightOutCount"},{default:t(()=>[l(d(a(o).nightOutCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"离院人数",prop:"nightLeaveCount"},{default:t(()=>[l(d(a(o).nightLeaveCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"病危人数",prop:"nightCriticalCount"},{default:t(()=>[l(d(a(o).nightCriticalCount||"-"),1)]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(n,{label:"死亡人数",prop:"nightDeathCount"},{default:t(()=>[l(d(a(o).nightDeathCount||"-"),1)]),_:1})]),_:1})]),_:1})])]),r("div",P,[e(w,{data:a(o).tNurseHandoverBedList,style:{width:"100%"},border:""},{default:t(()=>[e(i,{label:"房间号","min-width":"180",align:"center",prop:"roomNumber"}),e(i,{label:"床位号",width:"180",align:"center",prop:"bedNumber"}),e(i,{label:"老人姓名",width:"180",align:"center",prop:"elderName"}),e(i,{label:"白班交接内容","min-width":"300",align:"center",prop:"handoverContent1"}),e(i,{label:"夜班交接内容","min-width":"300",align:"center",prop:"handoverContent2"})]),_:1},8,["data"])])]),_:1},8,["model"])])),[[T,a(p)]])}}},Y=B(Q,[["__scopeId","data-v-c30b47a4"]]);export{Y as default};
