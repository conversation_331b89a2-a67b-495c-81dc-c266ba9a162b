import{cC as h,dc as q,r as d,e as n,c as V,o as m,f as l,h as o,l as u,D as L,K as j,L as E,j as K,i as R,n as g}from"./index-B0qHf98Y.js";const z={class:"dialog-footer"},G={__name:"TreeNodeDialog",props:{modelValue:{},modelModifiers:{}},emits:h(["confirm"],["update:modelValue"]),setup(k,{emit:y}){const r=q(k,"modelValue"),C=y,a=d({label:void 0,value:void 0}),x={label:[{required:!0,message:"请输入选项名",trigger:"blur"}],value:[{required:!0,message:"请输入选项值",trigger:"blur"}]},s=d("string"),N=d([{label:"字符串",value:"string"},{label:"数字",value:"number"}]),w=d(100),p=d();function B(){a.value={label:void 0,value:void 0}}function i(){r.value=!1}function F(){p.value.validate(c=>{c&&(s.value==="number"&&(a.value.value=parseFloat(a.value.value)),a.value.id=w.value++,C("commit",a.value),i())})}return(c,t)=>{const f=n("el-input"),_=n("el-form-item"),v=n("el-col"),T=n("el-option"),U=n("el-select"),D=n("el-form"),b=n("el-button"),M=n("el-dialog");return m(),V("div",null,[l(M,{title:"添加选项",modelValue:r.value,"onUpdate:modelValue":t[3]||(t[3]=e=>r.value=e),width:"800px","close-on-click-modal":!1,"modal-append-to-body":!1,onOpen:B,onClose:i},{footer:o(()=>[R("div",z,[l(b,{type:"primary",onClick:F},{default:o(()=>[g("确 定")]),_:1}),l(b,{onClick:i},{default:o(()=>[g("取 消")]),_:1})])]),default:o(()=>[l(D,{ref_key:"treeNodeForm",ref:p,model:u(a),rules:x,"label-width":"100px"},{default:o(()=>[l(v,{span:24},{default:o(()=>[l(_,{label:"选项名",prop:"label"},{default:o(()=>[l(f,{modelValue:u(a).label,"onUpdate:modelValue":t[0]||(t[0]=e=>u(a).label=e),placeholder:"请输入选项名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:24},{default:o(()=>[l(_,{label:"选项值",prop:"value"},{default:o(()=>[l(f,{modelValue:u(a).value,"onUpdate:modelValue":t[2]||(t[2]=e=>u(a).value=e),placeholder:"请输入选项值",clearable:""},{append:o(()=>[l(U,{modelValue:u(s),"onUpdate:modelValue":t[1]||(t[1]=e=>L(s)?s.value=e:null),style:{width:"100px"}},{default:o(()=>[(m(!0),V(j,null,E(u(N),(e,O)=>(m(),K(T,{key:O,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{G as default};
