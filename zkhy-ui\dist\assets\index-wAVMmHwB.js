import{B as W,d as X,r as m,C as Y,N as Z,e as f,I as A,c as ee,o as y,J as V,f as e,O as B,l as a,h as t,m as w,n as g,j as S,i as le,D as ae}from"./index-B0qHf98Y.js";import{l as oe,g as te,d as ne,u as se,a as re}from"./tassessmentForm-B23DRaU7.js";const ue={class:"app-container"},me={class:"dialog-footer"},de=W({name:"AssessmentForm"}),ce=Object.assign(de,{setup(ie){const{proxy:c}=X(),U=m([]),d=m(!1),C=m(!0),$=m(!0),h=m([]),D=m(!0),P=m(!0),k=m(0),N=m(""),q=Y({form:{},queryParams:{pageNum:1,pageSize:10,formName:null,formCode:null,version:null,status:null},rules:{formName:[{required:!0,message:"评估表单名称 (如: 老人能力评估表, MMSE)不能为空",trigger:"blur"}]}}),{queryParams:s,form:n,rules:K}=Z(q);function _(){C.value=!0,oe(s.value).then(r=>{U.value=r.rows,k.value=r.total,C.value=!1})}function z(){d.value=!1,F()}function F(){n.value={id:null,formName:null,formCode:null,remark:null,version:null,status:null,createTime:null,updateTime:null,createBy:null,updateBy:null},c.resetForm("assessmentFormRef")}function b(){s.value.pageNum=1,_()}function E(){c.resetForm("queryRef"),b()}function I(r){h.value=r.map(l=>l.id),D.value=r.length!=1,P.value=!r.length}function T(){F(),d.value=!0,N.value="添加评估单类型"}function j(r){F();const l=r.id||h.value;te(l).then(u=>{n.value=u.data,d.value=!0,N.value="修改评估单类型"})}function L(){c.$refs.assessmentFormRef.validate(r=>{r&&(n.value.id!=null?se(n.value).then(l=>{c.$modal.msgSuccess("修改成功"),d.value=!1,_()}):re(n.value).then(l=>{c.$modal.msgSuccess("新增成功"),d.value=!1,_()}))})}function M(r){const l=r.id||h.value;c.$modal.confirm('是否确认删除评估单类型编号为"'+l+'"的数据项？').then(function(){return ne(l)}).then(()=>{_(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}return _(),(r,l)=>{const u=f("el-input"),i=f("el-form-item"),v=f("el-button"),x=f("el-form"),p=f("el-table-column"),O=f("el-table"),Q=f("pagination"),J=f("el-dialog"),R=A("hasPermi"),G=A("loading");return y(),ee("div",ue,[V(e(x,{model:a(s),ref:"queryRef",inline:!0,"label-width":"128px"},{default:t(()=>[e(i,{label:"评估表单名称",prop:"formName"},{default:t(()=>[e(u,{modelValue:a(s).formName,"onUpdate:modelValue":l[0]||(l[0]=o=>a(s).formName=o),placeholder:"请输入评估表单名称",clearable:"",onKeyup:w(b,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"评估表单编码",prop:"formCode"},{default:t(()=>[e(u,{modelValue:a(s).formCode,"onUpdate:modelValue":l[1]||(l[1]=o=>a(s).formCode=o),placeholder:"请输入评估表单编码",clearable:"",onKeyup:w(b,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"表单版本号",prop:"version"},{default:t(()=>[e(u,{modelValue:a(s).version,"onUpdate:modelValue":l[2]||(l[2]=o=>a(s).version=o),placeholder:"请输入表单版本号",clearable:"",onKeyup:w(b,["enter"])},null,8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(v,{type:"primary",icon:"Search",onClick:b},{default:t(()=>[g("搜索")]),_:1}),e(v,{icon:"Refresh",onClick:E},{default:t(()=>[g("重置")]),_:1}),e(v,{type:"warning",plain:"",icon:"Plus",onClick:T},{default:t(()=>[g("新增")]),_:1})]),_:1})]),_:1},8,["model"]),[[B,a($)]]),V((y(),S(O,{data:a(U),border:"",stripe:"",onSelectionChange:I},{default:t(()=>[e(p,{type:"selection",width:"55",align:"center"}),e(p,{label:"表单ID",align:"center",prop:"id"}),e(p,{label:"评估表单名称",align:"center",prop:"formName"}),e(p,{label:"评估表单编码",align:"center",prop:"formCode"}),e(p,{label:"表单用途或描述",align:"center",prop:"remark"}),e(p,{label:"表单版本号",align:"center",prop:"version"}),e(p,{label:"状态",align:"center",prop:"status"}),e(p,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(o=>[V((y(),S(v,{link:"",type:"primary",icon:"Edit",onClick:H=>j(o.row)},{default:t(()=>[g("修改")]),_:2},1032,["onClick"])),[[R,["assessment:assessmentForm:edit"]]]),V((y(),S(v,{link:"",type:"primary",icon:"Delete",onClick:H=>M(o.row)},{default:t(()=>[g("删除")]),_:2},1032,["onClick"])),[[R,["assessment:assessmentForm:remove"]]])]),_:1})]),_:1},8,["data"])),[[G,a(C)]]),V(e(Q,{total:a(k),page:a(s).pageNum,"onUpdate:page":l[3]||(l[3]=o=>a(s).pageNum=o),limit:a(s).pageSize,"onUpdate:limit":l[4]||(l[4]=o=>a(s).pageSize=o),onPagination:_},null,8,["total","page","limit"]),[[B,a(k)>0]]),e(J,{title:a(N),modelValue:a(d),"onUpdate:modelValue":l[9]||(l[9]=o=>ae(d)?d.value=o:null),width:"50%","append-to-body":""},{footer:t(()=>[le("div",me,[e(v,{type:"primary",onClick:L},{default:t(()=>[g("确 定")]),_:1}),e(v,{onClick:z},{default:t(()=>[g("取 消")]),_:1})])]),default:t(()=>[e(x,{ref:"assessmentFormRef",model:a(n),rules:a(K),"label-width":"120px"},{default:t(()=>[e(i,{label:"评估表单名称",prop:"formName"},{default:t(()=>[e(u,{modelValue:a(n).formName,"onUpdate:modelValue":l[5]||(l[5]=o=>a(n).formName=o),placeholder:"请输入评估表单名称"},null,8,["modelValue"])]),_:1}),e(i,{label:"评估表单编码 ",prop:"formCode"},{default:t(()=>[e(u,{modelValue:a(n).formCode,"onUpdate:modelValue":l[6]||(l[6]=o=>a(n).formCode=o),placeholder:"请输入评估表单编码"},null,8,["modelValue"])]),_:1}),e(i,{label:"表单用途或描述",prop:"remark"},{default:t(()=>[e(u,{modelValue:a(n).remark,"onUpdate:modelValue":l[7]||(l[7]=o=>a(n).remark=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),e(i,{label:"表单版本号",prop:"version"},{default:t(()=>[e(u,{modelValue:a(n).version,"onUpdate:modelValue":l[8]||(l[8]=o=>a(n).version=o),placeholder:"请输入表单版本号"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ce as default};
