import{g as w}from"./tNursingHandover-ugsVwCUd.js";import{_ as T,d as L,r as h,e as _,j as N,o as f,h as o,i as e,f as s,k as I,t,n as r,c as H,K as A,L as E,l as U,dw as G,v as O,x as P}from"./index-B0qHf98Y.js";const a=u=>(O("data-v-fe10427b"),u=u(),P(),u),$={class:"room-info"},j=a(()=>e("h3",null,"房间信息",-1)),K={class:"top_info"},q={class:"nursing_detail"},z={class:"left_title"},J={class:"roomCss"},M=a(()=>e("span",null,"房间号：",-1)),Q={class:"floor mg10"},R={class:"left_title"},W=a(()=>e("span",null,"楼栋信息：",-1)),X={class:"bottom_title"},Y={class:"left_title"},Z=a(()=>e("span",null,"楼层信息：",-1)),ee={class:"left_title roomCss"},te=a(()=>e("span",null,"房间类型：",-1)),se={class:"handoverInfo"},oe=a(()=>e("h3",null,"交接信息",-1)),ae={style:{float:"right"}},le=a(()=>e("strong",null,"状态：",-1)),ne={style:{"margin-left":"43%"}},de=a(()=>e("strong",null,"交接日期：",-1)),ie=a(()=>e("h3",{class:"title_day",style:{color:"rgba(50, 109, 254, 0.607843137254902)"}}," 白班信息 ",-1)),_e={style:{margin:"10px"}},ce=a(()=>e("span",null,"白班护士：",-1)),re={style:{"margin-top":"6px"}},ue=a(()=>e("span",null,"交接时间：",-1)),he={class:"back1 backDiv"},pe=a(()=>e("div",{class:"h3_title"},"交接人数",-1)),ve={class:"textCenter"},me={class:"back2 backDiv"},fe=a(()=>e("div",{class:"h3_title"},"外出人数",-1)),ge={class:"textCenter"},be={class:"back3 backDiv"},ye=a(()=>e("div",{class:"h3_title"},"离院人数",-1)),Ne={class:"textCenter"},xe={class:"back5 backDiv"},ke=a(()=>e("div",{class:"h3_title"},"死亡人数",-1)),Ce={class:"textCenter"},Ve=a(()=>e("h3",{class:"title_day",style:{color:"rgba(245, 154, 35, 0.607843137254902)"}}," 夜班信息 ",-1)),De={style:{margin:"10px"}},Be=a(()=>e("span",null,"夜班护士：",-1)),Fe={style:{"margin-top":"6px"}},Se=a(()=>e("span",null,"交接时间：",-1)),we={class:"back1 backDiv"},Te=a(()=>e("div",{class:"h3_title"},"交接人数",-1)),Le={class:"textCenter"},Ie={class:"back2 backDiv"},He=a(()=>e("div",{class:"h3_title"},"外出人数",-1)),Ae={class:"textCenter"},Ee={class:"back3 backDiv"},Ue=a(()=>e("div",{class:"h3_title"},"离院人数",-1)),Ge={class:"textCenter"},Oe={class:"back5 backDiv"},Pe=a(()=>e("div",{class:"h3_title"},"死亡人数",-1)),$e={class:"textCenter"},je={class:"bottom_card"},Ke=a(()=>e("div",{class:"bed_detail"},"床位交接详情",-1)),qe={class:"collapse_card"},ze={class:"title_bg"},Je={class:"describe_look"},Me=a(()=>e("div",{class:"title_dayShift"},[e("span",{class:"circle"}),r(" 白班 ")],-1)),Qe={class:"describe"},Re={class:"describe_look"},We={class:"title_dayShift"},Xe={class:"describe"};const Ye={__name:"detailNursing",props:{detailId:{type:String,default:null}},setup(u,{expose:x}){const{proxy:k}=L(),{room_type:Ze,room_area:et}=k.useDict("room_type","room_area"),p=h(!1),C=h("详情"),v=h([]),l=h({});function V(g){w(g).then(i=>{console.log(i,"res===="),i.code===200&&(p.value=!0,l.value=i.data,v.value=i.data.tNursingHandoverBedList.map(m=>m.id)||[])})}return x({init:V}),(g,i)=>{const m=_("el-tag"),c=_("el-row"),d=_("el-col"),D=_("Place"),b=_("el-icon"),B=_("el-collapse-item"),F=_("el-collapse"),S=_("el-dialog");return f(),N(S,{title:C.value,modelValue:p.value,"onUpdate:modelValue":i[2]||(i[2]=n=>p.value=n),width:"50%","append-to-body":""},{default:o(()=>[e("div",$,[j,e("div",K,[e("div",q,[e("div",z,[e("div",J,[M,e("span",Q,t(l.value.roomNumber||"-"),1),s(m,{type:"danger",round:""},{default:o(()=>[r(t(l.value.areaName),1)]),_:1})])]),e("div",R,[W,e("span",null,t(l.value.buildingName||"-"),1)])]),e("div",X,[e("div",Y,[Z,e("span",null,t(l.value.floorNumber||"-"),1)]),e("div",ee,[te,e("span",null,t(l.value.roomType),1)])])])]),e("div",se,[oe,e("p",ae,[le,r(t(l.value.status==="complete"?"已完成":"未完成"),1)])]),e("div",ne,[e("p",null,[de,r(t(l.value.handoverDate||"-"),1)])]),s(c,{gutter:"20"},{default:o(()=>[s(d,{span:12,style:{"background-color":"rgb(242, 242, 242)","border-radius":"10px"}},{default:o(()=>[s(c,null,{default:o(()=>[e("div",null,[ie,e("div",_e,[e("div",null,[ce,e("span",null,t(l.value.dayNurse||"-"),1)]),e("div",re,[ue,e("span",null,t(l.value.dayHandoverTime||"-"),1)])])])]),_:1}),s(c,{gutter:15},{default:o(()=>[s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",he,[pe,e("div",ve,t(l.value.dayTotalCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",me,[fe,e("span",ge,t(l.value.dayOutCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",be,[ye,e("span",Ne,t(l.value.dayLeaveCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",xe,[ke,e("span",Ce,t(l.value.dayDeathCount||"0"),1)])]),_:1}),s(d,{span:12})]),_:1})]),_:1}),s(d,{span:12,style:{"background-color":"rgb(242, 242, 242)","border-radius":"10px"}},{default:o(()=>[s(c,null,{default:o(()=>[e("div",null,[Ve,e("div",De,[e("div",null,[Be,e("span",null,t(l.value.nightNurse||"-"),1)]),e("div",Fe,[Se,e("span",null,t(l.value.nightHandoverTime||"-"),1)])])])]),_:1}),s(c,{gutter:15},{default:o(()=>[s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",we,[Te,e("span",Le,t(l.value.nightTotalCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",Ie,[He,e("span",Ae,t(l.value.nightOutCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",Ee,[Ue,e("span",Ge,t(l.value.nightLeaveCount||"0"),1)])]),_:1}),s(d,{span:12,style:{"margin-bottom":"10px"}},{default:o(()=>[e("div",Oe,[Pe,e("span",$e,t(l.value.nightDeathCount||"0"),1)])]),_:1}),s(d,{span:12})]),_:1})]),_:1})]),_:1}),s(c,{gutter:20},{default:o(()=>[s(d,{span:24},{default:o(()=>[e("div",je,[Ke,e("div",qe,[s(F,{class:"collapse_card_list",modelValue:v.value,"onUpdate:modelValue":i[0]||(i[0]=n=>v.value=n)},{default:o(()=>[(f(!0),H(A,null,E(l.value.tNursingHandoverBedList,(n,y)=>(f(),N(B,{name:n.id,class:"collapse_card_list_item",key:y},{title:o(({isActive:tt})=>[e("div",ze,[s(b,null,{default:o(()=>[s(D)]),_:1}),r(" "+t(l.value.roomNumber||"-")+" - "+t(n.bedNumber>10?n.bedNumber:"0"+n.bedNumber||"-")+"床 "+t(n.elderName||"-")+"（"+t(n.elderGender=="0"?"女":"男")+" "+t(n.elderAge||"-")+"岁） ",1)])]),default:o(()=>[e("div",Je,[Me,e("div",Qe,t(n.handoverContent1||"-"),1)]),e("div",Re,[e("div",We,[s(b,{color:"#FF00FF"},{default:o(()=>[s(U(G))]),_:1}),r("夜班 ")]),e("div",Xe,t(n.handoverContent2||"-"),1)])]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])])])]),_:1})]),_:1}),I("",!0)]),_:1},8,["title","modelValue"])}}},nt=T(Ye,[["__scopeId","data-v-fe10427b"]]);export{nt as default};
