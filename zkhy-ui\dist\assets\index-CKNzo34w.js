import{X as V,B as me,d as ce,r as h,C as fe,N as _e,e as u,I as A,c as R,o as p,J as b,f as e,k as q,O as he,l as a,h as l,m as ve,K,L as O,j as v,n as m,D as Q,i as j,t as J,P as ye}from"./index-B0qHf98Y.js";function X(r){return V({url:"/system/dept/list",method:"get",params:r})}function ge(r){return V({url:"/system/dept/list/exclude/"+r,method:"get"})}function be(r){return V({url:"/system/dept/"+r,method:"get"})}function Ve(r){return V({url:"/system/dept",method:"post",data:r})}function ke(r){return V({url:"/system/dept",method:"put",data:r})}function we(r){return V({url:"/system/dept/"+r,method:"delete"})}const Ne={class:"app-container"},Ie={class:"dialog-footer"},Ce=me({name:"Dept"}),Ue=Object.assign(Ce,{setup(r){const{proxy:s}=ce(),{sys_user_sex:xe}=s.useDict("sys_user_sex"),P=h([]),_=h(!1),C=h(!0),N=h(!0),x=h(""),D=h([]),U=h(!0),S=h(!0),z=fe({form:{},queryParams:{deptName:void 0,status:void 0},rules:{parentId:[{required:!0,message:"上级部门不能为空",trigger:"blur"}],deptName:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"显示排序不能为空",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:y,form:o,rules:G}=_e(z);function g(){C.value=!0,X(y.value).then(d=>{P.value=s.handleTree(d.data,"deptId"),C.value=!1})}function H(){_.value=!1,T()}function T(){o.value={deptId:void 0,parentId:void 0,deptName:void 0,orderNum:0,leader:void 0,phone:void 0,email:void 0,status:"0"},s.resetForm("deptRef")}function $(){g()}function M(){s.resetForm("queryRef"),$()}function B(d){T(),X().then(n=>{D.value=s.handleTree(n.data,"deptId")}),d!=null&&(o.value.parentId=d.deptId),_.value=!0,x.value="添加部门"}function W(){S.value=!1,U.value=!U.value,ye(()=>{S.value=!0})}function Y(d){T(),ge(d.deptId).then(n=>{D.value=s.handleTree(n.data,"deptId")}),be(d.deptId).then(n=>{o.value=n.data,_.value=!0,x.value="修改部门"})}function Z(){s.$refs.deptRef.validate(d=>{d&&(o.value.deptId!=null?ke(o.value).then(n=>{s.$modal.msgSuccess("修改成功"),_.value=!1,g()}):Ve(o.value).then(n=>{s.$modal.msgSuccess("新增成功"),_.value=!1,g()}))})}function ee(d){s.$modal.confirm('是否确认删除名称为"'+d.deptName+'"的数据项?').then(function(){return we(d.deptId)}).then(()=>{g(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return g(),(d,n)=>{const k=u("el-input"),i=u("el-form-item"),le=u("el-option"),te=u("el-select"),c=u("el-button"),E=u("el-form"),f=u("el-col"),ae=u("right-toolbar"),F=u("el-row"),w=u("el-table-column"),ne=u("dict-tag"),oe=u("el-table"),de=u("el-tree-select"),ue=u("el-input-number"),re=u("el-radio"),se=u("el-radio-group"),pe=u("el-dialog"),I=A("hasPermi"),ie=A("loading");return p(),R("div",Ne,[b(e(E,{model:a(y),ref:"queryRef",inline:!0},{default:l(()=>[e(i,{label:"部门名称",prop:"deptName"},{default:l(()=>[e(k,{modelValue:a(y).deptName,"onUpdate:modelValue":n[0]||(n[0]=t=>a(y).deptName=t),placeholder:"请输入部门名称",clearable:"",style:{width:"200px"},onKeyup:ve($,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"状态",prop:"status"},{default:l(()=>[e(te,{modelValue:a(y).status,"onUpdate:modelValue":n[1]||(n[1]=t=>a(y).status=t),placeholder:"部门状态",clearable:"",style:{width:"200px"}},{default:l(()=>[(p(!0),R(K,null,O(d.sys_normal_disable,t=>(p(),v(le,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:$},{default:l(()=>[m("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:M},{default:l(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[he,a(N)]]),e(F,{gutter:10,class:"mb8"},{default:l(()=>[e(f,{span:1.5},{default:l(()=>[b((p(),v(c,{type:"primary",plain:"",icon:"Plus",onClick:B},{default:l(()=>[m("新增")]),_:1})),[[I,["system:dept:add"]]])]),_:1}),e(f,{span:1.5},{default:l(()=>[e(c,{type:"info",plain:"",icon:"Sort",onClick:W},{default:l(()=>[m("展开/折叠")]),_:1})]),_:1}),e(ae,{showSearch:a(N),"onUpdate:showSearch":n[2]||(n[2]=t=>Q(N)?N.value=t:null),onQueryTable:g},null,8,["showSearch"])]),_:1}),a(S)?b((p(),v(oe,{key:0,data:a(P),"row-key":"deptId","default-expand-all":a(U),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:l(()=>[e(w,{prop:"deptName",label:"部门名称",width:"260"}),e(w,{prop:"orderNum",label:"排序",width:"200"}),e(w,{prop:"status",label:"状态",width:"100"},{default:l(t=>[e(ne,{options:d.sys_normal_disable,value:t.row.status},null,8,["options","value"])]),_:1}),e(w,{label:"创建时间",align:"center",prop:"createTime",width:"200"},{default:l(t=>[j("span",null,J(d.parseTime(t.row.createTime)),1)]),_:1}),e(w,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(t=>[b((p(),v(c,{link:"",type:"primary",icon:"Edit",onClick:L=>Y(t.row)},{default:l(()=>[m("修改")]),_:2},1032,["onClick"])),[[I,["system:dept:edit"]]]),b((p(),v(c,{link:"",type:"primary",icon:"Plus",onClick:L=>B(t.row)},{default:l(()=>[m("新增")]),_:2},1032,["onClick"])),[[I,["system:dept:add"]]]),t.row.parentId!=0?b((p(),v(c,{key:0,link:"",type:"primary",icon:"Delete",onClick:L=>ee(t.row)},{default:l(()=>[m("删除")]),_:2},1032,["onClick"])),[[I,["system:dept:remove"]]]):q("",!0)]),_:1})]),_:1},8,["data","default-expand-all"])),[[ie,a(C)]]):q("",!0),e(pe,{title:a(x),modelValue:a(_),"onUpdate:modelValue":n[10]||(n[10]=t=>Q(_)?_.value=t:null),width:"600px","append-to-body":""},{footer:l(()=>[j("div",Ie,[e(c,{type:"primary",onClick:Z},{default:l(()=>[m("确 定")]),_:1}),e(c,{onClick:H},{default:l(()=>[m("取 消")]),_:1})])]),default:l(()=>[e(E,{ref:"deptRef",model:a(o),rules:a(G),"label-width":"80px"},{default:l(()=>[e(F,null,{default:l(()=>[a(o).parentId!==0?(p(),v(f,{key:0,span:24},{default:l(()=>[e(i,{label:"上级部门",prop:"parentId"},{default:l(()=>[e(de,{modelValue:a(o).parentId,"onUpdate:modelValue":n[3]||(n[3]=t=>a(o).parentId=t),data:a(D),props:{value:"deptId",label:"deptName",children:"children"},"value-key":"deptId",placeholder:"选择上级部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})):q("",!0),e(f,{span:12},{default:l(()=>[e(i,{label:"部门名称",prop:"deptName"},{default:l(()=>[e(k,{modelValue:a(o).deptName,"onUpdate:modelValue":n[4]||(n[4]=t=>a(o).deptName=t),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:l(()=>[e(i,{label:"显示排序",prop:"orderNum"},{default:l(()=>[e(ue,{modelValue:a(o).orderNum,"onUpdate:modelValue":n[5]||(n[5]=t=>a(o).orderNum=t),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:l(()=>[e(i,{label:"负责人",prop:"leader"},{default:l(()=>[e(k,{modelValue:a(o).leader,"onUpdate:modelValue":n[6]||(n[6]=t=>a(o).leader=t),placeholder:"请输入负责人",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:l(()=>[e(i,{label:"联系电话",prop:"phone"},{default:l(()=>[e(k,{modelValue:a(o).phone,"onUpdate:modelValue":n[7]||(n[7]=t=>a(o).phone=t),placeholder:"请输入联系电话",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:l(()=>[e(i,{label:"邮箱",prop:"email"},{default:l(()=>[e(k,{modelValue:a(o).email,"onUpdate:modelValue":n[8]||(n[8]=t=>a(o).email=t),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:l(()=>[e(i,{label:"部门状态"},{default:l(()=>[e(se,{modelValue:a(o).status,"onUpdate:modelValue":n[9]||(n[9]=t=>a(o).status=t)},{default:l(()=>[(p(!0),R(K,null,O(d.sys_normal_disable,t=>(p(),v(re,{key:t.value,value:t.value},{default:l(()=>[m(J(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ue as default};
