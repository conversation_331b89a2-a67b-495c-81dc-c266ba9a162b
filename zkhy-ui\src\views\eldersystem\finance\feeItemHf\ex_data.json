{"feeItemListAll": [{"id": "bed-1", "name": "标准床位费1型", "feeType": "bed", "feeLevel": "standard", "price": 2844, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-04-24", "expiryDate": null, "status": "enabled", "description": "标准床位服务", "remark": "基础 高级 其他"}, {"id": "bed-2", "name": "豪华床位费2型", "feeType": "bed", "feeLevel": "luxury", "price": 2529, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-03-19", "expiryDate": null, "status": "enabled", "description": "豪华床位服务", "remark": "护理 费用 服务"}, {"id": "bed-3", "name": "VIP床位费3型", "feeType": "bed", "feeLevel": "vip", "price": 3486, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2023-08-20", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "基础 高级 费用"}, {"id": "bed-4", "name": "标准床位费4型", "feeType": "bed", "feeLevel": "standard", "price": 1689, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-04-11", "expiryDate": "2025-07-09", "status": "disabled", "description": "标准床位服务", "remark": "特殊 其他 特殊"}, {"id": "bed-5", "name": "VIP床位费5型", "feeType": "bed", "feeLevel": "vip", "price": 2692, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2024-08-24", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "服务 服务 标准"}, {"id": "bed-6", "name": "豪华床位费6型", "feeType": "bed", "feeLevel": "luxury", "price": 3372, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-02-23", "expiryDate": "2025-06-26", "status": "disabled", "description": "豪华床位服务", "remark": "费用 特殊 餐饮"}, {"id": "bed-7", "name": "豪华床位费7型", "feeType": "bed", "feeLevel": "luxury", "price": 4035, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2025-04-12", "expiryDate": "2025-06-21", "status": "disabled", "description": "豪华床位服务", "remark": "护理 特殊 高级"}, {"id": "bed-8", "name": "标准床位费8型", "feeType": "bed", "feeLevel": "standard", "price": 2535, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2024-07-02", "expiryDate": "2025-06-25", "status": "disabled", "description": "标准床位服务", "remark": "其他 服务 费用"}, {"id": "bed-9", "name": "标准床位费9型", "feeType": "bed", "feeLevel": "standard", "price": 2102, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-12-14", "expiryDate": "2025-07-08", "status": "disabled", "description": "标准床位服务", "remark": "费用 标准 餐饮"}, {"id": "bed-10", "name": "标准床位费10型", "feeType": "bed", "feeLevel": "standard", "price": 4649, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2023-11-23", "expiryDate": null, "status": "enabled", "description": "标准床位服务", "remark": "其他 餐饮 特殊"}, {"id": "bed-11", "name": "VIP床位费11型", "feeType": "bed", "feeLevel": "vip", "price": 3881, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-09-24", "expiryDate": "2025-06-24", "status": "disabled", "description": "VIP床位服务", "remark": "护理 高级 特殊"}, {"id": "bed-12", "name": "标准床位费12型", "feeType": "bed", "feeLevel": "standard", "price": 2733, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-05-20", "expiryDate": null, "status": "enabled", "description": "标准床位服务", "remark": "服务 标准 基础"}, {"id": "bed-13", "name": "标准床位费13型", "feeType": "bed", "feeLevel": "standard", "price": 4620, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2025-02-19", "expiryDate": null, "status": "enabled", "description": "标准床位服务", "remark": "其他 特殊 餐饮"}, {"id": "bed-14", "name": "豪华床位费14型", "feeType": "bed", "feeLevel": "luxury", "price": 1487, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-10-31", "expiryDate": null, "status": "enabled", "description": "豪华床位服务", "remark": "费用 护理 标准"}, {"id": "bed-15", "name": "VIP床位费15型", "feeType": "bed", "feeLevel": "vip", "price": 2525, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-03-04", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "高级 床位 护理"}, {"id": "bed-16", "name": "VIP床位费16型", "feeType": "bed", "feeLevel": "vip", "price": 3411, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-07-13", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "餐饮 费用 其他"}, {"id": "bed-17", "name": "标准床位费17型", "feeType": "bed", "feeLevel": "standard", "price": 3637, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-01-09", "expiryDate": null, "status": "enabled", "description": "标准床位服务", "remark": "基础 床位 其他"}, {"id": "bed-18", "name": "VIP床位费18型", "feeType": "bed", "feeLevel": "vip", "price": 2955, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2023-07-20", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "高级 服务 基础"}, {"id": "bed-19", "name": "VIP床位费19型", "feeType": "bed", "feeLevel": "vip", "price": 1638, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-01-21", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "其他 餐饮 其他"}, {"id": "bed-20", "name": "豪华床位费20型", "feeType": "bed", "feeLevel": "luxury", "price": 1350, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-08-03", "expiryDate": null, "status": "enabled", "description": "豪华床位服务", "remark": "基础 护理 护理"}, {"id": "bed-21", "name": "VIP床位费21型", "feeType": "bed", "feeLevel": "vip", "price": 4234, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2023-11-10", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "标准 护理 标准"}, {"id": "bed-22", "name": "豪华床位费22型", "feeType": "bed", "feeLevel": "luxury", "price": 3961, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2023-10-07", "expiryDate": null, "status": "enabled", "description": "豪华床位服务", "remark": "特殊 高级 床位"}, {"id": "bed-23", "name": "豪华床位费23型", "feeType": "bed", "feeLevel": "luxury", "price": 2334, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-01-28", "expiryDate": "2025-06-14", "status": "disabled", "description": "豪华床位服务", "remark": "餐饮 基础 其他"}, {"id": "bed-24", "name": "VIP床位费24型", "feeType": "bed", "feeLevel": "vip", "price": 2720, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2025-01-23", "expiryDate": "2025-06-17", "status": "disabled", "description": "VIP床位服务", "remark": "服务 特殊 费用"}, {"id": "bed-25", "name": "豪华床位费25型", "feeType": "bed", "feeLevel": "luxury", "price": 1646, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-02-21", "expiryDate": null, "status": "enabled", "description": "豪华床位服务", "remark": "基础 餐饮 餐饮"}, {"id": "bed-26", "name": "VIP床位费26型", "feeType": "bed", "feeLevel": "vip", "price": 3935, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-02-02", "expiryDate": "2025-07-04", "status": "disabled", "description": "VIP床位服务", "remark": "餐饮 标准 服务"}, {"id": "bed-27", "name": "标准床位费27型", "feeType": "bed", "feeLevel": "standard", "price": 1222, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2023-12-09", "expiryDate": null, "status": "enabled", "description": "标准床位服务", "remark": "床位 其他 标准"}, {"id": "bed-28", "name": "VIP床位费28型", "feeType": "bed", "feeLevel": "vip", "price": 3254, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2024-02-25", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "特殊 服务 特殊"}, {"id": "bed-29", "name": "VIP床位费29型", "feeType": "bed", "feeLevel": "vip", "price": 1558, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-12-10", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "特殊 其他 护理"}, {"id": "bed-30", "name": "VIP床位费30型", "feeType": "bed", "feeLevel": "vip", "price": 3997, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2024-06-13", "expiryDate": null, "status": "enabled", "description": "VIP床位服务", "remark": "服务 特殊 高级"}, {"id": "meal-1", "name": "高级餐费1型", "feeType": "meal", "feeLevel": "premium", "price": 19, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2025-02-02", "expiryDate": "2025-06-25", "status": "disabled", "description": "高级餐饮服务", "remark": "基础 费用 费用"}, {"id": "meal-2", "name": "基础餐费2型", "feeType": "meal", "feeLevel": "basic", "price": 27, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-11-30", "expiryDate": "2025-07-03", "status": "disabled", "description": "基础餐饮服务", "remark": "标准 特殊 餐饮"}, {"id": "meal-3", "name": "基础餐费3型", "feeType": "meal", "feeLevel": "basic", "price": 56, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-09-07", "expiryDate": "2025-06-23", "status": "disabled", "description": "基础餐饮服务", "remark": "护理 床位 高级"}, {"id": "meal-4", "name": "基础餐费4型", "feeType": "meal", "feeLevel": "basic", "price": 30, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-03-25", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "护理 基础 标准"}, {"id": "meal-5", "name": "基础餐费5型", "feeType": "meal", "feeLevel": "basic", "price": 69, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-10-03", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "基础 护理 标准"}, {"id": "meal-6", "name": "基础餐费6型", "feeType": "meal", "feeLevel": "basic", "price": 78, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-11-25", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "服务 服务 基础"}, {"id": "meal-7", "name": "高级餐费7型", "feeType": "meal", "feeLevel": "premium", "price": 18, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-02-22", "expiryDate": "2025-06-28", "status": "disabled", "description": "高级餐饮服务", "remark": "服务 餐饮 费用"}, {"id": "meal-8", "name": "基础餐费8型", "feeType": "meal", "feeLevel": "basic", "price": 28, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-11-29", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "餐饮 护理 餐饮"}, {"id": "meal-9", "name": "基础餐费9型", "feeType": "meal", "feeLevel": "basic", "price": 75, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-03-04", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "高级 服务 服务"}, {"id": "meal-10", "name": "高级餐费10型", "feeType": "meal", "feeLevel": "premium", "price": 52, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-02-19", "expiryDate": "2025-06-21", "status": "disabled", "description": "高级餐饮服务", "remark": "服务 基础 基础"}, {"id": "meal-11", "name": "基础餐费11型", "feeType": "meal", "feeLevel": "basic", "price": 49, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2025-07-04", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "餐饮 费用 特殊"}, {"id": "meal-12", "name": "基础餐费12型", "feeType": "meal", "feeLevel": "basic", "price": 77, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2025-05-02", "expiryDate": "2025-06-28", "status": "disabled", "description": "基础餐饮服务", "remark": "费用 床位 服务"}, {"id": "meal-13", "name": "高级餐费13型", "feeType": "meal", "feeLevel": "premium", "price": 72, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-02-24", "expiryDate": null, "status": "enabled", "description": "高级餐饮服务", "remark": "护理 基础 特殊"}, {"id": "meal-14", "name": "高级餐费14型", "feeType": "meal", "feeLevel": "premium", "price": 50, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-08-08", "expiryDate": null, "status": "enabled", "description": "高级餐饮服务", "remark": "其他 费用 服务"}, {"id": "meal-15", "name": "高级餐费15型", "feeType": "meal", "feeLevel": "premium", "price": 66, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-06-14", "expiryDate": "2025-06-19", "status": "disabled", "description": "高级餐饮服务", "remark": "床位 基础 基础"}, {"id": "meal-16", "name": "基础餐费16型", "feeType": "meal", "feeLevel": "basic", "price": 21, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-03-24", "expiryDate": "2025-07-03", "status": "disabled", "description": "基础餐饮服务", "remark": "基础 特殊 高级"}, {"id": "meal-17", "name": "高级餐费17型", "feeType": "meal", "feeLevel": "premium", "price": 44, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-12-16", "expiryDate": null, "status": "enabled", "description": "高级餐饮服务", "remark": "特殊 服务 高级"}, {"id": "meal-18", "name": "基础餐费18型", "feeType": "meal", "feeLevel": "basic", "price": 31, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-11-30", "expiryDate": null, "status": "enabled", "description": "基础餐饮服务", "remark": "其他 标准 服务"}, {"id": "meal-19", "name": "基础餐费19型", "feeType": "meal", "feeLevel": "basic", "price": 62, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-08-09", "expiryDate": "2025-07-07", "status": "disabled", "description": "基础餐饮服务", "remark": "费用 费用 床位"}, {"id": "meal-20", "name": "高级餐费20型", "feeType": "meal", "feeLevel": "premium", "price": 25, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-12-25", "expiryDate": null, "status": "enabled", "description": "高级餐饮服务", "remark": "床位 费用 特殊"}, {"id": "nursing-1", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 232, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-08-13", "expiryDate": null, "status": "enabled", "description": "基础护理服务", "remark": "其他 护理 护理"}, {"id": "nursing-2", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 212, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-01-05", "expiryDate": null, "status": "enabled", "description": "基础护理服务", "remark": "高级 服务 服务"}, {"id": "nursing-3", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 251, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-11-09", "expiryDate": "2025-07-03", "status": "disabled", "description": "基础护理服务", "remark": "基础 护理 特殊"}, {"id": "nursing-4", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 220, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-02-03", "expiryDate": "2025-07-09", "status": "disabled", "description": "高级护理服务", "remark": "基础 其他 特殊"}, {"id": "nursing-5", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 128, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-08-11", "expiryDate": null, "status": "enabled", "description": "高级护理服务", "remark": "餐饮 特殊 基础"}, {"id": "nursing-6", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 157, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-04-13", "expiryDate": null, "status": "enabled", "description": "中等护理服务", "remark": "标准 费用 高级"}, {"id": "nursing-7", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 82, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-08-06", "expiryDate": null, "status": "enabled", "description": "基础护理服务", "remark": "费用 高级 其他"}, {"id": "nursing-8", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 234, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-01-10", "expiryDate": "2025-06-19", "status": "disabled", "description": "基础护理服务", "remark": "其他 标准 基础"}, {"id": "nursing-9", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 145, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-06-20", "expiryDate": null, "status": "enabled", "description": "中等护理服务", "remark": "高级 服务 特殊"}, {"id": "nursing-10", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 300, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-01-04", "expiryDate": null, "status": "enabled", "description": "基础护理服务", "remark": "服务 标准 其他"}, {"id": "nursing-11", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 166, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-09-09", "expiryDate": "2025-07-09", "status": "disabled", "description": "基础护理服务", "remark": "基础 其他 餐饮"}, {"id": "nursing-12", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 134, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2025-04-18", "expiryDate": null, "status": "enabled", "description": "基础护理服务", "remark": "餐饮 基础 护理"}, {"id": "nursing-13", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 247, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-04-19", "expiryDate": "2025-06-14", "status": "disabled", "description": "中等护理服务", "remark": "其他 基础 护理"}, {"id": "nursing-14", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 263, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-04-11", "expiryDate": "2025-06-17", "status": "disabled", "description": "中等护理服务", "remark": "服务 特殊 服务"}, {"id": "nursing-15", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 258, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-03-23", "expiryDate": null, "status": "enabled", "description": "高级护理服务", "remark": "费用 特殊 餐饮"}, {"id": "nursing-16", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 77, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-07-22", "expiryDate": "2025-06-24", "status": "disabled", "description": "基础护理服务", "remark": "其他 护理 餐饮"}, {"id": "nursing-17", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 170, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2023-12-01", "expiryDate": "2025-06-25", "status": "disabled", "description": "高级护理服务", "remark": "特殊 床位 餐饮"}, {"id": "nursing-18", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 198, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-03-04", "expiryDate": "2025-07-04", "status": "disabled", "description": "中等护理服务", "remark": "费用 标准 特殊"}, {"id": "nursing-19", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 185, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-05-08", "expiryDate": "2025-06-21", "status": "disabled", "description": "中等护理服务", "remark": "特殊 标准 床位"}, {"id": "nursing-20", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 103, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-07-30", "expiryDate": "2025-06-11", "status": "disabled", "description": "中等护理服务", "remark": "费用 特殊 服务"}, {"id": "nursing-21", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 267, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-01-21", "expiryDate": "2025-06-11", "status": "disabled", "description": "高级护理服务", "remark": "床位 特殊 床位"}, {"id": "nursing-22", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 296, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-02-12", "expiryDate": null, "status": "enabled", "description": "高级护理服务", "remark": "餐饮 特殊 护理"}, {"id": "nursing-23", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 204, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2024-07-31", "expiryDate": null, "status": "enabled", "description": "高级护理服务", "remark": "费用 标准 床位"}, {"id": "nursing-24", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 104, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-12-29", "expiryDate": null, "status": "enabled", "description": "中等护理服务", "remark": "费用 高级 床位"}, {"id": "nursing-25", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 218, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2025-03-17", "expiryDate": "2025-06-19", "status": "disabled", "description": "高级护理服务", "remark": "高级 其他 床位"}, {"id": "nursing-26", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 224, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2025-01-30", "expiryDate": null, "status": "enabled", "description": "中等护理服务", "remark": "其他 餐饮 服务"}, {"id": "nursing-27", "name": "三级护理费", "feeType": "nursing", "feeLevel": "level3", "price": 222, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-10-18", "expiryDate": null, "status": "enabled", "description": "高级护理服务", "remark": "床位 餐饮 床位"}, {"id": "nursing-28", "name": "一级护理费", "feeType": "nursing", "feeLevel": "level1", "price": 199, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-03-30", "expiryDate": null, "status": "enabled", "description": "基础护理服务", "remark": "标准 其他 其他"}, {"id": "nursing-29", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 228, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-04-11", "expiryDate": "2025-06-10", "status": "disabled", "description": "中等护理服务", "remark": "服务 特殊 服务"}, {"id": "nursing-30", "name": "二级护理费", "feeType": "nursing", "feeLevel": "level2", "price": 110, "allowDiscount": "N", "billingCycle": "daily", "effectiveDate": "2023-07-10", "expiryDate": "2025-06-15", "status": "disabled", "description": "中等护理服务", "remark": "特殊 服务 基础"}, {"id": "other-1", "name": "杂项费用1", "feeType": "other", "feeLevel": "other1", "price": 284, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-02-13", "expiryDate": null, "status": "enabled", "description": "其他服务费用1", "remark": "餐饮 基础 高级"}, {"id": "other-2", "name": "杂项费用2", "feeType": "other", "feeLevel": "other2", "price": 81, "allowDiscount": "N", "billingCycle": "quarterly", "effectiveDate": "2024-05-10", "expiryDate": "2025-06-16", "status": "disabled", "description": "其他服务费用2", "remark": "标准 服务 床位"}, {"id": "other-3", "name": "杂项费用3", "feeType": "other", "feeLevel": "other1", "price": 431, "allowDiscount": "Y", "billingCycle": "yearly", "effectiveDate": "2025-05-24", "expiryDate": null, "status": "enabled", "description": "其他服务费用3", "remark": "基础 特殊 服务"}, {"id": "other-4", "name": "杂项费用4", "feeType": "other", "feeLevel": "other1", "price": 419, "allowDiscount": "Y", "billingCycle": "weekly", "effectiveDate": "2025-05-01", "expiryDate": null, "status": "enabled", "description": "其他服务费用4", "remark": "高级 标准 特殊"}, {"id": "other-5", "name": "杂项费用5", "feeType": "other", "feeLevel": "other2", "price": 17, "allowDiscount": "N", "billingCycle": "yearly", "effectiveDate": "2025-05-05", "expiryDate": null, "status": "enabled", "description": "其他服务费用5", "remark": "基础 高级 餐饮"}, {"id": "other-6", "name": "杂项费用6", "feeType": "other", "feeLevel": "other1", "price": 269, "allowDiscount": "N", "billingCycle": "yearly", "effectiveDate": "2024-12-16", "expiryDate": "2025-06-29", "status": "disabled", "description": "其他服务费用6", "remark": "费用 标准 标准"}, {"id": "other-7", "name": "杂项费用7", "feeType": "other", "feeLevel": "other2", "price": 420, "allowDiscount": "Y", "billingCycle": "once", "effectiveDate": "2024-05-25", "expiryDate": null, "status": "enabled", "description": "其他服务费用7", "remark": "标准 护理 床位"}, {"id": "other-8", "name": "杂项费用8", "feeType": "other", "feeLevel": "other1", "price": 213, "allowDiscount": "N", "billingCycle": "quarterly", "effectiveDate": "2025-02-13", "expiryDate": "2025-07-09", "status": "disabled", "description": "其他服务费用8", "remark": "服务 餐饮 高级"}, {"id": "other-9", "name": "杂项费用9", "feeType": "other", "feeLevel": "other1", "price": 149, "allowDiscount": "Y", "billingCycle": "quarterly", "effectiveDate": "2023-07-19", "expiryDate": "2025-06-14", "status": "disabled", "description": "其他服务费用9", "remark": "其他 高级 基础"}, {"id": "other-10", "name": "杂项费用10", "feeType": "other", "feeLevel": "other2", "price": 174, "allowDiscount": "Y", "billingCycle": "weekly", "effectiveDate": "2023-09-25", "expiryDate": "2025-06-12", "status": "disabled", "description": "其他服务费用10", "remark": "餐饮 床位 基础"}, {"id": "other-11", "name": "杂项费用11", "feeType": "other", "feeLevel": "other2", "price": 439, "allowDiscount": "Y", "billingCycle": "quarterly", "effectiveDate": "2024-06-08", "expiryDate": "2025-06-21", "status": "disabled", "description": "其他服务费用11", "remark": "特殊 餐饮 标准"}, {"id": "other-12", "name": "杂项费用12", "feeType": "other", "feeLevel": "other1", "price": 403, "allowDiscount": "N", "billingCycle": "once", "effectiveDate": "2025-04-06", "expiryDate": "2025-07-05", "status": "disabled", "description": "其他服务费用12", "remark": "基础 标准 特殊"}, {"id": "other-13", "name": "杂项费用13", "feeType": "other", "feeLevel": "other1", "price": 70, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2024-07-22", "expiryDate": "2025-06-15", "status": "disabled", "description": "其他服务费用13", "remark": "床位 服务 基础"}, {"id": "other-14", "name": "杂项费用14", "feeType": "other", "feeLevel": "other2", "price": 356, "allowDiscount": "N", "billingCycle": "quarterly", "effectiveDate": "2024-07-03", "expiryDate": "2025-06-10", "status": "disabled", "description": "其他服务费用14", "remark": "餐饮 标准 护理"}, {"id": "other-15", "name": "杂项费用15", "feeType": "other", "feeLevel": "other2", "price": 46, "allowDiscount": "N", "billingCycle": "yearly", "effectiveDate": "2024-09-09", "expiryDate": "2025-06-11", "status": "disabled", "description": "其他服务费用15", "remark": "床位 餐饮 护理"}, {"id": "other-16", "name": "杂项费用16", "feeType": "other", "feeLevel": "other1", "price": 299, "allowDiscount": "N", "billingCycle": "monthly", "effectiveDate": "2023-12-21", "expiryDate": "2025-07-06", "status": "disabled", "description": "其他服务费用16", "remark": "床位 费用 服务"}, {"id": "other-17", "name": "杂项费用17", "feeType": "other", "feeLevel": "other2", "price": 314, "allowDiscount": "N", "billingCycle": "once", "effectiveDate": "2023-09-20", "expiryDate": null, "status": "enabled", "description": "其他服务费用17", "remark": "服务 标准 基础"}, {"id": "other-18", "name": "杂项费用18", "feeType": "other", "feeLevel": "other2", "price": 70, "allowDiscount": "Y", "billingCycle": "weekly", "effectiveDate": "2025-04-19", "expiryDate": "2025-06-28", "status": "disabled", "description": "其他服务费用18", "remark": "费用 其他 护理"}, {"id": "other-19", "name": "杂项费用19", "feeType": "other", "feeLevel": "other1", "price": 383, "allowDiscount": "Y", "billingCycle": "monthly", "effectiveDate": "2023-07-30", "expiryDate": null, "status": "enabled", "description": "其他服务费用19", "remark": "费用 基础 护理"}, {"id": "other-20", "name": "杂项费用20", "feeType": "other", "feeLevel": "other1", "price": 306, "allowDiscount": "Y", "billingCycle": "daily", "effectiveDate": "2024-05-03", "expiryDate": "2025-07-05", "status": "disabled", "description": "其他服务费用20", "remark": "高级 基础 床位"}], "feeTypeOptions": [{"value": "bed", "label": "床位费", "elTagType": "info"}, {"value": "meal", "label": "餐费", "elTagType": "success"}, {"value": "nursing", "label": "护理费", "elTagType": "primary"}, {"value": "other", "label": "杂项", "elTagType": "warning"}], "feeLevelOptions": [{"value": "standard", "label": "标准", "type": "bed", "elTagType": "success"}, {"value": "vip", "label": "VIP", "type": "bed", "elTagType": "primary"}, {"value": "luxury", "label": "豪华", "type": "bed", "elTagType": "warning"}, {"value": "basic", "label": "基础餐", "type": "meal", "elTagType": "primary"}, {"value": "premium", "label": "高级餐", "type": "meal", "elTagType": "warning"}, {"value": "level1", "label": "一级护理", "type": "nursing", "elTagType": "success"}, {"value": "level2", "label": "二级护理", "type": "nursing", "elTagType": "primary"}, {"value": "level3", "label": "三级护理", "type": "nursing", "elTagType": "warning"}, {"value": "other1", "label": "杂项1", "type": "other", "elTagType": "success"}, {"value": "other2", "label": "杂项2", "type": "other", "elTagType": "primary"}], "statusOptions": [{"value": "enabled", "label": "启用"}, {"value": "disabled", "label": "停用"}], "yesNoOptions": [{"value": "Y", "label": "是", "elTagType": "success"}, {"value": "N", "label": "否", "elTagType": "warning"}], "billingCycleOptions": [{"value": "daily", "label": "日"}, {"value": "weekly", "label": "周"}, {"value": "monthly", "label": "月"}, {"value": "quarterly", "label": "季度"}, {"value": "yearly", "label": "年"}, {"value": "once", "label": "次"}]}