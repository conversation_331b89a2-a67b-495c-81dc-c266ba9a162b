import{_ as v,r as p,e as u,c as x,o as w,f as h,h as _,i as t,t as s,l as o,n as c,D as y,v as C,x as D}from"./index-B0qHf98Y.js";const i=n=>(C("data-v-f20f71a7"),n=n(),D(),n),b={class:"replace-consumables"},V=i(()=>t("div",{class:"headerTitle"},[t("h2",{class:"tdColor"},"护士日志")],-1)),k={class:"table-style"},N={style:{"text-align":"left","white-space":"nowrap"}},S={style:{"text-align":"center"}},I={style:{"text-align":"center"}},z={style:{"text-align":"left"},colspan:"3"},B={class:"log-content"},T=i(()=>t("span",null,"工作内容:",-1)),E={class:"preContent"},P={style:{"text-align":"left"},colspan:"3"},R={class:"log-content"},U=i(()=>t("span",null,"工作计划:",-1)),$={class:"preContent"},j={style:{"text-align":"left"},colspan:"3"},q={class:"log-content"},A=i(()=>t("span",null,"工作建议:",-1)),F={class:"preContent"},G={class:"dialog-footer"},H={__name:"rzrcDetail",setup(n,{expose:f}){const l=p(!1),e=p({});return f({openDialog:d=>{console.log(d.rows.data,"rizhi"),l.value=!0,e.value=d.rows.data}}),(d,a)=>{const g=u("el-button"),m=u("el-dialog");return w(),x("div",b,[h(m,{modelValue:o(l),"onUpdate:modelValue":a[1]||(a[1]=r=>y(l)?l.value=r:null),title:"详情",width:"60%"},{footer:_(()=>[t("div",G,[h(g,{onClick:a[0]||(a[0]=r=>l.value=!1)},{default:_(()=>[c("返回")]),_:1})])]),default:_(()=>[V,t("table",k,[t("tbody",null,[t("tr",null,[t("td",N,"所属部门:"+s(o(e).departmentName||"-"),1),t("td",S,"护士姓名："+s(o(e).nurseName||"-"),1),t("td",I,"日志日期："+s(o(e).logDate||"-"),1)]),t("tr",null,[t("td",z,[t("div",B,[T,c(),t("pre",E,s(o(e).workContent||"-"),1)])])]),t("tr",null,[t("td",P,[t("div",R,[U,c(),t("pre",$,s(o(e).workPlan||"-"),1)])])]),t("tr",null,[t("td",j,[t("div",q,[A,c(),t("pre",F,s(o(e).workSuggestion||"-"),1)])])])])])]),_:1},8,["modelValue"])])}}},L=v(H,[["__scopeId","data-v-f20f71a7"]]);export{L as default};
