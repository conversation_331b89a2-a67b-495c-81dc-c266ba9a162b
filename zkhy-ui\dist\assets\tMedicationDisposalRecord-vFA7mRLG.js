import{X as e}from"./index-B0qHf98Y.js";function d(o){return e({url:"/medication/disposalRecord/list",method:"get",params:o})}function t(o){return e({url:"/medication/disposalRecord/"+o,method:"get"})}function a(o){return e({url:"/medication/disposalRecord",method:"post",data:o})}function r(o){return e({url:"/medication/disposalRecord",method:"put",data:o})}function i(o){return e({url:"/medication/disposalRecord/"+o,method:"delete"})}function c(o){return e({url:"/medication/disposalRecord/save",method:"post",data:o})}export{a,i as d,t as g,d as l,c as s,r as u};
