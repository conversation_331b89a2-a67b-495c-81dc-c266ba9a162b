import{_ as pl,B as ml,d as cl,C as vl,N as yl,r as c,F as fl,P as Be,e as h,I as gl,c as E,o as p,f as e,h as l,i as d,Q as pe,l as t,k as G,n as s,K as C,L as S,j as M,t as x,m as _l,D as z,Y as $,J as F,O as ze,v as bl,x as Dl}from"./index-B0qHf98Y.js";import{u as hl,a as Ml,g as Il,d as Vl,l as Ze,b as El,c as xl,e as wl}from"./telderHealthProfile-BPOGoL2M.js";import{i as J,L as Tl}from"./index-a8qYZQmS.js";const f=K=>(bl("data-v-be7261ff"),K=K(),Dl(),K),Nl={class:"cardDetailTop"},Cl={key:0},Sl=f(()=>d("div",{style:{width:"100%",height:"20px"}},null,-1)),zl=f(()=>d("div",{class:"dashboard"},[d("div",{class:"card"},[d("div",{class:"card-header"},[d("img",{alt:"血压",src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiM4QTJCRTIiLz48L3N2Zz4="}),d("span",null,"血压监测")]),d("div",{class:"value-display"},[s(" 128 "),d("span",{class:"unit"},"mmHg"),s(" / 89 "),d("span",{class:"unit"},"mmHg")]),d("div",{id:"chart1",class:"chart-container"})]),d("div",{class:"card"},[d("div",{class:"card-header"},[d("img",{alt:"心率",src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDIxLjM1TDEwLjU1IDIwLjAzQzUuNCAxNS4zNiAyIDEyLjI3IDIgOC41QzIgNS40MSA0LjQyIDMgNy41IDNDOS4yNCAzIDEwLjkxIDMuODEgMTIgNS4wO0MxMy4wOSAzLjgxIDE0Ljc2IDMgMTYuNSAzQzE5LjU4IDMgMjIgNS40MSAyMiA4LjVDMjIgMTIuMjcgMTguNiAxNS4zNiAxMy40NSAyMC4wM0wxMiAyMS4zNVoiIGZpbGw9IiNGRjY5QjQiLz48L3N2Zz4="}),d("span",null,"心率监测")]),d("div",{class:"value-display"},[s(" 72 "),d("span",{class:"unit"},"次/分")]),d("div",{id:"chart2",class:"chart-container"})]),d("div",{class:"card"},[d("div",{class:"card-header"},[d("img",{alt:"睡眠",src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIzIDEySDE5VjVDMTkgNC40NCAxOC41NiA0IDE4IDRIMTZDMTUuNDQgNCAxNSA0LjQ0IDE1IDVWMTJIMTNWNUMxMyA0LjQ0IDEyLjU2IDQgMTIgNEgxMEM5LjQ0IDQgOSA0LjQ0IDkgNVYxMkg1QzQuNDQgMTIgNCAxMi40NCA0IDEzVjE1QzQgMTUuNTYgNC40NCAxNiA1IDE2SDlWMTlDOSAxOS41NiA5LjQ0IDIwIDEwIDIwSDEyQzEyLjU2IDIwIDEzIDE5LjU2IDEzIDE5VjE2SDE1VjE5QzE1IDE5LjU2IDE1LjQ0IDIwIDE2IDIwSDE4QzE4LjU2IDIwIDE5IDE5LjU2IDE5IDE5VjE2SDIzQzIzLjU2IDE2IDI0IDE1LjU2IDI0IDE1VjEzQzI0IDEyLjQ0IDIzLjU2IDEyIDIzIDEyWiIgZmlsbD0iIzAwQjNENyIvPjwvc3ZnPg=="}),d("span",null,"睡眠监测")]),d("div",{class:"value-display"},[s(" 7.5 "),d("span",{class:"unit"},"小时")]),d("div",{id:"chart3",class:"chart-container"})]),d("div",{class:"card"},[d("div",{class:"card-header"},[d("img",{alt:"血氧",src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyLDMgQzE2Ljk3LDMgMjEsNi41OCAyMSwxMSBDMjEsMTUuNDIgMTYuOTcsMTkgMTIsMTkgQzcuMDMsMTkgMywxNS40MiAzLDExIEMzLDYuNTggNy4wMywzIDEyLDMgTTEyLDUgQzguMTQsNSA1LDcuNzEgNSwxMSBDNSwxNC4yOSA4LjE0LDE3IDEyLDE3IEMxNS44NiwxNyAxOSwxNC4yOSAxOSwxMSBDMTksNy43MSAxNS44Niw1IDEyLDUgTTEyLDcgQzE0LjI5LDcgMTYsOC4zNCAxNiwxMCBDMTYsMTEuNjYgMTQuMjksMTMgMTIsMTMgQzkuNzEsMTMgOCwxMS42NiA4LDEwIEM4LDguMzQgOS43MSw3IDEyLDcgTTEyLDkgQzExLjQ1LDkgMTEsOS40NSAxMSwxMCBDMTEsMTAuNTUgMTEuNDUsMTEgMTIsMTEgQzEyLjU1LDExIDEzLDEwLjU1IDEzLDEwIEMxMyw5LjQ1IDEyLjU1LDkgMTIsOSBaIiBmaWxsPSIjRkY0NDQ0Ii8+PC9zdmc+"}),d("span",null,"血氧监测")]),d("div",{class:"value-display"},[s(" 98 "),d("span",{class:"unit"},"%")]),d("div",{id:"chart4",class:"chart-container"})]),d("div",{class:"card"},[d("div",{class:"card-header"},[d("img",{alt:"体温",src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE5IDE0VjEwSDIxVjE0SDE5TTE3IDE0VjEwSDE1VjE0SDE3TTcgMTRWMTBIN1YxNEg3TTUgMTBWMTJIM1YxMEg1TTUgMTZWMTJIM1YxNkg1TTkgMTZWN0gxMVYxNkg5TTEzIDE2VjNIMTVWMTZIMTMiIGZpbGw9IiNGRjhCMDAiLz48L3N2Zz4="}),d("span",null,"体温监测")]),d("div",{class:"value-display"},[s(" 36.7 "),d("span",{class:"unit"},"°C")]),d("div",{id:"chart5",class:"chart-container"})]),d("div",{class:"card"},[d("div",{class:"card-header"},[d("img",{alt:"呼吸",src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xIDE4aDJ2LTJoLTJ2MnptMi4wNy0xMy4yNGMtLjI2LS4xOS0uNTktLjE5LS44MSAwbC0xLjA3Ljc1Yy0uMzMuMjMtLjQzLjY2LS4yNSAxLjAxLjE5LjM0LjU2LjUxLjkxLjQzLjM1LS4wOC42My0uMzEuNzItLjY2LjA5LS4zNSAwLS43Mi0uMjUtLjk3bC0uNzUtLjUzVjEyaDJWOC43NnoiIGZpbGw9IiMwMEI1RTMiLz48L3N2Zz4="}),d("span",null,"呼吸频率")]),d("div",{class:"value-display"},[s(" 16 "),d("span",{class:"unit"},"次/分")]),d("div",{id:"chart6",class:"chart-container"})])],-1)),kl={key:1},Ll=f(()=>d("span",{class:"subtitleCss"},"基本信息",-1)),jl=f(()=>d("div",{style:{height:"10px"}},null,-1)),Al=f(()=>d("span",{class:"subtitleCss"},"健康史",-1)),Ul={class:"historyDiv"},Hl={class:"cardDetailcenter"},Ql=f(()=>d("div",{class:"historyCss"},"既往病史",-1)),Pl={class:"timelineProcessBox"},Yl={style:{display:"flex","flex-direction":"column"}},Bl={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Zl=f(()=>d("span",{style:{"font-size":"0.8rem"}},"确诊时间",-1)),Ol={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Rl={class:"cardDetailcenter"},ql=f(()=>d("div",{class:"historyCss"},"手术史",-1)),Wl={class:"timelineProcessBox"},Gl={style:{display:"flex","flex-direction":"column"}},$l={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Fl={style:{margin:"10px 0","font-size":"12px",color:"#999"}},Jl={class:"cardDetailcenter"},Kl=f(()=>d("div",{class:"historyCss"},"长期用药史",-1)),Xl={class:"timelineProcessBox"},ea={style:{display:"flex","flex-direction":"column"}},la={style:{margin:"10px 0","font-size":"12px",color:"#999"}},aa={style:{margin:"10px 0","font-size":"12px",color:"#999"}},ta=f(()=>d("span",{class:"subtitleCss"},"其他信息",-1)),oa={class:"el-table tables"},da=f(()=>d("thead",{class:"el-table__header"},[d("tr",null,[d("th",{class:"border border-gray-300 p-2 bg-gray-100"},"序号"),d("th",{class:"border border-gray-300 p-2 bg-gray-100"},"能力"),d("th",{class:"border border-gray-300 p-2 bg-gray-100"},"操作")])],-1)),sa=f(()=>d("td",{class:"border border-gray-300 p-2"},"1",-1)),na=f(()=>d("td",{class:"border border-gray-300 p-2"},"进食",-1)),ra={class:"border border-gray-300 p-2"},ua=f(()=>d("td",{class:"border border-gray-300 p-2"},"2",-1)),ia=f(()=>d("td",{class:"border border-gray-300 p-2"},"洗澡",-1)),pa={class:"border border-gray-300 p-2"},ma=f(()=>d("td",{class:"border border-gray-300 p-2"},"3",-1)),ca=f(()=>d("td",{class:"border border-gray-300 p-2"},"修饰",-1)),va={class:"border border-gray-300 p-2"},ya=f(()=>d("td",{class:"border border-gray-300 p-2"},"4",-1)),fa=f(()=>d("td",{class:"border border-gray-300 p-2"},"穿衣",-1)),ga={class:"border border-gray-300 p-2"},_a=f(()=>d("td",{class:"border border-gray-300 p-2"},"5",-1)),ba=f(()=>d("td",{class:"border border-gray-300 p-2"},"如厕，排泄",-1)),Da={class:"border border-gray-300 p-2"},ha=f(()=>d("td",{class:"border border-gray-300 p-2"},"6",-1)),Ma=f(()=>d("td",{class:"border border-gray-300 p-2"},"移动",-1)),Ia={class:"border border-gray-300 p-2"},Va=f(()=>d("td",{class:"border border-gray-300 p-2"},"7",-1)),Ea=f(()=>d("td",{class:"border border-gray-300 p-2"},"认知能力",-1)),xa={class:"border border-gray-300 p-2"},wa=f(()=>d("td",{class:"border border-gray-300 p-2"},"8",-1)),Ta=f(()=>d("td",{class:"border border-gray-300 p-2"},"情绪能力",-1)),Na={class:"border border-gray-300 p-2"},Ca=f(()=>d("td",{class:"border border-gray-300 p-2"},"9",-1)),Sa=f(()=>d("td",{class:"border border-gray-300 p-2"},"视觉能力",-1)),za={class:"border border-gray-300 p-2"},ka=f(()=>d("td",{class:"border border-gray-300 p-2"},"10",-1)),La=f(()=>d("td",{class:"border border-gray-300 p-2"},"听力",-1)),ja={class:"border border-gray-300 p-2"},Aa={class:"dialog-footer",style:{"margin-left":"90%","margin-top":"10px"}},Ua={class:"Addtitle"},Ha={class:"dialog-footer"},Qa={style:{flex:"auto"}},Pa={class:"Addtitle"},Ya={class:"dialog-footer"},Ba={style:{flex:"auto"}},Za={class:"Addtitle"},Oa={class:"dialog-footer"},Ra={style:{flex:"auto"}},qa=ml({name:"healthRecords"}),Wa=Object.assign(qa,{props:{elderId:{type:String,default:null},isShow:{type:Boolean,default:!1}},setup(K){const{proxy:k}=cl(),{rh_type:Oe,elderly_blood_type:Re,sleep_quality:qe,smoking_frequency:We,drinking_frequency:Ge,sports_frequency:$e,dietary_preferences:Fe,medical_history_status:Ga}=k.useDict("rh_type","elderly_blood_type","sleep_quality","smoking_frequency","drinking_frequency","sports_frequency","dietary_preferences","medical_history_status"),Je=vl({formbase:{},queryform:{},formevent:{},queryParams:{pageSize:10,pageNum:1},queryParamslist:{pageSize:1e4,pageNum:1},queryParamsElderHealth:{},formeventrules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}]},queryrules:{},rules:{}}),b=K,{formbase:v,queryform:ve,formevent:n,queryParams:I,queryParamslist:ye,queryParamsElderHealth:fe,formeventrules:ge,queryrules:Ke,rules:Xe}=yl(Je),Z=c("01"),O=c([]),L=c({open1:!1,open2:!1,open3:!1}),R=c(""),ke=c([]),Le=c([]),je=c([]),X=c(!0);c(!0);const Ae=c([]);c(!0),c(!0);const U=c(0),V=c("01"),T=c(""),me=c("40%");c("");const q=c(""),H=c([]),_e=c(!1),Ue=c(),ee=c(0),le=c(0),ae=c(0),te=c(0),oe=c(0),de=c(0),se=c(0),ne=c(0),re=c(0),ue=c(0);c("");const Q={1:"既往病史",2:"手术史",3:"长期用药史"};function el(){ll(),al(),tl(),ol(),dl(),sl()}function ll(){var r=document.getElementById("chart1"),o=J(r),i;i={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},legend:{data:["收缩压","舒张压"]},series:[{data:[55,68,95,87,78,65],type:"line",smooth:!0,lineStyle:{color:"rgb(140, 47, 226)"}},{data:[65,78,65,77,98,67],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 169, 11)"}}]},i&&o.setOption(i)}function al(){var r=document.getElementById("chart2"),o=J(r),i;i={xAxis:{type:"category",boundaryGap:!1,data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},legend:{data:["收缩压","舒张压"]},series:[{data:[55,68,95,87,78,65],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 102, 108)"},areaStyle:{color:new Tl(0,0,0,1,[{offset:0,color:"rgba(250,188,228,0.8)"}])}}]},i&&o.setOption(i)}function tl(){var r=document.getElementById("chart3"),o=J(r),i;i={tooltip:{trigger:"item"},legend:{top:"5%",left:"center"},series:[{name:"Access From",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[{value:1048,name:"深睡眠"},{value:735,name:"浅睡眠"},{value:580,name:"清醒"}]}]},i&&o.setOption(i)}function ol(){var r=document.getElementById("chart4"),o=J(r),i;i={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[55,68,95,87,78,65],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 74, 74)"}}]},i&&o.setOption(i)}function dl(){var r=document.getElementById("chart5"),o=J(r),i;i={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[36.5,36.7,37.2,37.5,36.9,36.5],type:"line",smooth:!0,lineStyle:{color:"rgb(225, 152, 0)"}}]},i&&o.setOption(i)}function sl(){var r=document.getElementById("chart6"),o=J(r),i;i={xAxis:{type:"category",data:["00:00","4:00","8:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[14,16,19,23,17,21],type:"line",smooth:!0,lineStyle:{color:"rgb(57, 196, 234)"}}]},i&&o.setOption(i)}function He(r){r=="01"?(Z.value="01",initTargetChart()):r=="02"&&(Z.value="02",Qe(),Pe())}function Qe(){ye.value.elderId=b.elderId,console.log(ye.value,"queryParamslist"),Ze(ye.value).then(r=>{var o,i,N;ke.value=(o=r.rows)==null?void 0:o.filter(m=>m.recordType==Q[1]).slice(0,3),Le.value=(i=r.rows)==null?void 0:i.filter(m=>m.recordType==Q[2]).slice(0,3),je.value=(N=r.rows)==null?void 0:N.filter(m=>m.recordType==Q[3]).slice(0,3)})}function Pe(){fe.value.elderId=b.elderId,console.log(fe.value,"queryParamsElderHealth"),wl(fe.value).then(r=>{var i,N,m;if(!r.rows||r.rows.length==0){v.value={},H.value=[];return}v.value=r.rows[0];var o=(i=r.rows[0])==null?void 0:i.chronicDiseases.split(",");H.value=o,((N=r.rows[0])==null?void 0:N.elderAbilities.length)>0&&JSON.parse((m=r.rows[0])==null?void 0:m.elderAbilities).forEach(y=>{y.type=="进食"?ee.value=y.value:y.type=="洗澡"?le.value=y.value:y.type=="修饰"?ae.value=y.value:y.type=="穿衣"?te.value=y.value:y.type=="如厕，排泄"?oe.value=y.value:y.type=="移动"?de.value=y.value:y.type=="认知能力"?se.value=y.value:y.type=="情绪能力"?ne.value=y.value:y.type=="视觉能力"?re.value=y.value:y.type=="听力"&&(ue.value=y.value),console.log(y,"item")})}),Qe()}const nl=r=>{H.value.splice(H.value.indexOf(r),1)},rl=()=>{_e.value=!0,Be(()=>{Ue.value.inputs.focus()})},Ye=()=>{q.value&&H.value.push(q.value),_e.value=!1,q.value=""};function be(){V.value="01",ie()}function ie(){n.value={id:null,elderId:null,eventDate:null,name:null,details:null,location:null,medicationStatus:null,remark:null,createTime:null,updateTime:null,createBy:null,updateBy:null},k.resetForm("elderHealthEventRef"+T.value)}function De(r){X.value=!0,V.value="01",T.value=r,R.value=Q[T.value],me.value="80%",L.value["open"+T.value]=!0,P()}function P(){O.value=[],I.value.recordType=Q[T.value],I.value.elderId=b.elderId,console.log("openDrawGetDataList query params: ",Q[T.value],I.value),Ze(I.value).then(r=>{O.value=r.rows,U.value=r.total,X.value=!1})}function he(){V.value="02",R.value="新增"}function Me(){let r="elderHealthEventRef"+T.value;console.log(r,"validate formRef"),k.$refs[r].validate(o=>{o&&(n.value.elderId=b.elderId,n.value.recordType=Q[T.value],n.value.id!=null?El(n.value).then(i=>{k.$modal.msgSuccess("修改成功"),V.value="01",P(),ie()}):xl(n.value).then(i=>{k.$modal.msgSuccess("新增成功"),P(),ie()}))})}fl(()=>{Be(()=>{el()})});function ul(){k.$refs.telderinfoRef.validate(r=>{r&&(v.value.elderId=b.elderId,v.value.chronicDiseases=H.value.join(","),v.value.elderAbilities=JSON.stringify([{type:"进食",value:ee.value},{type:"洗澡",value:le.value},{type:"修饰",value:ae.value},{type:"穿衣",value:te.value},{type:"如厕，排泄",value:oe.value},{type:"移动",value:de.value},{type:"认知能力",value:se.value},{type:"情绪能力",value:ne.value},{type:"视觉能力",value:re.value},{type:"听力",value:ue.value}]),v.value.id!=null?hl(v.value).then(o=>{k.$modal.msgSuccess("修改成功")}):Ml(v.value).then(o=>{k.$modal.msgSuccess("新增成功")}))})}function Ie(r){ie(),V.value="02",R.value="修改";const o=r.id||Ae.value;Il(o).then(i=>{n.value=i.data})}function Ve(r){const o=r.id||Ae.value;k.$modal.confirm('是否确认删除老人健康事件记录编号为"'+o+'"的数据项？').then(function(){return Vl(o)}).then(()=>{P(),k.$modal.msgSuccess("删除成功")}).catch(()=>{})}function W(){L.value.open1=L.value.open2=L.value.open3=!1,ie(),O.value=[],I.value={},Pe()}return(r,o)=>{const i=h("el-date-picker"),N=h("el-form"),m=h("el-col"),y=h("el-row"),Ee=h("el-divider"),D=h("el-input"),g=h("el-form-item"),Y=h("el-option"),B=h("el-select"),il=h("el-tag"),_=h("el-button"),xe=h("el-timeline-item"),we=h("el-timeline"),u=h("el-radio"),j=h("el-radio-group"),ce=h("el-card"),A=h("el-table-column"),Te=h("el-table"),Ne=h("pagination"),Ce=h("el-drawer"),Se=gl("loading");return p(),E("div",null,[e(ce,{shadow:"never"},{default:l(()=>[d("div",Nl,[d("div",{class:pe([t(Z)=="01"?"cardDetailTopDivSelect":"","cardDetailTopDiv"]),onClick:o[0]||(o[0]=a=>He("01"))}," 健康信息 ",2),d("div",{class:pe([t(Z)=="02"?"cardDetailTopDivSelect":"","cardDetailTopDiv"]),onClick:o[1]||(o[1]=a=>He("02"))}," 健康档案 ",2)]),e(y,null,{default:l(()=>[e(m,{span:24},{default:l(()=>[t(Z)=="01"?(p(),E("div",Cl,[e(y,{gutter:15},{default:l(()=>[e(m,{span:24},{default:l(()=>[e(N,{ref:"telderinfoRef",model:t(ve),rules:t(Ke),"label-width":"120px"},{default:l(()=>[e(i,{modelValue:t(ve).dateRange,"onUpdate:modelValue":o[2]||(o[2]=a=>t(ve).dateRange=a),"date-format":"YYYY-MM-DD HH:mm","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm","range-separator":"至","start-placeholder":"开始时间","time-format":"hh:mm",type:"datetimerange"},null,8,["modelValue"])]),_:1},8,["model","rules"])]),_:1})]),_:1}),Sl,zl])):G("",!0),t(Z)=="02"?(p(),E("div",kl,[e(N,{ref:"telderinfoRef",model:t(v),rules:t(Xe),"label-width":"120px"},{default:l(()=>[e(Ee,{"content-position":"left"},{default:l(()=>[Ll]),_:1}),jl,e(y,{gutter:15},{default:l(()=>[e(m,{span:8},{default:l(()=>[e(g,{label:"老人身高",prop:"heightCm",size:"large"},{default:l(()=>[e(D,{modelValue:t(v).heightCm,"onUpdate:modelValue":o[3]||(o[3]=a=>t(v).heightCm=a),placeholder:"请输入身高(cm)",disabled:b.isShow},{append:l(()=>[s("cm")]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"老人体重",prop:"weightKg",size:"large"},{default:l(()=>[e(D,{modelValue:t(v).weightKg,"onUpdate:modelValue":o[4]||(o[4]=a=>t(v).weightKg=a),placeholder:"请输入体重(kg)",disabled:b.isShow},{append:l(()=>[s("kg")]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"RH因子",prop:"rhFactor",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).rhFactor,"onUpdate:modelValue":o[5]||(o[5]=a=>t(v).rhFactor=a),placeholder:"请选择RH因子",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t(Oe),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"血型",prop:"bloodType",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).bloodType,"onUpdate:modelValue":o[6]||(o[6]=a=>t(v).bloodType=a),placeholder:"请选择血型",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t(Re),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"睡眠质量",prop:"sleepQuality",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).sleepQuality,"onUpdate:modelValue":o[7]||(o[7]=a=>t(v).sleepQuality=a),placeholder:"请选择睡眠质量",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t(qe),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"吸烟频率",prop:"smokingFrequency",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).smokingFrequency,"onUpdate:modelValue":o[8]||(o[8]=a=>t(v).smokingFrequency=a),placeholder:"请选择吸烟频率",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t(We),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"饮酒频率",prop:"drinkingFrequency",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).drinkingFrequency,"onUpdate:modelValue":o[9]||(o[9]=a=>t(v).drinkingFrequency=a),placeholder:"请选择饮酒频率",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t(Ge),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"运动频率",prop:"exerciseFrequency",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).exerciseFrequency,"onUpdate:modelValue":o[10]||(o[10]=a=>t(v).exerciseFrequency=a),placeholder:"请选择运动频率",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t($e),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"饮食偏好",prop:"dietaryPreference",size:"large"},{default:l(()=>[e(B,{modelValue:t(v).dietaryPreference,"onUpdate:modelValue":o[11]||(o[11]=a=>t(v).dietaryPreference=a),placeholder:"请选择饮食偏好",disabled:b.isShow},{default:l(()=>[(p(!0),E(C,null,S(t(Fe),a=>(p(),M(Y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:8},{default:l(()=>[e(g,{label:"慢性病史",prop:"chronicDiseases",size:"large"},{default:l(()=>[(p(!0),E(C,null,S(t(H),a=>(p(),M(il,{key:a,"disable-transitions":!1,closable:"",style:{"margin-right":"4px"},onClose:w=>nl(a)},{default:l(()=>[s(x(a),1)]),_:2},1032,["onClose"]))),128)),t(_e)?(p(),M(D,{key:0,ref_key:"InputRef",ref:Ue,modelValue:t(q),"onUpdate:modelValue":o[12]||(o[12]=a=>z(q)?q.value=a:null),class:"w-20",size:"larger",onBlur:Ye,onKeyup:_l(Ye,["enter"])},null,8,["modelValue"])):(p(),M(_,{key:1,class:"button-new-tag",size:"large",disabled:b.isShow,onClick:rl},{default:l(()=>[s(" + 添加 ")]),_:1},8,["disabled"]))]),_:1})]),_:1}),e(Ee,{"content-position":"left"},{default:l(()=>[Al]),_:1}),d("div",Ul,[e(m,{span:24,style:{height:"130px"}},{default:l(()=>[d("div",Hl,[Ql,d("div",null,[e(_,{size:"small",type:"primary",onClick:o[13]||(o[13]=a=>De("1")),disabled:b.isShow},{default:l(()=>[s("既往病史管理 ")]),_:1},8,["disabled"])])]),d("div",Pl,[e(we,{class:"timeline"},{default:l(()=>[(p(!0),E(C,null,S(t(ke),(a,w)=>(p(),M(xe,{key:w,class:pe([a.done?"active":"inactive","lineitem"])},{default:l(()=>[d("span",Yl,[d("span",Bl,[s(x(t($)(a.eventDate,"{y}.{m}"))+" ",1),Zl]),d("span",Ol," 疾病名称："+x(a.name),1)])]),_:2},1032,["class"]))),128))]),_:1})])]),_:1}),e(m,{span:24},{default:l(()=>[d("div",Rl,[ql,d("div",null,[e(_,{size:"small",type:"primary",onClick:o[14]||(o[14]=a=>De("2")),disabled:b.isShow},{default:l(()=>[s("手术史管理 ")]),_:1},8,["disabled"])])]),d("div",Wl,[e(we,{class:"timeline"},{default:l(()=>[(p(!0),E(C,null,S(t(Le),(a,w)=>(p(),M(xe,{key:w,class:pe([a.done?"active":"inactive","lineitem"])},{default:l(()=>[d("span",Gl,[d("span",$l,x(t($)(a.eventDate,"{y}-{m}-{d}")),1),d("span",Fl," 手术名称："+x(a.name),1)])]),_:2},1032,["class"]))),128))]),_:1})])]),_:1}),e(m,{span:24},{default:l(()=>[d("div",Jl,[Kl,d("div",null,[e(_,{size:"small",type:"primary",onClick:o[15]||(o[15]=a=>De("3")),disabled:b.isShow},{default:l(()=>[s("长期用药史管理 ")]),_:1},8,["disabled"])])]),d("div",Xl,[e(we,{class:"timeline"},{default:l(()=>[(p(!0),E(C,null,S(t(je),(a,w)=>(p(),M(xe,{key:w,class:pe([a.done?"active":"inactive","lineitem"])},{default:l(()=>[d("span",ea,[d("span",la,x(t($)(a.eventDate,"{y}.{m}")),1),d("span",aa," 药品名称："+x(a.name),1)])]),_:2},1032,["class"]))),128))]),_:1})])]),_:1})]),e(Ee,{"content-position":"left"},{default:l(()=>[ta]),_:1}),e(m,{span:24,style:{"margin-top":"12px"}},{default:l(()=>[e(g,{label:"过敏史",prop:"allergyHistory",size:"large"},{default:l(()=>[e(D,{modelValue:t(v).allergyHistory,"onUpdate:modelValue":o[16]||(o[16]=a=>t(v).allergyHistory=a),placeholder:"请输入内容",rows:"4",type:"textarea",disabled:b.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(g,{label:"家住病史",prop:"familyHistory",size:"large"},{default:l(()=>[e(D,{modelValue:t(v).familyHistory,"onUpdate:modelValue":o[17]||(o[17]=a=>t(v).familyHistory=a),placeholder:"请输入内容",rows:"4",type:"textarea",disabled:b.isShow},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(g,{label:"能力评估",prop:"allergyHistory",size:"small",stripe:""})]),_:1})]),_:1})]),_:1},8,["model","rules"]),d("table",oa,[da,d("tbody",null,[d("tr",null,[sa,na,d("td",ra,[e(j,{modelValue:t(ee),"onUpdate:modelValue":o[18]||(o[18]=a=>z(ee)?ee.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[ua,ia,d("td",pa,[e(j,{modelValue:t(le),"onUpdate:modelValue":o[19]||(o[19]=a=>z(le)?le.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[ma,ca,d("td",va,[e(j,{modelValue:t(ae),"onUpdate:modelValue":o[20]||(o[20]=a=>z(ae)?ae.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[ya,fa,d("td",ga,[e(j,{modelValue:t(te),"onUpdate:modelValue":o[21]||(o[21]=a=>z(te)?te.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[_a,ba,d("td",Da,[e(j,{modelValue:t(oe),"onUpdate:modelValue":o[22]||(o[22]=a=>z(oe)?oe.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[ha,Ma,d("td",Ia,[e(j,{modelValue:t(de),"onUpdate:modelValue":o[23]||(o[23]=a=>z(de)?de.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[Va,Ea,d("td",xa,[e(j,{modelValue:t(se),"onUpdate:modelValue":o[24]||(o[24]=a=>z(se)?se.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[wa,Ta,d("td",Na,[e(j,{modelValue:t(ne),"onUpdate:modelValue":o[25]||(o[25]=a=>z(ne)?ne.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[Ca,Sa,d("td",za,[e(j,{modelValue:t(re),"onUpdate:modelValue":o[26]||(o[26]=a=>z(re)?re.value=a:null)},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue"])])]),d("tr",null,[ka,La,d("td",ja,[e(j,{modelValue:t(ue),"onUpdate:modelValue":o[27]||(o[27]=a=>z(ue)?ue.value=a:null),disabled:b.isShow},{default:l(()=>[e(u,{value:0},{default:l(()=>[s("正常")]),_:1}),e(u,{value:1},{default:l(()=>[s("轻度依赖")]),_:1}),e(u,{value:2},{default:l(()=>[s("重度依赖")]),_:1}),e(u,{value:3},{default:l(()=>[s("完全依赖")]),_:1})]),_:1},8,["modelValue","disabled"])])])])])])):G("",!0)]),_:1})]),_:1}),d("div",Aa,[b.isShow?G("",!0):(p(),M(_,{key:0,size:"large",type:"primary",onClick:ul},{default:l(()=>[s("确 定 ")]),_:1}))])]),_:1}),e(Ce,{modelValue:t(L).open1,"onUpdate:modelValue":o[36]||(o[36]=a=>t(L).open1=a),"before-close":W,size:t(me),direction:"rtl",title:"既往病史"},{footer:l(()=>[d("div",Qa,[e(_,{onClick:W},{default:l(()=>[s("关闭")]),_:1})])]),default:l(()=>[e(y,{gutter:15},{default:l(()=>[e(m,{span:t(V)=="01"?24:18},{default:l(()=>[e(y,{style:{display:"flex","justify-content":"flex-end","margin-bottom":"1rem"}},{default:l(()=>[e(_,{icon:"Plus",plain:"",type:"primary",onClick:he},{default:l(()=>[s("新增")]),_:1})]),_:1}),F((p(),M(Te,{data:t(O),border:"",stripe:""},{default:l(()=>[e(A,{align:"center",label:"确诊时间","min-width":"120",prop:"eventDate"},{default:l(a=>[d("span",null,x(t($)(a.row.eventDate,"{y}-{m}-{d}")),1)]),_:1}),e(A,{align:"center",label:"疾病名称","min-width":"200",prop:"name"}),e(A,{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"200"},{default:l(a=>[e(_,{icon:"Edit",link:"",type:"primary",onClick:w=>Ie(a.row)},{default:l(()=>[s("修改 ")]),_:2},1032,["onClick"]),e(_,{icon:"Delete",link:"",type:"primary",onClick:w=>Ve(a.row)},{default:l(()=>[s("删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Se,t(X)]]),F(e(Ne,{limit:t(I).pageSize,"onUpdate:limit":o[28]||(o[28]=a=>t(I).pageSize=a),page:t(I).pageNum,"onUpdate:page":o[29]||(o[29]=a=>t(I).pageNum=a),total:t(U),onPagination:P},null,8,["limit","page","total"]),[[ze,t(U)>0]])]),_:1},8,["span"]),t(V)=="02"&&t(T)==1?(p(),M(m,{key:0,span:t(V)=="01"?0:6},{default:l(()=>[e(ce,{shadow:"hover"},{default:l(()=>[d("div",Ua,x(t(R)),1),e(N,{ref:"elderHealthEventRef1",model:t(n),rules:t(ge),"label-width":"80px"},{default:l(()=>[e(D,{modelValue:t(n).id,"onUpdate:modelValue":o[30]||(o[30]=a=>t(n).id=a),type:"hidden"},null,8,["modelValue"]),e(g,{label:"确诊日期",prop:"eventDate"},{default:l(()=>[e(i,{modelValue:t(n).eventDate,"onUpdate:modelValue":o[31]||(o[31]=a=>t(n).eventDate=a),clearable:"",placeholder:"请选择确诊日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(g,{label:"疾病名称",prop:"name"},{default:l(()=>[e(D,{modelValue:t(n).name,"onUpdate:modelValue":o[32]||(o[32]=a=>t(n).name=a),placeholder:"请输入疾病名称"},null,8,["modelValue"])]),_:1}),e(g,{label:"治疗结果",prop:"details"},{default:l(()=>[e(D,{modelValue:t(n).details,"onUpdate:modelValue":o[33]||(o[33]=a=>t(n).details=a),placeholder:"请输入内容治疗结果",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1}),e(g,{label:"就诊地点",prop:"location"},{default:l(()=>[e(D,{modelValue:t(n).location,"onUpdate:modelValue":o[34]||(o[34]=a=>t(n).location=a),placeholder:"请输入就诊地点"},null,8,["modelValue"])]),_:1}),e(g,{label:"备注",prop:"remark"},{default:l(()=>[e(D,{modelValue:t(n).remark,"onUpdate:modelValue":o[35]||(o[35]=a=>t(n).remark=a),placeholder:"请输入备注内容",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),d("div",Ha,[e(_,{type:"primary",onClick:Me},{default:l(()=>[s("确 定")]),_:1}),e(_,{onClick:be},{default:l(()=>[s("取 消")]),_:1})])]),_:1})]),_:1},8,["span"])):G("",!0)]),_:1})]),_:1},8,["modelValue","size"]),e(Ce,{modelValue:t(L).open2,"onUpdate:modelValue":o[45]||(o[45]=a=>t(L).open2=a),"before-close":W,size:t(me),direction:"rtl",title:"手术史"},{footer:l(()=>[d("div",Ba,[e(_,{onClick:W},{default:l(()=>[s("关闭")]),_:1})])]),default:l(()=>[e(y,{gutter:15},{default:l(()=>[e(m,{span:t(V)=="01"?24:18},{default:l(()=>[e(y,{style:{display:"flex","justify-content":"flex-end","margin-bottom":"1rem"}},{default:l(()=>[e(_,{icon:"Plus",plain:"",type:"primary",onClick:he},{default:l(()=>[s("新增")]),_:1})]),_:1}),F((p(),M(Te,{data:t(O),border:"",stripe:""},{default:l(()=>[e(A,{align:"center",label:"手术日期","min-width":"120",prop:"eventDate"},{default:l(a=>[d("span",null,x(t($)(a.row.eventDate,"{y}-{m}-{d}")),1)]),_:1}),e(A,{align:"center",label:"手术名称","min-width":"200",prop:"name"}),e(A,{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"200"},{default:l(a=>[e(_,{icon:"Edit",link:"",type:"primary",onClick:w=>Ie(a.row)},{default:l(()=>[s("修改 ")]),_:2},1032,["onClick"]),e(_,{icon:"Delete",link:"",type:"primary",onClick:w=>Ve(a.row)},{default:l(()=>[s("删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Se,t(X)]]),F(e(Ne,{limit:t(I).pageSize,"onUpdate:limit":o[37]||(o[37]=a=>t(I).pageSize=a),page:t(I).pageNum,"onUpdate:page":o[38]||(o[38]=a=>t(I).pageNum=a),total:t(U),onPagination:P},null,8,["limit","page","total"]),[[ze,t(U)>0]])]),_:1},8,["span"]),t(V)=="02"&&t(T)==2?(p(),M(m,{key:0,span:t(V)=="01"?0:6},{default:l(()=>[e(ce,{shadow:"hover"},{default:l(()=>[d("div",Pa,x(t(R)),1),e(N,{ref:"elderHealthEventRef2",model:t(n),rules:t(ge),"label-width":"80px"},{default:l(()=>[e(D,{modelValue:t(n).id,"onUpdate:modelValue":o[39]||(o[39]=a=>t(n).id=a),type:"hidden"},null,8,["modelValue"]),e(g,{label:"手术日期",prop:"eventDate"},{default:l(()=>[e(i,{modelValue:t(n).eventDate,"onUpdate:modelValue":o[40]||(o[40]=a=>t(n).eventDate=a),clearable:"",placeholder:"请选择手术日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(g,{label:"手术名称",prop:"name"},{default:l(()=>[e(D,{modelValue:t(n).name,"onUpdate:modelValue":o[41]||(o[41]=a=>t(n).name=a),placeholder:"请输入手术名称"},null,8,["modelValue"])]),_:1}),e(g,{label:"手术描述",prop:"details"},{default:l(()=>[e(D,{modelValue:t(n).details,"onUpdate:modelValue":o[42]||(o[42]=a=>t(n).details=a),placeholder:"请输入手术描述",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1}),e(g,{label:"手术医院",prop:"location"},{default:l(()=>[e(D,{modelValue:t(n).location,"onUpdate:modelValue":o[43]||(o[43]=a=>t(n).location=a),placeholder:"请输入手术医院"},null,8,["modelValue"])]),_:1}),e(g,{label:"备注",prop:"remark"},{default:l(()=>[e(D,{modelValue:t(n).remark,"onUpdate:modelValue":o[44]||(o[44]=a=>t(n).remark=a),placeholder:"请输入备注内容",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),d("div",Ya,[e(_,{type:"primary",onClick:Me},{default:l(()=>[s("确 定")]),_:1}),e(_,{onClick:be},{default:l(()=>[s("取 消")]),_:1})])]),_:1})]),_:1},8,["span"])):G("",!0)]),_:1})]),_:1},8,["modelValue","size"]),e(Ce,{modelValue:t(L).open3,"onUpdate:modelValue":o[54]||(o[54]=a=>t(L).open3=a),"before-close":W,size:t(me),direction:"rtl",title:"长期用药史"},{footer:l(()=>[d("div",Ra,[e(_,{onClick:W},{default:l(()=>[s("关闭")]),_:1})])]),default:l(()=>[e(y,{gutter:15},{default:l(()=>[e(m,{span:t(V)=="01"?24:18},{default:l(()=>[e(y,{style:{display:"flex","justify-content":"flex-end","margin-bottom":"1rem"}},{default:l(()=>[e(_,{icon:"Plus",plain:"",type:"primary",onClick:he},{default:l(()=>[s("新增")]),_:1})]),_:1}),F((p(),M(Te,{data:t(O),border:"",stripe:""},{default:l(()=>[e(A,{align:"center",label:"用药日期","min-width":"120",prop:"eventDate"},{default:l(a=>[d("span",null,x(t($)(a.row.eventDate,"{y}-{m}-{d}")),1)]),_:1}),e(A,{align:"center",label:"药品名称","min-width":"200",prop:"name"}),e(A,{align:"center",label:"用药状态","min-width":"200",prop:"medicationStatus"}),e(A,{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"200"},{default:l(a=>[e(_,{icon:"Edit",link:"",type:"primary",onClick:w=>Ie(a.row)},{default:l(()=>[s("修改 ")]),_:2},1032,["onClick"]),e(_,{icon:"Delete",link:"",type:"primary",onClick:w=>Ve(a.row)},{default:l(()=>[s("删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Se,t(X)]]),F(e(Ne,{limit:t(I).pageSize,"onUpdate:limit":o[46]||(o[46]=a=>t(I).pageSize=a),page:t(I).pageNum,"onUpdate:page":o[47]||(o[47]=a=>t(I).pageNum=a),total:t(U),onPagination:P},null,8,["limit","page","total"]),[[ze,t(U)>0]])]),_:1},8,["span"]),t(V)=="02"&&t(T)==3?(p(),M(m,{key:0,span:t(V)=="01"?0:6},{default:l(()=>[e(ce,{shadow:"hover"},{default:l(()=>[d("div",Za,x(t(R)),1),e(N,{ref:"elderHealthEventRef3",model:t(n),rules:t(ge),"label-width":"80px"},{default:l(()=>[e(D,{modelValue:t(n).id,"onUpdate:modelValue":o[48]||(o[48]=a=>t(n).id=a),type:"hidden"},null,8,["modelValue"]),e(g,{label:"用药日期",prop:"eventDate"},{default:l(()=>[e(i,{modelValue:t(n).eventDate,"onUpdate:modelValue":o[49]||(o[49]=a=>t(n).eventDate=a),clearable:"",placeholder:"请选择用药日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(g,{label:"药品名称",prop:"name"},{default:l(()=>[e(D,{modelValue:t(n).name,"onUpdate:modelValue":o[50]||(o[50]=a=>t(n).name=a),placeholder:"请输入药品名称"},null,8,["modelValue"])]),_:1}),e(g,{label:"详情",prop:"details"},{default:l(()=>[e(D,{modelValue:t(n).details,"onUpdate:modelValue":o[51]||(o[51]=a=>t(n).details=a),placeholder:"请输入内容(剂量及用法/用药原因)",rows:"4",type:"textarea"},null,8,["modelValue"])]),_:1}),e(g,{label:"用药状态",prop:"medicationStatus"},{default:l(()=>[e(D,{modelValue:t(n).medicationStatus,"onUpdate:modelValue":o[52]||(o[52]=a=>t(n).medicationStatus=a),placeholder:"请输入用药状态(服用中/已停用)"},null,8,["modelValue"])]),_:1}),e(g,{label:"备注",prop:"remark"},{default:l(()=>[e(D,{modelValue:t(n).remark,"onUpdate:modelValue":o[53]||(o[53]=a=>t(n).remark=a),placeholder:"请输入内容",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),d("div",Oa,[e(_,{type:"primary",onClick:Me},{default:l(()=>[s("确 定")]),_:1}),e(_,{onClick:be},{default:l(()=>[s("取 消")]),_:1})])]),_:1})]),_:1},8,["span"])):G("",!0)]),_:1})]),_:1},8,["modelValue","size"])])}}}),Ka=pl(Wa,[["__scopeId","data-v-be7261ff"]]);export{Ka as default};
