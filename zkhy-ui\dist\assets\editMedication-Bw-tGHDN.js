import{u as H,a as P,b as te}from"./tWarehouseMedication-Ycu1QDaW.js";import{_ as ue,B as se,u as ne,a as ie,d as re,r as V,C as pe,N as me,e as _,c,k as I,f as e,h as a,i as p,t as z,o as m,j as y,l as d,K as g,L as h,n as M,v as be,x as fe}from"./index-B0qHf98Y.js";const k=O=>(be("data-v-83e19d91"),O=O(),fe(),O),_e={class:"app-container contentDiv"},ce={key:0},ve={class:"bottom_room_table"},Ve={class:"title_room"},ye=k(()=>p("h3",{class:"page-title"},"基本信息",-1)),ge={class:"hfyCodeCSS"},he={class:"bottom_room_table"},Ue={class:"title_room"},we=k(()=>p("h3",null,"包装信息",-1)),ke={class:"bottom_room_table"},Ce={class:"title_room"},xe=k(()=>p("h3",{class:"titleCss"},"库存信息",-1)),Ie={key:1},$e={class:"bottom_room_table"},Oe={class:"title_room"},Se=k(()=>p("h3",{class:"page-title"},"基本信息",-1)),De={class:"hfyCodeCSS"},Fe={class:"bottom_room_table"},Ne={class:"title_room"},Pe=k(()=>p("h3",null,"包装信息",-1)),Me={class:"bottom_room_table"},Ye={class:"title_room"},Be=k(()=>p("h3",{class:"titleCss"},"服用信息",-1)),Re={class:"bottom_room_table"},Te={class:"title_room"},We=k(()=>p("h3",{class:"titleCss"},"库存信息",-1)),Ee=se({name:"AddMedication"}),Qe=Object.assign(Ee,{setup(O){const v=ne(),U=ie(),{proxy:b}=re(),{medication_type:A,medication_dosage:K,is_otc:J,invoice_items:W,packing_unit:E,dosage_unit:qe,usage_type:Ge,goods_status:Q}=b.useDict("medication_type","medication_dosage","is_otc","invoice_items","packing_unit","dosage_unit","usage_type","goods_status");V([]);const S=V(!1);V(!0);const u=V(!0);V([]);const D=V(""),q=V(""),Y=V("");V("first"),V("");const $=V(""),X=pe({form:{status:0},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0},rules:{medicineName:[{required:!0,message:"请输入药品名称",trigger:"blur"}]},form2:{status:0},rules2:{medicineName:[{required:!0,message:"请输入药品名称",trigger:"blur"}]}}),{queryParams:Le,form:t,rules:Z,form2:n,rules2:ee}=me(X);function le(){$.value=null,v.params.type=="edit"?(Y.value="修改信息",u.value=!1,B()):v.params.type=="show"?(Y.value="查看信息",B(),u.value=!0):v.params.type=="copy"&&(Y.value="复制信息",B(),u.value=!1)}function B(){v.params.id&&te(v.params.id).then(f=>{console.log(f.data,"res"),f.data.goodsCategory=="物品"?(n.value=f.data,n.value.isOtc=f.data.isOtc.toString(),$.value="first",q.value=f.data.medicineCode):f.data.goodsCategory=="药品"&&(t.value=f.data,t.value.isOtc=f.data.isOtc.toString(),D.value=f.data.medicineCode,$.value="second")})}function G(){ae(),b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}function ae(){t.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},b.resetForm("addMedicationRef")}function de(){b.$refs.addMedicationRef.validate(f=>{f&&(t.value.category="药品",t.value.id!=null&&v.params.type=="edit"?H(t.value).then(o=>{b.$modal.msgSuccess("修改成功"),b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}):v.params.type=="add"?(t.value.medicineCode=D.value,P(t.value).then(o=>{b.$modal.msgSuccess("新增成功"),S.value=!1,b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")})):v.params.type=="copy"&&P(t.value).then(o=>{b.$modal.msgSuccess("复制添加成功"),S.value=!1,b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}))})}function oe(){b.$refs.addGoodsRef.validate(f=>{f&&(n.value.category="物品",n.value.id!=null&&v.params.type=="edit"?H(n.value).then(o=>{b.$modal.msgSuccess("修改成功"),b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}):v.params.type=="add"?(n.value.medicineCode=D.value,P(n.value).then(o=>{b.$modal.msgSuccess("新增成功"),S.value=!1,b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")})):v.params.type=="copy"&&P(n.value).then(o=>{b.$modal.msgSuccess("复制添加成功"),S.value=!1,b.$tab.closeOpenPage(),U.push("/warehouse/warehouse/wmsmedication")}))})}return le(),(f,o)=>{const r=_("el-input"),s=_("el-form-item"),i=_("el-col"),C=_("el-option"),x=_("el-select"),R=_("el-radio"),T=_("el-radio-group"),w=_("el-row"),F=_("el-input-number"),L=_("el-date-picker"),N=_("el-button"),j=_("el-form");return m(),c("div",_e,[$.value=="first"?(m(),c("div",ce,[e(j,{ref:"addGoodsRef",model:d(n),rules:d(ee),"label-width":"100px"},{default:a(()=>[p("div",ve,[p("div",Ve,[ye,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"物品编码",prop:"medicineCode"},{default:a(()=>[p("span",ge,z(q.value),1),I("",!0)]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"条形码",prop:"barcode"},{default:a(()=>[e(r,{modelValue:d(n).barcode,"onUpdate:modelValue":o[1]||(o[1]=l=>d(n).barcode=l),placeholder:"请输入条形码",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"物品名称",prop:"medicineName"},{default:a(()=>[e(r,{modelValue:d(n).medicineName,"onUpdate:modelValue":o[2]||(o[2]=l=>d(n).medicineName=l),placeholder:"请输入",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"生产厂家",prop:"manufacturer"},{default:a(()=>[e(r,{modelValue:d(n).manufacturer,"onUpdate:modelValue":o[3]||(o[3]=l=>d(n).manufacturer=l),placeholder:"请输入生产厂家",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"物品分类",prop:"category"},{default:a(()=>[e(r,{modelValue:d(n).category,"onUpdate:modelValue":o[4]||(o[4]=l=>d(n).category=l),placeholder:"请输入物品分类",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"物品规格",prop:"specification"},{default:a(()=>[e(r,{modelValue:d(n).specification,"onUpdate:modelValue":o[5]||(o[5]=l=>d(n).specification=l),placeholder:"请输入物品规格",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"发票项目",prop:"invoiceItem"},{default:a(()=>[e(x,{modelValue:d(n).invoiceItem,"onUpdate:modelValue":o[6]||(o[6]=l=>d(n).invoiceItem=l),placeholder:"请选择发票项目",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(W),l=>(m(),y(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"状态",prop:"manufacturer"},{default:a(()=>[e(T,{modelValue:d(n).status,"onUpdate:modelValue":o[7]||(o[7]=l=>d(n).status=l),placeholder:"请选择状态",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(Q),l=>(m(),y(R,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",he,[p("div",Ue,[we,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"包装单位",prop:"packageUnit"},{default:a(()=>[e(x,{modelValue:d(n).packageUnit,"onUpdate:modelValue":o[8]||(o[8]=l=>d(n).packageUnit=l),placeholder:"请选择包装单位",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(E),l=>(m(),y(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本系数",prop:"baseFactor"},{default:a(()=>[e(r,{modelValue:d(n).baseFactor,"onUpdate:modelValue":o[9]||(o[9]=l=>d(n).baseFactor=l),placeholder:"请输入基本系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本单位",prop:"baseUnit"},{default:a(()=>[e(r,{modelValue:d(n).baseUnit,"onUpdate:modelValue":o[10]||(o[10]=l=>d(n).baseUnit=l),placeholder:"请输入基本单位",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"剂量系数",prop:"dosageFactor"},{default:a(()=>[e(r,{modelValue:d(n).dosageFactor,"onUpdate:modelValue":o[11]||(o[11]=l=>d(n).dosageFactor=l),placeholder:"请输入剂量系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"采购价(元)",prop:"purchasePrice"},{default:a(()=>[e(r,{modelValue:d(n).purchasePrice,"onUpdate:modelValue":o[12]||(o[12]=l=>d(n).purchasePrice=l),placeholder:"请输入采购价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"零售价(元)",prop:"retailPrice"},{default:a(()=>[e(r,{modelValue:d(n).retailPrice,"onUpdate:modelValue":o[13]||(o[13]=l=>d(n).retailPrice=l),placeholder:"请输入零售价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",ke,[p("div",Ce,[xe,e(w,null,{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"库存上限",prop:"maxInventory"},{default:a(()=>[e(F,{modelValue:d(n).maxInventory,"onUpdate:modelValue":o[14]||(o[14]=l=>d(n).maxInventory=l),placeholder:"请输入库存上限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"库存下限",prop:"minInventory"},{default:a(()=>[e(F,{modelValue:d(n).minInventory,"onUpdate:modelValue":o[15]||(o[15]=l=>d(n).minInventory=l),placeholder:"请输入库存下限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"仓库",prop:"warehouse"},{default:a(()=>[e(r,{modelValue:d(n).warehouse,"onUpdate:modelValue":o[16]||(o[16]=l=>d(n).warehouse=l),placeholder:"请输入仓库",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"货位号",prop:"locationCode"},{default:a(()=>[e(r,{modelValue:d(n).locationCode,"onUpdate:modelValue":o[17]||(o[17]=l=>d(n).locationCode=l),placeholder:"请输入货位号",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"有效期预警",prop:"expiryWarningDays"},{default:a(()=>[e(L,{clearable:"",modelValue:d(n).expiryWarningDays,"onUpdate:modelValue":o[18]||(o[18]=l=>d(n).expiryWarningDays=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择有效期预警(天)",value:"YYYY-MM-DD",disabled:u.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:24},{default:a(()=>[e(s,{label:"备注",prop:"remark"},{default:a(()=>[e(r,{modelValue:d(n).remark,"onUpdate:modelValue":o[19]||(o[19]=l=>d(n).remark=l),type:"textarea",placeholder:"请输入内容",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),I("",!0)]),_:1})])]),e(s,{class:"footer_btn",style:{"margin-left":"80%"}},{default:a(()=>[e(N,{type:"primary",onClick:oe,disabled:u.value},{default:a(()=>[M("提 交")]),_:1},8,["disabled"]),e(N,{onClick:G},{default:a(()=>[M("取 消")]),_:1})]),_:1})]),_:1},8,["model","rules"])])):I("",!0),$.value=="second"?(m(),c("div",Ie,[e(j,{ref:"addMedicationRef",model:d(t),rules:d(Z),"label-width":"100px"},{default:a(()=>[p("div",$e,[p("div",Oe,[Se,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"药品编码",prop:"medicineCode"},{default:a(()=>[p("span",De,z(D.value),1),I("",!0)]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"条形码",prop:"barcode"},{default:a(()=>[e(r,{modelValue:d(t).barcode,"onUpdate:modelValue":o[22]||(o[22]=l=>d(t).barcode=l),placeholder:"请输入条形码",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品名称",prop:"medicineName"},{default:a(()=>[e(r,{modelValue:d(t).medicineName,"onUpdate:modelValue":o[23]||(o[23]=l=>d(t).medicineName=l),placeholder:"请输入",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"拼音码",prop:"pinyinCode"},{default:a(()=>[e(r,{modelValue:d(t).pinyinCode,"onUpdate:modelValue":o[24]||(o[24]=l=>d(t).pinyinCode=l),placeholder:"请输入拼音码",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品分类",prop:"category"},{default:a(()=>[e(x,{modelValue:d(t).category,"onUpdate:modelValue":o[25]||(o[25]=l=>d(t).category=l),placeholder:"请选择药品分类",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(A),l=>(m(),y(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品规格",prop:"specification"},{default:a(()=>[e(r,{modelValue:d(t).specification,"onUpdate:modelValue":o[26]||(o[26]=l=>d(t).specification=l),placeholder:"请输入药品规格",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"药品剂型",prop:"dosageForm"},{default:a(()=>[e(x,{modelValue:d(t).dosageForm,"onUpdate:modelValue":o[27]||(o[27]=l=>d(t).dosageForm=l),placeholder:"请选择药品剂型",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(K),l=>(m(),y(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"OTC药品",prop:"isOtc"},{default:a(()=>[e(T,{modelValue:d(t).isOtc,"onUpdate:modelValue":o[28]||(o[28]=l=>d(t).isOtc=l),placeholder:"请选择OTC药品",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(J),l=>(m(),y(R,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"发票项目",prop:"invoiceItem"},{default:a(()=>[e(x,{modelValue:d(t).invoiceItem,"onUpdate:modelValue":o[29]||(o[29]=l=>d(t).invoiceItem=l),placeholder:"请选择发票项目",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(W),l=>(m(),y(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"批准文号",prop:"approvalNumber"},{default:a(()=>[e(r,{modelValue:d(t).approvalNumber,"onUpdate:modelValue":o[30]||(o[30]=l=>d(t).approvalNumber=l),placeholder:"请输入批准文号",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"生产厂家",prop:"manufacturer"},{default:a(()=>[e(r,{modelValue:d(t).manufacturer,"onUpdate:modelValue":o[31]||(o[31]=l=>d(t).manufacturer=l),placeholder:"请输入生产厂家",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"状态",prop:"manufacturer"},{default:a(()=>[e(T,{modelValue:d(t).status,"onUpdate:modelValue":o[32]||(o[32]=l=>d(t).status=l),placeholder:"请选择状态",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(Q),l=>(m(),y(R,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",Fe,[p("div",Ne,[Pe,e(w,{gutter:15},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"包装单位",prop:"packageUnit"},{default:a(()=>[e(x,{modelValue:d(t).packageUnit,"onUpdate:modelValue":o[33]||(o[33]=l=>d(t).packageUnit=l),placeholder:"请选择包装单位",clearable:"",disabled:u.value},{default:a(()=>[(m(!0),c(g,null,h(d(E),l=>(m(),y(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本系数",prop:"baseFactor"},{default:a(()=>[e(r,{modelValue:d(t).baseFactor,"onUpdate:modelValue":o[34]||(o[34]=l=>d(t).baseFactor=l),placeholder:"请输入基本系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"基本单位",prop:"baseUnit"},{default:a(()=>[e(r,{modelValue:d(t).baseUnit,"onUpdate:modelValue":o[35]||(o[35]=l=>d(t).baseUnit=l),placeholder:"请输入基本单位",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"剂量系数",prop:"dosageFactor"},{default:a(()=>[e(r,{modelValue:d(t).dosageFactor,"onUpdate:modelValue":o[36]||(o[36]=l=>d(t).dosageFactor=l),placeholder:"请输入剂量系数",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"剂量单位",prop:"dosageUnit"},{default:a(()=>[e(r,{modelValue:d(t).dosageUnit,"onUpdate:modelValue":o[37]||(o[37]=l=>d(t).dosageUnit=l),placeholder:"请输入剂量单位",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"采购价(元)",prop:"purchasePrice"},{default:a(()=>[e(r,{modelValue:d(t).purchasePrice,"onUpdate:modelValue":o[38]||(o[38]=l=>d(t).purchasePrice=l),placeholder:"请输入采购价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"零售价(元)",prop:"retailPrice"},{default:a(()=>[e(r,{modelValue:d(t).retailPrice,"onUpdate:modelValue":o[39]||(o[39]=l=>d(t).retailPrice=l),placeholder:"请输入零售价(元)",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",Me,[p("div",Ye,[Be,e(w,null,{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"用法",prop:"usageMethod"},{default:a(()=>[e(r,{modelValue:d(t).usageMethod,"onUpdate:modelValue":o[40]||(o[40]=l=>d(t).usageMethod=l),placeholder:"请输入用法",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"单次用量",prop:"singleDose"},{default:a(()=>[e(r,{modelValue:d(t).singleDose,"onUpdate:modelValue":o[41]||(o[41]=l=>d(t).singleDose=l),placeholder:"请输入单次用量",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})])]),p("div",Re,[p("div",Te,[We,e(w,null,{default:a(()=>[e(i,{span:6},{default:a(()=>[e(s,{label:"库存上限",prop:"maxInventory"},{default:a(()=>[e(F,{modelValue:d(t).maxInventory,"onUpdate:modelValue":o[42]||(o[42]=l=>d(t).maxInventory=l),placeholder:"请输入库存上限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"库存下限",prop:"minInventory"},{default:a(()=>[e(F,{modelValue:d(t).minInventory,"onUpdate:modelValue":o[43]||(o[43]=l=>d(t).minInventory=l),placeholder:"请输入库存下限",disabled:u.value,style:{width:"100%"},step:"1",min:"0",max:"99999"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"仓库",prop:"warehouse"},{default:a(()=>[e(r,{modelValue:d(t).warehouse,"onUpdate:modelValue":o[44]||(o[44]=l=>d(t).warehouse=l),placeholder:"请输入仓库",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"货位号",prop:"locationCode"},{default:a(()=>[e(r,{modelValue:d(t).locationCode,"onUpdate:modelValue":o[45]||(o[45]=l=>d(t).locationCode=l),placeholder:"请输入货位号",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:6},{default:a(()=>[e(s,{label:"有效期预警",prop:"expiryWarningDays"},{default:a(()=>[e(L,{clearable:"",modelValue:d(t).expiryWarningDays,"onUpdate:modelValue":o[46]||(o[46]=l=>d(t).expiryWarningDays=l),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择有效期预警(天)",value:"YYYY-MM-DD",disabled:u.value,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:24},{default:a(()=>[e(s,{label:"备注",prop:"remark"},{default:a(()=>[e(r,{modelValue:d(t).remark,"onUpdate:modelValue":o[47]||(o[47]=l=>d(t).remark=l),type:"textarea",placeholder:"请输入内容",disabled:u.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),I("",!0)]),_:1})])]),e(s,{class:"footer_btn",style:{"margin-left":"80%"}},{default:a(()=>[e(N,{type:"primary",onClick:de,disabled:u.value},{default:a(()=>[M("提 交")]),_:1},8,["disabled"]),e(N,{onClick:G},{default:a(()=>[M("取 消")]),_:1})]),_:1})]),_:1},8,["model","rules"])])):I("",!0)])}}}),ze=ue(Qe,[["__scopeId","data-v-83e19d91"]]);export{ze as default};
