import{_ as O,e as i,c as V,o as d,f as e,h as a,K as y,L as F,j as f,n as s,k,G as D}from"./index-B0qHf98Y.js";const N={data(){return{dateRange:[],selectedAreas:[],selectedShifts:[],followStatus:1,staffName:"",areaList:[{id:1,name:"护理区域"},{id:2,name:"管理区域"},{id:3,name:"高位区域"}],shiftList:[{id:1,name:"早班"},{id:2,name:"中班"},{id:3,name:"晚班"},{id:4,name:"夜班"}],nurseList:[{id:1,name:"张护士"},{id:2,name:"王护士"},{id:3,name:"孙护士"}],tableData:[{area:"护理区域",date:"2023-04-01",shift:"早班",nurse:"张鸣",totalReceivers:1,newAdmissions:2,trialAdmissions:3,deaths:4,selfOutings:5,discharges:6,leaveOutings:7,emergencyReferrals:8,referralAndHospitalizations:9,medicalOutings:10,fillTime:"2025-04-01",persion:"admin",revTime:"2025-04-01"},{area:"护理区域",date:"2023-04-12",shift:"早班",nurse:"孙俪丽",totalReceivers:1,newAdmissions:2,trialAdmissions:3,deaths:4,selfOutings:5,discharges:6,leaveOutings:7,emergencyReferrals:8,referralAndHospitalizations:9,medicalOutings:10,fillTime:"2025-04-01",persion:"admin",revTime:"2025-04-01"},{area:"护理区域",date:"2023-04-13",shift:"早班",nurse:"程晓",totalReceivers:1,newAdmissions:2,trialAdmissions:3,deaths:4,selfOutings:5,discharges:6,leaveOutings:7,emergencyReferrals:8,referralAndHospitalizations:9,medicalOutings:10,fillTime:"2025-04-01",persion:"admin",revTime:"2025-04-01"},{area:"护理区域",date:"2023-04-16",shift:"早班",nurse:"王晓梅",totalReceivers:1,newAdmissions:2,trialAdmissions:3,deaths:4,selfOutings:5,discharges:6,leaveOutings:7,emergencyReferrals:8,referralAndHospitalizations:9,medicalOutings:10,fillTime:"2025-04-01",persion:"admin",revTime:"2025-04-01"}],handoverDialogVisible:!1,detailDialogVisible:!1,handoverForm:{date:"",nurseId:"",areaId:"",shiftId:"",fillTime:""},detailTableData:[{id:1,roomNumber:"102",bedNumber:"02",name:"张三",careLevel:"三级",content:"1.今日预约做检查"},{id:2,roomNumber:"102",bedNumber:"02",name:"张三",careLevel:"三级",content:"1.今日预约做检查"},{id:3,roomNumber:"102",bedNumber:"02",name:"张三",careLevel:"三级",content:"1.今日预约做检查"}],roomOptions:[{id:1,label:"101",value:"101"},{id:2,label:"202",value:"202"},{id:3,label:"303",value:"303"}]}},methods:{handleSearch(){},handleHandover(){this.handoverDialogVisible=!0},handleEdit(b){this.followStatus=1,console.log(b,"handleEdit"),this.handoverDialogVisible=!0,this.handoverForm=b},handleDetail(b){this.detailDialogVisible=!0},handleReceive(b){this.followStatus=2,this.handoverDialogVisible=!0,this.handoverForm=b},handleQuickFill(){},handleSubmitHandover(){D({message:"添加成功",type:"success"}),this.handoverDialogVisible=!1},handleSubmitHandover2(){D({message:"交接班成功",type:"success"}),this.handoverDialogVisible=!1}}},R={class:"app-container"};function T(b,n,S,L,o,v){const U=i("el-date-picker"),t=i("el-form-item"),h=i("el-option"),_=i("el-select"),g=i("el-input"),m=i("el-button"),A=i("el-form"),r=i("el-table-column"),w=i("el-table"),p=i("el-input-number"),c=i("el-row"),x=i("el-col"),C=i("el-dialog");return d(),V("div",R,[e(A,{inline:!0,class:"filter-form"},{default:a(()=>[e(t,{label:"交接班日期"},{default:a(()=>[e(U,{modelValue:o.dateRange,"onUpdate:modelValue":n[0]||(n[0]=l=>o.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"","value-format":"yyyy-MM-dd"},null,8,["modelValue"])]),_:1}),e(t,{label:"区域"},{default:a(()=>[e(_,{modelValue:o.selectedAreas,"onUpdate:modelValue":n[1]||(n[1]=l=>o.selectedAreas=l),multiple:"",placeholder:"请选择区域",clearable:"",style:{width:"130px"}},{default:a(()=>[(d(!0),V(y,null,F(o.areaList,l=>(d(),f(h,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(t,{label:"班次"},{default:a(()=>[e(_,{modelValue:o.selectedShifts,"onUpdate:modelValue":n[2]||(n[2]=l=>o.selectedShifts=l),multiple:"",placeholder:"请选择班次",clearable:"",style:{width:"100px"}},{default:a(()=>[(d(!0),V(y,null,F(o.shiftList,l=>(d(),f(h,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(t,{label:"人员"},{default:a(()=>[e(g,{modelValue:o.staffName,"onUpdate:modelValue":n[3]||(n[3]=l=>o.staffName=l),placeholder:"请输入姓名",clearable:""},null,8,["modelValue"])]),_:1}),e(t,null,{default:a(()=>[e(m,{type:"primary",onClick:v.handleSearch},{default:a(()=>[s("查询")]),_:1},8,["onClick"]),e(m,{type:"primary",onClick:v.handleHandover},{default:a(()=>[s("交班")]),_:1},8,["onClick"])]),_:1})]),_:1}),e(w,{data:o.tableData,border:"",style:{width:"100%"}},{default:a(()=>[e(r,{type:"index",label:"序号",width:"80"}),e(r,{prop:"area",label:"区域"}),e(r,{prop:"date",label:"日期"}),e(r,{prop:"shift",label:"班次"}),e(r,{prop:"nurse",label:"护士（交班人）"}),e(r,{label:"操作",width:"200"},{default:a(({row:l})=>[e(m,{type:"text",onClick:u=>v.handleEdit(l)},{default:a(()=>[s("修改")]),_:2},1032,["onClick"]),e(m,{type:"text",onClick:u=>v.handleDetail(l)},{default:a(()=>[s("详情")]),_:2},1032,["onClick"]),e(m,{type:"text",onClick:u=>v.handleReceive(l)},{default:a(()=>[s("接班")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e(C,{title:"交班",modelValue:o.handoverDialogVisible,"onUpdate:modelValue":n[22]||(n[22]=l=>o.handoverDialogVisible=l),width:"60%"},{footer:a(()=>[e(m,{onClick:n[21]||(n[21]=l=>o.handoverDialogVisible=!1)},{default:a(()=>[s("取消")]),_:1}),o.followStatus==1?(d(),f(m,{key:0,type:"primary",onClick:v.handleSubmitHandover},{default:a(()=>[s("提交")]),_:1},8,["onClick"])):k("",!0),o.followStatus==2?(d(),f(m,{key:1,type:"primary",onClick:v.handleSubmitHandover2},{default:a(()=>[s("接班")]),_:1},8,["onClick"])):k("",!0)]),default:a(()=>[e(A,{model:o.handoverForm,"label-width":"120px"},{default:a(()=>[e(t,{label:"交接日期"},{default:a(()=>[e(U,{modelValue:o.handoverForm.date,"onUpdate:modelValue":n[4]||(n[4]=l=>o.handoverForm.date=l),type:"date",placeholder:"选择日期","value-format":"yyyy-MM-dd"},null,8,["modelValue"])]),_:1}),e(t,{label:"交班人"},{default:a(()=>[e(_,{modelValue:o.handoverForm.nurse,"onUpdate:modelValue":n[5]||(n[5]=l=>o.handoverForm.nurse=l),placeholder:"请选择交班人"},{default:a(()=>[(d(!0),V(y,null,F(o.nurseList,l=>(d(),f(h,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(t,{label:"区域"},{default:a(()=>[e(_,{modelValue:o.handoverForm.area,"onUpdate:modelValue":n[6]||(n[6]=l=>o.handoverForm.area=l),placeholder:"请选择区域"},{default:a(()=>[(d(!0),V(y,null,F(o.areaList,l=>(d(),f(h,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(t,{label:"班次"},{default:a(()=>[e(_,{modelValue:o.handoverForm.shift,"onUpdate:modelValue":n[7]||(n[7]=l=>o.handoverForm.shift=l),placeholder:"请选择班次"},{default:a(()=>[(d(!0),V(y,null,F(o.shiftList,l=>(d(),f(h,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,null,{default:a(()=>[e(t,{label:"接班总人数"},{default:a(()=>[e(p,{modelValue:o.handoverForm.totalReceivers,"onUpdate:modelValue":n[8]||(n[8]=l=>o.handoverForm.totalReceivers=l),min:0},null,8,["modelValue"])]),_:1}),e(t,{label:"新入住"},{default:a(()=>[e(p,{modelValue:o.handoverForm.newAdmissions,"onUpdate:modelValue":n[9]||(n[9]=l=>o.handoverForm.newAdmissions=l),min:0},null,8,["modelValue"])]),_:1}),e(t,{label:"试入住"},{default:a(()=>[e(p,{modelValue:o.handoverForm.trialAdmissions,"onUpdate:modelValue":n[10]||(n[10]=l=>o.handoverForm.trialAdmissions=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(c,null,{default:a(()=>[e(t,{label:"死亡"},{default:a(()=>[e(p,{modelValue:o.handoverForm.deaths,"onUpdate:modelValue":n[11]||(n[11]=l=>o.handoverForm.deaths=l),min:0},null,8,["modelValue"])]),_:1}),e(t,{label:"自行外出"},{default:a(()=>[e(p,{modelValue:o.handoverForm.selfOutings,"onUpdate:modelValue":n[12]||(n[12]=l=>o.handoverForm.selfOutings=l),min:0},null,8,["modelValue"])]),_:1}),e(t,{label:"出院"},{default:a(()=>[e(p,{modelValue:o.handoverForm.discharges,"onUpdate:modelValue":n[13]||(n[13]=l=>o.handoverForm.discharges=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(c,null,{default:a(()=>[e(t,{label:"请假外出"},{default:a(()=>[e(p,{modelValue:o.handoverForm.leaveOutings,"onUpdate:modelValue":n[14]||(n[14]=l=>o.handoverForm.leaveOutings=l),min:0},null,8,["modelValue"])]),_:1}),e(t,{label:"紧急转诊"},{default:a(()=>[e(p,{modelValue:o.handoverForm.emergencyReferrals,"onUpdate:modelValue":n[15]||(n[15]=l=>o.handoverForm.emergencyReferrals=l),min:0},null,8,["modelValue"])]),_:1}),e(t,{label:"转诊并住院"},{default:a(()=>[e(p,{modelValue:o.handoverForm.referralAndHospitalizations,"onUpdate:modelValue":n[16]||(n[16]=l=>o.handoverForm.referralAndHospitalizations=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(c,null,{default:a(()=>[e(t,{label:"外出就医"},{default:a(()=>[e(p,{modelValue:o.handoverForm.medicalOutings,"onUpdate:modelValue":n[17]||(n[17]=l=>o.handoverForm.medicalOutings=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{label:"快速填写"},{default:a(()=>[e(m,{type:"primary",onClick:v.handleQuickFill},{default:a(()=>[s("导入上一班次数据")]),_:1},8,["onClick"])]),_:1}),e(t,{label:"填写时间"},{default:a(()=>[e(g,{modelValue:o.handoverForm.fillTime,"onUpdate:modelValue":n[18]||(n[18]=l=>o.handoverForm.fillTime=l),readonly:""},null,8,["modelValue"])]),_:1}),o.followStatus==2?(d(),f(t,{key:0,label:"接班人"},{default:a(()=>[e(g,{modelValue:o.handoverForm.persion,"onUpdate:modelValue":n[19]||(n[19]=l=>o.handoverForm.persion=l),disabled:""},null,8,["modelValue"])]),_:1})):k("",!0),o.followStatus==2?(d(),f(t,{key:1,label:"接班时间"},{default:a(()=>[e(g,{modelValue:o.handoverForm.revTime,"onUpdate:modelValue":n[20]||(n[20]=l=>o.handoverForm.revTime=l),disabled:""},null,8,["modelValue"])]),_:1})):k("",!0),e(c,{style:{margin:"5px 0px"}},{default:a(()=>[e(x,{offset:22},{default:a(()=>[e(m,{type:"primary"},{default:a(()=>[s("新增")]),_:1})]),_:1})]),_:1}),e(w,{data:o.detailTableData,border:"",style:{width:"100%"}},{default:a(()=>[e(r,{prop:"roomNumber",label:"房间号"},{default:a(l=>[e(_,{modelValue:l.row.roomNumber,"onUpdate:modelValue":u=>l.row.roomNumber=u,placeholder:"选择房间号",size:"large",style:{width:"120px"}},{default:a(()=>[(d(!0),V(y,null,F(o.roomOptions,u=>(d(),f(h,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{prop:"bedNumber",label:"床位号"}),e(r,{prop:"name",label:"姓名"}),e(r,{prop:"careLevel",label:"护理等级"}),e(r,{prop:"content",label:"交接内容"},{default:a(l=>[e(g,{modelValue:l.row.content,"onUpdate:modelValue":u=>l.row.content=u,style:{width:"240px"},rows:2,type:"textarea",placeholder:"交接内容"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(C,{title:"交接详情",modelValue:o.detailDialogVisible,"onUpdate:modelValue":n[25]||(n[25]=l=>o.detailDialogVisible=l),width:"80%"},{footer:a(()=>[e(m,{onClick:n[24]||(n[24]=l=>o.detailDialogVisible=!1)},{default:a(()=>[s("关闭")]),_:1})]),default:a(()=>[e(w,{data:o.detailTableData,border:"",style:{width:"100%"}},{default:a(()=>[e(r,{prop:"roomNumber",label:"房间号"}),e(r,{prop:"bedNumber",label:"床位号"}),e(r,{prop:"name",label:"姓名"}),e(r,{prop:"careLevel",label:"护理等级"}),e(r,{prop:"content",label:"交接内容"},{default:a(l=>[e(g,{modelValue:b.textarea,"onUpdate:modelValue":n[23]||(n[23]=u=>b.textarea=u),style:{width:"240px"},rows:2,type:"textarea",placeholder:"交接内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}const z=O(N,[["render",T],["__scopeId","data-v-c8e7b8fe"]]);export{z as default};
