import{_ as N,r as $,e as s,c as v,o as f,f as l,i as u,h as n,n as i,k as B,t as h}from"./index-B0qHf98Y.js";const S={class:"replace-consumables"},z={key:0,class:"elder-info"},L=["src"],O={class:"info"},T={style:{"margin-top":"20px"}},j={__name:"replaceConsumables",setup(A){const d=$([{avatar:"https://placehold.co/64x64",name:"王药师",id:"301 301-01",serviceRecords:[{serviceDate:"",serviceItem:"",quantity:1,price:"",remark:""}]}]),_=t=>d.value[t].serviceRecords.length,w=()=>{d.value.push({avatar:"https://placehold.co/64x64",name:"新老人",id:"",serviceRecords:[{serviceDate:"",serviceItem:"",quantity:1,price:"",remark:""}]})},b=t=>{const r=V(t);d.value[r].serviceRecords.splice(k(t),1)},V=t=>{let r=0;for(let o=0;o<d.value.length;o++){const a=_(o);if(t<r+a)return o;r+=a}return-1},k=t=>{let r=0;for(let o=0;o<d.value.length;o++){const a=_(o);if(t<r+a)return t-r;r+=a}return-1},C=t=>{d.value.splice(t,1)},y=(t,r,o,a)=>{if(r.label==="老人信息"){const p=d.value.indexOf(t);a.target.classList.contains("el-icon-delete")&&C(p)}},g=(t,r)=>{console.log("编辑:",t,r)},x=()=>{console.log("保存数据:",d.value)},I=()=>{console.log("提交数据:",d.value)},U=()=>{console.log("取消操作")};return(t,r)=>{const o=s("el-button"),a=s("el-table-column"),p=s("el-date-picker"),D=s("el-option"),R=s("el-select"),q=s("el-input-number"),m=s("el-input"),E=s("el-table");return f(),v("div",S,[l(o,{type:"primary",onClick:w},{default:n(()=>[i("+ 新增老人")]),_:1}),l(E,{data:d.value,border:"",style:{width:"100%"},onCellClick:y},{default:n(()=>[l(a,{label:"老人信息",width:"180"},{default:n(e=>[e.row.rowspan>0?(f(),v("div",z,[u("img",{src:e.row.avatar,alt:"老人头像",class:"avatar"},null,8,L),u("div",O,[u("p",null,h(e.row.name),1),u("p",null,h(e.row.id),1)])])):B("",!0)]),_:1}),l(a,{prop:"serviceDate",label:"服务日期",width:"180"},{default:n(e=>[l(p,{modelValue:e.row.serviceDate,"onUpdate:modelValue":c=>e.row.serviceDate=c,type:"date",placeholder:"选择日期"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(a,{prop:"serviceItem",label:"服务项目",width:"180"},{default:n(e=>[l(R,{modelValue:e.row.serviceItem,"onUpdate:modelValue":c=>e.row.serviceItem=c,placeholder:"请选择服务项目"},{default:n(()=>[l(D,{label:"胃管",value:"胃管"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(a,{prop:"quantity",label:"数量",width:"180"},{default:n(e=>[l(q,{modelValue:e.row.quantity,"onUpdate:modelValue":c=>e.row.quantity=c,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(a,{prop:"price",label:"价格",width:"180"},{default:n(e=>[l(m,{modelValue:e.row.price,"onUpdate:modelValue":c=>e.row.price=c,placeholder:"请输入价格"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(a,{prop:"remark",label:"备注",width:"180"},{default:n(e=>[l(m,{modelValue:e.row.remark,"onUpdate:modelValue":c=>e.row.remark=c,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(a,{label:"操作",width:"180"},{default:n(e=>[l(o,{size:"mini",onClick:c=>g(e.$index,e.row)},{default:n(()=>[i("编辑")]),_:2},1032,["onClick"]),l(o,{size:"mini",type:"danger",onClick:c=>b(e.$index)},{default:n(()=>[i("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),u("div",T,[l(o,{type:"success",onClick:x},{default:n(()=>[i("保存")]),_:1}),l(o,{type:"primary",onClick:I},{default:n(()=>[i("提交")]),_:1}),l(o,{onClick:U},{default:n(()=>[i("取消")]),_:1})])])}}},G=N(j,[["__scopeId","data-v-4750ab78"]]);export{G as default};
