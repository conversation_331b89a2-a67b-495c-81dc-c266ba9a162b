import{_ as n,j as r,o,f,h as u,J as p,i as s,p as h,au as m,c as v,k as b,bq as l,O as g,br as y,bs as _}from"./index-B0qHf98Y.js";const k={class:"dialog-body"},w={key:0,class:"dialog-footer"},B={__name:"noticeDialog",props:{visible:{type:Boolean,default:!1},title:{type:String,default:""},width:{type:String,default:"50%"},closeOnClickModal:{type:Boolean,default:!0}},emits:["update:visible","close"],setup(e,{emit:i}){const d=e,a=i,c=()=>{d.closeOnClickModal&&(a("update:visible",!1),a("close"))};return(t,C)=>(o(),r(_,{to:"body"},[f(y,{name:"dialog-fade"},{default:u(()=>[p(s("div",{class:"dialog-wrapper",onClick:h(c,["self"])},[s("div",{class:"dialog",style:m({width:e.width})},[s("div",k,[l(t.$slots,"default",{},void 0,!0)]),t.$slots.footer?(o(),v("div",w,[l(t.$slots,"footer",{},void 0,!0)])):b("",!0)],4)],512),[[g,e.visible]])]),_:3})]))}},D=n(B,[["__scopeId","data-v-c76da1a5"]]);export{D as default};
