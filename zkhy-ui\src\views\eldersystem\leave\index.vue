<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-bar">
      <el-form-item label="老人姓名" prop="elderName">
        <el-input v-model="queryParams.elderName" placeholder="请输入老人姓名" clearable style="width: 140px;"/>
      </el-form-item>
      <el-form-item label="计划外出时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 220px;"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable   style="width: 200px">
          <el-option label="全部" value=""/>
          <el-option label="待审批" value="PENDING" />
          <el-option label="已驳回" value="REJECTED" />
          <el-option label="待销假" value="APPROVED" />
          <el-option label="已完成" value="COMPLETE" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button icon="Plus" type="primary" @click="handleAdd" plain>外出申请</el-button>
      </el-form-item>
    </el-form>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="所有" name="all">
        <el-table :data="tableData" border stripe>
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column prop="elderName1" label="老人头像" width="100" align="center">
            <template #default="scope">
              <el-avatar shape="circle" :size="60" fit="fill" :src="scope.row.avatar" />
            </template>
          </el-table-column>
          <el-table-column prop="elderName" label="老人姓名" width="100" align="center"/>
          <el-table-column prop="elderCode" label="老人编号" width="120" align="center"/>
          <el-table-column prop="bedNumber" label="床位编号" width="120" align="center"/>
          <el-table-column prop="companionName" label="陪同人" width="120" align="center"/>
          <el-table-column prop="companionPhone" label="陪同人电话" width="120" align="center"/>
          <el-table-column prop="relationship" label="与老人关系" width="120" align="center"/>
          <el-table-column prop="plannedLeaveTime" label="计划请假时间" min-width="180" align="center">
            <template #default="scope">
              {{ scope.row.plannedLeaveTime + '~'+ scope.row.plannedReturnTime}}
            </template>
          </el-table-column>
          <el-table-column prop="plannedDays" label="计划请假天数" width="120" align="center"/>
          <el-table-column prop="actualLeaveTime" label="实际请假时间" width="180" align="center">
            <template #default="scope">
              {{ scope.row.actualLeaveTime && scope.row.actualReturnTime?scope.row.actualLeaveTime + '~'+ scope.row.actualReturnTime:''}}
            </template>
          </el-table-column>
          <el-table-column prop="actualDays" label="实际请假天数" width="120" align="center"/>
          <el-table-column prop="handlerName" label="经办人" width="100" align="center"/>
          <el-table-column prop="approverName" label="审批人" width="160" align="center"/>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
             {{ scope.row.status === 'PENDING'?'待审批' : scope.row.status === 'REJECTED' ? '已驳回' : scope.row.status === 'APPROVED' ? '待销假' : scope.row.status === 'COMPLETE' ?'已完成':''}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container" v-if="total > 0">
         <el-pagination
            layout="prev, pager, next, sizes, jumper"
            :total="total"
            background
            :page-size="queryParams.pageSize"
            :current-page="queryParams.pageNum"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            :page-sizes="[10, 20, 50]"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="申请" name="apply">
        <el-table :data="tableData" border stripe>
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column prop="elderName1" label="老人头像" width="100" align="center">
            <template #default="scope">
                <el-avatar shape="circle" :size="60" fit="fill" :src="scope.row.avatar" />
            </template>
          </el-table-column>
          <el-table-column prop="elderName" label="老人姓名" width="100" align="center"/>
          <el-table-column prop="elderCode" label="老人编号" width="120" align="center"/>
          <el-table-column prop="bedNumber" label="床位编号" width="120" align="center"/>
          <el-table-column prop="companionName" label="陪同人" width="120" align="center"/>
          <el-table-column prop="companionPhone" label="陪同人电话" width="120" align="center"/>
          <el-table-column prop="relationship" label="与老人关系" width="120" align="center"/>
          <el-table-column prop="plannedLeaveTime" label="计划请假时间" min-width="180" align="center">
            <template #default="scope">
              {{ scope.row.plannedLeaveTime + '~'+ scope.row.plannedReturnTime}}
            </template>
          </el-table-column>
          <el-table-column prop="plannedDays" label="计划请假天数" width="120" align="center"/>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              {{ scope.row.status === 'PENDING'?'待审批' : scope.row.status === 'REJECTED' ? '已驳回' : scope.row.status === 'APPROVED' ? '待销假' : scope.row.status === 'COMPLETE' ?'已完成':''}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="handleDetail(scope.row)">查看</el-button>
              <el-button link type="primary" @click="handleDeleteLeave(scope.row)" v-if="scope.row.status === 'PENDING'">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container"  v-if="total > 0">
          <el-pagination
            layout="prev, pager, next, sizes, jumper"
            :total="total"
            background
            :page-size="queryParams.pageSize"
            :current-page="queryParams.pageNum"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            :page-sizes="[10, 20, 50]"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="审核" name="audit" v-if="isApprovalTag">
        <el-table :data="tableData" border stripe>
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column prop="elderName1" label="老人头像" width="100" align="center">
            <template #default="scope">
                <el-avatar shape="circle" :size="60" fit="fill" :src="scope.row.avatar" />
            </template>
          </el-table-column>
          <el-table-column prop="elderName" label="老人姓名" width="100" align="center"/>
          <el-table-column prop="elderCode" label="老人编号" width="120" align="center"/>
          <el-table-column prop="bedNumber" label="床位编号" width="120" align="center"/>
          <el-table-column prop="companionName" label="陪同人" width="120" align="center"/>
          <el-table-column prop="companionPhone" label="陪同人电话" width="120" align="center"/>
          <el-table-column prop="relationship" label="与老人关系" width="120" align="center"/>
          <el-table-column prop="plannedLeaveTime" label="计划请假时间" min-width="180" align="center">
            <template #default="scope">
              {{ scope.row.plannedLeaveTime + '~'+ scope.row.plannedReturnTime}}
            </template>
          </el-table-column>
          <el-table-column prop="plannedDays" label="计划请假天数" width="120" align="center"/>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              {{ scope.row.status === 'PENDING'?'待审批' : scope.row.status === 'REJECTED' ? '已驳回' : scope.row.status === 'APPROVED' ? '待销假' : scope.row.status === 'COMPLETE' ?'已完成':''}}    
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="handleReview(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            layout="prev, pager, next, sizes, jumper"
            :total="total"
            background
            :page-size="queryParams.pageSize"
            :current-page="queryParams.pageNum"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            :page-sizes="[10, 20, 50]"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="销假" name="fromLeave">
        <el-table :data="tableData" border stripe>
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column prop="elderName1" label="老人头像" width="100" align="center">
            <template #default="scope">
                <el-avatar shape="circle" :size="60" fit="fill" :src="scope.row.avatar" />
            </template>
          </el-table-column>
          <el-table-column prop="elderName" label="老人姓名" width="100" align="center"/>
          <el-table-column prop="elderCode" label="老人编号" width="120" align="center"/>
          <el-table-column prop="bedNumber" label="床位编号" width="120" align="center"/>
          <el-table-column prop="companionName" label="陪同人" width="120" align="center"/>
          <el-table-column prop="companionPhone" label="陪同人电话" width="120" align="center"/>
          <el-table-column prop="relationship" label="与老人关系" width="120" align="center"/>
          <el-table-column prop="plannedLeaveTime" label="计划请假时间" min-width="180" align="center">
            <template #default="scope">
              {{ scope.row.plannedLeaveTime + '~'+ scope.row.plannedReturnTime}}
            </template>
          </el-table-column>
          <el-table-column prop="plannedDays" label="计划请假天数" width="120" align="center"/>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              {{ scope.row.status === 'PENDING'?'待审批' : scope.row.status === 'REJECTED' ? '已驳回' : scope.row.status === 'APPROVED' ? '待销假' : scope.row.status === 'COMPLETE' ?'已完成':''}}            
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="handleReturnFromLeave(scope.row)">销假</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            layout="prev, pager, next, sizes, jumper"
            :total="total"
            background
            :page-size="queryParams.pageSize"
            :current-page="queryParams.pageNum"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            :page-sizes="[10, 20, 50]"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    <LeaveForm ref="leaveFormRef" :activeTabValue="activeTab" @refresh="resetQuery" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage ,ElMessageBox} from 'element-plus'
import LeaveForm from './leaveForm.vue'
import {getLeaveList,delLeave} from '@/api/leave/leave'
import { getUserProfile } from "@/api/system/user";
// 获取全局代理对象
const { proxy } = getCurrentInstance()

// 字典数据
const { leave_status } = proxy.useDict('leave_status')
const clientHeightIng = ref(document.documentElement.clientHeight - 100)
// 状态数据
const activeTab = ref('all')
const tableData = ref([])
const total = ref(0)
const leaveFormRef = ref()
const isAddOrEdit = ref('')
const dateRange = ref([]);
const isApprovalTag = ref(true);
// 查询参数
const queryParams = reactive({
  elderName: '',
  plannedLeaveTime: [],
  status: '',
  statusList: [],
  pageSize: 10,
  pageNum: 1
})

// 查询列表数据
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  resetForm()
  proxy.$refs.queryForm.resetFields()
  handleQuery()
}
// 重置表单
const resetForm = () => {
  dateRange.value = [];
  activeTab.value = 'all'
  tableData.value = []
  queryParams.pageNum = 1
  queryParams.statusList = []
}

// 获取列表数据
const getList = async () => {
  console.log('queryParams', queryParams)
  try {
    const res = await getLeaveList(proxy.addDateRange(queryParams, dateRange.value,'PlannedLeaveTime'))
    tableData.value = res.rows;
    total.value = res.total
  } catch (error) {
    console.error('获取列表数据失败:', error)
  }
}

// 标签页切换
const handleTabClick = (val) => {
  activeTab.value = val.props.name
  if(activeTab.value === 'apply'){
    queryParams.statusList = ['PENDING','REJECTED','COMPLETE']
  }else if(activeTab.value === 'audit'){
    queryParams.statusList = ['PENDING']
  } else if(activeTab.value === 'fromLeave'){
    queryParams.statusList = ['APPROVED']
  } else{
    queryParams.statusList = []
  }
  queryParams.pageNum = 1
  tableData.value = []
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}
//销假
const handleReturnFromLeave = (row) => {
  isAddOrEdit.value = 'fromLeave'
  leaveFormRef.value.openDialog(isAddOrEdit.value,row)
}
const handlePageChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 新增
const handleAdd = (row) => {
  isAddOrEdit.value = 'wcsq'
  leaveFormRef.value.openDialog(isAddOrEdit.value,row)
}

// 编辑
const handleEdit = (row) => {
  // TODO: 实现编辑功能
}

// 删除
const handleDelete = async (row) => {
  // TODO: 实现删除功能
}

// 查看详情
const handleDetail = (row) => {
  isAddOrEdit.value = 'detail'
  leaveFormRef.value.openDialog(isAddOrEdit.value,row)
}
//删除老人外出申请
const handleDeleteLeave = async (row) => {
  try { 
    await ElMessageBox.confirm('确定要删除该条外出申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    const datas = await delLeave(row.id)
    if(datas.code === 200){
      ElMessage.success('删除成功')      
      getList()
    }
  } catch (error) {
    console.error('删除老人外出申请失败:', error)
  }
}
// 审核
const handleReview = (row) => {
  isAddOrEdit.value = 'review'
  leaveFormRef.value.openDialog(isAddOrEdit.value,row)
}
const init = () => {  
  getList()
  getUserProfile().then((res) => {
     res.data.roles.map((item) => {
      if (item.roleKey == "process_approval" || item.roleKey == "admin") {
        isApprovalTag.value = true;
      } else {
        isApprovalTag.value = false;
      }
    });
  });
}
onMounted(() => {
  init()
})
</script>

<style scoped>
.el-tabs--top{
  flex-direction: column;
}
.leave-tabs {
  background: #fff;
  border-radius: 8px;
  height: calc(100vh - 320px);
  display: flex;
  flex-direction: column;
  
  .el-tabs__header {
    margin-bottom: 0;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .el-tabs__content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
  }

  .el-table {
    margin-top: 0;
    th {
      background: #396cff;
      color: #fff;
      font-weight: 500;
      font-size: 15px;
      position: sticky;
      top: 0;
      z-index: 1;
    }
    td {
      font-size: 14px;
    }
  }
}
.app-container {
  padding: 20px;
}
.search-bar {
  padding: 18px 0 0 0;
  background: #fff;
  margin-bottom: 8px;
  .el-form-item {
    margin-right: 24px;
    margin-bottom: 0;
  }
}
.search-form {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-form-item {
    margin-bottom: 0;
    margin-right: 20px;
  }

  .el-date-editor {
    width: 240px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>


