import{X as t}from"./index-B0qHf98Y.js";function o(e){return t({url:"/warehouse/stockIn",method:"post",data:e})}function r(e){return t({url:`/warehouse/stockIn/${e}`,method:"get"})}function n(e){return t({url:"/warehouse/stockIn",method:"put",data:e})}function s(e){return t({url:`/warehouse/stockIn/${e}`,method:"delete"})}function a(e){return t({url:"/warehouse/stockIn/list",method:"get",params:e})}function c(e){return t({url:"/warehouse/medication/getNewCode",method:"get",params:e})}function h(e){return t({url:"/warehouse/medication/list",method:"get",params:e})}function d(e){return t({url:"/warehouse/stockOut",method:"post",data:e})}function i(e){return t({url:"/warehouse/stockOut/list",method:"get",params:e})}function k(e){return t({url:"/warehouse/stockOut",method:"put",data:e})}function l(e){return t({url:`/warehouse/stockOut/${e}`,method:"delete"})}function m(e){return t({url:"/warehouse/stockOut/"+e,method:"get"})}export{h as a,c as b,o as c,m as d,d as e,k as f,r as g,a as h,s as i,i as j,l as k,n as u};
