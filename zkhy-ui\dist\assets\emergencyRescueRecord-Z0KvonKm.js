import{_ as C,a as S,d as U,r as _,e as g,c as D,o as z,f as s,i as e,h as v,n as u,p as Y,l as t,t as c,v as T,x as M,G as x,aQ as B}from"./index-B0qHf98Y.js";import E from"./index-CCXF19OR.js";import"./leave-Dd4WELmg.js";const i=m=>(T("data-v-313dfa72"),m=m(),M(),m),H={class:"nurse-log"},P=i(()=>e("h2",{class:"titleLog"},"老人意外情况记录表",-1)),A={class:"table-style"},O={style:{"text-align":"left",width:"33%"}},j={style:{"text-align":"left",width:"33%"}},$={style:{"text-align":"left",width:"33%"}},F={style:{"text-align":"left"}},G={style:{"text-align":"left"}},J={style:{"text-align":"left"}},Q={style:{"text-align":"left"}},q={style:{"text-align":"left"}},K={style:{"text-align":"left"}},W=i(()=>e("td",{style:{"text-align":"center"}},"意外发生时间",-1)),X={colspan:"2"},Z=i(()=>e("td",{style:{"text-align":"center"}},"意外发生地址",-1)),ee={colspan:"2"},te=i(()=>e("td",{style:{"text-align":"center"}},"伤情描述",-1)),le={colspan:"2"},oe=i(()=>e("td",{style:{"text-align":"center"}},"身体处置情况",-1)),ae={colspan:"2"},ne=i(()=>e("td",{style:{"text-align":"center"}},"生命体征情况",-1)),se={colspan:"2"},de=i(()=>e("td",{style:{"text-align":"center"}},"送往医院方式及医院名称",-1)),ie={colspan:"2"},re=i(()=>e("td",{style:{"text-align":"center"}},"通知监护人情况",-1)),ue={colspan:"2"},ce=i(()=>e("td",{style:{"text-align":"center"}},"发生意外情况描述",-1)),me={colspan:"2"},pe=i(()=>e("td",{style:{"text-align":"center"}},"意外处置参与人员",-1)),_e={colspan:"2"},ge=i(()=>e("td",{style:{"text-align":"center"}},"谈话记录",-1)),ye={colspan:"2"},he={style:{"text-align":"center","margin-top":"20px"}},ve={__name:"emergencyRescueRecord",setup(m){const f=S(),{proxy:y}=U(),{sys_user_sex:w}=y.useDict("sys_user_sex"),V=_(null),p=_(JSON.parse(localStorage.getItem("userInfo"))),a=_({}),l=_({}),b=()=>{const r={...a.value,nurseId:p.value.userId,nurseName:p.value.userName,recorderName:p.value.userName,elderName:l.value.elderName,elderId:l.value.id,age:l.value.age,bedId:l.value.bedId,bedName:l.value.bedName,buildingId:l.value.buildingId,buildingName:l.value.buildingName,careLevel:l.value.careLevel,checkInDate:l.value.checkInDate,floorId:l.value.floorId,floorName:l.value.floorName,gender:l.value.gender,nursingLevel:l.value.nursingLevel,roomId:l.value.roomId,roomName:l.value.roomName,roomNumber:l.value.roomNumber,abilityLevel:l.value.abilityLevel};if(!r.elderName){x.error("请选择老人");return}B(r).then(o=>{o.code===200?(x.success("提交成功"),y.$tab.closeOpenPage(),f.push("/work/nurseworkstation")):x.error(o.msg)})},N=()=>{y.$tab.closeOpenPage(),f.push("/work/nurseworkstation")},R=()=>{V.value.openElderSelect()},I=r=>{r&&(l.value={...r},a.value.paramedicName=p.value.userName)};return(r,o)=>{const h=g("el-button"),d=g("el-input"),k=g("dict-tag-span"),L=g("el-date-picker");return z(),D("div",H,[s(h,{type:"primary",onClick:N},{default:v(()=>[u(" 返回工作台 ")]),_:1}),P,e("table",A,[e("tbody",null,[e("tr",null,[e("td",O,[u("老人姓名:"),s(d,{modelValue:t(l).elderName,"onUpdate:modelValue":o[0]||(o[0]=n=>t(l).elderName=n),placeholder:"请选择老人",style:{width:"80%"},onClick:Y(R,["stop"]),readonly:""},null,8,["modelValue"])]),e("td",j,[u("老人性别: "),s(k,{options:t(w),value:t(l).gender,style:{width:"80%"}},null,8,["options","value"])]),e("td",$,"老人年龄："+c(t(l).age),1)]),e("tr",null,[e("td",F,"房间信息:"+c(t(l).buildingName&&t(l).roomNumber?t(l).buildingName+"-"+t(l).roomNumber:""),1),e("td",G,"入住时间："+c(t(l).checkInDate),1),e("td",J,"能力等级："+c(t(l).abilityLevel),1)]),e("tr",null,[e("td",Q,"护理等级:"+c(t(l).careLevel),1),e("td",q,"照护等级:"+c(t(l).nursingLevel),1),e("td",K,[u("当天护理员："),s(d,{modelValue:t(a).paramedicName,"onUpdate:modelValue":o[1]||(o[1]=n=>t(a).paramedicName=n),placeholder:"请输入",style:{width:"77%"}},null,8,["modelValue"])])]),e("tr",null,[W,e("td",X,[s(L,{modelValue:t(a).accidentTime,"onUpdate:modelValue":o[2]||(o[2]=n=>t(a).accidentTime=n),type:"datetime",placeholder:"请选择时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm",style:{width:"100%"}},null,8,["modelValue"])])]),e("tr",null,[Z,e("td",ee,[s(d,{placeholder:"请输入",modelValue:t(a).accidentLocation,"onUpdate:modelValue":o[3]||(o[3]=n=>t(a).accidentLocation=n)},null,8,["modelValue"])])]),e("tr",null,[te,e("td",le,[s(d,{placeholder:"请输入",modelValue:t(a).injuryCondition,"onUpdate:modelValue":o[4]||(o[4]=n=>t(a).injuryCondition=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[oe,e("td",ae,[s(d,{placeholder:"请输入",modelValue:t(a).physicalTreatment,"onUpdate:modelValue":o[5]||(o[5]=n=>t(a).physicalTreatment=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[ne,e("td",se,[s(d,{placeholder:"请输入",modelValue:t(a).vitalSigns,"onUpdate:modelValue":o[6]||(o[6]=n=>t(a).vitalSigns=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[de,e("td",ie,[s(d,{placeholder:"请输入",modelValue:t(a).hospitalTransport,"onUpdate:modelValue":o[7]||(o[7]=n=>t(a).hospitalTransport=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[re,e("td",ue,[s(d,{placeholder:"请输入",modelValue:t(a).guardianNotification,"onUpdate:modelValue":o[8]||(o[8]=n=>t(a).guardianNotification=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[ce,e("td",me,[s(d,{placeholder:"请输入",modelValue:t(a).accidentDescription,"onUpdate:modelValue":o[9]||(o[9]=n=>t(a).accidentDescription=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[pe,e("td",_e,[s(d,{placeholder:"请输入",modelValue:t(a).handlingParticipants,"onUpdate:modelValue":o[10]||(o[10]=n=>t(a).handlingParticipants=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])]),e("tr",null,[ge,e("td",ye,[s(d,{placeholder:"请输入",modelValue:t(a).conversationRecord,"onUpdate:modelValue":o[11]||(o[11]=n=>t(a).conversationRecord=n),type:"textarea",autosize:{minRows:4,maxRows:8}},null,8,["modelValue"])])])])]),e("div",he,[s(h,{type:"primary",onClick:b},{default:v(()=>[u("提交")]),_:1}),s(h,{onClick:N},{default:v(()=>[u("取消")]),_:1})]),s(E,{ref_key:"elderSelectComponent",ref:V,onSelectLerder:I},null,512)])}}},Ne=C(ve,[["__scopeId","data-v-313dfa72"]]);export{Ne as default};
