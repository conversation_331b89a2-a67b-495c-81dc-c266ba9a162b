import{_ as k,r as m,e as h,c as S,o as C,f as l,h as s,l as R,i as d,t as n,n as u,D as Y,v as B,x as E,aq as T,M as A}from"./index-B0qHf98Y.js";const G=v=>(B("data-v-a311ae8e"),v=v(),E(),v),J={class:"replace-consumables"},L=G(()=>d("div",{class:"headerTitle"},[d("h2",null,"更换易耗品记录表")],-1)),O={class:"elder-info"},U=["src"],$={class:"info"},j={class:"leaderName"},z={class:"processIndex"},F={class:"seqNo"},H={class:"dialog-footer"},K={__name:"ghyhpDetail",setup(v,{expose:N}){const y=m(JSON.parse(localStorage.getItem("userInfo"))),i=m([]),_=m(!1),g=m([]),x=({row:c,column:t,rowIndex:a,columnIndex:o})=>{const p=["avatar"],r=c[t.property];if(p.includes(t.property)||p.includes(t.label)||p.includes(t.type)){const e=i.value[a-1],w=i.value[a+1],b=r==null,q=e&&e.elderId===c.elderId&&(e[t.property]===r||b),M=w&&w.elderId===c.elderId&&(w[t.property]===r||b);if(q)return{rowspan:0,colspan:0};if(M){let I=1,f=a+1;for(;f<i.value.length&&i.value[f].elderId===c.elderId&&(i.value[f][t.property]===r||b);)I++,f++;if(I>1)return{rowspan:I,colspan:1}}}return{rowspan:1,colspan:1}};function D(c){let t={},a=1;return c.flatMap(o=>{(!o.serviceRecords||o.serviceRecords.length===0)&&(o.serviceRecords=[createEmptyVisit(0)]),t[o.id]||(t[o.id]=a++);const p=t[o.id];return o.serviceRecords.forEach((r,e)=>{r.seqNo=e+1}),o.serviceRecords.map((r,e)=>({...o,processIndex:p,...r,elderId:o.id,_serviceRecords:r}))})}const V=async(c,t)=>{const a=await T({status:1,nurseId:y.value.userId,serviceDate:t||A().format("YYYY-MM-DD")});g.value=a.data,i.value=D(g.value)};return N({openDialog:(c,t)=>{_.value=!0,V(c,t)}}),(c,t)=>{const a=h("el-table-column"),o=h("el-table"),p=h("el-button"),r=h("el-dialog");return C(),S("div",J,[l(r,{modelValue:R(_),"onUpdate:modelValue":t[1]||(t[1]=e=>Y(_)?_.value=e:null),title:"详情",width:"80%"},{footer:s(()=>[d("div",H,[l(p,{onClick:t[0]||(t[0]=e=>_.value=!1)},{default:s(()=>[u("返回")]),_:1})])]),default:s(()=>[L,l(o,{data:R(i),border:"",style:{width:"100%"},"span-method":x},{default:s(()=>[l(a,{label:"老人信息",width:"200",align:"center",prop:"avatar"},{default:s(e=>[d("div",O,[d("img",{src:e.row.avatar,alt:"老人头像",class:"avatar"},null,8,U),d("div",$,[d("p",j,n(e.row.elderName),1),d("p",null,n(e.row.roomNumber?e.row.roomNumber:"")+" "+n(e.row.roomNumber&&e.row.bedNumber?e.row.roomNumber+"-"+e.row.bedNumber:""),1),d("span",z,n(e.row.processIndex),1)])])]),_:1}),l(a,{prop:"seqNo",width:"80",align:"center"},{default:s(e=>[d("div",F,n(e.row._serviceRecords.seqNo),1)]),_:1}),l(a,{prop:"serviceDate",label:"服务日期",width:"230",align:"center"},{default:s(e=>[u(n(e.row._serviceRecords.serviceDate),1)]),_:1}),l(a,{prop:"supplyItem",label:"服务项目",width:"180",align:"center"},{default:s(e=>[u(n(e.row._serviceRecords.supplyItem),1)]),_:1}),l(a,{prop:"quantity",label:"数量",width:"130",align:"center"},{default:s(e=>[u(n(e.row._serviceRecords.quantity),1)]),_:1}),l(a,{prop:"price",label:"价格",width:"130",align:"center"},{default:s(e=>[u(" ￥"+n(e.row._serviceRecords.price),1)]),_:1}),l(a,{prop:"total",label:"总价",width:"130",align:"center"},{default:s(e=>[u(" ￥"+n(e.row._serviceRecords.total),1)]),_:1}),l(a,{prop:"remark",label:"备注","min-width":"140",align:"center"},{default:s(e=>[u(n(e.row._serviceRecords.remark),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}},W=k(K,[["__scopeId","data-v-a311ae8e"]]);export{W as default};
