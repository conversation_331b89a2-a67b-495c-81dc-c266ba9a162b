import{a as le,g as te}from"./roommanage-DBG5TiIR.js";import{l as oe}from"./tLiveRoom-DmSXfHxo.js";import{l as ae}from"./tLiveBed-B9bJPM9s.js";import{g as de,a as ne,b as re}from"./index-DCxZ1IEc.js";import{_ as ue,d as se,a as ie,r as v,F as me,e as _,I as pe,J as ge,l as o,c as h,o as f,f as e,i as m,h as t,K as C,L as V,j as N,n as B,a1 as _e,v as fe,x as be,E as ve,G as D}from"./index-B0qHf98Y.js";const H=x=>(fe("data-v-129e61a8"),x=x(),be(),x),he={class:"wrapBox"},ce={class:"room_info_top"},ye=H(()=>m("div",{class:"title_room"},[m("h3",null,"房间信息")],-1)),Ce={class:"room_form"},Ve={class:"room_info_top"},Ne=H(()=>m("div",{class:"title_room"},[m("h3",null,"人员交接信息")],-1)),we={class:"room_form"},Ie=H(()=>m("div",{class:"title_room_h4"},[m("span",null,"白班交接信息")],-1)),xe={class:"room_form"},Le={class:"title_room_h5"},Ue={class:"bottom_room_table"},qe=H(()=>m("div",{class:"title_room"},[m("h3",null,"床位交接详情")],-1)),Be={class:"add_room_table"},De={class:"footer_btn"},He={__name:"nurseAdd",setup(x){const{proxy:k}=se(),Y=ie(),T=v([]),L=v([]),$=v([]),w=v(!1),a=v({tNurseHandoverBedList:[]}),R=v([]),c=v({buildingId:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼层",trigger:"change"}],handoverDate:[{required:!0,message:"请选择交接日期",trigger:"change"}],dayNurse:[{required:!0,message:"请选择白班护士",trigger:"change"}],dayHandoverTime:[{required:!0,message:"请选择交接日期",trigger:"change"}],dayTotalCount:[{required:!0,message:"请输入",trigger:"blur"}],dayOutCount:[{required:!0,message:"请输入",trigger:"blur"}],dayLeaveCount:[{required:!0,message:"请输入",trigger:"blur"}],dayCriticalCount:[{required:!0,message:"请输入",trigger:"blur"}],dayDeathCount:[{required:!0,message:"请输入",trigger:"blur"}],nightNurse:[{required:!0,message:"请选择夜班护士",trigger:"change"}],nightHandoverTime:[{required:!0,message:"请选择交接日期",trigger:"change"}],nightTotalCount:[{required:!0,message:"请输入",trigger:"blur"}],nightOutCount:[{required:!0,message:"请输入",trigger:"blur"}],nightLeaveCount:[{required:!0,message:"请输入",trigger:"blur"}],nightCriticalCount:[{required:!0,message:"请输入",trigger:"blur"}],nightDeathCount:[{required:!0,message:"请输入",trigger:"blur"}],requiredSelect:{required:!0,message:"请选择",trigger:"change"},requiredInput:{required:!0,message:"请输入",trigger:"blur"}}),U=v([]),F=async r=>{L.value=[],a.value.tNurseHandoverBedList=[],a.value.floorId="";const d=await le(r);L.value=d.rows},M=async r=>{U.value=[],a.value.tNurseHandoverBedList=[{roomId:"",bedNumber:"",elderName:"",handoverContent1:"",handoverContent2:""}];const d=await oe({floorId:r});U.value=d.rows},S=async r=>{var p,g;r.bedNumber="",r.elderName="";const d=await ae({roomId:r.roomId});$.value=d.rows,r.roomName=((g=(p=U.value)==null?void 0:p.filter(n=>n.id===r.roomId)[0])==null?void 0:g.roomName)||""},A=async r=>{var g,n,u,s;r.elderName="";const d=(g=$.value.filter(I=>I.bedNumber===r.bedNumber)[0])==null?void 0:g.id,p=await de({bedId:d});r.elderName=((n=p.rows[0])==null?void 0:n.elderName)||"",r.elderAge=((u=p.rows[0])==null?void 0:u.age)||"",r.elderGender=((s=p.rows[0])==null?void 0:s.gender)||"",r.bedId=d},E=()=>{a.value.tNurseHandoverBedList.push({roomId:"",bedNumber:"",elderName:"",handoverContent1:"",handoverContent2:""})},G=r=>{ve.confirm("确认删除该条记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{a.value.tNurseHandoverBedList.splice(r,1),D.success("删除成功")})},K=()=>{console.log(a.value,"dayin"),w.value=!0,k.$refs.formRef.validate(async r=>{var d,p;if(r){let g=[];a.value.tNurseHandoverBedList.forEach(s=>{g.push({roomId:s.roomId,roomName:s.roomName,bedNumber:s.bedNumber,bedId:s.bedId,elderName:s.elderName,elderAge:s.elderAge,elderGender:s.elderGender,handoverContent1:s.handoverContent1,handoverContent2:s.handoverContent2})});let n={...a.value,buildingName:((d=T.value.find(s=>s.id===a.value.buildingId))==null?void 0:d.buildingName)||"",floorNumber:((p=L.value.find(s=>s.id===a.value.floorId))==null?void 0:p.floorNumber)||"",tNurseHandoverBedList:g||[]};console.log(n,"ff888888888888888888");const u=await ne(n);u.code===200?(D.success("提交成功"),k.$tab.closeOpenPage(),Y.go(-1)):D.error(u.msg),w.value=!1}else return w.value=!1,D.error("请填写完整信息"),!1})},P=()=>{k.$tab.closeOpenPage(),Y.go(-1)},j=async()=>{const r=await re({roleKeys:["nurse"],pageSize:1e3});R.value=r.rows||[]},z=async()=>{const r=await te();T.value=r.rows||[]};function J(){z(),j()}return me(()=>{J()}),(r,d)=>{const p=_("el-option"),g=_("el-select"),n=_("el-form-item"),u=_("el-col"),s=_("el-date-picker"),I=_("el-row"),b=_("el-input-number"),Q=_("Moon"),W=_("el-icon"),q=_("el-button"),y=_("el-table-column"),O=_("el-input"),X=_("el-table"),Z=_("el-form"),ee=pe("loading");return ge((f(),h("div",he,[e(Z,{inline:!0,model:o(a),"label-width":"100px",rules:o(c),ref:"formRef"},{default:t(()=>[m("div",ce,[ye,m("div",Ce,[e(I,{gutter:24},{default:t(()=>[e(u,{span:8},{default:t(()=>[e(n,{label:"楼栋信息",prop:"buildingId"},{default:t(()=>[e(g,{modelValue:o(a).buildingId,"onUpdate:modelValue":d[0]||(d[0]=l=>o(a).buildingId=l),style:{width:"200px"},onChange:F},{default:t(()=>[(f(!0),h(C,null,V(o(T),l=>(f(),N(p,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"楼栋层数",prop:"floorId"},{default:t(()=>[e(g,{modelValue:o(a).floorId,"onUpdate:modelValue":d[1]||(d[1]=l=>o(a).floorId=l),disabled:!o(a).buildingId,style:{width:"200px"},onChange:M},{default:t(()=>[(f(!0),h(C,null,V(o(L),l=>(f(),N(p,{key:l.value,label:l.floorName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"交接日期",prop:"handoverDate"},{default:t(()=>[e(s,{modelValue:o(a).handoverDate,"onUpdate:modelValue":d[2]||(d[2]=l=>o(a).handoverDate=l),type:"date",placeholder:"选择日期",style:{width:"200px"},"value-format":"YYYY-MM-DD hh:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),m("div",Ve,[Ne,m("div",we,[Ie,e(I,{gutter:24},{default:t(()=>[e(u,{span:8},{default:t(()=>[e(n,{label:"白班护士",prop:"dayNurse"},{default:t(()=>[e(g,{modelValue:o(a).dayNurse,"onUpdate:modelValue":d[3]||(d[3]=l=>o(a).dayNurse=l),style:{width:"200px"}},{default:t(()=>[(f(!0),h(C,null,V(o(R),l=>(f(),N(p,{key:l.userId,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"交接日期",prop:"dayHandoverTime"},{default:t(()=>[e(s,{modelValue:o(a).dayHandoverTime,"onUpdate:modelValue":d[4]||(d[4]=l=>o(a).dayHandoverTime=l),type:"date",placeholder:"选择日期",style:{width:"200px"},"value-format":"YYYY-MM-DD hh:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"交接人数",prop:"dayTotalCount"},{default:t(()=>[e(b,{modelValue:o(a).dayTotalCount,"onUpdate:modelValue":d[5]||(d[5]=l=>o(a).dayTotalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"外出人数",prop:"dayOutCount"},{default:t(()=>[e(b,{modelValue:o(a).dayOutCount,"onUpdate:modelValue":d[6]||(d[6]=l=>o(a).dayOutCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"离院人数",prop:"dayLeaveCount"},{default:t(()=>[e(b,{modelValue:o(a).dayLeaveCount,"onUpdate:modelValue":d[7]||(d[7]=l=>o(a).dayLeaveCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"病危人数",prop:"dayCriticalCount"},{default:t(()=>[e(b,{modelValue:o(a).dayCriticalCount,"onUpdate:modelValue":d[8]||(d[8]=l=>o(a).dayCriticalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"死亡人数",prop:"dayDeathCount"},{default:t(()=>[e(b,{modelValue:o(a).dayDeathCount,"onUpdate:modelValue":d[9]||(d[9]=l=>o(a).dayDeathCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),m("div",xe,[m("div",Le,[m("span",null,[e(W,{color:"#FF00FF"},{default:t(()=>[e(Q)]),_:1}),B(" 夜班交接信息")])]),e(I,{gutter:24},{default:t(()=>[e(u,{span:8},{default:t(()=>[e(n,{label:"夜班护士",prop:"nightNurse"},{default:t(()=>[e(g,{modelValue:o(a).nightNurse,"onUpdate:modelValue":d[10]||(d[10]=l=>o(a).nightNurse=l),style:{width:"200px"}},{default:t(()=>[(f(!0),h(C,null,V(o(R),l=>(f(),N(p,{key:l.userId,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"交接日期",prop:"nightHandoverTime"},{default:t(()=>[e(s,{modelValue:o(a).nightHandoverTime,"onUpdate:modelValue":d[11]||(d[11]=l=>o(a).nightHandoverTime=l),type:"date",placeholder:"选择日期",style:{width:"200px"},"value-format":"YYYY-MM-DD hh:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"交接人数",prop:"nightTotalCount"},{default:t(()=>[e(b,{modelValue:o(a).nightTotalCount,"onUpdate:modelValue":d[12]||(d[12]=l=>o(a).nightTotalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"外出人数",prop:"nightOutCount"},{default:t(()=>[e(b,{modelValue:o(a).nightOutCount,"onUpdate:modelValue":d[13]||(d[13]=l=>o(a).nightOutCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"离院人数",prop:"nightLeaveCount"},{default:t(()=>[e(b,{modelValue:o(a).nightLeaveCount,"onUpdate:modelValue":d[14]||(d[14]=l=>o(a).nightLeaveCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"病危人数",prop:"nightCriticalCount"},{default:t(()=>[e(b,{modelValue:o(a).nightCriticalCount,"onUpdate:modelValue":d[15]||(d[15]=l=>o(a).nightCriticalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(n,{label:"死亡人数",prop:"nightDeathCount"},{default:t(()=>[e(b,{modelValue:o(a).nightDeathCount,"onUpdate:modelValue":d[16]||(d[16]=l=>o(a).nightDeathCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),m("div",Ue,[qe,m("div",Be,[e(q,{type:"primary",onClick:E,icon:"plus",disabled:!o(a).floorId},{default:t(()=>[B("添加床位")]),_:1},8,["disabled"])]),e(X,{data:o(a).tNurseHandoverBedList,style:{width:"100%"},border:"",disabled:!o(a).floorId},{default:t(()=>[e(y,{label:"房间号","min-width":"180",align:"center"},{default:t(l=>[e(n,{prop:`tNurseHandoverBedList.${l.$index}.roomId`,rules:o(c).requiredSelect,style:{width:"100%"}},{default:t(()=>[e(g,{modelValue:l.row.roomId,"onUpdate:modelValue":i=>l.row.roomId=i,placeholder:"请选择",onChange:i=>S(l.row)},{default:t(()=>[(f(!0),h(C,null,V(o(U),i=>(f(),N(p,{key:i.id,label:i.roomName,value:i.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(y,{label:"床位号",width:"180",align:"center"},{default:t(l=>[e(n,{prop:`tNurseHandoverBedList.${l.$index}.bedNumber`,rules:o(c).requiredSelect,style:{width:"100%"}},{default:t(()=>[e(g,{modelValue:l.row.bedNumber,"onUpdate:modelValue":i=>l.row.bedNumber=i,placeholder:"请选择床位",disabled:!l.row.roomId,loading:o(w),onChange:i=>A(l.row)},{default:t(()=>[(f(!0),h(C,null,V(o($),i=>(f(),N(p,{key:i.id,label:i.bedNumber,value:i.bedNumber},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","loading","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(y,{label:"老人姓名",width:"180",align:"center"},{default:t(l=>[e(n,{prop:`tNurseHandoverBedList.${l.$index}.elderName`,rules:o(c).requiredInput,style:{width:"100%"}},{default:t(()=>[e(O,{modelValue:l.row.elderName,"onUpdate:modelValue":i=>l.row.elderName=i,disabled:!l.row.bedNumber},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop","rules"])]),_:1}),e(y,{label:"白班交接内容","min-width":"300",align:"center"},{default:t(l=>[e(n,{prop:`tNurseHandoverBedList.${l.$index}.handoverContent1`,rules:o(c).requiredInput,style:{width:"100%"}},{default:t(()=>[e(O,{modelValue:l.row.handoverContent1,"onUpdate:modelValue":i=>l.row.handoverContent1=i,placeholder:"请输入白班交接内容",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(y,{label:"夜班交接内容","min-width":"300",align:"center"},{default:t(l=>[e(n,{prop:`tNurseHandoverBedList.${l.$index}.handoverContent2`,rules:o(c).requiredInput,style:{width:"100%"}},{default:t(()=>[e(O,{modelValue:l.row.handoverContent2,"onUpdate:modelValue":i=>l.row.handoverContent2=i,placeholder:"请输入夜班交接内容",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(y,{label:"操作",width:"180",align:"center",fixed:"right"},{default:t(l=>[e(q,{type:"danger",icon:o(_e),circle:"",onClick:i=>G(l.$index)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data","disabled"])])]),_:1},8,["model","rules"]),m("div",De,[e(q,{type:"primary",onClick:K},{default:t(()=>[B("提交")]),_:1}),e(q,{onClick:P},{default:t(()=>[B("取消")]),_:1})])])),[[ee,o(w)]])}}},Ye=ue(He,[["__scopeId","data-v-129e61a8"]]);export{Ye as default};
