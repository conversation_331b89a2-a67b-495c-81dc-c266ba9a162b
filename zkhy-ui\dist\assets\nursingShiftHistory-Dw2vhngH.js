import{_ as se,d as de,r as c,a as ue,C as ce,N as pe,F as me,e as i,I as fe,c as p,o as s,f as e,h as n,i as d,k as ge,K as h,t as k,l as r,ao as _e,J as he,L as I,j as x,n as b,v as be,x as ve,E as ye,G as z}from"./index-B0qHf98Y.js";import{g as Ne}from"./buildmanage-CIqJJJF0.js";import{d as we,c as ke}from"./index-DCxZ1IEc.js";import{a as xe,g as Ve}from"./roommanage-DBG5TiIR.js";const F=v=>(be("data-v-3eb2de23"),v=v(),ve(),v),Ce={class:"app-container"},De={class:"treeStyle"},Se=F(()=>d("div",{class:"panel-header"},[d("span",{class:"title"},"楼层信息")],-1)),Be={class:"rightTable"},Le={class:"btn-group"},Ie={key:0},ze=F(()=>d("i",{class:"el-icon-check",style:{color:"green"}},null,-1)),Fe={key:1},Te=F(()=>d("i",{class:"el-icon-loading",style:{color:"orange"}},null,-1)),He={class:"pagination-container"},Re={__name:"nursingShiftHistory",setup(v){const{proxy:T}=de(),V=c(!1),{handover_status:M}=T.useDict("handover_status"),P=ue();c(!1),c(""),c(!1);const C=c([]),D=c([]),S=c([]),Y={children:"children",label:"label"},$=c(""),H=c(0),R=c([]),j=ce({queryParams:{pageNum:1,pageSize:10,status:void 0,handoverDate:void 0,dayNurse:void 0,nightNurse:void 0,buildingName:void 0,floorNumber:void 0},form:{},rules:{}}),{queryParams:t,form:Ue,rules:qe}=pe(j);function O(a){a.type=="building"?(t.value.buildingName=a.label,t.value.buildingId=a.id,U(a.label)):a.type=="floor"&&(t.value.floorNumber=a.floorNumber,t.value.floorId=a.id),N()}const Q=a=>{t.value.pageNum=a,y()},A=a=>{P.push({path:"/nurseShiftChangeReport/nurseShiftDetail/add/0/view",query:{id:a.id}})},G=a=>{ye.confirm("确定删除该交接班吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{we(a.id).then(o=>{o.code===200?(z({type:"success",message:"删除成功!"}),N()):z({type:"error",message:o.msg})})})},J=async()=>{try{const a=await Ne();a.code===200&&(R.value=a.data.map(o=>{var g;return{...o,children:(g=o.children)==null?void 0:g.map(f=>({...f,children:[]}))}}))}catch(a){console.error("获取房间树形数据失败:",a),z.error("获取楼栋信息失败")}},y=()=>{V.value=!0,ke({...t.value}).then(a=>{C.value=a.rows,H.value=a.total}).finally(()=>{V.value=!1})};function N(){C.value=[],t.pageNum=1,y()}const U=async a=>{S.value=[],t.value.floorNumber="";const o=D.value.filter(f=>f.buildingName==a),g=await xe(o[0].id);S.value=g.rows};function K(){T.$refs.queryForm.resetFields(),t.value={pageNum:1,pageSize:10,status:void 0,handoverDate:void 0,dayNurse:void 0,nightNurse:void 0,buildingName:void 0,buildingId:void 0,floorNumber:void 0,floorId:void 0},N()}const W=a=>{t.value.pageSize=a,y()};function X(){J(),Z(),y()}const Z=async()=>{const a=await Ve();D.value=a.rows||[]};return me(()=>{X()}),(a,o)=>{const g=i("OfficeBuilding"),f=i("el-icon"),ee=i("List"),le=i("el-tree"),q=i("el-col"),ae=i("el-date-picker"),_=i("el-form-item"),E=i("el-input"),B=i("el-option"),L=i("el-select"),w=i("el-button"),te=i("el-form"),u=i("el-table-column"),ne=i("el-table"),oe=i("el-pagination"),re=i("el-row"),ie=fe("loading");return s(),p("div",Ce,[e(re,{gutter:24},{default:n(()=>[e(q,{span:4},{default:n(()=>[d("div",De,[Se,e(le,{data:R.value,props:Y,"node-key":"id","highlight-current":"","default-expand-all":"","expand-on-click-node":!1,"current-node-key":$.value,onNodeClick:O},{default:n(({node:l,data:m})=>[m.type==="building"?(s(),p(h,{key:0},[e(f,{style:{color:"#409EFF","margin-right":"4px"}},{default:n(()=>[e(g)]),_:1}),d("span",null,k(m.label),1)],64)):m.type==="floor"?(s(),p(h,{key:1},[e(f,{style:{color:"#67C23A","margin-right":"4px"}},{default:n(()=>[e(ee)]),_:1}),d("span",null,k(m.label),1)],64)):m.type==="bed"?(s(),p(h,{key:2},[e(f,{style:{color:"#1890ff","margin-right":"4px"}},{default:n(()=>[e(r(_e))]),_:1}),d("span",null,k(m.label),1)],64)):ge("",!0)]),_:1},8,["data","current-node-key"])])]),_:1}),e(q,{span:20},{default:n(()=>[d("div",Be,[e(te,{inline:!0,model:r(t),class:"search-form","label-width":"100px",ref:"queryForm"},{default:n(()=>[e(_,{label:"交接班日期",prop:"handoverDate"},{default:n(()=>[e(ae,{modelValue:r(t).handoverDate,"onUpdate:modelValue":o[0]||(o[0]=l=>r(t).handoverDate=l),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"160px"},clearable:""},null,8,["modelValue"])]),_:1}),e(_,{label:"白班交接人",prop:"dayNurse"},{default:n(()=>[e(E,{modelValue:r(t).dayNurse,"onUpdate:modelValue":o[1]||(o[1]=l=>r(t).dayNurse=l),placeholder:"请输入交接人",style:{width:"160px"},clearable:""},null,8,["modelValue"])]),_:1}),e(_,{label:"夜班交接人",prop:"nightNurse"},{default:n(()=>[e(E,{modelValue:r(t).nightNurse,"onUpdate:modelValue":o[2]||(o[2]=l=>r(t).nightNurse=l),placeholder:"请输入交接人",style:{width:"160px"},clearable:""},null,8,["modelValue"])]),_:1}),e(_,{label:"楼栋信息",prop:"buildingName"},{default:n(()=>[e(L,{modelValue:r(t).buildingName,"onUpdate:modelValue":o[3]||(o[3]=l=>r(t).buildingName=l),style:{width:"160px"},placeholder:"全部",clearable:"",onChange:U},{default:n(()=>[(s(!0),p(h,null,I(D.value,l=>(s(),x(B,{key:l.value,label:l.buildingName,value:l.buildingName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"楼栋层数",prop:"floorNumber"},{default:n(()=>[e(L,{modelValue:r(t).floorNumber,"onUpdate:modelValue":o[4]||(o[4]=l=>r(t).floorNumber=l),style:{width:"160px"},placeholder:"全部",clearable:"",disabled:!r(t).buildingName},{default:n(()=>[(s(!0),p(h,null,I(S.value,l=>(s(),x(B,{key:l.value,label:l.floorName,value:l.floorName},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(_,{label:"状 态",prop:"status"},{default:n(()=>[e(L,{modelValue:r(t).status,"onUpdate:modelValue":o[5]||(o[5]=l=>r(t).status=l),style:{width:"160px"},placeholder:"全部",clearable:""},{default:n(()=>[(s(!0),p(h,null,I(r(M),l=>(s(),x(B,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d("div",Le,[e(w,{type:"primary",icon:"Search",onClick:N,style:{"margin-left":"60px"}},{default:n(()=>[b("查询")]),_:1}),e(w,{icon:"Refresh",onClick:K},{default:n(()=>[b("重置")]),_:1})])]),_:1},8,["model"]),he((s(),x(ne,{data:C.value,style:{width:"100%"},border:"",stripe:""},{default:n(()=>[e(u,{prop:"index",label:"序号",width:"60",align:"center"},{default:n(l=>[d("span",null,k(l.$index+1),1)]),_:1}),e(u,{prop:"handoverDate",label:"交接日期",width:"100",align:"center"}),e(u,{prop:"floorNumber",label:"楼栋层数",width:"80",align:"center"}),e(u,{prop:"buildingName",label:"楼栋信息",width:"120",align:"center"}),e(u,{prop:"dayNurse",label:"白班护士",width:"100",align:"center"}),e(u,{prop:"dayHandoverTime2",label:"白班班次","min-width":"180",align:"center"}),e(u,{prop:"nightNurse",label:"夜班护士",width:"100",align:"center"}),e(u,{prop:"nightHandoverTime2",label:"夜班班次","min-width":"180",align:"center"}),e(u,{prop:"status",label:"状态","min-width":"180",align:"center"},{default:n(l=>[l.row.status==="complete"?(s(),p("span",Ie,[ze,b(" 已完成 ")])):(s(),p("span",Fe,[Te,b(" 未完成 ")]))]),_:1}),e(u,{label:"操作",width:"180",fixed:"right",align:"center"},{default:n(l=>[e(w,{type:"primary",onClick:m=>A(l.row),link:""},{default:n(()=>[b("详情")]),_:2},1032,["onClick"]),e(w,{type:"primary",onClick:m=>G(l.row),link:""},{default:n(()=>[b("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ie,V.value]]),d("div",He,[e(oe,{layout:"prev, pager, next, sizes, jumper",total:H.value,background:"","page-size":r(t).pageSize,"current-page":r(t).pageNum,onSizeChange:W,onCurrentChange:Q,"page-sizes":[10,20,50]},null,8,["total","page-size","current-page"])])])]),_:1})]),_:1})])}}},$e=se(Re,[["__scopeId","data-v-3eb2de23"]]);export{$e as default};
