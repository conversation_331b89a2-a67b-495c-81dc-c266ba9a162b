import{X as L,B as me,d as ce,r as f,C as fe,N as _e,e as p,I as E,c as k,o as m,J as y,f as e,O as H,l as t,h as l,m as $,K as A,L as J,j as b,D as q,n as d,i as B,t as i,k as X}from"./index-B0qHf98Y.js";function ge(C){return L({url:"/monitor/operlog/list",method:"get",params:C})}function be(C){return L({url:"/monitor/operlog/"+C,method:"delete"})}function ve(){return L({url:"/monitor/operlog/clean",method:"delete"})}const he={class:"app-container"},ye={key:0},we={key:1},Ve={class:"dialog-footer"},ke=me({name:"Operlog"}),Te=Object.assign(ke,{setup(C){const{proxy:_}=ce(),{sys_oper_type:N,sys_common_status:P}=_.useDict("sys_oper_type","sys_common_status"),K=f([]),w=f(!1),I=f(!0),x=f(!0),O=f([]);f(!0);const M=f(!0),U=f(0);f("");const V=f([]),R=f({prop:"operTime",order:"descending"}),G=fe({form:{},queryParams:{pageNum:1,pageSize:10,operIp:void 0,title:void 0,operName:void 0,businessType:void 0,status:void 0}}),{queryParams:n,form:r}=_e(G);function v(){I.value=!0,ge(_.addDateRange(n.value,V.value)).then(u=>{K.value=u.rows,U.value=u.total,I.value=!1})}function W(u,a){return _.selectDictLabel(N.value,u.businessType)}function T(){n.value.pageNum=1,v()}function Z(){V.value=[],_.resetForm("queryRef"),n.value.pageNum=1,_.$refs.operlogRef.sort(R.value.prop,R.value.order)}function ee(u){O.value=u.map(a=>a.operId),M.value=!u.length}function le(u,a,S){n.value.orderByColumn=u.prop,n.value.isAsc=u.order,v()}function te(u){w.value=!0,r.value=u}function oe(u){const a=u.operId||O.value;_.$modal.confirm('是否确认删除日志编号为"'+a+'"的数据项?').then(function(){return be(a)}).then(()=>{v(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ae(){_.$modal.confirm("是否确认清空所有操作日志数据项?").then(function(){return ve()}).then(()=>{v(),_.$modal.msgSuccess("清空成功")}).catch(()=>{})}function ne(){_.download("monitor/operlog/export",{...n.value},`config_${new Date().getTime()}.xlsx`)}return v(),(u,a)=>{const S=p("el-input"),s=p("el-form-item"),Y=p("el-option"),j=p("el-select"),re=p("el-date-picker"),h=p("el-button"),z=p("el-form"),c=p("el-col"),se=p("right-toolbar"),F=p("el-row"),g=p("el-table-column"),Q=p("dict-tag"),ue=p("el-table"),de=p("pagination"),pe=p("el-dialog"),D=E("hasPermi"),ie=E("loading");return m(),k("div",he,[y(e(z,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(s,{label:"操作地址",prop:"operIp"},{default:l(()=>[e(S,{modelValue:t(n).operIp,"onUpdate:modelValue":a[0]||(a[0]=o=>t(n).operIp=o),placeholder:"请输入操作地址",clearable:"",style:{width:"240px"},onKeyup:$(T,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"系统模块",prop:"title"},{default:l(()=>[e(S,{modelValue:t(n).title,"onUpdate:modelValue":a[1]||(a[1]=o=>t(n).title=o),placeholder:"请输入系统模块",clearable:"",style:{width:"240px"},onKeyup:$(T,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"操作人员",prop:"operName"},{default:l(()=>[e(S,{modelValue:t(n).operName,"onUpdate:modelValue":a[2]||(a[2]=o=>t(n).operName=o),placeholder:"请输入操作人员",clearable:"",style:{width:"240px"},onKeyup:$(T,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"类型",prop:"businessType"},{default:l(()=>[e(j,{modelValue:t(n).businessType,"onUpdate:modelValue":a[3]||(a[3]=o=>t(n).businessType=o),placeholder:"操作类型",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),k(A,null,J(t(N),o=>(m(),b(Y,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"状态",prop:"status"},{default:l(()=>[e(j,{modelValue:t(n).status,"onUpdate:modelValue":a[4]||(a[4]=o=>t(n).status=o),placeholder:"操作状态",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),k(A,null,J(t(P),o=>(m(),b(Y,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"操作时间",style:{width:"308px"}},{default:l(()=>[e(re,{modelValue:t(V),"onUpdate:modelValue":a[5]||(a[5]=o=>q(V)?V.value=o:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),e(s,null,{default:l(()=>[e(h,{type:"primary",icon:"Search",onClick:T},{default:l(()=>[d("搜索")]),_:1}),e(h,{icon:"Refresh",onClick:Z},{default:l(()=>[d("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[H,t(x)]]),e(F,{gutter:10,class:"mb8"},{default:l(()=>[e(c,{span:1.5},{default:l(()=>[y((m(),b(h,{type:"danger",plain:"",icon:"Delete",disabled:t(M),onClick:oe},{default:l(()=>[d("删除")]),_:1},8,["disabled"])),[[D,["monitor:operlog:remove"]]])]),_:1}),e(c,{span:1.5},{default:l(()=>[y((m(),b(h,{type:"danger",plain:"",icon:"Delete",onClick:ae},{default:l(()=>[d("清空")]),_:1})),[[D,["monitor:operlog:remove"]]])]),_:1}),e(c,{span:1.5},{default:l(()=>[y((m(),b(h,{type:"warning",plain:"",icon:"Download",onClick:ne},{default:l(()=>[d("导出")]),_:1})),[[D,["monitor:operlog:export"]]])]),_:1}),e(se,{showSearch:t(x),"onUpdate:showSearch":a[6]||(a[6]=o=>q(x)?x.value=o:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),y((m(),b(ue,{ref:"operlogRef",data:t(K),onSelectionChange:ee,"default-sort":t(R),onSortChange:le},{default:l(()=>[e(g,{type:"selection",width:"50",align:"center"}),e(g,{label:"日志编号",align:"center",prop:"operId"}),e(g,{label:"系统模块",align:"center",prop:"title","show-overflow-tooltip":!0}),e(g,{label:"操作类型",align:"center",prop:"businessType"},{default:l(o=>[e(Q,{options:t(N),value:o.row.businessType},null,8,["options","value"])]),_:1}),e(g,{label:"操作人员",align:"center",width:"110",prop:"operName","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"操作地址",align:"center",prop:"operIp",width:"130","show-overflow-tooltip":!0}),e(g,{label:"操作状态",align:"center",prop:"status"},{default:l(o=>[e(Q,{options:t(P),value:o.row.status},null,8,["options","value"])]),_:1}),e(g,{label:"操作日期",align:"center",prop:"operTime",width:"180",sortable:"custom","sort-orders":["descending","ascending"]},{default:l(o=>[B("span",null,i(u.parseTime(o.row.operTime)),1)]),_:1}),e(g,{label:"消耗时间",align:"center",prop:"costTime",width:"110","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]},{default:l(o=>[B("span",null,i(o.row.costTime)+"毫秒",1)]),_:1}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[y((m(),b(h,{link:"",type:"primary",icon:"View",onClick:Ce=>te(o.row,o.index)},{default:l(()=>[d("详细")]),_:2},1032,["onClick"])),[[D,["monitor:operlog:query"]]])]),_:1})]),_:1},8,["data","default-sort"])),[[ie,t(I)]]),y(e(de,{total:t(U),page:t(n).pageNum,"onUpdate:page":a[7]||(a[7]=o=>t(n).pageNum=o),limit:t(n).pageSize,"onUpdate:limit":a[8]||(a[8]=o=>t(n).pageSize=o),onPagination:v},null,8,["total","page","limit"]),[[H,t(U)>0]]),e(pe,{title:"操作日志详细",modelValue:t(w),"onUpdate:modelValue":a[10]||(a[10]=o=>q(w)?w.value=o:null),width:"800px","append-to-body":""},{footer:l(()=>[B("div",Ve,[e(h,{onClick:a[9]||(a[9]=o=>w.value=!1)},{default:l(()=>[d("关 闭")]),_:1})])]),default:l(()=>[e(z,{model:t(r),"label-width":"100px"},{default:l(()=>[e(F,null,{default:l(()=>[e(c,{span:12},{default:l(()=>[e(s,{label:"操作模块："},{default:l(()=>[d(i(t(r).title)+" / "+i(W(t(r))),1)]),_:1}),e(s,{label:"登录信息："},{default:l(()=>[d(i(t(r).operName)+" / "+i(t(r).operIp)+" / "+i(t(r).operLocation),1)]),_:1})]),_:1}),e(c,{span:12},{default:l(()=>[e(s,{label:"请求地址："},{default:l(()=>[d(i(t(r).operUrl),1)]),_:1}),e(s,{label:"请求方式："},{default:l(()=>[d(i(t(r).requestMethod),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(s,{label:"操作方法："},{default:l(()=>[d(i(t(r).method),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(s,{label:"请求参数："},{default:l(()=>[d(i(t(r).operParam),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(s,{label:"返回参数："},{default:l(()=>[d(i(t(r).jsonResult),1)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(s,{label:"操作状态："},{default:l(()=>[t(r).status===0?(m(),k("div",ye,"正常")):t(r).status===1?(m(),k("div",we,"失败")):X("",!0)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(s,{label:"消耗时间："},{default:l(()=>[d(i(t(r).costTime)+"毫秒",1)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(s,{label:"操作时间："},{default:l(()=>[d(i(u.parseTime(t(r).operTime)),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[t(r).status===1?(m(),b(s,{key:0,label:"异常信息："},{default:l(()=>[d(i(t(r).errorMsg),1)]),_:1})):X("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{Te as default};
