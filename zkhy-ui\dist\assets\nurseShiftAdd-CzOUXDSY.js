import{a as de,g as ne}from"./roommanage-DBG5TiIR.js";import{l as re}from"./tLiveRoom-DmSXfHxo.js";import{l as ue}from"./tLiveBed-B9bJPM9s.js";import{g as se,a as ie,b as me}from"./index-DCxZ1IEc.js";import{_ as pe,d as ge,a as fe,r as c,M as _e,F as ve,e as g,I as be,J as ce,l as o,c as h,o as m,f as e,i as f,h as t,n as k,k as S,K as x,L as H,j as y,a1 as he,v as ye,x as Ce,E as Ve,G as q}from"./index-B0qHf98Y.js";const M=U=>(ye("data-v-5f746af7"),U=U(),Ce(),U),Ne={class:"wrapBox"},we={class:"room_info_top"},Ie=M(()=>f("div",{class:"title_room"},[f("h3",null,"人员交接信息")],-1)),xe={class:"room_form"},He=M(()=>f("div",{class:"title_room_h4"},[f("span",null,"白班交接信息")],-1)),Le={key:0,class:"room_form"},ke={class:"title_room_h5"},Ue={class:"room_info_top"},Be=M(()=>f("div",{class:"title_room"},[f("h3",null,"工作交接详情")],-1)),Te={class:"room_form"},qe={class:"bottom_room_table"},De={class:"footer_btn"},$e={__name:"nurseShiftAdd",setup(U){const{proxy:D}=ge(),$=fe(),Y=c(JSON.parse(localStorage.getItem("userInfo"))),R=c([]),B=c([]),O=c([]),L=c(!1),a=c({handoverDate:_e().format("YYYY-MM-DD"),dayNurse:Y.value.userName,tNurseHandoverBedList:[]}),F=c([]),V=c({buildingId:[{required:!0,message:"请选择楼栋",trigger:"change"}],floorId:[{required:!0,message:"请选择楼层",trigger:"change"}],handoverDate:[{required:!0,message:"请选择交接日期",trigger:"change"}],dayHandoverTime:[{required:!0,message:"请选择交接日期",trigger:"change"}],dayTotalCount:[{required:!0,message:"请输入",trigger:"blur"}],dayOutCount:[{required:!0,message:"请输入",trigger:"blur"}],dayLeaveCount:[{required:!0,message:"请输入",trigger:"blur"}],dayCriticalCount:[{required:!0,message:"请输入",trigger:"blur"}],dayDeathCount:[{required:!0,message:"请输入",trigger:"blur"}],nightNurse:[{required:!0,message:"请选择夜班护士",trigger:"change"}],nightHandoverTime:[{required:!0,message:"请选择交接班次",trigger:"change"}],nightTotalCount:[{required:!0,message:"请输入",trigger:"blur"}],nightOutCount:[{required:!0,message:"请输入",trigger:"blur"}],nightLeaveCount:[{required:!0,message:"请输入",trigger:"blur"}],nightCriticalCount:[{required:!0,message:"请输入",trigger:"blur"}],nightDeathCount:[{required:!0,message:"请输入",trigger:"blur"}],requiredSelect:{required:!0,message:"请选择",trigger:"change"},requiredInput:{required:!0,message:"请输入",trigger:"blur"}}),T=c([]),A=async u=>{B.value=[],a.value.tNurseHandoverBedList=[],a.value.floorId="";const d=await de(u);B.value=d.rows},E=async u=>{T.value=[],a.value.tNurseHandoverBedList=[{roomId:"",bedNumber:"",elderName:"",handoverContent1:"",handoverContent2:"",deletable:!1}];const d=await re({floorId:u});T.value=d.rows},G=async u=>{var p,n;u.bedNumber="",u.elderName="";const d=await ue({roomId:u.roomId});O.value=d.rows,u.roomName=((n=(p=T.value)==null?void 0:p.filter(_=>_.id===u.roomId)[0])==null?void 0:n.roomName)||""},j=async u=>{var n,_,C,r;u.elderName="";const d=(n=O.value.filter(b=>b.bedNumber===u.bedNumber)[0])==null?void 0:n.id,p=await se({bedId:d});u.elderName=((_=p.rows[0])==null?void 0:_.elderName)||"",u.elderAge=((C=p.rows[0])==null?void 0:C.age)||"",u.elderGender=((r=p.rows[0])==null?void 0:r.gender)||"",u.bedId=d},J=()=>{a.value.tNurseHandoverBedList.push({roomId:"",bedNumber:"",elderName:"",handoverContent1:"",handoverContent2:"",deletable:!0})},K=u=>{Ve.confirm("确认删除该条记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{a.value.tNurseHandoverBedList.splice(u,1),q.success("删除成功")})},P=()=>{console.log(a.value,"dayin"),L.value=!0,D.$refs.formRef.validate(async u=>{var d,p,n,_;if(u){let C=[];a.value.tNurseHandoverBedList.forEach(i=>{C.push({roomId:i.roomId,roomName:i.roomName,bedNumber:i.bedNumber,bedId:i.bedId,elderName:i.elderName,elderAge:i.elderAge,elderGender:i.elderGender,handoverContent1:i.handoverContent1,handoverContent2:i.handoverContent2})});let r={...a.value,dayHandoverTime2:((d=a.value.dayHandoverTime2)==null?void 0:d.length)>0?a.value.dayHandoverTime2[0]+"~"+a.value.dayHandoverTime2[1]:void 0,nightHandoverTime2:((p=a.value.nightHandoverTime2)==null?void 0:p.length)>0?a.value.nightHandoverTime2[0]+"~"+a.value.nightHandoverTime2[1]:void 0,buildingName:((n=R.value.find(i=>i.id===a.value.buildingId))==null?void 0:n.buildingName)||"",floorNumber:((_=B.value.find(i=>i.id===a.value.floorId))==null?void 0:_.floorNumber)||"",tNurseHandoverBedList:C||[]};const b=await ie(r);b.code===200?(q.success("提交成功"),D.$tab.closeOpenPage(),$.go(-1)):q.error(b.msg),L.value=!1}else return L.value=!1,q.error("请填写完整信息"),!1})},z=()=>{D.$tab.closeOpenPage(),$.go(-1)},Q=async()=>{const u=await me({roleKeys:["nurse"],pageSize:1e3});F.value=u.rows||[]},W=async()=>{const u=await ne();R.value=u.rows||[]};function X(){W(),Q()}const Z=()=>{$.push("/work/nurseworkstation")};return ve(()=>{X()}),(u,d)=>{const p=g("el-button"),n=g("el-col"),_=g("el-row"),C=g("el-date-picker"),r=g("el-form-item"),b=g("el-input"),i=g("el-time-picker"),N=g("el-option"),w=g("el-select"),v=g("el-input-number"),ee=g("Moon"),le=g("el-icon"),I=g("el-table-column"),te=g("el-table"),ae=g("el-form"),oe=be("loading");return ce((m(),h("div",Ne,[e(_,{gutter:24},{default:t(()=>[e(n,{span:8},{default:t(()=>[e(p,{type:"primary",onClick:Z},{default:t(()=>[k("返回工作台")]),_:1})]),_:1})]),_:1}),e(ae,{inline:!0,model:o(a),"label-width":"100px",rules:o(V),ref:"formRef"},{default:t(()=>[e(_,{gutter:24,class:"formRow"},{default:t(()=>[e(n,{span:8},{default:t(()=>[e(r,{label:"交接日期",prop:"handoverDate"},{default:t(()=>[e(C,{modelValue:o(a).handoverDate,"onUpdate:modelValue":d[0]||(d[0]=l=>o(a).handoverDate=l),type:"date",placeholder:"选择日期",style:{width:"200px"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),f("div",we,[Ie,f("div",xe,[He,e(_,{gutter:24},{default:t(()=>[e(n,{span:8},{default:t(()=>[e(r,{label:"白班护士",prop:"dayNurse"},{default:t(()=>[e(b,{modelValue:o(a).dayNurse,"onUpdate:modelValue":d[1]||(d[1]=l=>o(a).dayNurse=l),style:{width:"200px"},placeholder:"请输入白班护士",readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"交班班次",prop:"dayHandoverTime2"},{default:t(()=>[e(i,{modelValue:o(a).dayHandoverTime2,"onUpdate:modelValue":d[2]||(d[2]=l=>o(a).dayHandoverTime2=l),"is-range":"","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"200px"},format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"接班护士",prop:"relievingNurse"},{default:t(()=>[e(w,{modelValue:o(a).relievingNurse,"onUpdate:modelValue":d[3]||(d[3]=l=>o(a).relievingNurse=l),style:{width:"200px"},clearable:""},{default:t(()=>[(m(!0),h(x,null,H(o(F),l=>(m(),y(N,{key:l.userId,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"交接人数",prop:"dayTotalCount"},{default:t(()=>[e(v,{modelValue:o(a).dayTotalCount,"onUpdate:modelValue":d[4]||(d[4]=l=>o(a).dayTotalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"外出人数",prop:"dayOutCount"},{default:t(()=>[e(v,{modelValue:o(a).dayOutCount,"onUpdate:modelValue":d[5]||(d[5]=l=>o(a).dayOutCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"离院人数",prop:"dayLeaveCount"},{default:t(()=>[e(v,{modelValue:o(a).dayLeaveCount,"onUpdate:modelValue":d[6]||(d[6]=l=>o(a).dayLeaveCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"病危人数",prop:"dayCriticalCount"},{default:t(()=>[e(v,{modelValue:o(a).dayCriticalCount,"onUpdate:modelValue":d[7]||(d[7]=l=>o(a).dayCriticalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"死亡人数",prop:"dayDeathCount"},{default:t(()=>[e(v,{modelValue:o(a).dayDeathCount,"onUpdate:modelValue":d[8]||(d[8]=l=>o(a).dayDeathCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),o(a).relievingNurse?(m(),h("div",Le,[f("div",ke,[f("span",null,[e(le,{color:"#FF00FF"},{default:t(()=>[e(ee)]),_:1}),k(" 夜班交接信息")])]),e(_,{gutter:24},{default:t(()=>[e(n,{span:8},{default:t(()=>[e(r,{label:"夜班护士",prop:"nightNurse"},{default:t(()=>[e(w,{modelValue:o(a).nightNurse,"onUpdate:modelValue":d[9]||(d[9]=l=>o(a).nightNurse=l),style:{width:"200px"}},{default:t(()=>[(m(!0),h(x,null,H(o(F),l=>(m(),y(N,{key:l.userId,label:l.nickName,value:l.nickName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"交班班次",prop:"nightHandoverTime2"},{default:t(()=>[e(i,{modelValue:o(a).nightHandoverTime2,"onUpdate:modelValue":d[10]||(d[10]=l=>o(a).nightHandoverTime2=l),"is-range":"","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"200px"},format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"交接人数",prop:"nightTotalCount"},{default:t(()=>[e(v,{modelValue:o(a).nightTotalCount,"onUpdate:modelValue":d[11]||(d[11]=l=>o(a).nightTotalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"外出人数",prop:"nightOutCount"},{default:t(()=>[e(v,{modelValue:o(a).nightOutCount,"onUpdate:modelValue":d[12]||(d[12]=l=>o(a).nightOutCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"离院人数",prop:"nightLeaveCount"},{default:t(()=>[e(v,{modelValue:o(a).nightLeaveCount,"onUpdate:modelValue":d[13]||(d[13]=l=>o(a).nightLeaveCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"病危人数",prop:"nightCriticalCount"},{default:t(()=>[e(v,{modelValue:o(a).nightCriticalCount,"onUpdate:modelValue":d[14]||(d[14]=l=>o(a).nightCriticalCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"死亡人数",prop:"nightDeathCount"},{default:t(()=>[e(v,{modelValue:o(a).nightDeathCount,"onUpdate:modelValue":d[15]||(d[15]=l=>o(a).nightDeathCount=l),min:0,style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])):S("",!0)]),f("div",Ue,[Be,f("div",Te,[e(_,{gutter:24},{default:t(()=>[e(n,{span:8},{default:t(()=>[e(r,{label:"楼栋信息",prop:"buildingId"},{default:t(()=>[e(w,{modelValue:o(a).buildingId,"onUpdate:modelValue":d[16]||(d[16]=l=>o(a).buildingId=l),style:{width:"200px"},onChange:A},{default:t(()=>[(m(!0),h(x,null,H(o(R),l=>(m(),y(N,{key:l.value,label:l.buildingName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(n,{span:8},{default:t(()=>[e(r,{label:"楼栋层数",prop:"floorId"},{default:t(()=>[e(w,{modelValue:o(a).floorId,"onUpdate:modelValue":d[17]||(d[17]=l=>o(a).floorId=l),disabled:!o(a).buildingId,style:{width:"200px"},onChange:E},{default:t(()=>[(m(!0),h(x,null,H(o(B),l=>(m(),y(N,{key:l.value,label:l.floorName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:8,style:{display:"flex","justify-content":"flex-end"}},{default:t(()=>[e(r,null,{default:t(()=>[e(p,{type:"primary",onClick:J,icon:"plus",disabled:!o(a).floorId},{default:t(()=>[k("添加床位")]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1})])]),f("div",qe,[e(te,{data:o(a).tNurseHandoverBedList,style:{width:"100%"},border:"",disabled:!o(a).floorId},{default:t(()=>[e(I,{label:"房间号","min-width":"180",align:"center"},{default:t(l=>[e(r,{prop:`tNurseHandoverBedList.${l.$index}.roomId`,rules:o(V).requiredSelect,style:{width:"100%"}},{default:t(()=>[e(w,{modelValue:l.row.roomId,"onUpdate:modelValue":s=>l.row.roomId=s,placeholder:"请选择",onChange:s=>G(l.row)},{default:t(()=>[(m(!0),h(x,null,H(o(T),s=>(m(),y(N,{key:s.id,label:s.roomName,value:s.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(I,{label:"床位号",width:"180",align:"center"},{default:t(l=>[e(r,{prop:`tNurseHandoverBedList.${l.$index}.bedNumber`,rules:o(V).requiredSelect,style:{width:"100%"}},{default:t(()=>[e(w,{modelValue:l.row.bedNumber,"onUpdate:modelValue":s=>l.row.bedNumber=s,placeholder:"请选择床位",disabled:!l.row.roomId,loading:o(L),onChange:s=>j(l.row)},{default:t(()=>[(m(!0),h(x,null,H(o(O),s=>(m(),y(N,{key:s.id,label:s.bedNumber,value:s.bedNumber},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","loading","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(I,{label:"老人姓名",width:"180",align:"center"},{default:t(l=>[e(r,{prop:`tNurseHandoverBedList.${l.$index}.elderName`,rules:o(V).requiredInput,style:{width:"100%"}},{default:t(()=>[e(b,{modelValue:l.row.elderName,"onUpdate:modelValue":s=>l.row.elderName=s,disabled:!l.row.bedNumber},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop","rules"])]),_:1}),e(I,{label:"白班交接内容","min-width":"300",align:"center"},{default:t(l=>[e(r,{prop:`tNurseHandoverBedList.${l.$index}.handoverContent1`,rules:o(V).requiredInput,style:{width:"100%"}},{default:t(()=>[e(b,{modelValue:l.row.handoverContent1,"onUpdate:modelValue":s=>l.row.handoverContent1=s,placeholder:"请输入白班交接内容",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),o(a).relievingNurse?(m(),y(I,{key:0,label:"夜班交接内容","min-width":"300",align:"center"},{default:t(l=>[e(r,{prop:`tNurseHandoverBedList.${l.$index}.handoverContent2`,rules:o(V).requiredInput,style:{width:"100%"}},{default:t(()=>[e(b,{modelValue:l.row.handoverContent2,"onUpdate:modelValue":s=>l.row.handoverContent2=s,placeholder:"请输入夜班交接内容",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1})):S("",!0),e(I,{label:"操作",width:"180",align:"center",fixed:"right"},{default:t(l=>[l.row.deletable?(m(),y(p,{key:0,type:"danger",icon:o(he),circle:"",onClick:s=>K(l.$index)},null,8,["icon","onClick"])):S("",!0)]),_:1})]),_:1},8,["data","disabled"])])]),_:1},8,["model","rules"]),f("div",De,[e(p,{type:"primary",onClick:P},{default:t(()=>[k("提交")]),_:1}),e(p,{onClick:z},{default:t(()=>[k("取消")]),_:1})])])),[[oe,o(L)]])}}},Ye=pe($e,[["__scopeId","data-v-5f746af7"]]);export{Ye as default};
