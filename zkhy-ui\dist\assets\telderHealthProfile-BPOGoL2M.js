import{X as t}from"./index-B0qHf98Y.js";function r(e){return t({url:"/elderinfo/elderHealthEvent/list",method:"get",params:e})}function d(e){return t({url:"/elderinfo/elderHealthEvent/"+e,method:"get"})}function a(e){return t({url:"/elderinfo/elderHealthEvent",method:"post",data:e})}function n(e){return t({url:"/elderinfo/elderHealthEvent",method:"put",data:e})}function o(e){return t({url:"/elderinfo/elderHealthEvent/"+e,method:"delete"})}function u(e){return t({url:"/elderinfo/elderHealthProfile/list",method:"get",params:e})}function i(e){return t({url:"/elderinfo/elderHealthProfile",method:"post",data:e})}function h(e){return t({url:"/elderinfo/elderHealthProfile",method:"put",data:e})}export{i as a,n as b,a as c,o as d,u as e,d as g,r as l,h as u};
