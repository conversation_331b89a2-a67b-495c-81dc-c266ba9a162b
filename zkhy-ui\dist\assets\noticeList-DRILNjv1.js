import{_ as z,r,F as H,e as n,c as U,f as a,h as l,i as s,p as E,n as v,t as d,G as b,H as j,o as I}from"./index-B0qHf98Y.js";import{g as $,a as q}from"./notice-DJ0irKZd.js";const F={class:"notice-list-container"},G={class:"notice-list-header"},P={class:"notice-list-pagination"},A={class:"notice-detail"},J={class:"notice-detail-title"},K={class:"notice-detail-info"},O=["innerHTML"],Q={__name:"noticeList",setup(W){const h=r([]),y=r(0),e=r({title:"",creator:"",dateRange:[],pageNum:1,pageSize:10}),u=r(!1),g=r(!1),c=r({noticeTitle:"",noticeContent:"",createTime:"",createBy:""}),p=async()=>{try{const i={pageNum:e.value.pageNum,pageSize:e.value.pageSize,noticeTitle:e.value.title,createBy:e.value.creator,createTimeStart:e.value.dateRange&&e.value.dateRange[0]?e.value.dateRange[0]:void 0,createTimeEnd:e.value.dateRange&&e.value.dateRange[1]?e.value.dateRange[1]:void 0},t=await $(i);t.code===200?(h.value=t.rows,y.value=t.total):b.error(t.msg||"获取公告列表失败")}catch{b.error("获取公告列表失败")}},V=()=>{e.value.pageNum=1,p()},k=()=>{e.value.title="",e.value.creator="",e.value.dateRange=[],e.value.pageNum=1,p()},T=i=>{e.value.pageNum=i,p()},N=i=>j(i).format("YYYY-MM-DD HH:mm:ss"),C=async i=>{u.value=!0,g.value=!0;try{const t=await q(i.noticeId);t.code===200&&(c.value=t.data)}catch{b.error("获取公告详情失败")}finally{g.value=!1}};return H(()=>{p()}),(i,t)=>{const w=n("el-input"),m=n("el-form-item"),R=n("el-date-picker"),f=n("el-button"),M=n("el-form"),_=n("el-table-column"),x=n("el-link"),D=n("el-table"),L=n("el-pagination"),S=n("el-card"),Y=n("el-skeleton"),B=n("el-dialog");return I(),U("div",F,[a(S,null,{default:l(()=>[s("div",G,[a(M,{inline:!0,model:e.value,class:"notice-list-form",onSubmit:t[3]||(t[3]=E(()=>{},["prevent"]))},{default:l(()=>[a(m,{label:"公告标题"},{default:l(()=>[a(w,{modelValue:e.value.title,"onUpdate:modelValue":t[0]||(t[0]=o=>e.value.title=o),placeholder:"请输入公告标题",clearable:""},null,8,["modelValue"])]),_:1}),a(m,{label:"创建者"},{default:l(()=>[a(w,{modelValue:e.value.creator,"onUpdate:modelValue":t[1]||(t[1]=o=>e.value.creator=o),placeholder:"请输入创建者",clearable:""},null,8,["modelValue"])]),_:1}),a(m,{label:"创建时间"},{default:l(()=>[a(R,{modelValue:e.value.dateRange,"onUpdate:modelValue":t[2]||(t[2]=o=>e.value.dateRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1}),a(m,null,{default:l(()=>[a(f,{type:"primary",onClick:V},{default:l(()=>[v("查询")]),_:1}),a(f,{onClick:k},{default:l(()=>[v("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),a(D,{data:h.value,border:"",stripe:"",style:{width:"100%","margin-top":"20px"}},{default:l(()=>[a(_,{type:"index",label:"序号",width:"60",align:"center"}),a(_,{prop:"noticeTitle",label:"公告标题","min-width":"200"},{default:l(o=>[a(x,{type:"primary",onClick:X=>C(o.row)},{default:l(()=>[v(d(o.row.noticeTitle),1)]),_:2},1032,["onClick"])]),_:1}),a(_,{prop:"createBy",label:"创建者","min-width":"120"}),a(_,{prop:"createTime",label:"创建时间","min-width":"160"},{default:l(o=>[s("span",null,d(N(o.row.createTime)),1)]),_:1})]),_:1},8,["data"]),s("div",P,[a(L,{background:"",layout:"total, prev, pager, next, jumper",total:y.value,"page-size":e.value.pageSize,"current-page":e.value.pageNum,onCurrentChange:T},null,8,["total","page-size","current-page"])])]),_:1}),a(B,{modelValue:u.value,"onUpdate:modelValue":t[5]||(t[5]=o=>u.value=o),title:"公告详情",width:"60%","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[a(f,{onClick:t[4]||(t[4]=o=>u.value=!1)},{default:l(()=>[v("关闭")]),_:1})]),default:l(()=>[a(Y,{rows:6,loading:g.value,animated:""},{default:l(()=>[s("div",A,[s("h2",J,d(c.value.noticeTitle),1),s("div",K,[s("span",null,"发布时间："+d(c.value.createTime),1),s("span",null,"发布人："+d(c.value.createBy),1)]),s("div",{class:"notice-detail-content",innerHTML:c.value.noticeContent},null,8,O)])]),_:1},8,["loading"])]),_:1},8,["modelValue"])])}}},te=z(Q,[["__scopeId","data-v-7e232cdf"]]);export{te as default};
