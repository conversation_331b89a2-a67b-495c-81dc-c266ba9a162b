import{g as le}from"./tReception-DwUHUj_X.js";import{X as D,_ as oe,B as ae,u as ne,d as ie,r as y,C as de,N as ue,e as d,c as P,o as _,f as s,h as t,i as e,t as u,l as n,Y as H,n as v,j as b,K as U,L as q,k,v as ce,x as re,M as _e}from"./index-B0qHf98Y.js";function pe(r){return D({url:"/eldersystem/visit",method:"post",data:r})}function me(r){return D({url:"/eldersystem/visit",method:"put",data:r})}function ve(r){return D({url:"/eldersystem/visit/list/"+r,method:"get"})}const fe="/assets/reMsg-Bm6EefSe.png",o=r=>(ce("data-v-443cb9b8"),r=r(),re(),r),he={class:"app-container"},ge=o(()=>e("div",{class:"olderbaseTitle",width:"100%"},[e("h2",null,"老人基本信息")],-1)),ye=o(()=>e("td",{class:"tbTitle"},"老 人 姓 名:",-1)),be={class:"tbvalue"},Ve=o(()=>e("td",{class:"tbTitle"},"老 人 性 别:",-1)),xe={class:"tbvalue"},Te=o(()=>e("td",{class:"tbTitle"},"老 人 年 龄:",-1)),ke={class:"tbvalue"},Ce=o(()=>e("td",{class:"tbTitle"},"老 人 电 话:",-1)),Re={class:"tbvalue"},Ie=o(()=>e("td",{class:"tbTitle"},"咨 询 时 间:",-1)),we={class:"tbvalue"},Ye=o(()=>e("td",{class:"tbTitle"},"咨 询 类 型:",-1)),Pe={class:"tbvalue"},De=o(()=>e("td",{class:"tbTitle"}," 渠          道: ",-1)),Me={class:"tbvalue"},Be=o(()=>e("td",{class:"tbTitle"},"接  待  人:",-1)),Ne={class:"tbvalue"},Le=o(()=>e("td",{class:"tbTitle"},"咨询人姓名:",-1)),Se={class:"tbvalue"},He=o(()=>e("td",{class:"tbTitle"},"咨询人电话:",-1)),Ue={class:"tbvalue"},qe=o(()=>e("td",{class:"tbTitle"},"与老人关系:",-1)),$e={class:"tbvalue"},je=o(()=>e("td",null,null,-1)),Oe=o(()=>e("td",null,null,-1)),ze=o(()=>e("h2",null,"回访记录",-1)),Ae=o(()=>e("div",{class:"divider"},null,-1)),Ee=o(()=>e("span",null,"暂无回访记录",-1)),Fe={class:"resLeftCss"},Ge={class:"resLeftCssDate"},Ke={class:"resLeftCssDate"},Xe={style:{margin:"5px 20px"},class:"container"},Je={style:{margin:"10px 10px"}},Qe={class:"resContent"},We={style:{"margin-top":"10px"}},Ze=o(()=>e("span",{class:"resContent"},"回访结果：",-1)),et={class:"dc-dialog-header-16"},tt=o(()=>e("div",{style:{width:"100px",height:"3px","background-color":"rgb(50, 109, 234)"}},null,-1)),st={class:"dialog-footer"},lt=ae({name:"receptionVisit"}),ot=Object.assign(lt,{setup(r){var L;const C=ne(),{proxy:V}=ie(),{sys_yes_no:at,sys_user_sex:M,consultation_type:nt,relationship_elderly:it,channel_type:$,follow_results:j,consultation_type2:O}=V.useDict("sys_yes_no","sys_user_sex","consultation_type","relationship_elderly","channel_type","follow_results","consultation_type2");console.log(M);const c=y([]),B=y(),R=y("");y([]);const f=y(!1),z=de({form:{},queryParams:{pageNum:1,pageSize:20,receptionId:void 0},rules:{visitContent:[{required:!0,message:"回访内容不能为空",trigger:"blur"}],visitResult:[{required:!0,message:"回访结果不能为空",trigger:"blur"}],ReceptionValue:[{required:!0,message:"参数键值不能为空",trigger:"blur"}]}}),{queryParams:A,form:i,rules:E}=ue(z),x=y([]);function I(p){console.log(p,"id11111111111"),le(p).then(a=>{console.log(a,"getReception"),c.value=a.data}),B.value=p,A.value.receptionId=p,ve(p).then(a=>{console.log(a,"listVisit"),x.value=a.data})}function F(){i.value={},N(),f.value=!0,R.value="新增"}function G(){f.value=!1,N()}function K(){i.value.receptionId=B.value,V.$refs.visitRef.validate(p=>{p&&(i.value.id!=null?me(i.value).then(a=>{var m;V.$modal.msgSuccess("修改成功"),f.value=!1,I(((m=C.params)==null?void 0:m.id)??-1)}):pe(i.value).then(a=>{var m;V.$modal.msgSuccess("新增成功"),f.value=!1,I(((m=C.params)==null?void 0:m.id)??-1)}))})}function N(){i.value.visitTime=_e().format("YYYY-MM-DD HH:mm:ss")}return I(((L=C.params)==null?void 0:L.id)??-1),(p,a)=>{const m=d("dict-tag-span"),X=d("dict-tag"),h=d("el-col"),g=d("el-row"),w=d("el-button"),J=d("el-image"),Y=d("el-tag"),S=d("el-input"),T=d("el-form-item"),Q=d("el-radio"),W=d("el-radio-group"),Z=d("el-date-picker"),ee=d("el-form"),te=d("el-dialog");return _(),P("div",he,[s(g,null,{default:t(()=>[s(h,{span:24},{default:t(()=>[ge,e("table",null,[e("tr",null,[ye,e("td",be,u(c.value.elderName),1),Ve,e("td",xe,[s(m,{options:n(M),value:c.value.elderGender},null,8,["options","value"])]),Te,e("td",ke,u(c.value.elderAge),1),Ce,e("td",Re,u(c.value.elderPhone),1)]),e("tr",null,[Ie,e("td",we,u(n(H)(c.value.consultTime,"{y}-{m}-{d} {h}:{m}")),1),Ye,e("td",Pe,[s(X,{options:n(O),value:c.value.consultType},null,8,["options","value"]),v(" "+u(c.value.consultType),1)]),De,e("td",Me,[s(m,{options:n($),value:c.value.channel},null,8,["options","value"])]),Be,e("td",Ne,u(c.value.receptionPerson),1)]),e("tr",null,[Le,e("td",Se,u(c.value.consultPersonName),1),He,e("td",Ue,u(c.value.consultPersonPhone),1),qe,e("td",$e,u(c.value.relation),1),je,Oe])])]),_:1})]),_:1}),s(g,{style:{"margin-top":"10px"}},{default:t(()=>[s(h,{span:24},{default:t(()=>[s(g,{align:"center",class:"resTitleBtn"},{default:t(()=>[ze,s(w,{icon:"Plus",style:{},type:"primary",onClick:F},{default:t(()=>[v("新增回访")]),_:1})]),_:1}),Ae,!x.value||x.value.length<1?(_(),b(g,{key:0,style:{"justify-content":"space-evenly",color:"red"}},{default:t(()=>[Ee]),_:1})):(_(!0),P(U,{key:1},q(x.value,(l,se)=>(_(),b(g,{key:se,gutter:10,style:{"border-bottom":"1px solid rgb(242, 242, 242)"}},{default:t(()=>[s(h,{span:3.5},{default:t(()=>[e("div",Fe,[e("div",Ge,u(n(H)(l.visitTime,"{y}-{m}-{d} {h}:{m}")),1),e("div",Ke,u(l.visitPerson),1)])]),_:2},1024),s(h,{span:1.5},{default:t(()=>[e("div",Xe,[s(J,{src:n(fe),class:"resImage resImageOverlay"},null,8,["src"])])]),_:1}),s(h,{span:19},{default:t(()=>[e("div",Je,[e("div",null,[e("span",Qe,"回访内容："+u(l.visitContent),1)]),e("div",We,[Ze,l.visitResult=="01"?(_(),b(Y,{key:0,type:"primary"},{default:t(()=>[v("跟进中")]),_:1})):k("",!0),l.visitResult=="02"?(_(),b(Y,{key:1,type:"success"},{default:t(()=>[v("已入住")]),_:1})):k("",!0),l.visitResult=="03"?(_(),b(Y,{key:2,type:"info"},{default:t(()=>[v("已放弃")]),_:1})):k("",!0)])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),s(te,{modelValue:f.value,"onUpdate:modelValue":a[5]||(a[5]=l=>f.value=l),title:R.value,"append-to-body":"",width:"40%"},{header:t(()=>[e("h2",et,u(R.value)+"回访",1),tt]),footer:t(()=>[e("div",st,[s(w,{type:"primary",onClick:K},{default:t(()=>[v("确 定")]),_:1}),s(w,{onClick:G},{default:t(()=>[v("取 消")]),_:1})])]),default:t(()=>[s(g,{gutter:20},{default:t(()=>[s(h,{span:23},{default:t(()=>[s(ee,{ref:"visitRef",model:n(i),rules:n(E),"label-width":"100px"},{default:t(()=>[k("",!0),s(T,{label:"回访内容",prop:"visitContent"},{default:t(()=>[s(S,{modelValue:n(i).visitContent,"onUpdate:modelValue":a[1]||(a[1]=l=>n(i).visitContent=l),placeholder:"请输入内容",rows:"6",type:"textarea"},null,8,["modelValue"])]),_:1}),s(T,{label:"回访结果",prop:"visitResult"},{default:t(()=>[s(W,{modelValue:n(i).visitResult,"onUpdate:modelValue":a[2]||(a[2]=l=>n(i).visitResult=l)},{default:t(()=>[(_(!0),P(U,null,q(n(j),l=>(_(),b(Q,{key:l.value,value:l.value},{default:t(()=>[v(u(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(T,{label:"回访人",prop:"visitPerson"},{default:t(()=>[s(S,{modelValue:n(i).visitPerson,"onUpdate:modelValue":a[3]||(a[3]=l=>n(i).visitPerson=l),placeholder:"请输入回访人"},null,8,["modelValue"])]),_:1}),s(T,{label:"回访时间",prop:"visitTime"},{default:t(()=>[s(Z,{modelValue:n(i).visitTime,"onUpdate:modelValue":a[4]||(a[4]=l=>n(i).visitTime=l),clearable:"",format:"YYYY-MM-DD HH:mm",placeholder:"请选择回访时间",size:"large",style:{width:"100%"},type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1},8,["modelValue","title"])])}}}),ct=oe(ot,[["__scopeId","data-v-443cb9b8"]]);export{ct as default};
