import{g as W,a as X}from"./roommanage-DBG5TiIR.js";import{h as Y}from"./index-2bfkpdNb.js";import{_ as Z,r as b,d as ee,F as te,e as u,I as le,c as n,o as t,f as l,J as ae,k as oe,h as a,i as r,K as _,L as g,j as y,n as N,t as d,v as ne,x as ie}from"./index-B0qHf98Y.js";const P=w=>(ne("data-v-e312797e"),w=w(),ie(),w),se={class:"drug-receive-record-container"},re={class:"button-group",style:{"text-align":"right"}},de={class:"medication-names-container"},ce={class:"medication-names"},ue={class:"medication-detail-popup"},pe={key:1,class:"no-plan"},me={class:"medication-detail-popup"},_e={key:1,class:"no-plan"},ge={class:"daily-medication-popup"},ve={class:"time-slot"},be=P(()=>r("div",{class:"time-title",style:{color:"rgb(112, 182, 3)"}},"早晨:",-1)),ye={key:1,class:"no-plan"},fe={class:"time-slot"},he=P(()=>r("div",{class:"time-title",style:{color:"rgb(99, 0, 191)"}},"中午:",-1)),Ne={key:1,class:"no-plan"},we={class:"time-slot"},ke=P(()=>r("div",{class:"time-title",style:{color:"rgb(245, 154, 35)"}},"晚上:",-1)),xe={key:1,class:"no-plan"},Ve={key:1,class:"no-plan"},Se={key:0,class:"pagination-container"},Ie={__name:"index",setup(w){const S=b(!1),{proxy:F}=ee(),{inventory_results:Ce}=F.useDict("inventory_results"),D=b([]),I=b([]),o=b({pageSize:10,pageNum:1}),R=b([]),C=b(0),T=()=>{console.log("查询",o.value),o.value.pageNum=1,f()},j=s=>s.map(i=>i.medicationName).join("，"),z=s=>({0:"餐前",1:"餐中",2:"餐后",3:"睡前"})[s]||"",E=()=>{o.value={pageSize:10,pageNum:1},f()},H=async s=>{I.value=[],o.value.floorId="";const i=await X(s);I.value=i.rows},J=s=>{o.value.pageSize=s,f()},K=s=>{o.value.pageNum=s,f()},f=async()=>{S.value=!0;const s=await Y({...o.value});R.value=s.rows||[],C.value=s.total||0,S.value=!1},O=async()=>{const s=await W();D.value=s.rows||[]};return te(()=>{O(),f()}),(s,i)=>{const B=u("el-input"),k=u("el-form-item"),x=u("el-option"),U=u("el-select"),L=u("el-button"),$=u("el-form"),p=u("el-table-column"),M=u("el-popover"),q=u("el-tag"),A=u("el-table"),G=u("el-pagination"),Q=le("loading");return t(),n("div",se,[l($,{inline:!0,model:o.value,class:"search-form","label-width":"100px"},{default:a(()=>[l(k,{label:"老人姓名",prop:"elderName"},{default:a(()=>[l(B,{modelValue:o.value.elderName,"onUpdate:modelValue":i[0]||(i[0]=e=>o.value.elderName=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),l(k,{label:"楼栋信息",prop:"buildingId"},{default:a(()=>[l(U,{modelValue:o.value.buildingId,"onUpdate:modelValue":i[1]||(i[1]=e=>o.value.buildingId=e),placeholder:"全部",style:{width:"200px"},clearable:"",onChange:H},{default:a(()=>[l(x,{label:"全部",value:""}),(t(!0),n(_,null,g(D.value,e=>(t(),y(x,{key:e.value,label:e.buildingName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"楼栋层数",prop:"floorId"},{default:a(()=>[l(U,{modelValue:o.value.floorId,"onUpdate:modelValue":i[2]||(i[2]=e=>o.value.floorId=e),placeholder:"全部",style:{width:"200px"},clearable:"",disabled:!o.value.buildingId},{default:a(()=>[l(x,{label:"全部",value:""}),(t(!0),n(_,null,g(I.value,e=>(t(),y(x,{key:e.value,label:e.floorName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),l(k,{label:"房间号",prop:"roomNumber"},{default:a(()=>[l(B,{modelValue:o.value.roomNumber,"onUpdate:modelValue":i[3]||(i[3]=e=>o.value.roomNumber=e),placeholder:"请输入",style:{width:"200px"},clearable:""},null,8,["modelValue"])]),_:1}),r("div",re,[l(L,{type:"primary",onClick:T,icon:"search"},{default:a(()=>[N("查询")]),_:1}),l(L,{onClick:E,icon:"refresh"},{default:a(()=>[N("重置")]),_:1})])]),_:1},8,["model"]),ae((t(),y(A,{data:R.value,border:"",style:{width:"100%"}},{default:a(()=>[l(p,{prop:"id",label:"序号",width:"60",align:"center"},{default:a(e=>[N(d(e.$index+1),1)]),_:1}),l(p,{prop:"elderName",label:"老人姓名",align:"center"}),l(p,{prop:"age",label:"老人年龄",align:"center",width:"80"}),l(p,{prop:"floorNumber",label:"楼层信息",align:"center"}),l(p,{prop:"roomNumber",label:"房间号",align:"center"}),l(p,{prop:"bedNumber",label:"床位号",align:"center"}),l(p,{prop:"buildingName",label:"楼栋信息",align:"center"}),l(p,{prop:"medicationName",label:"药品",align:"center","min-width":"140"},{default:a(e=>[r("div",de,[e.row.currentMedicines&&e.row.currentMedicines.length>0?(t(),y(M,{key:0,effect:"light",placement:"top",trigger:"click"},{reference:a(()=>[r("span",ce,d(j(e.row.currentMedicines)),1)]),default:a(()=>[r("div",ue,[(t(!0),n(_,null,g(e.row.currentMedicines,(m,v)=>(t(),n("div",{key:v,class:"medication-item"},[r("span",null,d(m.medicationName),1)]))),128))])]),_:2},1024)):(t(),n("span",pe," 暂无 "))])]),_:1}),l(p,{prop:"medicationStatus",label:"存量预警",align:"center"},{default:a(e=>{var m;return[((m=e.row.emergencyMedicines)==null?void 0:m.length)>0?(t(),y(M,{key:0,placement:"top",trigger:"click"},{reference:a(()=>[l(q,{type:"danger",style:{cursor:"pointer"}},{default:a(()=>[N("告急")]),_:1})]),default:a(()=>[r("div",me,[(t(!0),n(_,null,g(e.row.emergencyMedicines,(v,V)=>(t(),n("div",{key:V,class:"medication-item"},[r("span",null,d(v.medicationName),1)]))),128))])]),_:2},1024)):(t(),n("span",_e,"暂无"))]}),_:1}),l(p,{prop:"medicationStatus",label:"当日用药",align:"center"},{default:a(e=>[e.row.dailyRecords?(t(),y(M,{key:0,placement:"left",width:"200",trigger:"click"},{reference:a(()=>[l(L,{link:"",type:"primary"},{default:a(()=>[N("查看详情")]),_:1})]),default:a(()=>{var m,v,V;return[r("div",ge,[r("div",ve,[be,(m=e.row.dailyRecords.morning)!=null&&m.length?(t(!0),n(_,{key:0},g(e.row.dailyRecords.morning,(c,h)=>(t(),n("div",{key:"morning-"+h,class:"medication-item"},d(c.medicineName)+" "+d(z(c.timePeriodPrecise)||"")+" "+d(c.dosage),1))),128)):(t(),n("div",ye,"暂无用药计划"))]),r("div",fe,[he,(v=e.row.dailyRecords.noon)!=null&&v.length?(t(!0),n(_,{key:0},g(e.row.dailyRecords.noon,(c,h)=>(t(),n("div",{key:"noon-"+h,class:"medication-item"},d(c.medicineName)+" "+d(z(c.timePeriodPrecise)||"")+" "+d(c.dosage),1))),128)):(t(),n("div",Ne,"暂无用药计划"))]),r("div",we,[ke,(V=e.row.dailyRecords.evening)!=null&&V.length?(t(!0),n(_,{key:0},g(e.row.dailyRecords.evening,(c,h)=>(t(),n("div",{key:"evening-"+h,class:"medication-item"},d(c.medicineName)+" "+d(z(c.timePeriodPrecise)||"")+" "+d(c.dosage),1))),128)):(t(),n("div",xe,"暂无用药计划"))])])]}),_:2},1024)):(t(),n("span",Ve,"暂无"))]),_:1})]),_:1},8,["data"])),[[Q,S.value]]),C.value>0?(t(),n("div",Se,[l(G,{background:"","current-page":o.value.pageNum,"onUpdate:currentPage":i[4]||(i[4]=e=>o.value.pageNum=e),"page-size":o.value.pageSize,"onUpdate:pageSize":i[5]||(i[5]=e=>o.value.pageSize=e),"page-sizes":[10,20,30,40],total:C.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","total"])])):oe("",!0)])}}},Pe=Z(Ie,[["__scopeId","data-v-e312797e"]]);export{Pe as default};
